{"version": 3, "file": "SourceSelector.js", "sources": ["../../../../src/components/SourceSelector.vue", "../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvU291cmNlU2VsZWN0b3IudnVl"], "sourcesContent": ["<script setup>\nimport { ref, watch, onUnmounted } from 'vue';\nimport { navto } from '@/utils';\n\n// {{ AURA-X: Add - 创建出处选择器组件. Confirmed via 寸止 }}\nconst props = defineProps({\n  modelValue: {\n    type: Object,\n    default: null\n  },\n  placeholder: {\n    type: String,\n    default: '选择出处'\n  }\n});\n\nconst emit = defineEmits(['update:modelValue']);\n\nconst selectedSource = ref(props.modelValue);\n\n// 监听外部值变化\nwatch(() => props.modelValue, (newVal) => {\n  selectedSource.value = newVal;\n});\n\n// 打开出处搜索页面\nconst openSelector = () => {\n  navto('/pages/bundle/world/source/search?type=select');\n};\n\n// 清除选择\nconst clearSelection = (event) => {\n  event.stopPropagation();\n  selectedSource.value = null;\n  emit('update:modelValue', null);\n};\n\n// 监听全局事件，接收选择结果\nuni.$on('sourceSelected', (source) => {\n  selectedSource.value = source;\n  emit('update:modelValue', source);\n});\n\n// 组件销毁时移除事件监听\nonUnmounted(() => {\n  uni.$off('sourceSelected');\n});\n</script>\n\n<template>\n  <view class=\"source-selector\" @click=\"openSelector\">\n    <view class=\"selector-content\" :class=\"{ 'has-value': selectedSource }\">\n      <u-icon name=\"bookmark-fill\" size=\"20\" color=\"#999\" class=\"icon\"></u-icon>\n      \n      <view v-if=\"selectedSource\" class=\"selected-item\">\n        <image \n          v-if=\"selectedSource.cover_image\" \n          :src=\"selectedSource.cover_image\" \n          class=\"cover\"\n          mode=\"aspectFill\"\n        ></image>\n        <view v-else class=\"cover-placeholder\">\n          <u-icon name=\"bookmark\" size=\"24\" color=\"#6AC086\"></u-icon>\n        </view>\n        <view class=\"source-info\">\n          <text class=\"name\">{{ selectedSource.name }}</text>\n          <text v-if=\"selectedSource.publisher\" class=\"publisher\">{{ selectedSource.publisher }}</text>\n          <text v-else-if=\"selectedSource.category\" class=\"category\">{{ selectedSource.category }}</text>\n        </view>\n        <u-icon \n          name=\"close-circle-fill\" \n          @click=\"clearSelection\" \n          class=\"clear-btn\"\n          size=\"18\"\n          color=\"#ccc\"\n        ></u-icon>\n      </view>\n      \n      <view v-else class=\"placeholder\">\n        <text class=\"placeholder-text\">{{ placeholder }}</text>\n        <u-icon name=\"arrow-right\" size=\"16\" color=\"#ccc\" class=\"arrow\"></u-icon>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.source-selector {\n  width: 100%;\n  \n  .selector-content {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 0;\n    min-height: 88rpx; // 最小触摸区域\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .icon {\n      margin-right: 24rpx;\n      flex-shrink: 0;\n    }\n    \n    .selected-item {\n      display: flex;\n      align-items: center;\n      flex: 1;\n      \n      .cover {\n        width: 60rpx;\n        height: 80rpx;\n        border-radius: 8rpx;\n        margin-right: 24rpx;\n        flex-shrink: 0;\n        background-color: #f5f5f5;\n      }\n      \n      .cover-placeholder {\n        width: 60rpx;\n        height: 80rpx;\n        border-radius: 8rpx;\n        background-color: #f8f9fa;\n        border: 2rpx solid #6AC086;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 24rpx;\n        flex-shrink: 0;\n      }\n      \n      .source-info {\n        flex: 1;\n        \n        .name {\n          display: block;\n          font-size: 32rpx;\n          color: #333;\n          font-weight: 500;\n          line-height: 1.4;\n        }\n        \n        .publisher,\n        .category {\n          display: block;\n          font-size: 24rpx;\n          color: #999;\n          margin-top: 4rpx;\n        }\n      }\n      \n      .clear-btn {\n        margin-left: 16rpx;\n        flex-shrink: 0;\n      }\n    }\n    \n    .placeholder {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      flex: 1;\n      \n      .placeholder-text {\n        font-size: 32rpx;\n        color: #999;\n      }\n      \n      .arrow {\n        flex-shrink: 0;\n      }\n    }\n    \n    &.has-value {\n      background-color: #f8f9fa;\n      border-radius: 12rpx;\n      padding: 24rpx 32rpx;\n      border-bottom: none;\n    }\n  }\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/SourceSelector.vue'\nwx.createComponent(Component)"], "names": ["selectedSource", "ref", "props", "modelValue", "watch", "newVal", "value", "openSelector", "navto", "clearSelection", "event", "stopPropagation", "emit", "uni", "$on", "source", "onUnmounted", "$off", "wx", "createComponent", "Component"], "mappings": "yiBAkBMA,EAAiBC,EAAGA,IAACC,EAAMC,YAGjCC,EAAKA,OAAC,IAAMF,EAAMC,aAAaE,IAC7BL,EAAeM,MAAQD,CAAA,IAIzB,MAAME,EAAe,KACnBC,EAAKA,MAAC,gDAA+C,EAIjDC,EAAkBC,IACtBA,EAAMC,kBACNX,EAAeM,MAAQ,KACvBM,EAAK,oBAAqB,KAAI,SAIhCC,EAAAA,MAAIC,IAAI,kBAAmBC,IACzBf,EAAeM,MAAQS,EACvBH,EAAK,oBAAqBG,EAAM,IAIlCC,EAAAA,aAAY,aACNC,KAAK,iBAAgB,8kBC5C3BC,GAAGC,gBAAgBC"}