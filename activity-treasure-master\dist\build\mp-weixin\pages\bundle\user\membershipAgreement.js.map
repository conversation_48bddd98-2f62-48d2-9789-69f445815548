{"version": 3, "file": "membershipAgreement.js", "sources": ["../../../../../../src/pages/bundle/user/membershipAgreement.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHVzZXJcbWVtYmVyc2hpcEFncmVlbWVudC52dWU"], "sourcesContent": ["<template>\n  <view class=\"page\">\n    <myTitle\n        bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n        height=\"200rpx\"\n        title=\"会员服务协议\"\n        :backShow=\"true\"\n        color=\"#ffffff\"\n        :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <view class=\"agreement-content\">\n        \n        <!-- 会员权益 -->\n        <view class=\"section\">\n          <view class=\"section-title\">会员权益</view>\n          <view class=\"section-content\">\n            <view class=\"benefit-item\">\n              <view class=\"benefit-title\"># 无限次报名活动</view>\n              <view class=\"benefit-desc\">不限城市、不限次数、不限类型，线上线下均可报名，自主发起活动</view>\n            </view>\n            \n            <view class=\"benefit-item\">\n              <view class=\"benefit-title\">平台上发起活动</view>\n              <view class=\"benefit-desc\">自定主题、时间、地点（也可选择官方合作场地），在会员社群</view>\n            </view>\n            \n            <view class=\"benefit-item\">\n              <view class=\"benefit-title\">会员社群</view>\n              <view class=\"benefit-desc\">加入同城会员社群，活动小群等读书社群</view>\n            </view>\n            \n            <view class=\"benefit-item\">\n              <view class=\"benefit-title\">参与制定活动主题</view>\n              <view class=\"benefit-desc\">在群内制定主题/任务，官管官方发起活动</view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 体验不满意可全额退款 -->\n        <view class=\"section\">\n          <view class=\"section-title\">体验不满意可全额退款</view>\n          <view class=\"section-content\">\n            <view class=\"refund-item\">\n              <text>新用户注册30天内，日报名参加活动 1场，</text>\n            </view>\n            <view class=\"refund-item\">\n              <text>不满意</text>\n            </view>\n            <view class=\"refund-item\">\n              <text>可退款，超过30天或超过1场则无法退款</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 享受平台其他功能 -->\n        <view class=\"section\">\n          <view class=\"section-title\">享受平台其他功能</view>\n          <view class=\"section-content\">\n            <view class=\"feature-desc\">\n              如笔记、推书、文章等内容创作，加入建议读书小组等\n            </view>\n          </view>\n        </view>\n\n        <!-- 服务条款 -->\n        <view class=\"section\">\n          <view class=\"section-title\">服务条款</view>\n          <view class=\"section-content\">\n            <view class=\"terms-item\">\n              <view class=\"terms-number\">1.</view>\n              <view class=\"terms-text\">会员服务期限为购买时选择的期限，到期后需重新购买。</view>\n            </view>\n            \n            <view class=\"terms-item\">\n              <view class=\"terms-number\">2.</view>\n              <view class=\"terms-text\">会员权益仅限本人使用，不得转让或借用他人。</view>\n            </view>\n            \n            <view class=\"terms-item\">\n              <view class=\"terms-number\">3.</view>\n              <view class=\"terms-text\">平台保留对会员服务内容进行调整的权利，调整前会提前通知用户。</view>\n            </view>\n            \n            <view class=\"terms-item\">\n              <view class=\"terms-number\">4.</view>\n              <view class=\"terms-text\">如发现恶意使用会员权益或违反平台规定，平台有权取消会员资格。</view>\n            </view>\n            \n            <view class=\"terms-item\">\n              <view class=\"terms-number\">5.</view>\n              <view class=\"terms-text\">退款申请需在购买后30天内提出，超期不予受理。</view>\n            </view>\n            \n            <view class=\"terms-item\">\n              <view class=\"terms-number\">6.</view>\n              <view class=\"terms-text\">本协议的解释权归平台所有，如有争议以平台最终解释为准。</view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 联系我们 -->\n        <view class=\"section\">\n          <view class=\"section-title\">联系我们</view>\n          <view class=\"section-content\">\n            <view class=\"contact-item\">\n              <text>如有任何问题或建议，请联系我们的客服团队。</text>\n            </view>\n            <view class=\"contact-item\">\n              <text>客服邮箱：<EMAIL></text>\n            </view>\n            <view class=\"contact-item\">\n              <text>客服电话：400-123-4567</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 生效时间 -->\n        <view class=\"section\">\n          <view class=\"section-title\">协议生效</view>\n          <view class=\"section-content\">\n            <view class=\"effective-date\">\n              <text>本协议自用户点击\"我已阅读\"按钮时生效。</text>\n            </view>\n            <view class=\"effective-date\">\n              <text>最后更新时间：{{ new Date().toLocaleDateString() }}</text>\n            </view>\n          </view>\n        </view>\n\n      </view>\n      \n      <!-- 底部按钮 -->\n      <view class=\"bottom-actions\">\n        <u-button\n            text=\"我已阅读并同意\"\n            shape=\"circle\"\n            color=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n            :customStyle=\"{\n              width: '100%',\n              height: '88rpx',\n              fontSize: '32rpx',\n              fontWeight: '600',\n              boxShadow: '0 4rpx 16rpx rgba(106, 192, 134, 0.3)'\n            }\"\n            @click=\"agreeAndClose\"\n        ></u-button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport myTitle from \"@/components/myTitle.vue\";\n\nexport default {\n  components: {\n    myTitle\n  },\n  data() {\n    return {};\n  },\n  methods: {\n    agreeAndClose() {\n      // 返回上一页\n      uni.navigateBack();\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"less\">\n.page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);\n  padding-bottom: 120rpx;\n}\n\n.content-container {\n  padding: 40rpx 30rpx 0;\n  margin-top: 40rpx;\n}\n\n.agreement-content {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24rpx;\n  padding: 40rpx 30rpx;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);\n  backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(106, 192, 134, 0.1);\n}\n\n.section {\n  margin-bottom: 40rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #6AC086;\n  margin-bottom: 20rpx;\n  padding-bottom: 12rpx;\n  border-bottom: 2rpx solid rgba(106, 192, 134, 0.2);\n}\n\n.section-content {\n  line-height: 1.6;\n}\n\n.benefit-item {\n  margin-bottom: 24rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.benefit-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.benefit-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n}\n\n.refund-item {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 8rpx;\n}\n\n.feature-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n}\n\n.terms-item {\n  display: flex;\n  margin-bottom: 16rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.terms-number {\n  font-size: 26rpx;\n  color: #6AC086;\n  font-weight: 600;\n  margin-right: 8rpx;\n  flex-shrink: 0;\n}\n\n.terms-text {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n  flex: 1;\n}\n\n.contact-item {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 8rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.effective-date {\n  font-size: 24rpx;\n  color: #999;\n  line-height: 1.5;\n  margin-bottom: 8rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 20rpx 30rpx;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-top: 1rpx solid rgba(106, 192, 134, 0.1);\n  box-shadow: 0 -4rpx 16rpx rgba(106, 192, 134, 0.08);\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/user/membershipAgreement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["_sfc_main", "components", "myTitle", "data", "methods", "agreeAndClose", "uni", "index", "navigateBack", "wx", "createPage", "MiniProgramPage"], "mappings": "0DA4JKA,EAAU,CACbC,WAAY,CACVC,QAJY,IAAW,kCAMzBC,KAAO,KACE,IAETC,QAAS,CACP,aAAAC,GAEEC,EAAGC,MAACC,cACN,otBCtKJC,GAAGC,WAAWC"}