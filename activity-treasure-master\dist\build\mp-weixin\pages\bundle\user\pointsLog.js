"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),o=require("../../../store/index.js"),l=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/cacheManager.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||(n+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const n=()=>"../../../components/myTitle.js",i={__name:"pointsLog",setup(n){const i=e.ref([]),a=e.ref(!1),u=e.ref(!1),r=e.ref(1),s=e.ref(0),c=e.ref(0);e.onLoad((()=>{v()})),e.onShow((()=>{d()})),e.onPullDownRefresh((()=>{d()})),e.onReachBottom((()=>{u.value||a.value||g()}));const v=async(n=!1)=>{var v,d,g;if(!a.value&&l.requireLogin("","请先登录后查看积分记录")){a.value=!0;try{console.log("开始获取积分记录，参数:",{uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token,page:n?1:r.value,page_size:20});const l=await t.userget_points_log({uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token,page:n?1:r.value,page_size:20});if(console.log("积分记录API响应:",l),"ok"===(null==l?void 0:l.status)){const e=(null==(v=l.data)?void 0:v.list)||[];n?(i.value=e,r.value=1,u.value=!1):i.value=[...i.value,...e],s.value=(null==(d=l.data)?void 0:d.total)||0,c.value=(null==(g=l.data)?void 0:g.current_points)||0,e.length<20?u.value=!0:r.value++}else"empty"===(null==l?void 0:l.status)?(n&&(i.value=[]),u.value=!0,console.log("没有积分记录数据")):(console.error("获取积分记录失败:",(null==l?void 0:l.msg)||"未知错误"),e.index.$u.toast((null==l?void 0:l.msg)||"获取积分记录失败"))}catch(p){console.error("获取积分记录异常:",p),e.index.$u.toast("获取积分记录失败，请稍后重试")}finally{a.value=!1,e.index.stopPullDownRefresh()}}},d=()=>{r.value=1,u.value=!1,v(!0)},g=()=>{v(!1)},p=e=>{if(!e)return"";const t=e.replace(/-/g,"/"),o=new Date(t);return isNaN(o.getTime())?"时间格式错误":o.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},f=e=>e>0?"#6AC086":"#FF6B35";return(t,o)=>e.e({a:e.p({title:"积分记录","bg-color":"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",titleStyle:{color:"#ffffff",fontWeight:"600",fontSize:"36rpx"}}),b:e.p({name:"star-fill",color:"#ffffff",size:"44rpx"}),c:e.t(c.value),d:i.value.length>0},i.value.length>0?{e:e.f(i.value,((t,o,l)=>{return{a:"411122e2-2-"+l,b:e.p({name:(i=t.points_change,i>0?"plus-circle-fill":"minus-circle-fill"),color:f(t.points_change),size:"32rpx"}),c:e.t((n=t.source_type,{member_init:"开通会员",activity_checkin:"活动签到",activity_complete:"活动完成",daily_signin:"每日签到",invite_friend:"邀请好友",consume_points:"积分消费",admin_adjust:"管理员调整"}[n]||"其他")),d:e.t(p(t.created_at)),e:e.t(t.points_change>0?"+":""),f:e.t(t.points_change),g:f(t.points_change),h:e.t(t.description),i:e.t(t.points_balance),j:t.id};var n,i}))}:a.value?{}:{g:e.p({name:"star",color:"#ccc",size:"120rpx"})},{f:!a.value,h:a.value&&0===i.value.length},a.value&&0===i.value.length?{i:e.p({mode:"circle",color:"#6AC086",size:"60rpx"})}:{},{j:a.value&&i.value.length>0},a.value&&i.value.length>0?{k:e.p({mode:"circle",color:"#6AC086",size:"40rpx"})}:{},{l:u.value&&i.value.length>0},(u.value&&i.value.length,{}))}};wx.createPage(i);
//# sourceMappingURL=pointsLog.js.map
