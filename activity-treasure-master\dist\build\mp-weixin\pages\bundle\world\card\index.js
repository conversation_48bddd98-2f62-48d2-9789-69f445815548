"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../utils/index.js"),t=require("../../../../store/index.js"),a=require("../../../../api/index.js"),i=require("../../../../utils/auth.js");if(require("../../../../utils/systemInfo.js"),require("../../../../store/counter.js"),require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-empty"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-empty/u-empty.js")+n)();const n=()=>"../../../../components/share-popup/share-popup.js",r={__name:"index",props:{cards:{type:Array,default:()=>[]},loading:{type:Boolean,default:!0},error:{type:String,default:""}},setup(n){const r=n,s=e.ref([]),l=e.ref(""),d=e.ref(0),u=e.ref(""),c=e.ref(!1),v=e.ref(null),p=e.ref(!1);e.onShareAppMessage((()=>{var e,o,a,i,n,r,s,l,d;try{return v.value?{title:v.value.description?v.value.description.length>30?v.value.description.substring(0,30)+"...":v.value.description:"分享一张精美日卡",path:`/pages/bundle/world/card/detail?cardId=${v.value.id}`,imageUrl:v.value.background_image_url||(null==(r=null==(n=null==(i=t.store().$state.config)?void 0:i.img_config)?void 0:n.app_logo)?void 0:r.val)||""}:(console.warn("日卡信息未设置，使用默认分享信息"),{title:"分享一张精美日卡",path:"/pages/bundle/world/card/index",imageUrl:(null==(a=null==(o=null==(e=t.store().$state.config)?void 0:e.img_config)?void 0:o.app_logo)?void 0:a.val)||""})}catch(u){return console.error("日卡分享配置失败:",u),{title:"分享一张精美日卡",path:"/pages/bundle/world/card/index",imageUrl:(null==(d=null==(l=null==(s=t.store().$state.config)?void 0:s.img_config)?void 0:l.app_logo)?void 0:d.val)||""}}}));const g=e.computed((()=>h(new Date))),h=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,m=e=>{if(!e)return"";return["日","一","二","三","四","五","六"][new Date(e).getDay()]},f=o=>{let t=-1;if(o&&o.detail&&"number"==typeof o.detail.current)t=o.detail.current;else{if(!o||"number"!=typeof o.current)return void console.error("onSwiperChange called with invalid or unexpected event object structure:",o);t=o.current,console.warn("onSwiperChange received event with direct 'current' property:",o)}if(console.log("Swiper changed to index:",t),r.cards&&r.cards[t]){const o=r.cards[t].date;l.value!==o&&(l.value=o,e.nextTick$1((()=>{u.value=`date-${t}`,console.log(`[${Date.now()}] Inside nextTick (SwiperChange) - Scrolling date axis to:`,u.value)})))}else console.warn(`Swiper changed to invalid index ${t} or cards not ready.`)},w=o=>{console.log("分享成功:",o),e.index.showToast({title:"分享成功",icon:"success"})},x=o=>{console.error("分享失败:",o),e.index.showToast({title:"分享失败",icon:"none"})},k=()=>{e.index.showToast({title:"刷新中...",icon:"loading",duration:1e3}),setTimeout((()=>{e.index.showToast({title:"刷新完成",icon:"success"})}),1e3)};return e.watch((()=>r.cards),(o=>{if(console.log("Props cards updated in CardIndex watcher:",o),o&&o.length>0){(e=>{const o=g.value;s.value=e.map(((e,t)=>{const a=e.date.split("-");return{date:e.date,displayShort:`${a[2]}`,displayDay:m(e.date),isToday:e.date===o,id:`date-${t}`}})),console.log("Generated date list:",s.value)})(o);const t=o.findIndex((e=>e.date===g.value)),a=-1!==t?t:0;0===d.value&&""===l.value?(d.value=a,l.value=o[a].date,console.log("CardIndex: Initial setup - Swiper index:",a,"Selected date:",l.value),e.nextTick$1((()=>{u.value=`date-${a}`,console.log(`[${Date.now()}] Inside nextTick (Watch Initial) - Scrolling date axis to:`,u.value)}))):console.log("CardIndex: Cards updated, maintaining current index:",d.value)}else s.value=[],l.value="",d.value=0}),{immediate:!0,deep:!0}),(g,h)=>{var m,y,$,I,T,S;return e.e({a:e.f(s.value,((o,t,a)=>({a:e.t(o.displayDay),b:e.t(o.displayShort),c:o.date===l.value?1:"",d:o.isToday?1:"",e:e.o((a=>((o,t)=>{if(console.log("Date selected:",o.date,"at index:",t),l.value!==o.date){l.value=o.date;const a=r.cards.findIndex((e=>e.date===o.date));-1!==a&&d.value!==a?(console.log("Setting swiper index to:",a),c.value=!0,d.value=a,setTimeout((()=>{c.value=!1}),150)):-1===a&&console.warn("Selected date not found in cards data:",o.date),e.nextTick$1((()=>{u.value=`date-${t}`,console.log(`[${Date.now()}] Inside nextTick (DateSelect) - Scrolling date axis to:`,u.value)}))}})(o,t)),o.date),f:o.date,g:o.id}))),b:u.value,c:e.p({name:"reload",size:"20",color:"#999"}),d:e.o(k),e:n.loading&&0===n.cards.length},n.loading&&0===n.cards.length?{f:e.p({mode:"circle",size:"24"})}:n.error?{h:e.p({mode:"list",text:n.error})}:0===n.cards.length?{j:e.p({mode:"list",text:"暂无日卡数据"})}:e.e({k:n.cards&&n.cards.length>0},n.cards&&n.cards.length>0?{l:e.f(n.cards,((n,s,l)=>e.e({a:n},n?e.e({b:n.background_image_url||"/static/default-card-bg.png",c:e.t(n.description),d:n.author},n.author?{e:e.t(n.author)}:{},{f:"5a05a621-4-"+l,g:e.p({name:"chat",size:"20",color:"#666"}),h:e.t(n.commentCount||0),i:e.o((e=>{return t=n.id,(a=e)&&a.stopPropagation(),void o.navto(`/pages/bundle/world/card/detail?cardId=${t}&showComments=true`);var t,a}),(null==n?void 0:n.id)||s),j:"5a05a621-5-"+l,k:e.p({name:n.isLiked?"heart-fill":"heart",size:"20",color:n.isLiked?"#ff6b81":"#666"}),l:e.t(n.likeCount||0),m:n.isLiked?1:"",n:e.o((o=>(async(o,n)=>{if(!i.requireLogin("","请先登录后再点赞"))return;const s=r.cards.find((e=>e.id===o));if(!s)return void e.index.showToast({title:"卡片数据不存在",icon:"none"});n&&n.stopPropagation();const l=s.isLiked,d=s.likeCount;s.isLiked=!s.isLiked,s.likeCount+=s.isLiked?1:-1;try{console.log("发送点赞请求:",{id:o,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});const i=await a.likeCard({id:o,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});console.log("点赞响应:",i),"ok"!==i.status?(s.isLiked=l,s.likeCount=d,e.index.showToast({title:i.msg||"操作失败",icon:"none"})):e.index.showToast({title:i.msg||"操作成功",icon:"success"})}catch(u){s.isLiked=l,s.likeCount=d,console.error("点赞失败:",u),e.index.showToast({title:"操作失败，请重试",icon:"none"})}})(n.id,o)),(null==n?void 0:n.id)||s),o:"5a05a621-6-"+l,p:e.p({name:n.isFavorited?"star-fill":"star",size:"20",color:n.isFavorited?"#FFD700":"#666"}),q:e.o((o=>(async(o,n)=>{if(!i.requireLogin("","请先登录后再收藏"))return;n&&n.stopPropagation();const s=r.cards.find((e=>e.id===o));if(!s)return void e.index.showToast({title:"卡片数据不存在",icon:"none"});const l=s.isFavorited||!1;s.isFavorited=!s.isFavorited;try{const i=await a.favoriteCard({id:o,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});"ok"!==i.status?(s.isFavorited=l,e.index.showToast({title:i.msg||"操作失败",icon:"none"})):e.index.showToast({title:i.msg||"操作成功",icon:"success"})}catch(d){s.isFavorited=l,console.error("收藏失败:",d),e.index.showToast({title:"操作失败，请重试",icon:"none"})}})(n.id,o)),(null==n?void 0:n.id)||s),r:e.o((e=>{return t=n.id,void o.navto(`/pages/bundle/world/card/detail?cardId=${t}`);var t}),(null==n?void 0:n.id)||s)}):{},{s:(null==n?void 0:n.id)||s})))}:{},{m:d.value,n:e.o(f),o:n.cards&&n.cards.length>0?1:0}),{g:n.error,i:0===n.cards.length,p:c.value},c.value?{q:e.p({mode:"circle",size:"30",color:"#FFFFFF"})}:{},{r:e.o((e=>p.value=!1)),s:e.o(w),t:e.o(x),v:e.p({show:p.value,title:"分享日卡","share-data":{image:null==(m=v.value)?void 0:m.background_image_url,content:null==(y=v.value)?void 0:y.description,author:null==($=v.value)?void 0:$.author,date:null==(I=v.value)?void 0:I.date,template:"card"},"show-member-invite":0===(null==(T=e.unref(t.store)().$state.userInfo)?void 0:T.role_type)||1===(null==(S=e.unref(t.store)().$state.userInfo)?void 0:S.role_type)})})}}},s=e._export_sfc(r,[["__scopeId","data-v-5a05a621"]]);wx.createComponent(s);
//# sourceMappingURL=index.js.map
