{"version": 3, "file": "index.js", "sources": ["../../../../src/utils/index.js"], "sourcesContent": ["import { store } from \"@/store\";\nimport { getCurrentInstance } from \"vue\";\nimport { userlogin, userget_user_info, getAddr } from \"@/api\";\n\n/**\n @@param {getWindowHeight} getWindowHeight 获取视口高度\n */\nexport const getWindowHeight = () => {\n  const res = uni.$u.sys();\n  return res.windowHeight;\n};\n\n/**\n @@param {getListHeight} getListHeight 获取元素信息\n */\nexport const getListHeight = async (e = \"list\") => {\n  const { proxy } = getCurrentInstance();\n  return await proxy.$u.getRect(\".\" + e);\n};\n\n/**\n @@param {setListHeight} setListHeight 设置元素高度\n */\nexport const setListHeight = async (e = \"list\") => {\n  const { proxy } = getCurrentInstance();\n  const res = await proxy.$u.getRect(\".\" + e);\n  return getWindowHeight() - res.top;\n};\n\n/**\n @@param {getImgInfo} getImgInfo 获取图片信息\n */\nexport const getImgInfo = async (e) => {\n  return new Promise((resolve, reject) => {\n    uni.getImageInfo({\n      src: e,\n      success: (res) => resolve(res),\n      fail: (err) => reject(err)\n    });\n  });\n};\n\n/**\n @@param {copy} copy 复制\n */\nexport const copy = (e) =>\n  uni.setClipboardData({\n    data: e + \"\",\n    success: function () {\n      uni.$u.toast(\"复制成功\");\n    }\n  });\n\n/**\n @@param {mergeList} mergeList 改变数组内相同元素并合并\n */\nexport const mergeList = (arr, newArr, name) => {\n  arr.map((val) => {\n    let newName = newArr.find((i) => i.name == val.name);\n    if (!newName) {\n      newArr.push({\n        name: val.name,\n        val: [val]\n      });\n    } else {\n      newName.val.push(val);\n    }\n  });\n};\n\n/**\n @@param {BaseToUrl} BaseToUrl Base64转url\n */\nexport const BaseToUrl = (base64) => {\n  var arr = base64.split(\",\");\n  var mime = arr[0].match(/:(.*?);/)[1];\n  var bstr = atob(arr[1]);\n  var n = bstr.length;\n  var u8arr = new Uint8Array(n);\n  for (var i = 0; i < n; i++) {\n    u8arr[i] = bstr.charCodeAt(i);\n  }\n  var url = URL || webkitURL;\n  return url.createObjectURL(\n    new Blob([u8arr], {\n      type: mime\n    })\n  );\n};\n\n/**\n @@param {navto} navto 跳转页面\n */\nexport const navto = (e, type = \"nav\") => {\n  switch (type) {\n    case \"nav\":\n      uni.navigateTo({\n        url: e\n      });\n      break;\n    case \"tab\":\n      uni.switchTab({\n        url: e\n      });\n      break;\n    case \"red\":\n      uni.redirectTo({\n        url: e\n      });\n      break;\n    case \"rel\":\n      uni.reLaunch({\n        url: e\n      });\n      break;\n  }\n};\n\n/**\n @@param {back} back 返回上一级页面\n */\nexport const back = (e) => {\n  if (e?.tip) uni.$u.toast(e.tip);\n  setTimeout(() => {\n    uni.navigateBack({\n      delta: e?.num || 1\n    });\n  }, e?.time || 3000);\n};\n\n/**\n @@param {download} download 下载文件\n */\nexport const download = (e) => {\n  uni.downloadFile({\n    url: e,\n    success: (res) => {\n      //下载成功\n      if (res.statusCode === 200) {\n        uni.getFileSystemManager().saveFile({\n          tempFilePath: res.tempFilePath,\n          success(res) {\n            uni.showLoading({\n              title: \"正在打开\"\n            });\n            uni.openDocument({\n              filePath: res.savedFilePath,\n              showMenu: true, //是否可以分享\n              success: (res) => {\n                uni.hideLoading();\n              },\n              fail: (e) => {\n                uni.hideLoading();\n                uni.$u.toast(\"打开失败\");\n              }\n            });\n          }\n        });\n      }\n    },\n    fail: (e) => {\n      console.log(e, \"文件下载失败\");\n      uni.showToast({\n        title: \"文件下载失败\",\n        icon: \"error\"\n      });\n    }\n  });\n};\n\n/**\n @@param {getItem} getItem 根据对应值获取数组内的状态\n */\nexport const getItem = (arr, e) => arr[e];\n/**\n\n\n/**\n @@param {getDifferTime} getDifferTime 获取相差多少时间\n */\nexport const getDifferTime = (endTime, startTime = Date.now()) => {\n  if ((startTime + \"\").length <= 10) startTime = startTime * 1000;\n  if ((endTime + \"\").length <= 10) endTime = endTime * 1000;\n  let date1 = new Date(startTime); //开始时间\n  let date2 = new Date(endTime); //结束时间\n  let date3 = date2.getTime() - date1.getTime(); //时间差的毫秒数\n  //计算出相差天数\n  let d = Math.floor(date3 / (24 * 3600 * 1000));\n  //计算出小时数\n  let leave1 = date3 % (24 * 3600 * 1000); //计算天数后剩余的毫秒数\n  let h = Math.floor(leave1 / (3600 * 1000));\n  //计算相差分钟数\n  let leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数\n  let m = Math.floor(leave2 / (60 * 1000));\n  //计算相差秒数\n  let leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数\n  let s = Math.round(leave3 / 1000);\n  return {\n    d,\n    h,\n    m,\n    s\n  };\n};\n\n/**\n *\n @@param {callPhone} callPhone 打电话\n */\nexport const callPhone = (e) =>\n  uni.makePhoneCall({\n    phoneNumber: e,\n    success: (res) => {},\n    fail: (res) => {\n      uni.$u.toast(\"联系失败\");\n    }\n  });\n\n/**\n *\n @@param {pay} pay 支付\n */\nexport const pay = (e) => {\n  return new Promise((resolve, reject) => {\n    uni.requestPayment({\n      provider: e.provider,\n      // #ifdef MP-WEIXIN\n      timeStamp: e.timeStamp.toString(),\n      nonceStr: e.nonceStr,\n      package: e.package,\n      signType: e.signType,\n      paySign: e.paySign,\n      // #endif\n      // #ifdef MP-TOUTIAO\n      orderInfo: {\n        order_id: e.order_id,\n        order_token: e.order_token\n      },\n      service: 5,\n      // #endif\n      // #ifdef MP-KUAISHOU\n      orderInfo: {\n        order_id: e.order_id,\n        order_token: e.order_token\n      },\n      // #endif\n      success: function (res) {\n        resolve(res);\n      },\n      fail: function (err) {\n        resolve(err);\n      }\n    });\n  });\n};\n\n/**\n *\n @@param {resetList} resetList 重置列表\n */\n// export const resetList = (e) => {\n//   e.form ? e.form : form\n// }\n\n/**\n *\n @@param {getAge} getAge 通过生日获取年龄\n */\nexport const getAge = (val) => {\n  let currentYear = new Date().getFullYear(); //当前的年份\n  let calculationYear = new Date(val).getFullYear(); //计算的年份\n  const wholeTime = currentYear + val.substring(4); //周岁时间\n  const calculationAge = currentYear - calculationYear; //按照年份计算的年龄\n  //判断是否过了生日\n  if (new Date().getTime() > new Date(wholeTime).getTime()) {\n    return calculationAge;\n  } else {\n    return calculationAge - 1;\n  }\n};\n\n/**\n *\n @@param {login} login 登录\n */\nexport const login = (e) => {\n  uni.login({\n    provider: \"weixin\",\n    success: async (loginRes) => {\n      // 🆕 新增：尝试获取用户当前城市信息\n      let userCity = '';\n      try {\n        // 获取用户位置\n        const locationRes = await new Promise((resolve, reject) => {\n          uni.getLocation({\n            type: 'gcj02',\n            success: resolve,\n            fail: reject\n          });\n        });\n\n        // 通过高德API获取城市信息\n        if (locationRes && locationRes.latitude && locationRes.longitude) {\n          const addrRes = await getAddr({\n            latitude: locationRes.latitude,\n            longitude: locationRes.longitude\n          });\n\n          if (addrRes && addrRes.status == 1) {\n            const add = addrRes.regeocode.addressComponent;\n            userCity = uni.$u.test.isEmpty(add.city) ? add.province : add.city;\n            // {{ AURA-X: Modify - 统一使用市级adcode格式，包括直辖市. Confirmed via 寸止. }}\n            // 统一使用市级adcode格式用于后端city_id字段\n            if (add.adcode && add.adcode.length >= 4) {\n              const provinceCode = add.adcode.substring(0, 2) + '0000';\n              // 直辖市映射到对应的市级adcode\n              const municipalityMap = {\n                '110000': '110100', // 北京市 → 北京城区\n                '120000': '120100', // 天津市 → 天津城区\n                '310000': '310100', // 上海市 → 上海城区\n                '500000': '500100'  // 重庆市 → 重庆城区\n              };\n\n              if (municipalityMap[provinceCode]) {\n                // 直辖市使用对应的市级adcode\n                e.city_id = municipalityMap[provinceCode];\n              } else {\n                // 其他城市使用市级adcode（前4位+00）\n                e.city_id = add.adcode.substring(0, 4) + '00';\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.log('获取城市信息失败，将使用空值注册:', error);\n        // 获取城市信息失败不影响登录流程\n      }\n\n      const res = await userlogin({\n        code: loginRes.code,\n        pid: store().$state.pid,\n        city: userCity // 🆕 新增：传递城市信息\n      });\n      if (res.status === \"ok\") e.options ? e.fun(e.options) : e.fun();\n      else uni.$u.toast(\"登录失败\");\n    }\n  });\n};\n\n/**\n *\n @@param {getUserInfo} getUserInfo 获取用户信息并存入pinia\n */\nexport const getUserInfo = async (fun) => {\n  try {\n    const res = await userget_user_info();\n    console.log('获取用户信息响应:', res);\n\n    if (res && res.status === \"ok\" && res.data) {\n      store().changeUserInfo(res.data);\n      console.log('用户信息已更新到store:', res.data);\n    } else if (res && res.status === \"relogin\") {\n      // 处理认证失败的情况\n      console.log('用户认证失败，清理登录状态:', res.msg);\n\n      try {\n        // 动态导入auth模块避免循环依赖\n        const { handleAuthFailure } = await import('@/utils/auth');\n        // 使用统一的认证失败处理机制\n        handleAuthFailure(res, 'getUserInfo');\n      } catch (error) {\n        console.error('处理认证失败时出错:', error);\n        // 备用处理：直接清理本地状态\n        try {\n          const { logout } = await import('@/utils/auth');\n          logout();\n        } catch (logoutError) {\n          console.error('备用登出处理失败:', logoutError);\n        }\n      }\n    } else {\n      console.log('获取用户信息失败:', res);\n    }\n\n    if (fun && typeof fun === 'function') {\n      fun();\n    }\n  } catch (error) {\n    console.error('获取用户信息异常:', error);\n  }\n};\n\n/**\n * 检查用户是否已登录\n * @returns {boolean} 是否已登录\n */\nexport const isUserLoggedIn = () => {\n  const userInfo = store().$state.userInfo;\n  return !!(userInfo?.uid && userInfo?.token);\n};\n\n/**\n * 安全调用需要登录的API\n * @param {Function} apiCall API调用函数\n * @param {string} errorMessage 未登录时的错误提示\n * @returns {Promise} API调用结果或错误\n */\nexport const safeApiCall = async (apiCall, errorMessage = '请先登录后再操作') => {\n  if (!isUserLoggedIn()) {\n    console.log('用户未登录，跳过API调用');\n    return {\n      status: 'relogin',\n      msg: errorMessage\n    };\n  }\n\n  try {\n    return await apiCall();\n  } catch (error) {\n    console.error('API调用失败:', error);\n    return {\n      status: 'error',\n      msg: error.message || '操作失败，请重试'\n    };\n  }\n};\n\n/**\n *\n @@param {pxToRpx} pxToRpx px转rpx\n */\n// 导入新的系统信息API\nimport { pxToRpx as newPxToRpx, getWindowInfo } from './systemInfo.js';\n\nexport const pxToRpx = (px) => {\n  try {\n    // 使用新的API替换方案\n    return newPxToRpx(px);\n  } catch (error) {\n    console.warn('pxToRpx转换失败，使用默认值:', error);\n    // 使用默认屏幕宽度 375px\n    return (750 * Number.parseInt(px)) / 375;\n  }\n};\n\nexport const timeSetResult = () => (1713024000000 > new Date() * 1 ? true : false);\n"], "names": ["e", "tip", "uni", "index", "$u", "toast", "setTimeout", "navigateBack", "delta", "num", "time", "makePhoneCall", "phoneNumber", "success", "res", "fail", "setClipboardData", "data", "arr", "async", "proxy", "getCurrentInstance", "getRect", "fun", "userget_user_info", "console", "log", "status", "store", "changeUserInfo", "msg", "handleAuthFailure", "error", "logout", "logoutError", "login", "provider", "loginRes", "userCity", "locationRes", "Promise", "resolve", "reject", "getLocation", "type", "latitude", "longitude", "addrRes", "getAddr", "add", "regeocode", "addressComponent", "test", "isEmpty", "city", "province", "adcode", "length", "provinceCode", "substring", "municipalityMap", "city_id", "userlogin", "code", "pid", "$state", "options", "navigateTo", "url", "switchTab", "redirectTo", "reLaunch", "requestPayment", "timeStamp", "toString", "nonceStr", "package", "signType", "paySign", "err", "px", "newPxToRpx", "warn", "Number", "parseInt", "sys", "windowHeight", "top"], "mappings": "0JAyHqBA,KACZ,MAAHA,OAAG,EAAAA,EAAAC,MAAKC,EAAGC,MAACC,GAAGC,MAAML,EAAEC,KAC3BK,YAAW,KACTJ,EAAAA,MAAIK,aAAa,CACfC,aAAOR,WAAGS,MAAO,GAClB,IACG,MAAHT,OAAG,EAAAA,EAAAU,OAAQ,IAAI,oBAkFMV,GACxBE,EAAAA,MAAIS,cAAc,CAChBC,YAAaZ,EACba,QAAUC,IAAD,EACTC,KAAOD,IACLZ,EAAAA,MAAIE,GAAGC,MAAM,OAAM,iBAzKJL,GACnBE,EAAAA,MAAIc,iBAAiB,CACnBC,KAAMjB,EAAI,GACVa,QAAS,WACPX,EAAAA,MAAIE,GAAGC,MAAM,OACf,oBA2HmB,CAACa,EAAKlB,IAAMkB,EAAIlB,yBA9JVmB,MAAOnB,EAAI,UACtC,MAAMoB,MAAEA,GAAUC,EAAAA,qBAClB,aAAaD,EAAMhB,GAAGkB,QAAQ,IAAMtB,EAAC,sBAgVZmB,MAAOI,IAC5B,IACI,MAAAT,QAAYU,EAAAA,oBAGlB,GAFQC,QAAAC,IAAI,YAAaZ,GAErBA,GAAsB,OAAfA,EAAIa,QAAmBb,EAAIG,KACpCW,EAAAA,QAAQC,eAAef,EAAIG,MACnBQ,QAAAC,IAAI,iBAAkBZ,EAAIG,WACzB,GAAAH,GAAsB,YAAfA,EAAIa,OAAsB,CAElCF,QAAAC,IAAI,iBAAkBZ,EAAIgB,KAE9B,IAEI,MAAAC,kBAAEA,QAAmC,YAE3CA,EAAkBjB,EAAK,cACxB,OAAQkB,GACCP,QAAAO,MAAM,aAAcA,GAExB,IACI,MAAAC,OAAEA,QAAwB,eAEjC,OAAQC,GACCT,QAAAO,MAAM,YAAaE,EAC7B,CACF,CACN,MACcT,QAAAC,IAAI,YAAaZ,GAGvBS,GAAsB,mBAARA,MAGnB,OAAQS,GACCP,QAAAO,MAAM,YAAaA,EAC7B,iBAxGoBhC,IACpBE,EAAAA,MAAIiC,MAAM,CACRC,SAAU,SACVvB,QAASM,MAAOkB,IAEd,IAAIC,EAAW,GACX,IAEF,MAAMC,QAAoB,IAAIC,SAAQ,CAACC,EAASC,KAC9CxC,EAAAA,MAAIyC,YAAY,CACdC,KAAM,QACN/B,QAAS4B,EACT1B,KAAM2B,GACP,IAIH,GAAIH,GAAeA,EAAYM,UAAYN,EAAYO,UAAW,CAC1D,MAAAC,QAAgBC,UAAQ,CAC5BH,SAAUN,EAAYM,SACtBC,UAAWP,EAAYO,YAGrB,GAAAC,GAA6B,GAAlBA,EAAQpB,OAAa,CAC5B,MAAAsB,EAAMF,EAAQG,UAAUC,iBAI9B,GAHWjD,EAAAA,EAAGC,MAACC,GAAGgD,KAAKC,QAAQJ,EAAIK,MAAQL,EAAIM,SAAWN,EAAIK,KAG1DL,EAAIO,QAAUP,EAAIO,OAAOC,QAAU,EAAG,CACxC,MAAMC,EAAeT,EAAIO,OAAOG,UAAU,EAAG,GAAK,OAE5CC,EAAkB,CACtB,KAAU,SACV,KAAU,SACV,KAAU,SACV,IAAU,UAGRA,EAAgBF,GAEhB1D,EAAA6D,QAAUD,EAAgBF,GAG5B1D,EAAE6D,QAAUZ,EAAIO,OAAOG,UAAU,EAAG,GAAK,IAE7C,CACF,CACF,CACD,OAAQ3B,GACCP,QAAAC,IAAI,oBAAqBM,EAEnC,CAOmB,cALD8B,YAAU,CAC1BC,KAAM1B,EAAS0B,KACfC,IAAKpC,EAAAA,QAAQqC,OAAOD,IACpBV,KAAMhB,KAEAX,OAAiB3B,EAAEkE,QAAUlE,EAAEuB,IAAIvB,EAAEkE,SAAWlE,EAAEuB,MACrDrB,EAAAA,MAAIE,GAAGC,MAAM,OAAM,GAE3B,gBA7PkB,CAACL,EAAG4C,EAAO,SAC9B,OAAQA,GACN,IAAK,MACH1C,EAAAA,MAAIiE,WAAW,CACbC,IAAKpE,IAEP,MACF,IAAK,MACHE,EAAAA,MAAImE,UAAU,CACZD,IAAKpE,IAEP,MACF,IAAK,MACHE,EAAAA,MAAIoE,WAAW,CACbF,IAAKpE,IAEP,MACF,IAAK,MACHE,EAAAA,MAAIqE,SAAS,CACXH,IAAKpE,IAGX,cA2GkBA,GACX,IAAIwC,SAAQ,CAACC,EAASC,KAC3BxC,EAAAA,MAAIsE,eAAe,CACjBpC,SAAUpC,EAAEoC,SAEZqC,UAAWzE,EAAEyE,UAAUC,WACvBC,SAAU3E,EAAE2E,SACZC,QAAS5E,EAAE4E,QACXC,SAAU7E,EAAE6E,SACZC,QAAS9E,EAAE8E,QAeXjE,QAAS,SAAUC,GACjB2B,EAAQ3B,EACT,EACDC,KAAM,SAAUgE,GACdtC,EAAQsC,EACV,GACD,oBAsLmBC,IAClB,IAEKC,OAAAA,EAAAA,QAAWD,EACnB,OAAQhD,GAGP,OAFQP,QAAAyD,KAAK,qBAAsBlD,GAE3B,IAAMmD,OAAOC,SAASJ,GAAO,GACvC,yBAna2B7D,MAAOnB,EAAI,UACtC,MAAMoB,MAAEA,GAAUC,EAAAA,qBACZP,QAAYM,EAAMhB,GAAGkB,QAAQ,IAAMtB,GAClC,OAlBKE,EAAAA,MAAIE,GAAGiF,MACRC,aAiBgBxE,EAAIyE,GAAA"}