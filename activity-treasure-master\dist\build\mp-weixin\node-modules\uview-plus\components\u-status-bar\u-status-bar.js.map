{"version": 3, "file": "u-status-bar.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-status-bar/u-status-bar.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN0YXR1cy1iYXIvdS1zdGF0dXMtYmFyLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view\n\t    :style=\"[style]\"\n\t    class=\"u-status-bar\"\n\t>\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge, sys } from '../../libs/function/index';\n\t/**\n\t * StatbusBar 状态栏占位\n\t * @description 本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。\n\t * @tutorial https://uview-plus.jiangruyi.com/components/statusBar.html\n\t * @property {String}\t\t\tbgColor\t\t\t背景色 (默认 'transparent' )\n\t * @property {String | Object}\tcustomStyle\t\t自定义样式 \n\t * @example <u-status-bar></u-status-bar>\n\t */\n\texport default {\n\t\tname: 'u-status-bar',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tstyle() {\n\t\t\t\tconst style = {}\n\t\t\t\t// 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式\n\t\t\t\tstyle.height = addUnit(sys().statusBarHeight, 'px')\n\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.u-status-bar {\n\t\t// nvue会默认100%，如果nvue下，显式写100%的话，会导致宽度不为100%而异常\n\t\t/* #ifndef APP-NVUE */\n\t\twidth: 100%;\n\t\t/* #endif */\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-status-bar/u-status-bar.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "computed", "style", "height", "addUnit", "sys", "statusBarHeight", "backgroundColor", "this", "bgColor", "deepMerge", "addStyle", "customStyle", "wx", "createComponent", "Component"], "mappings": "6DAsBMA,EAAU,CACdC,KAAM,eACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,KAAO,KACC,CACP,GAEDC,SAAU,CACT,KAAAC,GACC,MAAMA,EAAQ,CAAC,EAIf,OAFAA,EAAMC,OAASC,EAAOA,QAACC,EAAGA,MAAGC,gBAAiB,MAC9CJ,EAAMK,gBAAkBC,KAAKC,QACtBC,EAASA,UAACR,EAAOS,EAAQA,SAACH,KAAKI,aACvC,wHCnCHC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}