{"version": 3, "file": "edit.js", "sources": ["../../../../../../../src/pages/bundle/world/feed/edit.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGZlZWRcZWRpdC52dWU"], "sourcesContent": ["<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { getFeedDetail, editFeed, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport { onLoad } from '@dcloudio/uni-app';\nimport { requireLogin } from '@/utils/auth';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 实现动态编辑页面. Confirmed via 寸止. }}\n\n// 状态管理\nconst content = ref(''); // 动态内容\nconst images = ref([]); // 图片列表\nconst location = ref(null); // 位置信息\nconst tags = ref(''); // 标签\nconst privacy = ref('public'); // 隐私设置\nconst isPrivate = ref(false); // 是否设为私密\nconst isSubmitting = ref(false); // 提交状态\nconst isLoading = ref(true); // 加载状态\nconst feedId = ref(null); // 动态ID\n\n// 隐私选项\nconst privacyOptions = [\n  { label: '公开', value: 'public' },\n  { label: '私密', value: 'private' }\n];\n\n// 计算最大上传图片数量（会员4张，非会员1张）\nconst maxImageCount = computed(() => {\n    const userInfo = store().$state.userInfo;\n    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员\n    return isVip ? 4 : 1;\n});\n\n// 位置显示文本\nconst locationDisplay = computed(() => {\n    return location.value ? (location.value.name || location.value.address) : '添加位置';\n});\n\n// 页面加载时获取动态数据\nonLoad(async (options) => {\n    if (!options.id) {\n        uni.showToast({ title: '参数错误', icon: 'none' });\n        uni.navigateBack();\n        return;\n    }\n    \n    feedId.value = options.id;\n    await loadFeedData();\n});\n\n// 加载动态数据\nconst loadFeedData = async () => {\n    try {\n        isLoading.value = true;\n        const userInfo = store().$state.userInfo;\n        \n        const response = await getFeedDetail({\n            id: feedId.value,\n            uid: userInfo.uid,\n            token: userInfo.token\n        });\n        \n        if (response.status === 'ok' && response.data) {\n            const feed = response.data;\n            \n            // 检查权限\n            if (feed.user_id !== userInfo.uid) {\n                uni.showToast({ title: '您无权编辑此动态', icon: 'none' });\n                uni.navigateBack();\n                return;\n            }\n            \n            // 填充数据\n            content.value = feed.content || '';\n            tags.value = feed.tags || '';\n            privacy.value = feed.privacy || 'public';\n            isPrivate.value = feed.privacy === 'private';\n            \n            // 处理位置信息\n            if (feed.location) {\n                location.value = { name: feed.location };\n            }\n            \n            // 处理图片\n            if (feed.images && feed.images.length > 0) {\n                images.value = feed.images.map((img, index) => ({\n                    url: img,\n                    status: 'success',\n                    message: '',\n                    uid: Date.now() + index\n                }));\n            }\n        } else {\n            uni.showToast({ title: response.msg || '加载失败', icon: 'none' });\n            uni.navigateBack();\n        }\n    } catch (error) {\n        console.error('加载动态数据失败:', error);\n        uni.showToast({ title: '加载失败', icon: 'none' });\n        uni.navigateBack();\n    } finally {\n        isLoading.value = false;\n    }\n};\n\n// 处理图片上传\nconst handleAfterRead = async (event) => {\n    let lists = [].concat(event.file);\n    let fileListLen = images.value.length;\n\n    lists.map((item) => {\n        images.value.push({\n            ...item,\n            status: 'uploading',\n            message: '上传中'\n        });\n    });\n\n    for (let i = 0; i < lists.length; i++) {\n        const result = await uploadFilePromise(lists[i].file);\n        let item = images.value[fileListLen];\n        images.value.splice(fileListLen, 1, Object.assign(item, {\n            status: result.success ? 'success' : 'failed',\n            message: result.success ? '' : result.message,\n            url: result.success ? result.data : ''\n        }));\n        fileListLen++;\n    }\n};\n\n// 上传文件Promise\nconst uploadFilePromise = (file) => {\n    return new Promise((resolve, reject) => {\n        let formData = new FormData();\n        formData.append('file', file);\n        \n        upload_img(formData).then(res => {\n            if (res.status === 'ok') {\n                resolve({ success: true, data: res.data.url });\n            } else {\n                resolve({ success: false, message: res.msg || '上传失败' });\n            }\n        }).catch(err => {\n            resolve({ success: false, message: '上传失败' });\n        });\n    });\n};\n\n// 删除图片\nconst handleDelete = (event) => {\n    images.value.splice(event.index, 1);\n};\n\n// 隐私设置切换\nconst handlePrivacyChange = (value) => {\n    privacy.value = value ? 'private' : 'public';\n};\n\n// 选择位置\nconst chooseLocation = () => {\n    uni.chooseLocation({\n        success: (res) => {\n            location.value = {\n                name: res.name,\n                address: res.address,\n                latitude: res.latitude,\n                longitude: res.longitude\n            };\n        },\n        fail: (err) => {\n            console.log('选择位置失败:', err);\n        }\n    });\n};\n\n// 提交编辑\nconst handleSubmit = async () => {\n    if (!requireLogin()) return;\n    \n    if (!content.value.trim()) {\n        uni.showToast({ title: '请输入动态内容', icon: 'none' });\n        return;\n    }\n    \n    if (isSubmitting.value) return;\n    \n    try {\n        isSubmitting.value = true;\n        uni.showLoading({ title: '保存中...' });\n        \n        const userInfo = store().$state.userInfo;\n        const imageUrls = images.value\n            .filter(img => img.status === 'success' && img.url)\n            .map(img => img.url);\n        \n        const params = {\n            uid: userInfo.uid,\n            token: userInfo.token,\n            feed_id: feedId.value,\n            content: content.value.trim(),\n            images: imageUrls,\n            location: location.value ? location.value.name : '',\n            tags: tags.value.trim(),\n            privacy: privacy.value\n        };\n        \n        const response = await editFeed(params);\n        \n        if (response.status === 'ok') {\n            uni.hideLoading();\n            uni.showToast({ title: '保存成功', icon: 'success' });\n\n            // {{ AURA-X: Add - 通知详情页刷新数据. Confirmed via 寸止. }}\n            // 通知详情页刷新数据\n            uni.$emit('feed-updated', { id: feedId.value });\n\n            // 延迟返回，让用户看到成功提示\n            setTimeout(() => {\n                uni.navigateBack();\n            }, 1500);\n        } else {\n            uni.hideLoading();\n            uni.showToast({ title: response.msg || '保存失败', icon: 'none' });\n        }\n    } catch (error) {\n        console.error('保存动态失败:', error);\n        uni.hideLoading();\n        uni.showToast({ title: '保存失败，请重试', icon: 'none' });\n    } finally {\n        isSubmitting.value = false;\n    }\n};\n\n// 返回\nconst handleClose = () => {\n    uni.navigateBack();\n};\n</script>\n\n<template>\n  <view class=\"feed-edit-page\">\n    <!-- 自定义导航栏 -->\n    <customNavbar \n      title=\"编辑动态\" \n      :showBack=\"true\" \n      @back=\"handleClose\"\n    />\n    \n    <!-- 加载状态 -->\n    <view v-if=\"isLoading\" class=\"loading-container\">\n      <u-loading-icon mode=\"spinner\" color=\"#6AC086\" size=\"40\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n    \n    <!-- 编辑表单 -->\n    <view v-else class=\"edit-form\">\n      <!-- 内容输入 -->\n      <view class=\"content-section\">\n        <u-textarea\n          v-model=\"content\"\n          placeholder=\"分享你的动态...\"\n          :maxlength=\"5000\"\n          :showWordLimit=\"true\"\n          :autoHeight=\"true\"\n          :minHeight=\"200\"\n          class=\"content-textarea\"\n        />\n      </view>\n      \n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <view class=\"section-title\">添加图片</view>\n        <u-upload\n          :fileList=\"images\"\n          @afterRead=\"handleAfterRead\"\n          @delete=\"handleDelete\"\n          :maxCount=\"maxImageCount\"\n          :multiple=\"true\"\n          :previewFullImage=\"true\"\n          accept=\"image\"\n          :maxSize=\"400 * 1024\"\n          @oversize=\"() => uni.showToast({ title: '图片大小不能超过400KB', icon: 'none' })\"\n        />\n        <view class=\"upload-tip\">\n          最多上传{{ maxImageCount }}张图片，单张不超过400KB\n        </view>\n      </view>\n      \n      <!-- 位置信息 -->\n      <view class=\"location-section\">\n        <view class=\"section-title\">位置</view>\n        <view class=\"location-selector\" @click=\"chooseLocation\">\n          <u-icon name=\"map\" color=\"#6AC086\" size=\"20\"></u-icon>\n          <text class=\"location-text\">{{ locationDisplay }}</text>\n          <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n        </view>\n      </view>\n      \n      <!-- 标签输入 -->\n      <view class=\"tags-section\">\n        <view class=\"section-title\">标签</view>\n        <u-input\n          v-model=\"tags\"\n          placeholder=\"添加标签，用逗号分隔\"\n          :maxlength=\"100\"\n          class=\"tags-input\"\n        />\n      </view>\n      \n      <!-- 隐私设置 -->\n      <view class=\"privacy-section\">\n        <view class=\"section-title\">隐私设置</view>\n        <view class=\"privacy-toggle\">\n          <text class=\"privacy-label\">设为私密</text>\n          <u-switch \n            v-model=\"isPrivate\" \n            @change=\"handlePrivacyChange\"\n            activeColor=\"#6AC086\"\n          />\n        </view>\n        <view class=\"privacy-tip\">私密动态仅自己可见</view>\n      </view>\n    </view>\n    \n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <u-button\n        type=\"primary\"\n        :loading=\"isSubmitting\"\n        :disabled=\"!content.trim() || isSubmitting\"\n        @click=\"handleSubmit\"\n        class=\"submit-btn\"\n        color=\"#6AC086\"\n      >\n        {{ isSubmitting ? '保存中...' : '保存' }}\n      </u-button>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.feed-edit-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  padding-bottom: 120rpx;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400rpx;\n}\n\n.loading-text {\n  margin-top: 20rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.edit-form {\n  padding: 32rpx;\n}\n\n.content-section {\n  margin-bottom: 40rpx;\n}\n\n.content-textarea {\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.image-section,\n.location-section,\n.tags-section,\n.privacy-section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 24rpx;\n}\n\n.upload-tip {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 16rpx;\n}\n\n.location-selector {\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.location-text {\n  flex: 1;\n  margin-left: 16rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.tags-input {\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.privacy-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.privacy-label {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.privacy-tip {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 16rpx;\n}\n\n.submit-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: white;\n  padding: 24rpx 32rpx;\n  border-top: 1rpx solid #eee;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 88rpx;\n  border-radius: 50rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/feed/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "content", "ref", "images", "location", "tags", "privacy", "isPrivate", "isSubmitting", "isLoading", "feedId", "maxImageCount", "computed", "userInfo", "store", "$state", "role_type", "locationDisplay", "value", "name", "address", "common_vendor", "onLoad", "async", "options", "id", "uni", "index", "showToast", "title", "icon", "navigateBack", "loadFeedData", "response", "getFeedDetail", "uid", "token", "status", "data", "feed", "user_id", "length", "map", "img", "url", "message", "Date", "now", "msg", "error", "console", "handleAfterRead", "event", "lists", "concat", "file", "fileListLen", "item", "push", "i", "result", "uploadFilePromise", "splice", "Object", "assign", "success", "Promise", "resolve", "reject", "formData", "FormData", "append", "upload_img", "then", "res", "catch", "err", "handleDelete", "handlePrivacyChange", "chooseLocation", "latitude", "longitude", "fail", "log", "handleSubmit", "requireLogin", "trim", "showLoading", "imageUrls", "filter", "params", "feed_id", "editFeed", "hideLoading", "$emit", "setTimeout", "handleClose", "wx", "createPage", "MiniProgramPage"], "mappings": "+rCAMA,MAAMA,EAAe,IAAW,mEAK1B,MAAAC,EAAUC,EAAAA,IAAI,IACdC,EAASD,EAAAA,IAAI,IACbE,EAAWF,EAAAA,IAAI,MACfG,EAAOH,EAAAA,IAAI,IACXI,EAAUJ,EAAAA,IAAI,UACdK,EAAYL,EAAAA,KAAI,GAChBM,EAAeN,EAAAA,KAAI,GACnBO,EAAYP,EAAAA,KAAI,GAChBQ,EAASR,EAAAA,IAAI,MASbS,EAAgBC,EAAQA,UAAC,KAC3B,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAEhC,OADsC,KAAxB,MAAAA,OAAA,EAAAA,EAAUG,YAA2C,WAAxBH,WAAUG,WACtC,EAAI,CAAA,IAIjBC,EAAkBL,EAAQA,UAAC,IACtBR,EAASc,MAASd,EAASc,MAAMC,MAAQf,EAASc,MAAME,QAAW,SAIxEC,EAAAC,QAACC,MAAOC,IACN,IAACA,EAAQC,GAGT,OAFAC,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,cACrCJ,EAAGC,MAACI,eAIRrB,EAAOQ,MAAQM,EAAQC,SACjBO,GAAY,IAItB,MAAMA,EAAeT,UACb,IACAd,EAAUS,OAAQ,EAClB,MAAML,EAAWC,EAAAA,QAAQC,OAAOF,SAE1BoB,QAAiBC,gBAAc,CACjCT,GAAIf,EAAOQ,MACXiB,IAAKtB,EAASsB,IACdC,MAAOvB,EAASuB,QAGpB,GAAwB,OAApBH,EAASI,QAAmBJ,EAASK,KAAM,CAC3C,MAAMC,EAAON,EAASK,KAGlB,GAAAC,EAAKC,UAAY3B,EAASsB,IAG1B,OAFAT,EAAGC,MAACC,UAAU,CAAEC,MAAO,WAAYC,KAAM,cACzCJ,EAAGC,MAACI,eAKA9B,EAAAiB,MAAQqB,EAAKtC,SAAW,GAC3BI,EAAAa,MAAQqB,EAAKlC,MAAQ,GAClBC,EAAAY,MAAQqB,EAAKjC,SAAW,SACtBC,EAAAW,MAAyB,YAAjBqB,EAAKjC,QAGnBiC,EAAKnC,WACLA,EAASc,MAAQ,CAAEC,KAAMoB,EAAKnC,WAI9BmC,EAAKpC,QAAUoC,EAAKpC,OAAOsC,OAAS,IACpCtC,EAAOe,MAAQqB,EAAKpC,OAAOuC,KAAI,CAACC,EAAKhB,KAAW,CAC5CiB,IAAKD,EACLN,OAAQ,UACRQ,QAAS,GACTV,IAAKW,KAAKC,MAAQpB,MAGtC,cACgBC,UAAU,CAAEC,MAAOI,EAASe,KAAO,OAAQlB,KAAM,SACrDJ,EAAGC,MAACI,cAEX,OAAQkB,GACGC,QAAAD,MAAM,YAAaA,GAC3BvB,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,SACrCJ,EAAGC,MAACI,cACZ,CAAc,QACNtB,EAAUS,OAAQ,CACtB,GAIEiC,EAAkB5B,MAAO6B,IAC3B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MACxBC,EAAcrD,EAAOe,MAAMuB,OAEzBY,EAAAX,KAAKe,IACPtD,EAAOe,MAAMwC,KAAK,IACXD,EACHpB,OAAQ,YACRQ,QAAS,OACZ,IAGL,IAAA,IAASc,EAAI,EAAGA,EAAIN,EAAMZ,OAAQkB,IAAK,CACnC,MAAMC,QAAeC,EAAkBR,EAAMM,GAAGJ,MAC5C,IAAAE,EAAOtD,EAAOe,MAAMsC,GACxBrD,EAAOe,MAAM4C,OAAON,EAAa,EAAGO,OAAOC,OAAOP,EAAM,CACpDpB,OAAQuB,EAAOK,QAAU,UAAY,SACrCpB,QAASe,EAAOK,QAAU,GAAKL,EAAOf,QACtCD,IAAKgB,EAAOK,QAAUL,EAAOtB,KAAO,MAExCkB,GACJ,GAIEK,EAAqBN,GAChB,IAAIW,SAAQ,CAACC,EAASC,KACrB,IAAAC,EAAW,IAAIC,SACVD,EAAAE,OAAO,OAAQhB,GAExBiB,EAAAA,WAAWH,GAAUI,MAAYC,IACV,OAAfA,EAAIrC,OACJ8B,EAAQ,CAAEF,SAAS,EAAM3B,KAAMoC,EAAIpC,KAAKM,MAExCuB,EAAQ,CAAEF,SAAS,EAAOpB,QAAS6B,EAAI1B,KAAO,QAClD,IACD2B,OAAaC,IACZT,EAAQ,CAAEF,SAAS,EAAOpB,QAAS,QAAQ,GAC9C,IAKHgC,EAAgBzB,IAClBjD,EAAOe,MAAM4C,OAAOV,EAAMzB,MAAO,EAAC,EAIhCmD,EAAuB5D,IACjBZ,EAAAY,MAAQA,EAAQ,UAAY,QAAA,EAIlC6D,EAAiB,KACnBrD,EAAAA,MAAIqD,eAAe,CACfd,QAAUS,IACNtE,EAASc,MAAQ,CACbC,KAAMuD,EAAIvD,KACVC,QAASsD,EAAItD,QACb4D,SAAUN,EAAIM,SACdC,UAAWP,EAAIO,UAC/B,EAEQC,KAAON,IACK1B,QAAAiC,IAAI,UAAWP,EAAG,GAEjC,EAICQ,EAAe7D,UACb,GAAC8D,EAAYA,eAEjB,GAAKpF,EAAQiB,MAAMoE,QAKnB,IAAI9E,EAAaU,MAEb,IACAV,EAAaU,OAAQ,EACrBQ,EAAAA,MAAI6D,YAAY,CAAE1D,MAAO,WAEzB,MAAMhB,EAAWC,EAAAA,QAAQC,OAAOF,SAC1B2E,EAAYrF,EAAOe,MACpBuE,WAA6B,YAAf9C,EAAIN,QAAwBM,EAAIC,MAC9CF,KAAIC,GAAOA,EAAIC,MAEd8C,EAAS,CACXvD,IAAKtB,EAASsB,IACdC,MAAOvB,EAASuB,MAChBuD,QAASjF,EAAOQ,MAChBjB,QAASA,EAAQiB,MAAMoE,OACvBnF,OAAQqF,EACRpF,SAAUA,EAASc,MAAQd,EAASc,MAAMC,KAAO,GACjDd,KAAMA,EAAKa,MAAMoE,OACjBhF,QAASA,EAAQY,OAGfe,QAAiB2D,WAASF,GAER,OAApBzD,EAASI,QACTX,EAAGC,MAACkE,cACJnE,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,YAIrCJ,EAAGC,MAACmE,MAAM,eAAgB,CAAErE,GAAIf,EAAOQ,QAGvC6E,YAAW,KACPrE,EAAGC,MAACI,cAAY,GACjB,QAEHL,EAAGC,MAACkE,sBACAjE,UAAU,CAAEC,MAAOI,EAASe,KAAO,OAAQlB,KAAM,SAE5D,OAAQmB,GACGC,QAAAD,MAAM,UAAWA,GACzBvB,EAAGC,MAACkE,cACJnE,EAAGC,MAACC,UAAU,CAAEC,MAAO,WAAYC,KAAM,QACjD,CAAc,QACNtB,EAAaU,OAAQ,CACzB,OAlDIQ,EAAGC,MAACC,UAAU,CAAEC,MAAO,UAAWC,KAAM,QAkD5C,EAIEkE,EAAc,KAChBtE,EAAGC,MAACI,cAAY,+7BC3OpBkE,GAAGC,WAAWC"}