"use strict";const e=require("../common/vendor.js"),l=require("../utils/index.js");if(require("../store/index.js"),require("../store/counter.js"),require("../api/index.js"),require("../utils/request.js"),require("../utils/BaseUrl.js"),require("../utils/auth.js"),require("../utils/cacheManager.js"),require("../utils/systemInfo.js"),!Array){e.resolveComponent("u-icon")()}Math;const r={__name:"SourceSelector",props:{modelValue:{type:Object,default:null},placeholder:{type:String,default:"选择出处"}},emits:["update:modelValue"],setup(r,{emit:o}){const a=r,u=e.ref(a.modelValue);e.watch((()=>a.modelValue),(e=>{u.value=e}));const t=()=>{l.navto("/pages/bundle/world/source/search?type=select")},s=e=>{e.stopPropagation(),u.value=null,o("update:modelValue",null)};return e.index.$on("sourceSelected",(e=>{u.value=e,o("update:modelValue",e)})),e.onUnmounted((()=>{e.index.$off("sourceSelected")})),(l,o)=>e.e({a:e.p({name:"bookmark-fill",size:"20",color:"#999"}),b:u.value},u.value?e.e({c:u.value.cover_image},u.value.cover_image?{d:u.value.cover_image}:{e:e.p({name:"bookmark",size:"24",color:"#6AC086"})},{f:e.t(u.value.name),g:u.value.publisher},u.value.publisher?{h:e.t(u.value.publisher)}:u.value.category?{j:e.t(u.value.category)}:{},{i:u.value.category,k:e.o(s),l:e.p({name:"close-circle-fill",size:"18",color:"#ccc"})}):{m:e.t(r.placeholder),n:e.p({name:"arrow-right",size:"16",color:"#ccc"})},{o:u.value?1:"",p:e.o(t)})}},o=e._export_sfc(r,[["__scopeId","data-v-017fc69b"]]);wx.createComponent(o);
//# sourceMappingURL=SourceSelector.js.map
