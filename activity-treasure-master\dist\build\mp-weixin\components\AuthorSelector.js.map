{"version": 3, "file": "AuthorSelector.js", "sources": ["../../../../src/components/AuthorSelector.vue", "../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvQXV0aG9yU2VsZWN0b3IudnVl"], "sourcesContent": ["<script setup>\nimport { ref, watch, onUnmounted } from 'vue';\nimport { navto } from '@/utils';\n\n// {{ AURA-X: Add - 创建作者选择器组件. Confirmed via 寸止 }}\nconst props = defineProps({\n  modelValue: {\n    type: Object,\n    default: null\n  },\n  placeholder: {\n    type: String,\n    default: '选择作者'\n  }\n});\n\nconst emit = defineEmits(['update:modelValue']);\n\nconst selectedAuthor = ref(props.modelValue);\n\n// 监听外部值变化\nwatch(() => props.modelValue, (newVal) => {\n  selectedAuthor.value = newVal;\n});\n\n// 打开作者搜索页面\nconst openSelector = () => {\n  navto('/pages/bundle/world/author/search?type=select');\n};\n\n// 清除选择\nconst clearSelection = (event) => {\n  event.stopPropagation();\n  selectedAuthor.value = null;\n  emit('update:modelValue', null);\n};\n\n// 监听全局事件，接收选择结果\nuni.$on('authorSelected', (author) => {\n  selectedAuthor.value = author;\n  emit('update:modelValue', author);\n});\n\n// 组件销毁时移除事件监听\nonUnmounted(() => {\n  uni.$off('authorSelected');\n});\n</script>\n\n<template>\n  <view class=\"author-selector\" @click=\"openSelector\">\n    <view class=\"selector-content\" :class=\"{ 'has-value': selectedAuthor }\">\n      <u-icon name=\"account-fill\" size=\"20\" color=\"#999\" class=\"icon\"></u-icon>\n      \n      <view v-if=\"selectedAuthor\" class=\"selected-item\">\n        <image \n          v-if=\"selectedAuthor.avatar\" \n          :src=\"selectedAuthor.avatar\" \n          class=\"avatar\"\n          mode=\"aspectFill\"\n        ></image>\n        <view v-else class=\"avatar-placeholder\">\n          {{ selectedAuthor.name.charAt(0) }}\n        </view>\n        <view class=\"author-info\">\n          <text class=\"name\">{{ selectedAuthor.name }}</text>\n          <text v-if=\"selectedAuthor.category\" class=\"category\">{{ selectedAuthor.category }}</text>\n        </view>\n        <u-icon \n          name=\"close-circle-fill\" \n          @click=\"clearSelection\" \n          class=\"clear-btn\"\n          size=\"18\"\n          color=\"#ccc\"\n        ></u-icon>\n      </view>\n      \n      <view v-else class=\"placeholder\">\n        <text class=\"placeholder-text\">{{ placeholder }}</text>\n        <u-icon name=\"arrow-right\" size=\"16\" color=\"#ccc\" class=\"arrow\"></u-icon>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.author-selector {\n  width: 100%;\n  \n  .selector-content {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 0;\n    min-height: 88rpx; // 最小触摸区域\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .icon {\n      margin-right: 24rpx;\n      flex-shrink: 0;\n    }\n    \n    .selected-item {\n      display: flex;\n      align-items: center;\n      flex: 1;\n      \n      .avatar {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        margin-right: 24rpx;\n        flex-shrink: 0;\n      }\n      \n      .avatar-placeholder {\n        width: 60rpx;\n        height: 60rpx;\n        border-radius: 50%;\n        background-color: #6AC086;\n        color: white;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 24rpx;\n        font-weight: 500;\n        margin-right: 24rpx;\n        flex-shrink: 0;\n      }\n      \n      .author-info {\n        flex: 1;\n        \n        .name {\n          display: block;\n          font-size: 32rpx;\n          color: #333;\n          font-weight: 500;\n          line-height: 1.4;\n        }\n        \n        .category {\n          display: block;\n          font-size: 24rpx;\n          color: #999;\n          margin-top: 4rpx;\n        }\n      }\n      \n      .clear-btn {\n        margin-left: 16rpx;\n        flex-shrink: 0;\n      }\n    }\n    \n    .placeholder {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      flex: 1;\n      \n      .placeholder-text {\n        font-size: 32rpx;\n        color: #999;\n      }\n      \n      .arrow {\n        flex-shrink: 0;\n      }\n    }\n    \n    &.has-value {\n      background-color: #f8f9fa;\n      border-radius: 12rpx;\n      padding: 24rpx 32rpx;\n      border-bottom: none;\n    }\n  }\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/AuthorSelector.vue'\nwx.createComponent(Component)"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "ref", "props", "modelValue", "watch", "newVal", "value", "openSelector", "navto", "clearSelection", "event", "stopPropagation", "emit", "uni", "$on", "author", "onUnmounted", "$off", "wx", "createComponent", "Component"], "mappings": "yiBAkBMA,EAAiBC,EAAGA,IAACC,EAAMC,YAGjCC,EAAKA,OAAC,IAAMF,EAAMC,aAAaE,IAC7BL,EAAeM,MAAQD,CAAA,IAIzB,MAAME,EAAe,KACnBC,EAAKA,MAAC,gDAA+C,EAIjDC,EAAkBC,IACtBA,EAAMC,kBACNX,EAAeM,MAAQ,KACvBM,EAAK,oBAAqB,KAAI,SAIhCC,EAAAA,MAAIC,IAAI,kBAAmBC,IACzBf,EAAeM,MAAQS,EACvBH,EAAK,oBAAqBG,EAAM,IAIlCC,EAAAA,aAAY,aACNC,KAAK,iBAAgB,weC5C3BC,GAAGC,gBAAgBC"}