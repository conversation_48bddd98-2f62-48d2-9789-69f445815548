"use strict";const e=require("../../../common/vendor.js"),a=require("../../../api/index.js"),l=require("../../../store/index.js"),t=require("../../../utils/index.js"),o=require("../../../utils/auth.js"),i=require("../../../utils/permissions.js");if(require("../../../utils/china.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-upload")+e.resolveComponent("u-form-item")+e.resolveComponent("u-input")+e.resolveComponent("u-text")+e.resolveComponent("u-switch")+e.resolveComponent("u-form")+e.resolveComponent("u-gap")+e.resolveComponent("u-button")+e.resolveComponent("u-safe-bottom")+e.resolveComponent("u-datetime-picker")+e.resolveComponent("u-picker")+e.resolveComponent("u-modal")+e.resolveComponent("u-notify"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../node-modules/uview-plus/components/u-form-item/u-form-item.js")+(()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../node-modules/uview-plus/components/u-form/u-form.js")+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-safe-bottom/u-safe-bottom.js")+(()=>"../../../node-modules/uview-plus/components/u-datetime-picker/u-datetime-picker.js")+(()=>"../../../node-modules/uview-plus/components/u-picker/u-picker.js")+(()=>"../../../node-modules/uview-plus/components/u-modal/u-modal.js")+(()=>"../../../node-modules/uview-plus/components/u-notify/u-notify.js"))();const n={__name:"addActive",setup(n){const u=e.ref([[]]),r=e.ref(),s=e.ref({name:"",title:"",type_id:null,sheng_id:"",shi_id:"",qu_id:"",addr:"",lng:"",lat:"",num:"",money:"",member_money:"",img_url:"",contents:"",start_time:"",end_time:"",baoming_end_time:"",pay_type:2,is_online:0,member_only:0,refund_rule:1,enable_checkin:0,lianxi_name:"",lianxi_mobile:"",lianxi_qrcode:"",qun_qrcode:"",imgs_url:[],imgs:[],liucheng:[]}),c=e.ref({name:{type:"string",required:!0,message:"请输入2-50个字符的活动名称",trigger:["blur","change"]},title:{type:"string",required:!0,message:"请输入2-50个字符的活动标题",trigger:["blur","change"]},type_id:{type:"number",required:!0,message:"请选择一个活动类型（如聚餐、运动、学习等）",trigger:["blur","change"]},sheng_id:{type:"number",required:!0,message:"请在地图上选择活动举办的具体位置",trigger:["blur","change"]},lng:{type:"number",required:!0,validator:(e,a)=>a&&!isNaN(a)&&a>0,message:"请在地图上点击选择活动地点以获取准确坐标",trigger:["blur","change"]},lat:{type:"number",required:!0,validator:(e,a)=>a&&!isNaN(a)&&a>0,message:"请在地图上点击选择活动地点以获取准确坐标",trigger:["blur","change"]},start_time:[{type:"string",required:!0,message:"请选择活动开始的具体日期和时间",trigger:["change"]},{validator:(e,a,l)=>{if(!a)return!1;const t=a.replace(/-/g,"/"),o=new Date(t);return!isNaN(o.getTime())},message:"请选择正确的日期时间格式（如：2024-01-01 14:00）",trigger:["change"]}],num:[{type:"number",required:!0,message:"请输入活动参与人数（如：10人、50人）",trigger:["blur","change"]},{validator:(a,l,t)=>e.index.$u.test.digits(l),message:"请输入有效的数字，范围1-9999人",trigger:["change","blur"]}],money:[{type:"number",required:!1,validator:(e,a)=>""===a||null==a||!isNaN(a)&&parseFloat(a)>=0&&/^\d+(\.\d{1,2})?$/.test(a),message:"请输入正确的费用金额（如：0、50、99.99），最多两位小数",trigger:["change","blur"]}],member_money:[{type:"number",required:!1,validator:(e,a)=>""===a||!isNaN(a)&&parseFloat(a)>=0&&/^\d+(\.\d{1,2})?$/.test(a),message:"请输入正确的会员优惠价格（如：0、30、79.99），最多两位小数",trigger:["change","blur"]}],img_url:{type:"string",required:!0,validator:(a,l)=>l&&e.index.$u.test.url(l),message:"请上传一张吸引人的活动封面图片（支持JPG、PNG格式）",trigger:["blur","change"]},contents:{type:"string",required:!0,message:"请详细描述活动内容、流程和注意事项（至少10个字）",trigger:["blur","change"]},lianxi_name:{type:"string",required:!1,message:"请输入活动发起人的真实姓名（2-10个字符）",trigger:["blur","change"]},lianxi_qrcode:{type:"string",required:!0,validator:(a,l)=>l&&e.index.$u.test.url(l),message:"请上传您的微信二维码图片，方便参与者联系您",trigger:["blur","change"]},qun_qrcode:{type:"string",required:!0,validator:(a,l)=>l&&e.index.$u.test.url(l),message:"请上传活动微信群二维码，方便参与者加入讨论",trigger:["blur","change"]},addr:{type:"string",required:!0,message:"请输入详细的活动地址（如：XX大厦XX楼XX室）",trigger:["blur","change"]},baoming_end_time:{type:"string",required:!0,message:"请选择活动报名截止时间",trigger:["blur","change"]},refund_rule:{type:"number",required:!1,validator:(e,a)=>{const l=i.hasActivityPublishPermission(),t=parseFloat(s.value.money)||0,o=parseFloat(s.value.member_money)||0,n=t>0||o>0,u=1===s.value.pay_type;return!(l&&n&&u)||a&&a>0},message:"请选择退款规则",trigger:["blur","change"]}}),d=e.ref(!1),v=e.ref(!1),m=e.ref(!1),g=e.ref(Date.now()),p=e.ref(Date.now()),_=e.ref(!1);e.ref(1);const h=e.ref("datetime");e.ref(0);const f=e.ref(""),y=e.ref(0),x=e.ref([]),b=e.ref([]),q=e.ref([]),w=e.ref([]),$=e.ref(""),A=e.ref(!1),L=e.ref(!1),k=e.ref(!1);e.ref(!1);const T=e.ref(!0),S=e.ref(""),C=e.ref(),D=e.ref(!1);e.ref(!1);const P=new Date((new Date).setFullYear((new Date).getFullYear()-1)).getTime(),z=new Date((new Date).setFullYear((new Date).getFullYear()+1)).getTime(),j=e.ref(1),I=e.ref(!1),N=e.ref(!1),F=e.ref(null),O=e.ref(null),E=e.ref(0),M=e.ref(!1),X=e.ref(null),R=e.ref(!1),H=e.ref(0),V=(e,a)=>"year"===e?`${a}年`:"month"===e?`${a}月`:"day"===e?`${a}日`:"hour"===e?`${a}时`:"minute"===e?`${a}分`:a,B=e=>{const a=e.value||e,l=Y(a);s.value.start_time=l,s.value.baoming_end_time||(s.value.baoming_end_time=l),d.value=!1},W=a=>{const l=a.value||a,t=Y(l);if(s.value.start_time){if(new Date(t.replace(/-/g,"/"))>new Date(s.value.start_time.replace(/-/g,"/")))return void e.index.showToast({title:"报名截止时间不能晚于活动开始时间",icon:"none"})}s.value.baoming_end_time=t,v.value=!1},G=a=>{console.log("退款规则选择确认事件:",a);try{let l=null;if(a.value&&a.value.length>0){const e=a.value[0];if("object"==typeof e&&void 0!==e.value)l=e.value;else if("number"==typeof e)l=e;else if("string"==typeof e){const a=parseInt(e);isNaN(a)||(l=a)}}if(null===l&&a.indexs&&a.indexs.length>0){const e=a.indexs[0];Se.value[e]&&(l=Se.value[e].value)}null!==l?(s.value.refund_rule=l,console.log("已设置退款规则:",l),e.nextTick$1((()=>{r.value&&r.value.validateField("refund_rule")}))):console.error("无法获取选中的退款规则值")}catch(l){console.error("处理退款规则选择时出错:",l)}m.value=!1},U=e=>{console.log("退款规则选择器变化:",e);const a=Date.now();a-(U.lastClickTime||0)<500&&(console.log("检测到双击，直接确认选择"),G(e)),U.lastClickTime=a},Y=e=>{const a="number"==typeof e?e:Date.parse(e),l=new Date(a);if(isNaN(l.getTime()))return"";return`${l.getFullYear()}-${String(l.getMonth()+1).padStart(2,"0")}-${String(l.getDate()).padStart(2,"0")} ${String(l.getHours()).padStart(2,"0")}:${String(l.getMinutes()).padStart(2,"0")}:${String("00")}`};e.onPageScroll((e=>{e.scrollTop>420&&(T.value=!1)}));const J=function(e,a){let l;const t=function(...t){clearTimeout(l),l=setTimeout((()=>{clearTimeout(l),l=null,e(...t)}),a)};return t.cancel=function(){l&&(clearTimeout(l),l=null)},t}((()=>{oe()}),5e3);e.onLoad((async t=>{var o,i,n;try{e.index.showLoading({title:"加载中...",mask:!0});let c=t.huodong_id;const d="1"===t.duplicate;if(L.value=d,!c&&(ze(),d))try{const l=t.duplicate_id;if(l){console.log("再办一场：从数据库查询活动数据，ID:",l);const t=await a.huodongget_info({huodong_id:l});if("ok"===t.status&&t.data){const a=t.data;console.log("从数据库获取的复制活动数据:",a),Object.keys(a).forEach((e=>{s.value.hasOwnProperty(e)&&!["baoming_start_time","baoming_end_time","start_time","end_time","id","huodong_id"].includes(e)&&(s.value[e]=a[e])})),a.type_id&&(s.value.type_id=a.type_id,e.nextTick$1((()=>{setTimeout((()=>{if(u.value[0]){const e=u.value[0].find((e=>e.id===a.type_id));e&&(f.value=e.name)}}),200)}))),void 0!==a.is_online&&(s.value.is_online=a.is_online),void 0!==a.pay_type&&(s.value.pay_type=a.pay_type),void 0!==a.member_only&&(s.value.member_only=a.member_only),a.address&&(s.value.address=a.address),$.value=a.sheng&&a.shi&&a.qu?`${a.sheng}-${a.shi}-${a.qu}`:"",void 0!==a.refund_rule&&(s.value.refund_rule=a.refund_rule),a.img_url?(s.value.img_url=a.img_url,x.value=[{url:a.img_url,status:"success"}]):(s.value.img_url="",x.value=[]),a.lianxi_qrcode?(s.value.lianxi_qrcode=a.lianxi_qrcode,b.value=[{url:a.lianxi_qrcode,status:"success"}]):(s.value.lianxi_qrcode="",b.value=[]),a.qun_qrcode?(s.value.qun_qrcode=a.qun_qrcode,w.value=[{url:a.qun_qrcode,status:"success"}]):(s.value.qun_qrcode="",w.value=[]),a.imgs&&Array.isArray(a.imgs)?(s.value.imgs_url=[...a.imgs],s.value.imgs=[...a.imgs],q.value=a.imgs.map((e=>({url:e,status:"success"})))):(s.value.imgs_url=[],s.value.imgs=[],q.value=[]);const l=a.contents||"";S.value=l,s.value.contents=l,console.log("设置复制活动的描述内容，长度:",l.length,"包含HTML:",l.includes("<"),"内容预览:",l.substring(0,100)),D.value&&C.value&&e.nextTick$1((()=>{setTimeout((()=>{K(l)}),100)})),void 0!==a.money&&(s.value.money=a.money),void 0!==a.member_money&&(s.value.member_money=a.member_money),void 0!==a.num&&(s.value.num=a.num),void 0!==a.addr&&(s.value.addr=a.addr),void 0!==a.lianxi_name&&(s.value.lianxi_name=a.lianxi_name),void 0!==a.lianxi_mobile&&(s.value.lianxi_mobile=a.lianxi_mobile),k.value=!!a.is_choujiang,console.log("再办一场数据回填完成，表单数据:",s.value)}else console.error("从数据库查询复制活动数据失败:",t),e.index.$u.toast("获取活动数据失败，请重试")}else console.error("再办一场：缺少活动ID参数"),e.index.$u.toast("参数错误，请重试")}catch(r){console.error("加载复制活动数据失败:",r),e.index.$u.toast("获取活动数据失败，请重试")}s.value.lianxi_name=(null==(o=l.store().$state.userInfo)?void 0:o.nickname)||"";try{e.index.removeStorageSync("activity_edit_state")}catch(r){}const v=[],m=l.store().$state.activeTypeList;if(m&&Array.isArray(m)&&0!==m.length?u.value=[m]:v.push(a.huodongget_type().then((e=>({type:"activityTypes",data:e}))).catch((e=>({type:"activityTypes",error:e})))),c){A.value=!0,s.value.huodong_id=c;const e={huodong_id:c,uid:(null==(i=l.store().$state.userInfo)?void 0:i.uid)||0,token:(null==(n=l.store().$state.userInfo)?void 0:n.token)||""};v.push(a.huodongget_info(e).then((e=>({type:"activityInfo",data:e}))).catch((e=>({type:"activityInfo",error:e}))))}if(v.length>0){const a=await Promise.allSettled(v),t=a.find((e=>{var a;return"activityTypes"===(null==(a=e.value)?void 0:a.type)}));if(t&&"fulfilled"===t.status&&!t.value.error){const a=t.value.data;"ok"===a.status&&a.data&&Array.isArray(a.data)?(u.value=[a.data],l.store().changeActiveTypeList(a.data)):e.index.$u.toast("活动类型数据格式异常")}const o=a.find((e=>{var a;return"activityInfo"===(null==(a=e.value)?void 0:a.type)}));if(o&&"fulfilled"===o.status&&!o.value.error){const a=o.value.data;if("ok"===a.status&&a.data){const l=a.data,t=["img_url","lianxi_qrcode","qun_qrcode","imgs_url","imgs"];for(let e in s.value)void 0===l[e]||null===l[e]||t.includes(e)||(s.value[e]=l[e]);e.index.setNavigationBarTitle({title:"修改活动"});const o=l.contents||"";if(S.value=o,s.value.contents=o,console.log("活动数据加载完成，内容长度:",o.length,"内容预览:",o.substring(0,100)),D.value&&C.value&&e.nextTick$1((()=>{setTimeout((()=>{K(o)}),200)})),l.type_id&&u.value[0]){const e=u.value[0].find((e=>e.id===l.type_id));e&&(f.value=e.name)}l.img_url?(s.value.img_url=l.img_url,x.value=[{url:l.img_url,status:"success"}]):(s.value.img_url="",x.value=[]),l.lianxi_qrcode?(s.value.lianxi_qrcode=l.lianxi_qrcode,b.value=[{url:l.lianxi_qrcode,status:"success"}]):(s.value.lianxi_qrcode="",b.value=[]),l.qun_qrcode?(s.value.qun_qrcode=l.qun_qrcode,w.value=[{url:l.qun_qrcode,status:"success"}]):(s.value.qun_qrcode="",w.value=[]),l.imgs&&Array.isArray(l.imgs)?(s.value.imgs_url=[...l.imgs],s.value.imgs=[...l.imgs],q.value=l.imgs.map((e=>({url:e,status:"success"})))):(s.value.imgs_url=[],s.value.imgs=[],q.value=[]),$.value=l.sheng&&l.shi&&l.qu?`${l.sheng}-${l.shi}-${l.qu}`:"",k.value=!!l.is_choujiang,void 0!==l.is_online&&(s.value.is_online=l.is_online),void 0!==l.money&&(s.value.money=l.money),void 0!==l.member_money&&(s.value.member_money=l.member_money),void 0!==l.num&&(s.value.num=l.num),void 0!==l.start_time&&(s.value.start_time=l.start_time),void 0!==l.member_only&&(s.value.member_only=l.member_only),be(),console.log("编辑模式数据加载完成，图片数据:",{img_url:s.value.img_url,fileListLength:x.value.length,lianxi_qrcode:s.value.lianxi_qrcode,qun_qrcode:s.value.qun_qrcode}),void 0!==l.baoming_start_time&&(s.value.baoming_start_time=l.baoming_start_time),void 0!==l.baoming_end_time&&(s.value.baoming_end_time=l.baoming_end_time)}else{const l=a.msg||a.message||"获取活动信息失败";e.index.$u.toast(l),setTimeout((()=>e.index.navigateBack()),1500)}}else if(o&&"rejected"===o.status){const a="网络连接失败，请检查网络后重试";e.index.$u.toast(a),setTimeout((()=>e.index.navigateBack()),1500)}}c||(te(),Le.value||(s.value.member_only=1)),setTimeout((()=>{be(),console.log("页面加载完成，最终图片数据状态:",{img_url:s.value.img_url,fileList_length:x.value.length,lianxi_qrcode:s.value.lianxi_qrcode,fileList1_length:b.value.length,qun_qrcode:s.value.qun_qrcode,fileList3_length:w.value.length,imgs_url_length:Array.isArray(s.value.imgs_url)?s.value.imgs_url.length:0,fileList2_length:q.value.length})}),500),e.index.hideLoading()}catch(r){e.index.hideLoading(),e.index.getNetworkType({success:a=>{"none"===a.networkType?e.index.$u.toast("网络连接异常，请检查网络设置"):e.index.$u.toast("页面加载失败，请重试")},fail:()=>{e.index.$u.toast("页面加载失败，请重试")}})}})),e.onReady((async()=>{const e=await t.getListHeight("bottomBox");y.value=e.height,setTimeout((()=>{A.value&&s.value.contents&&D.value&&C.value&&(console.log("页面准备完成，检查编辑器内容设置，内容长度:",s.value.contents.length,"包含HTML:",s.value.contents.includes("<")),K(s.value.contents))}),500)})),e.onUnload((()=>{if(A.value&&s.value.huodong_id)try{const a={huodong_id:s.value.huodong_id,isEdit:!0,timestamp:Date.now()};e.index.setStorageSync("activity_edit_state",a)}catch(a){}else try{const e="activity_publish_draft",a=l.store().getActivityDraft(e),t=!s.value.name&&!s.value.contents&&!s.value.type_id;a&&t&&(console.log("检测到可能的残留草稿，清除中..."),l.store().removeActivityDraft(e))}catch(a){}l.store().editActive({},!1),O.value&&(clearTimeout(O.value),O.value=null),X.value&&(clearTimeout(X.value),X.value=null),Z.latestContent&&s.value.contents!==Z.latestContent&&(s.value.contents=Z.latestContent,console.log("页面卸载，保存编辑器内容")),J&&J.cancel&&J.cancel(),e.index.hideLoading()}));const K=(e,a=0)=>{if(!C.value)return void console.warn("编辑器未准备好，无法设置内容");null==e&&(e="");const l=(t=e)?t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t;var t;const o=l.includes("<")&&l.includes(">");try{console.log("设置编辑器内容，原始长度:",e.length,"解码后长度:",l.length,"包含HTML:",o,"重试次数:",a),console.log("原始内容预览:",e.substring(0,100)),console.log("解码后内容预览:",l.substring(0,100)),o?C.value.setContents({html:l}):C.value.setContents({html:l||"<p><br></p>"})}catch(i){console.error("设置编辑器内容失败:",i),a<3&&setTimeout((()=>{K(e,a+1)}),300*(a+1))}},Q=()=>{e.index.createSelectorQuery().select("#editor").context((a=>{C.value=a.context,D.value=!0,console.log("编辑器准备就绪");const l=s.value.contents||S.value||"";console.log("编辑器准备就绪，准备设置内容:",{formContentsLength:(s.value.contents||"").length,valuesLength:(S.value||"").length,finalContentLength:l.length,contentPreview:l.substring(0,100),isEdit:A.value,isDuplicate:L.value}),l?e.nextTick$1((()=>{setTimeout((()=>{console.log("编辑器准备就绪后设置内容，内容长度:",l.length),K(l)}),300)})):console.log("编辑器准备就绪，但暂无内容需要设置，等待数据加载...")})).exec()},Z=e=>{X.value&&clearTimeout(X.value);const a=e.detail.html||"";Z.latestContent,Z.latestContent=a},ee=e=>{X.value&&(clearTimeout(X.value),X.value=null);const a=Z.latestContent||e.detail.html||"";a&&s.value.contents!==a&&(s.value.contents=a,console.log("编辑器失去焦点，保存内容长度:",a.length)),Z.latestContent=null},ae=()=>{try{e.index.authorize({scope:"scope.userLocation",success(){le()},fail:()=>{e.index.showModal({title:"是否授权当前位置",content:"需要获取您的地理位置，请确认授权，否则地图功能将无法使用",success:a=>{if(a.confirm)try{e.index.openSetting({success:a=>{!0===a.authSetting["scope.userLocation"]?(e.index.$u.toast("授权成功"),le()):e.index.$u.toast("授权失败")},fail:()=>{e.index.$u.toast("无法打开设置页面，请手动在设置中开启位置权限")}})}catch(l){e.index.$u.toast("位置功能暂时不可用，请稍后重试")}}})}})}catch(a){e.index.$u.toast("位置功能初始化失败，请重试")}},le=async()=>{try{e.index.getLocation({type:"wgs84",isHighAccuracy:!0,success:l=>{e.index.chooseLocation({latitude:l.latitude,longitude:l.longitude,success:async l=>{try{if(l.address){s.value.lat=l.latitude,s.value.lng=l.longitude,e.index.showLoading({title:"正在获取地址信息..."});const o=await a.getAddr({location:`${l.longitude},${l.latitude}`});if(1==o.status){const a=o.regeocode.addressComponent;-1!==a.province.indexOf("市")?a.province.substring(0,a.province.indexOf("市")):a.province,e.index.$u.test.isEmpty(a.city)?a.province:a.city,a.district;s.value.addr=o.regeocode.formatted_address.slice(o.regeocode.formatted_address.indexOf(a.township)),$.value=`${a.province}-${a.city}-${a.district}`;try{if(a.adcode&&a.adcode.length>=6){const e=a.adcode.substring(0,2)+"0000";s.value.sheng_id=e;const l={11e4:"110100",12e4:"120100",31e4:"310100",5e5:"500100"};l[e]?s.value.shi_id=l[e]:s.value.shi_id=a.adcode.substring(0,4)+"00",s.value.qu_id=a.adcode,console.log("地区ID设置成功:",{sheng_id:s.value.sheng_id,shi_id:s.value.shi_id,qu_id:s.value.qu_id})}else console.warn("未获取到有效的adcode，使用默认值"),s.value.sheng_id="",s.value.shi_id="",s.value.qu_id="";e.index.hideLoading(),e.index.$u.toast("地址选择成功")}catch(t){console.error("地区信息处理失败:",t),e.index.hideLoading(),e.index.$u.toast("地区信息获取失败，但位置已保存")}}else e.index.hideLoading(),e.index.$u.toast("获取地址详情失败，请重新选择")}else e.index.$u.toast("请重新选择位置并勾选详细地址列表,3秒后将重新进入地图选择页面"),setTimeout((()=>{le()}),3e3)}catch(t){e.index.hideLoading(),e.index.$u.toast("地址处理失败，请重试")}},fail:()=>{e.index.$u.toast("选择位置失败，请检查位置权限")}})},fail:l=>{console.error("获取当前位置失败:",l),e.index.chooseLocation({success:async l=>{try{if(l.address){s.value.lat=l.latitude,s.value.lng=l.longitude,e.index.showLoading({title:"正在获取地址信息..."});const o=await a.getAddr({location:`${l.longitude},${l.latitude}`});if(1==o.status){const a=o.regeocode.addressComponent,i=[-1!==a.province.indexOf("市")?a.province.substring(0,a.province.indexOf("市")):a.province,-1!==a.city.indexOf("市")?a.city.substring(0,a.city.indexOf("市")):a.city,-1!==a.district.indexOf("区")?a.district.substring(0,a.district.indexOf("区")):-1!==a.district.indexOf("县")?a.district.substring(0,a.district.indexOf("县")):a.district];s.value.address=i.join(""),$.value=l.name;try{if(a.adcode&&a.adcode.length>=6){const e=a.adcode.substring(0,2)+"0000";s.value.sheng_id=e;const l={11e4:"110100",12e4:"120100",31e4:"310100",5e5:"500100"};l[e]?s.value.shi_id=l[e]:s.value.shi_id=a.adcode.substring(0,4)+"00",s.value.qu_id=a.adcode,console.log("地区ID设置成功:",{sheng_id:s.value.sheng_id,shi_id:s.value.shi_id,qu_id:s.value.qu_id})}else console.warn("未获取到有效的adcode，使用默认值"),s.value.sheng_id="",s.value.shi_id="",s.value.qu_id="";e.index.hideLoading(),e.index.$u.toast("地址选择成功")}catch(t){console.error("地区信息处理失败:",t),e.index.hideLoading(),e.index.$u.toast("地区信息获取失败，但位置已保存")}}else e.index.hideLoading(),e.index.$u.toast("获取地址详情失败，请重新选择")}else e.index.$u.toast("请重新选择位置并勾选详细地址列表,3秒后将重新进入地图选择页面"),setTimeout((()=>{le()}),3e3)}catch(t){e.index.hideLoading(),e.index.$u.toast("地址处理失败，请重试")}},fail:()=>{e.index.$u.toast("选择位置失败，请检查位置权限")}})}})}catch(l){console.error("地图功能异常:",l),e.index.$u.toast("地图功能暂时不可用，请稍后重试")}},te=()=>{var a,t,o,i,n,u;try{const s="activity_publish_draft",c=l.store().getActivityDraft(s);if(c&&c.formData){const d=c.formData;if(!!((null==(a=d.form)?void 0:a.name)||(null==(t=d.form)?void 0:t.type_id)||(null==(o=d.form)?void 0:o.img_url)||(null==(i=d.form)?void 0:i.contents)||(null==(n=d.form)?void 0:n.start_time)||(null==(u=d.form)?void 0:u.num))){if(Date.now()-c.lastSaved>6048e5)l.store().removeActivityDraft(s);else{if(Date.now()-c.lastSaved<3e5)try{const a=e.index.getStorageSync("last_publish_success");if(a&&Date.now()-a<6e5)return console.log("检测到近期发布成功，清除可能的残留草稿"),l.store().removeActivityDraft(s),e.index.removeStorageSync("last_publish_success"),void(M.value=!0)}catch(r){}N.value=!0}}else l.store().removeActivityDraft(s)}M.value=!0}catch(s){console.error("草稿检查失败:",s),M.value=!0}},oe=()=>{try{if(!!!(s.value.name||s.value.type_id||s.value.img_url||s.value.contents||s.value.start_time||s.value.num||s.value.addr||s.value.lianxi_name||s.value.money))return;const e="activity_publish_draft",a={form:{...s.value},activeType:f.value,place:$.value,values:S.value,fileList:x.value,fileList1:b.value,fileList2:q.value,fileList3:w.value,currentStep:j.value};l.store().saveActivityDraft(e,a),E.value=Date.now()}catch(e){}},ie=async()=>{try{const e="activity_publish_draft";l.store().removeActivityDraft(e),await new Promise((a=>{setTimeout((()=>{l.store().getActivityDraft(e)&&(console.warn("草稿清除不完整，重试清除"),l.store().removeActivityDraft(e)),a()}),100)})),console.log("草稿清除完成")}catch(e){console.error("清除草稿失败:",e)}},ne=e=>{const a="string"==typeof e.name?parseInt(e.name):e.name;switch(console.log("删除图片:",{uploadType:a,index:e.index,name:e.name}),a){case 1:s.value.img_url="",x.value=[];break;case 2:s.value.lianxi_qrcode="",b.value=[];break;case 3:Array.isArray(s.value.imgs_url)&&e.index>=0&&e.index<s.value.imgs_url.length&&(s.value.imgs_url.splice(e.index,1),console.log("删除后的imgs_url:",s.value.imgs_url)),Array.isArray(q.value)&&e.index>=0&&e.index<q.value.length&&(q.value.splice(e.index,1),console.log("删除后的fileList2:",q.value.length));break;case 4:s.value.qun_qrcode="",w.value=[];break;default:return void console.warn("未知的上传类型:",a)}},ue=async l=>{let t;const o="string"==typeof l.name?parseInt(l.name):l.name;switch(o){case 1:t=x.value;break;case 2:t=b.value;break;case 3:t=q.value;break;case 4:t=w.value;break;default:return}let i=[].concat(l.file),n=t.length;i.map((e=>{t.push({...e,status:"uploading",message:"上传中"})}));for(let u=0;u<i.length;u++){const l=await a.upload_img(i[u].url);let r=t[n];if("ok"===l.status)switch(t.splice(n,1,{...r,status:"success",message:"",url:l.data}),o){case 1:s.value.img_url=l.data,x.value=[...t],console.log("活动封面图片上传成功:",{img_url:s.value.img_url,fileList_length:x.value.length});break;case 2:s.value.lianxi_qrcode=l.data,b.value=[...t],console.log("联系人二维码上传成功:",{lianxi_qrcode:s.value.lianxi_qrcode,fileList1_length:b.value.length});break;case 3:Array.isArray(s.value.imgs_url)||(s.value.imgs_url=[]),s.value.imgs_url.push(l.data),q.value=[...t],console.log("活动图片上传成功:",{imgs_url_length:s.value.imgs_url.length,fileList2_length:q.value.length});break;case 4:s.value.qun_qrcode=l.data,w.value=[...t],console.log("群二维码上传成功:",{qun_qrcode:s.value.qun_qrcode,fileList3_length:w.value.length})}else t.splice(n,1),e.index.$u.toast("上传失败");n++}},re=a=>{a&&a.value&&Array.isArray(a.value)&&a.value[0]&&a.value[0].id&&a.value[0].name?(s.value.type_id=a.value[0].id,f.value=a.value[0].name,_.value=!1):e.index.$u.toast("选择失败，请重试")},se=async()=>{const l=Date.now();if(R.value)e.index.$u.toast("正在提交中，请稍候...");else if(l-H.value<3e3){const a=Math.ceil((3e3-(l-H.value))/1e3);e.index.$u.toast(`请等待${a}秒后再次提交`)}else if(o.requireLogin("","请先登录后再发布活动")){R.value=!0,H.value=l,s.value.title=s.value.name,s.value.money&&""!==s.value.money||(s.value.money="0"),s.value.member_money&&""!==s.value.member_money||(s.value.member_money="0"),i.hasActivityPublishPermission()||(s.value.money="0",s.value.member_money="0");try{await _e(),r.value.validate().then((async()=>{e.index.showLoading({title:A.value?"正在更新活动...":"正在提交活动..."});try{const o=A.value?a.huodongupdate_huodong:a.huodongadd_huodong,i={...s.value,imgs_url:Array.isArray(s.value.imgs_url)?s.value.imgs_url.join(","):s.value.imgs_url,imgs:Array.isArray(s.value.imgs)?s.value.imgs.join(","):s.value.imgs,liucheng:Array.isArray(s.value.liucheng)?JSON.stringify(s.value.liucheng):s.value.liucheng,yongjin_bili:0},n=await o(i);if(e.index.hideLoading(),"ok"===n.status){try{e.index.setStorageSync("last_publish_success",Date.now())}catch(l){}if(await ie(),A.value){try{e.index.removeStorageSync("activity_edit_state")}catch(l){}t.back({tip:`${n.msg},即将返回上级页面`,time:2e3})}else e.index.$u.toast("活动正在审核中，敬请期待!"),setTimeout((()=>e.index.reLaunch({url:"/pages/index"})),2e3)}else"relogin"===n.status?(e.index.$u.toast("登录已过期，请重新登录"),setTimeout((()=>t.navto("/pages/bundle/common/login")),1500)):e.index.$u.toast(n.msg||"提交失败，请重试");R.value=!1}catch(o){e.index.hideLoading(),e.index.$u.toast("网络错误，请检查网络连接后重试"),R.value=!1}})).catch((a=>{Array.isArray(a)&&a.length>0&&a[0].message?e.index.$u.toast(a[0].message):e.index.$u.toast("表单验证失败，请检查必填项"),R.value=!1}))}catch(n){R.value=!1}}},ce=()=>{d.value=!0,h.value="datetime",g.value=Date.now()},de=()=>{v.value=!0,p.value=s.value.baoming_end_time?new Date(s.value.baoming_end_time).getTime():Date.now()},ve=()=>{m.value=!0},me={required_field:"请填写必填信息",invalid_format:"格式不正确，请检查输入",network_error:"网络连接异常，请稍后重试",permission_denied:"权限不足，请联系管理员",file_too_large:"文件过大，请选择较小的文件",upload_failed:"上传失败，请重新尝试"},ge=(a="",l="",t="error")=>{let o="";o=l&&""!==l.trim()?l:a&&me[a]?me[a]:a&&""!==a.trim()?a:"请检查输入信息",console.log("显示验证错误:",{messageKey:a,customMessage:l,friendlyMessage:o,type:t}),e.nextTick$1((()=>{F.value&&"function"==typeof F.value.show?F.value.show({type:t,message:o,duration:3e3,safeAreaInsetTop:!0}):e.index.showToast({title:o,icon:"error"===t?"none":"success",duration:3e3})}))},pe=()=>new Promise(((e,a)=>{const l=[];if(s.value.start_time&&""!==s.value.start_time.trim()||l.push("请选择活动开始的具体日期和时间"),(!s.value.num||s.value.num<=0)&&l.push("请输入活动参与人数（如：10人、50人）"),s.value.contents&&""!==s.value.contents.trim()?s.value.contents.trim().length<10&&l.push("活动内容描述至少需要10个字符"):l.push("请详细描述活动内容、流程和注意事项（至少10个字）"),0===s.value.is_online&&(s.value.addr&&""!==s.value.addr.trim()||l.push("请输入详细的活动地址（如：XX大厦XX楼XX室）"),(!s.value.lng||!s.value.lat||s.value.lng<=0||s.value.lat<=0)&&l.push("请在地图上点击选择活动地点以获取准确坐标")),l.length>0)return ge("",l[0],"error"),void a(l);const t=(()=>{const e=["start_time","baoming_end_time","num","contents"];if(0===s.value.is_online&&e.push("sheng_id","lng","lat","addr"),Te()&&(1===s.value.pay_type||2===s.value.pay_type)){e.push("money");const a=parseFloat(s.value.money)||0,l=parseFloat(s.value.member_money)||0,t=a>0||l>0,o=1===s.value.pay_type;t&&o&&e.push("refund_rule")}return e})();r.value.validateField(t,(l=>{l&&l.length>0?(ge("",l[0].message,"error"),a(l)):e(!0)}))})),_e=()=>new Promise(((e,a)=>{const l=[];if(console.log("验证第三页二维码:",{lianxi_qrcode:s.value.lianxi_qrcode,qun_qrcode:s.value.qun_qrcode,fileList1_length:b.value.length,fileList3_length:w.value.length}),(!s.value.lianxi_qrcode||""===s.value.lianxi_qrcode.trim())&&b.value.length>0&&b.value[0].url&&(s.value.lianxi_qrcode=b.value[0].url,console.log("从fileList1同步联系人二维码URL:",s.value.lianxi_qrcode)),(!s.value.qun_qrcode||""===s.value.qun_qrcode.trim())&&w.value.length>0&&w.value[0].url&&(s.value.qun_qrcode=w.value[0].url,console.log("从fileList3同步群二维码URL:",s.value.qun_qrcode)),(!s.value.lianxi_qrcode||"string"==typeof s.value.lianxi_qrcode&&""===s.value.lianxi_qrcode.trim()||"string"!=typeof s.value.lianxi_qrcode)&&(l.push("请上传您的微信二维码图片，方便参与者联系您"),console.log("联系人二维码验证失败:",s.value.lianxi_qrcode)),(!s.value.qun_qrcode||"string"==typeof s.value.qun_qrcode&&""===s.value.qun_qrcode.trim()||"string"!=typeof s.value.qun_qrcode)&&(l.push("请上传活动微信群二维码，方便参与者加入讨论"),console.log("群二维码验证失败:",s.value.qun_qrcode)),l.length>0)return ge("",l[0],"error"),void a(l);const t=["qun_qrcode","lianxi_qrcode"];r.value.validateField(t,(l=>{l&&l.length>0?(ge("",l[0].message,"error"),a(l)):e(!0)}))})),he=()=>{I.value=!1,t.navto("/pages/bundle/user/vip")},fe=()=>{I.value=!1},ye=async()=>{if(1===j.value)try{await new Promise(((e,a)=>{const l=[];if(s.value.name&&""!==s.value.name.trim()?(s.value.name.trim().length<2||s.value.name.trim().length>50)&&l.push("请输入2-50个字符的活动名称"):l.push("请填写活动名称"),s.value.type_id&&0!==s.value.type_id||l.push("请选择一个活动类型"),(!s.value.img_url||""===s.value.img_url.trim())&&x.value.length>0&&x.value[0].url&&(s.value.img_url=x.value[0].url,console.log("从fileList同步封面图片URL:",s.value.img_url)),!s.value.img_url||"string"==typeof s.value.img_url&&""===s.value.img_url.trim()||"string"!=typeof s.value.img_url?(l.push("请上传一张吸引人的活动封面图片（支持JPG、PNG格式）"),console.log("封面图片验证失败:",s.value.img_url)):console.log("封面图片验证通过:",s.value.img_url),l.length>0)return ge("",l[0],"error"),void a(l);const t=["name","type_id","img_url"];r.value.validateField(t,(l=>{l&&l.length>0?(ge("",l[0].message,"error"),a(l)):e(!0)}))})),j.value=2}catch(e){}else if(2===j.value)try{if(await pe(),!(()=>{const e=l.store().$state.userInfo;if(!o.requireLogin("","请先登录后再发布活动"))return!1;const a=e.is_huiyuan;return 1===a||"1"===a||!0===a||(I.value=!0,!1)})())return;j.value=3}catch(e){}},xe=()=>{2===j.value?j.value=1:3===j.value&&(j.value=2)},be=()=>{try{if(x.value.length>0&&x.value[0].url){const e=x.value[0].url;s.value.img_url&&""!==s.value.img_url.trim()||(s.value.img_url=e)}else s.value.img_url&&""!==s.value.img_url.trim()&&(x.value=[{url:s.value.img_url,status:"success"}],s.value.img_url);if(b.value.length>0&&b.value[0].url){const e=b.value[0].url;s.value.lianxi_qrcode&&""!==s.value.lianxi_qrcode.trim()||(s.value.lianxi_qrcode=e)}else s.value.lianxi_qrcode&&""!==s.value.lianxi_qrcode.trim()&&(b.value=[{url:s.value.lianxi_qrcode,status:"success"}],s.value.lianxi_qrcode);if(w.value.length>0&&w.value[0].url){const e=w.value[0].url;s.value.qun_qrcode&&""!==s.value.qun_qrcode.trim()||(s.value.qun_qrcode=e)}else s.value.qun_qrcode&&""!==s.value.qun_qrcode.trim()&&(w.value=[{url:s.value.qun_qrcode,status:"success"}],s.value.qun_qrcode);if(q.value.length>0){const e=q.value.map((e=>e.url)).filter((e=>e));Array.isArray(s.value.imgs_url)&&0!==s.value.imgs_url.length||(s.value.imgs_url=e)}else Array.isArray(s.value.imgs_url)&&s.value.imgs_url.length>0&&(q.value=s.value.imgs_url.map((e=>({url:e,status:"success"}))),s.value.imgs_url)}catch(e){}},qe=()=>{N.value=!1;(()=>{try{const e="activity_publish_draft",a=l.store().getActivityDraft(e);if(a&&a.formData){const e=a.formData;return e.form&&(s.value={...s.value,...e.form}),x.value=e.fileList||[],b.value=e.fileList1||[],q.value=e.fileList2||[],w.value=e.fileList3||[],be(),f.value=e.activeType||"",$.value=e.place||"",S.value=e.values||"",j.value=e.currentStep||1,s.value.img_url,x.value.length,s.value.lianxi_qrcode,s.value.qun_qrcode,!0}}catch(e){console.error("草稿加载失败:",e)}return!1})()?e.index.$u.toast("草稿已恢复"):e.index.$u.toast("草稿恢复失败")},we=async()=>{N.value=!1,await ie(),$e(),e.index.$u.toast("已清除草稿，开始新的填写")},$e=()=>{var e;s.value={name:"",title:"",type_id:null,sheng_id:"",shi_id:"",qu_id:"",addr:"",lng:"",lat:"",num:"",money:"",member_money:"",img_url:"",contents:"",start_time:"",end_time:"",pay_type:2,is_online:0,enable_checkin:0,lianxi_name:(null==(e=l.store().$state.userInfo)?void 0:e.nickname)||"",lianxi_mobile:"",lianxi_qrcode:"",qun_qrcode:"",imgs_url:[],imgs:[],liucheng:[]},x.value=[],b.value=[],q.value=[],w.value=[],f.value="",$.value="",j.value=1},Ae=e.computed((()=>{const e=parseFloat(s.value.money),a=parseFloat(s.value.member_money);if(!isNaN(e)&&e>0&&!isNaN(a)&&a>=0&&a<e){return`会员价立减 ${(e-a).toFixed(2)} 元`}return""})),Le=e.computed((()=>i.hasActivityPublishPermission())),ke=e.computed((()=>{const e=i.hasActivityPublishPermission(),a=parseFloat(s.value.money)||0,l=parseFloat(s.value.member_money)||0,t=a>0||l>0,o=1===s.value.pay_type;return e&&t&&o}));e.computed((()=>{const e=parseFloat(s.value.money)||0,a=parseFloat(s.value.member_money)||0;return e>0||a>0}));const Te=()=>i.hasActivityPublishPermission(),Se=e.ref([{value:1,label:"随时退款：活动开始前随时申请退款100%，活动开始后不支持退款"},{value:2,label:"12小时规则：开始前12小时前申请退款100%，开始前12小时内申请退款50%，开始后不支持退款"},{value:3,label:"24小时规则：开始前24小时前申请退款100%，开始前24小时内申请退款50%，开始后不支持退款"},{value:4,label:"48小时规则：开始前48小时前申请退款100%，开始前48小时内申请退款30%，开始后不支持退款"},{value:5,label:"72小时规则：开始前72小时前申请退款100%，开始前72小时内申请退款30%，开始后不支持退款"}]),Ce=e.computed((()=>{const e=Se.value.find((e=>e.value===s.value.refund_rule));return e?e.label:"请选择退款规则"})),De=e=>{s.value.pay_type=e,0===e&&(s.value.money="",s.value.member_money="")};e.watch((()=>s.value.is_online),(e=>{const a=["sheng_id","addr","lng","lat"];1===e?(["sheng_id","shi_id","qu_id","addr","lng","lat"].forEach((e=>{["sheng_id","shi_id","qu_id","lng","lat"].includes(e)?s.value[e]=0:s.value[e]=""})),s.value.enable_checkin=0,$.value="",a.forEach((e=>{c.value[e]&&(Array.isArray(c.value[e])?c.value[e][0].required=!1:c.value[e].required=!1)})),r.value&&r.value.clearValidate(a)):a.forEach((e=>{c.value[e]&&(Array.isArray(c.value[e])?c.value[e][0].required=!0:c.value[e].required=!0)}))}));const Pe=()=>{e.index.reLaunch({url:"/pages/index"})},ze=()=>{s.value={name:"",title:"",type_id:null,sheng_id:"",shi_id:"",qu_id:"",addr:"",lng:"",lat:"",num:"",money:"",member_money:"",img_url:"",contents:"",start_time:"",end_time:"",pay_type:2,is_online:0,enable_checkin:0,lianxi_name:"",lianxi_mobile:"",lianxi_qrcode:"",qun_qrcode:"",imgs_url:[],imgs:[],liucheng:[]},f.value="",$.value="",S.value="",x.value=[],b.value=[],q.value=[],w.value=[],j.value=1,A.value=!1,console.log("表单已重置到初始状态")};e.watch([D,()=>s.value.contents,()=>S.value],(([a,l,t])=>{if(a&&C.value){const o=l||t||"";o?(console.log("监听到编辑器准备就绪且有内容，开始设置，内容长度:",o.length,"包含HTML:",o.includes("<"),"模式:",{isEdit:A.value,isDuplicate:L.value,formContentsLength:(l||"").length,valuesLength:(t||"").length,editorReady:a,editorExists:!!C.value}),e.nextTick$1((()=>{setTimeout((()=>{console.log("监听器触发设置编辑器内容，最终内容长度:",o.length),K(o)}),150)}))):console.log("监听器触发，但无内容需要设置，编辑器状态:",{ready:a,editorExists:!!C.value,formContentsLength:(l||"").length,valuesLength:(t||"").length})}}),{immediate:!0});return["name","type_id","contents","start_time","num","addr","money"].forEach((a=>{e.watch((()=>s.value[a]),(()=>{!A.value&&M.value&&J()}),{immediate:!1})})),(a,t)=>{var o;return e.e({a:e.p({name:"home",size:"60rpx",color:"#ffffff"}),b:e.o(Pe),c:e.t(A.value?"编辑活动":"发布活动"),d:e.o(ue),e:e.o(ne),f:e.p({fileList:x.value,name:"1",maxCount:1,width:320,height:180,uploadIcon:"photo",uploadText:"",uploadIconColor:"#a8b8a8",previewSize:320,previewFullImage:!0,deletable:!0}),g:e.p({prop:"img_url",borderBottom:!0}),h:e.o((e=>s.value.name=e)),i:e.p({customStyle:{padding:"0",background:"transparent",border:"none",fontSize:"28rpx",color:"#333333"},placeholder:"为活动取一个响亮的标题吧！",border:"none","placeholder-style":"font-size:28rpx;color:#999999",modelValue:s.value.name}),j:e.p({label:"活动标题",prop:"name",labelPosition:"top"}),k:e.p({align:"left",size:"28rpx",color:f.value?"#333333":"#999999",text:f.value||"请选择活动类型"}),l:e.o((e=>_.value=!0)),m:e.p({name:"arrow-down",size:"24rpx",color:"#999999"}),n:e.o((e=>_.value=!0)),o:e.p({label:"活动类型",prop:"type_id",labelPosition:"top"}),p:0===s.value.is_online?1:"",q:e.o((e=>s.value.is_online=0)),r:1===s.value.is_online?1:"",s:e.o((e=>s.value.is_online=1)),t:e.p({label:"活动性质",labelPosition:"top"}),v:0===s.value.is_online},0===s.value.is_online?{w:e.p({text:"是否启用签到功能",size:"28rpx",color:"#333"}),x:e.p({text:"启用后参与者需要签到，正常签到+3分，迟到-10分，缺席-20分",size:"24rpx",color:"#999"}),y:e.o((e=>s.value.enable_checkin=e)),z:e.p({activeColor:"#6AC086",inactiveColor:"#ddd",size:"24",modelValue:s.value.enable_checkin}),A:e.p({label:"签到功能",labelPosition:"top"})}:{},{B:e.unref(Le)},e.unref(Le)?{C:0===s.value.member_only?1:"",D:e.o((e=>s.value.member_only=0)),E:1===s.value.member_only?1:"",F:e.o((e=>s.value.member_only=1)),G:e.p({label:"参与权限",labelPosition:"top"})}:{},{H:1===j.value,I:0===s.value.is_online},0===s.value.is_online?{J:e.p({align:"left",size:"28rpx",color:$.value?"#333333":"#999999",text:$.value||"应该在哪片云朵下等你呢？"}),K:e.o(ae),L:e.p({name:"arrow-down",size:"24rpx",color:"#999999"}),M:e.o(ae),N:e.p({label:"活动地点",prop:"sheng_id",labelPosition:"top"}),O:e.o((e=>s.value.addr=e)),P:e.p({customStyle:{padding:"0",background:"transparent",border:"none",fontSize:"28rpx",color:"#333333"},placeholder:"请输入活动详细地址",border:"none","placeholder-style":"font-size:28rpx;color:#999999",modelValue:s.value.addr}),Q:e.p({label:"活动详细地址",labelPosition:"top"})}:{},{R:e.p({align:"left",size:"28rpx",color:s.value.start_time?"#333333":"#999999",text:s.value.start_time||"请选择活动开始时间"}),S:e.o(ce),T:e.p({name:"arrow-down",size:"24rpx",color:"#999999"}),U:e.o(ce),V:e.p({label:"活动开始时间",prop:"start_time",labelPosition:"top"}),W:e.p({align:"left",size:"28rpx",color:s.value.baoming_end_time?"#333333":"#999999",text:s.value.baoming_end_time||"请选择活动报名截止时间"}),X:e.o(de),Y:e.p({name:"arrow-down",size:"24rpx",color:"#999999"}),Z:e.o(de),aa:e.p({label:"活动报名截止时间",prop:"baoming_end_time",labelPosition:"top"}),ab:e.o((e=>s.value.num=e)),ac:e.p({customStyle:{padding:"0",background:"transparent",border:"none",fontSize:"28rpx",color:"#333333"},type:"number",placeholder:"请输入参与人数",border:"none","suffix-icon":"人","suffix-icon-style":"fontSize:28rpx;color:#666666","placeholder-style":"font-size:28rpx;color:#999999",modelValue:s.value.num}),ad:e.p({label:"参与人数",prop:"num",labelPosition:"top"}),ae:Te()},Te()?e.e({af:e.p({text:"线上收费",size:"28rpx",color:1===s.value.pay_type?"#6AC086":"#333",bold:1===s.value.pay_type}),ag:e.p({text:"用户在线支付，自动分配收入",size:"24rpx",color:"#999"}),ah:1===s.value.pay_type?1:"",ai:e.o((e=>De(1))),aj:e.p({text:"线下收费",size:"28rpx",color:2===s.value.pay_type?"#6AC086":"#333",bold:2===s.value.pay_type}),ak:e.p({text:"现场收费，无需在线支付",size:"24rpx",color:"#999"}),al:2===s.value.pay_type?1:"",am:e.o((e=>De(2))),an:e.p({label:"支付方式",labelPosition:"top"}),ao:1===s.value.pay_type||2===s.value.pay_type},1===s.value.pay_type||2===s.value.pay_type?e.e({ap:e.o((e=>s.value.money=e)),aq:e.p({customStyle:{padding:"0",background:"transparent",border:"none",fontSize:"28rpx",color:"#333333"},type:"digit",placeholder:"请输入普通用户报名费用",border:"none",prefixIcon:"rmb",prefixIconStyle:"font-size: 28rpx; color: #666666","placeholder-style":"font-size:28rpx;color:#999999",modelValue:s.value.money}),ar:e.p({label:"普通用户费用",prop:"money",labelPosition:"top"}),as:e.o((e=>s.value.member_money=e)),at:e.p({customStyle:{padding:"0",background:"transparent",border:"none",fontSize:"28rpx",color:"#333333"},type:"digit",placeholder:"选填，会员优惠价格",border:"none",prefixIcon:"rmb",prefixIconStyle:"font-size: 28rpx; color: #666666","placeholder-style":"font-size:28rpx;color:#999999",modelValue:s.value.member_money}),av:e.unref(Ae)},e.unref(Ae)?{aw:e.p({text:e.unref(Ae),size:"24rpx",color:"#666666",margin:"5rpx 0 0 0"})}:{},{ax:e.p({label:"会员用户费用",prop:"member_money",labelPosition:"top"}),ay:e.unref(ke)},e.unref(ke)?{az:e.p({align:"left",size:"28rpx",color:s.value.refund_rule?"#333333":"#999999",text:e.unref(Ce)}),aA:e.o(ve),aB:e.p({name:"arrow-down",size:"24rpx",color:"#999999"}),aC:e.o(ve),aD:e.p({label:"退款规则",prop:"refund_rule",labelPosition:"top"})}:{}):{}):{},{aE:e.p({label:"活动描述",prop:"contents"}),aF:e.o(Z),aG:e.o(ee),aH:e.o(Q),aI:2===j.value,aJ:e.o(ue),aK:e.o(ne),aL:e.p({fileList:w.value,name:"4",maxCount:1,width:160,height:160,uploadIconColor:"#a8b8a8",previewSize:160}),aM:e.p({"label-width":"320rpx",label:"上传活动群微信二维码",labelPosition:"top",prop:"qun_qrcode"}),aN:e.o(ue),aO:e.o(ne),aP:e.p({fileList:b.value,name:"2",width:160,height:160,maxCount:1,uploadIconColor:"#a8b8a8",previewSize:160}),aQ:e.p({"label-width":"320rpx",label:"上传联系人微信二维码",labelPosition:"top",prop:"lianxi_qrcode"}),aR:e.p({label:"活动图片(不超过9张)",labelPosition:"top"}),aS:e.o(ue),aT:e.o(ne),aU:e.p({fileList:q.value,name:"3",multiple:!0,width:160,height:160,maxCount:9,uploadIconColor:"#a8b8a8",previewSize:160}),aV:3===j.value,aW:e.sr(r,"3e9dfa88-1",{k:"formRef"}),aX:e.p({labelPosition:"left",model:s.value,"label-width":"260rpx",rules:c.value,"label-style":{padding:"15rpx 0",fontSize:"28rpx",color:"#333333",fontWeight:"normal"},errorType:"toast"}),aY:e.p({height:y.value+20}),aZ:1===j.value},1===j.value?{ba:e.o(ye),bb:e.p({text:"下一页",shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{height:"98rpx",color:"#fff",fontWeight:"600",boxShadow:"0 4rpx 16rpx rgba(106, 192, 134, 0.3)"}})}:{},{bc:2===j.value},2===j.value?{bd:e.o(xe),be:e.p({text:"上一页",shape:"circle",color:"#ffffff",customStyle:{height:"98rpx",color:"#333333",fontWeight:"600",border:"2rpx solid #e6e6e6",boxShadow:"0 4rpx 16rpx rgba(0, 0, 0, 0.1)",marginRight:"20rpx",flex:"1"}}),bf:e.o(ye),bg:e.p({text:"下一页",shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{height:"98rpx",color:"#fff",fontWeight:"600",boxShadow:"0 4rpx 16rpx rgba(106, 192, 134, 0.3)",flex:"1"}})}:{},{bh:3===j.value},3===j.value?{bi:e.o(xe),bj:e.p({text:"上一页",shape:"circle",color:"#ffffff",customStyle:{height:"98rpx",color:"#333333",fontWeight:"600",border:"2rpx solid #e6e6e6",boxShadow:"0 4rpx 16rpx rgba(0, 0, 0, 0.1)",marginRight:"20rpx",flex:"1"}}),bk:e.o(se),bl:e.p({text:R.value?"提交中...":"发布",shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{height:"98rpx",color:"#fff",fontWeight:"600",boxShadow:"0 4rpx 16rpx rgba(106, 192, 134, 0.3)",flex:"1"},loading:R.value,disabled:R.value})}:{},{bm:e.o(B),bn:e.o((e=>d.value=!1)),bo:e.o((e=>d.value=!1)),bp:e.o((e=>g.value=e)),bq:e.p({show:d.value,type:"datetime","min-date":e.unref(P),"max-date":e.unref(z),"confirm-color":"#6AC086","cancel-color":"#999999",formatter:V,modelValue:g.value}),br:e.o((e=>_.value=!1)),bs:e.o((e=>_.value=!1)),bt:e.o(re),bv:e.p({show:_.value,columns:u.value,"close-on-click-overlay":!0,"key-name":"name","confirm-color":"#6AC086","cancel-color":"#999999"}),bw:e.o(we),bx:e.o(qe),by:e.o((e=>N.value=!1)),bz:e.p({show:N.value,title:"发现未完成的活动",content:"您有未发布的活动，是否继续填写？","show-cancel-button":!0,"cancel-text":"重新填写","confirm-text":"继续填写"}),bA:e.t(0===(null==(o=e.unref(l.store)().$state.userInfo)?void 0:o.is_huiyuan)?"(或已到期)":""),bB:e.o(he),bC:e.o(fe),bD:e.p({show:I.value,title:"会员功能",closeOnClickOverlay:!1,showCancelButton:!0,confirmText:"成为会员",cancelText:"取消",confirmColor:"#6AC086",cancelColor:"#666666"}),bE:e.sr(F,"3e9dfa88-59",{k:"notifyRef"}),bF:e.p({safeAreaInsetTop:!0,duration:3e3}),bG:e.sr("baomingEndTimePicker","3e9dfa88-60"),bH:e.o(W),bI:e.o((e=>v.value=!1)),bJ:e.o((e=>p.value=e)),bK:e.p({mode:"datetime",show:v.value,minDate:Date.now(),maxDate:e.unref(z),title:"选择报名截止时间",modelValue:p.value}),bL:e.sr("refundRulePicker","3e9dfa88-61"),bM:e.o(G),bN:e.o(U),bO:e.o((e=>m.value=!1)),bP:e.o((e=>m.value=!1)),bQ:e.p({show:m.value,columns:[Se.value],keyName:"label",title:"选择退款规则","confirm-color":"#6AC086","cancel-color":"#999999"})})}},__runtimeHooks:1};wx.createPage(n);
//# sourceMappingURL=addActive.js.map
