"use strict";const e=require("../common/vendor.js"),a=require("../api/index.js");if(require("../utils/request.js"),require("../utils/BaseUrl.js"),require("../store/index.js"),require("../store/counter.js"),require("../utils/index.js"),require("../utils/systemInfo.js"),require("../utils/auth.js"),require("../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const t={__name:"city-select",setup(t){const o=e.ref(""),l=e.ref([]),u=e.ref({}),n=e.ref(null),s=e.ref(!1),c=e.ref(1e3),i=e.ref(""),r=e.ref("A"),d=e.ref(!1),v=e.ref(null),p=e.computed((()=>o.value?l.value.filter((e=>e.name.toLowerCase().includes(o.value.toLowerCase())||e.pinyin&&e.pinyin.toLowerCase().includes(o.value.toLowerCase()))):[])),m=e.computed((()=>Object.keys(u.value).sort()));e.onLoad((()=>{f(),g(),y()})),e.onShow((()=>{f()}));const f=async()=>{await e.nextTick$1();const a=e.index.getWindowInfo().windowHeight,t=208+(n.value?100:0)+80+40;c.value=2*a-t},g=()=>{e.index.getLocation({type:"wgs84",isHighAccuracy:!0,success:async t=>{try{const o=await a.getAddr({longitude:t.longitude,latitude:t.latitude});if(o&&1==o.status){const a=o.regeocode.addressComponent,t=e.index.$u.test.isEmpty(a.city)?a.province:a.city;if(a.adcode&&a.adcode.length>=4){const e=a.adcode.substring(0,2)+"0000",o={11e4:"110100",12e4:"120100",31e4:"310100",5e5:"500100"};let l;l=o[e]?o[e]:a.adcode.substring(0,4)+"00",n.value={adcode:l,name:t},f()}}}catch(o){console.log("获取定位失败:",o)}},fail:e=>{console.log("定位失败:",e)}})},y=async()=>{try{s.value=!0;const t=await a.getCityList("",1);"ok"===t.status?t.data&&"object"==typeof t.data?(u.value=t.data,l.value=[],Object.values(t.data).forEach((e=>{l.value.push(...e)}))):(l.value=t.data||[],x()):e.index.$u.toast(t.msg||"获取城市列表失败")}catch(t){console.error("加载城市列表失败:",t),e.index.$u.toast("网络错误，请稍后重试")}finally{s.value=!1}},x=()=>{const e={};l.value.forEach((a=>{const t=a.first_letter||L(a.name);e[t]||(e[t]=[]),e[t].push(a)})),u.value=e},L=e=>({"阿":"A","安":"A","鞍":"A","北":"B","保":"B","包":"B","蚌":"B","滨":"B","亳":"B","白":"B","百":"B","成":"C","重":"C","长":"C","常":"C","沧":"C","承":"C","赤":"C","大":"D","东":"D","丹":"D","德":"D","佛":"F","福":"F","抚":"F","阜":"F","防":"F","广":"G","贵":"G","桂":"G","赣":"G","杭":"H","哈":"H","合":"H","海":"H","惠":"H","邯":"H","衡":"H","呼":"H","葫":"H","鹤":"H","黑":"H","淮":"H","湖":"H","黄":"H","菏":"H","怀":"H","贺":"H","济":"J","吉":"J","锦":"J","佳":"J","荆":"J","九":"J","吉":"J","景":"J","金":"J","嘉":"J","昆":"K","开":"K","兰":"L","拉":"L","洛":"L","柳":"L","廊":"L","聊":"L","临":"L","莱":"L","辽":"L","连":"L","丽":"L","泸":"L","乐":"L","凉":"L","娄":"L","绵":"M","牡":"M","马":"M","梅":"M","眉":"M","南":"N","宁":"N","内":"N","平":"P","盘":"P","萍":"P","莆":"P","青":"Q","秦":"Q","齐":"Q","泉":"Q","曲":"Q","日":"R","上":"S","深":"S","沈":"S","石":"S","苏":"S","三":"S","绍":"S","宿":"S","遂":"S","天":"T","太":"T","唐":"T","铁":"T","台":"T","通":"T","泰":"T","武":"W","无":"W","温":"W","潍":"W","威":"W","芜":"W","梧":"W","西":"X","厦":"X","西":"X","徐":"X","邢":"X","信":"X","新":"X","许":"X","孝":"X","襄":"X","湘":"X","银":"Y","烟":"Y","扬":"Y","盐":"Y","岳":"Y","益":"Y","永":"Y","玉":"Y","宜":"Y","雅":"Y","郑":"Z","珠":"Z","湛":"Z","中":"Z","张":"Z","淄":"Z","枣":"Z","镇":"Z","舟":"Z","株":"Z","遵":"Z","自":"Z","资":"Z"}[e.charAt(0)]||"Z"),h=e=>{o.value=e.detail.value},C=()=>{},H=()=>{o.value=""},j=a=>{e.index.setStorageSync("selectedCity",{adcode:a.adcode,name:a.name}),e.index.navigateBack({success:()=>{e.index.$emit("citySelected",a)}})},w=()=>{e.index.navigateBack()},Z=e=>{};return(a,t)=>e.e({a:e.p({name:"arrow-left",size:"40rpx",color:"#333"}),b:e.o(w),c:e.p({name:"search",size:"32rpx",color:"#999"}),d:e.o([e=>o.value=e.detail.value,h]),e:e.o(C),f:o.value,g:o.value},o.value?{h:e.p({name:"close-circle-fill",size:"32rpx",color:"#ccc"}),i:e.o(H)}:{},{j:n.value&&!o.value},n.value&&!o.value?{k:e.p({name:"map-pin",size:"32rpx",color:"#6AC086"}),l:e.t(n.value.name),m:e.o((e=>j(n.value)))}:{},{n:s.value},s.value?{o:e.p({mode:"spinner",size:"60rpx",color:"#6AC086"})}:e.e({p:o.value},o.value?e.e({q:e.unref(p).length>0},e.unref(p).length>0?{r:e.f(e.unref(p),((a,t,o)=>({a:e.t(a.name),b:e.t(a.pinyin),c:a.adcode,d:e.o((e=>j(a)),a.adcode)})))}:{s:e.p({name:"search",size:"80rpx",color:"#ccc"})},{t:c.value+"rpx"}):e.e({v:Object.keys(u.value).length>0},Object.keys(u.value).length>0?{w:e.f(u.value,((a,t,o)=>({a:e.t(t),b:e.f(a,((a,t,o)=>({a:e.t(a.name),b:a.adcode,c:e.o((e=>j(a)),a.adcode)}))),c:t,d:`letter-${t}`})))}:{x:e.p({name:"list",size:"80rpx",color:"#ccc"})},{y:c.value+"rpx",z:i.value,A:e.o(Z),B:e.f(e.unref(m),((a,t,o)=>({a:e.t(a),b:a,c:r.value===a?1:"",d:e.o((e=>(e=>{i.value=`letter-${e}`,r.value=e,d.value=!0,v.value&&clearTimeout(v.value),v.value=setTimeout((()=>{d.value=!1}),1e3)})(a)),a)})))})),{C:d.value},d.value?{D:e.t(r.value)}:{})}},o=e._export_sfc(t,[["__scopeId","data-v-220e068e"]]);wx.createPage(o);
//# sourceMappingURL=city-select.js.map
