"use strict";const t=require("../../../../common/vendor.js"),e={name:"u-empty",mixins:[t.mpMixin,t.mixin,t.props$35],data:()=>({icons:{car:"购物车为空",page:"页面不存在",search:"没有搜索结果",address:"没有收货地址",wifi:"没有WiFi",order:"订单为空",coupon:"没有优惠券",favor:"暂无收藏",permission:"无权限",history:"无历史记录",news:"无新闻列表",message:"消息列表为空",list:"列表为空",data:"数据为空",comment:"暂无评论"}}),computed:{emptyStyle(){const e={};return e.marginTop=t.addUnit(this.marginTop),t.deepMerge(t.addStyle(this.customStyle),e)},textStyle(){const e={};return e.color=this.textColor,e.fontSize=t.addUnit(this.textSize),e},isSrc(){return this.icon.indexOf("/")>=0}},methods:{addUnit:t.addUnit}};if(!Array){t.resolveComponent("u-icon")()}Math;const o=t._export_sfc(e,[["render",function(e,o,i,s,n,r){return t.e({a:e.show},e.show?t.e({b:!r.isSrc},r.isSrc?{d:r.addUnit(e.width),e:r.addUnit(e.height),f:e.icon}:{c:t.p({name:"message"===e.mode?"chat":`empty-${e.mode}`,size:e.iconSize,color:e.iconColor,"margin-top":"14"})},{g:t.t(e.text?e.text:n.icons[e.mode]),h:t.s(r.textStyle),i:e.$slots.default||e.$slots.$default},(e.$slots.default||e.$slots.$default,{}),{j:t.s(r.emptyStyle)}):{})}],["__scopeId","data-v-6ea1a180"]]);wx.createComponent(o);
//# sourceMappingURL=u-empty.js.map
