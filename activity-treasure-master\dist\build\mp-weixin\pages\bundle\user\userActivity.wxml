<view class="page data-v-09005178"><my-title wx:if="{{a}}" class="data-v-09005178" u-i="09005178-0" bind:__l="__l" u-p="{{a}}"></my-title><view class="tab-container data-v-09005178"><u-tabs wx:if="{{c}}" class="data-v-09005178" bindclick="{{b}}" u-i="09005178-1" bind:__l="__l" u-p="{{c}}"></u-tabs></view><view class="px30 data-v-09005178"><mescroll-uni wx:if="{{m}}" u-s="{{['d']}}" class="list data-v-09005178" bindinit="{{i}}" binddown="{{j}}" bindup="{{k}}" bindtopclick="{{l}}" u-i="09005178-2" bind:__l="__l" u-p="{{m}}"><view wx:if="{{d}}" class="empty data-v-09005178"><image src="{{e}}" mode="aspectFit" class="empty-img data-v-09005178"></image><text class="empty-text data-v-09005178">暂无{{f}}内容</text></view><view wx:else class="data-v-09005178"><view wx:for="{{g}}" wx:for-item="val" wx:key="m" class="item data-v-09005178" bindtap="{{val.n}}"><view wx:if="{{val.a}}" class="img data-v-09005178"><image class="pic data-v-09005178" src="{{val.b}}" mode="aspectFill" lazy-load></image></view><view class="{{['info', 'data-v-09005178', val.k && 'full']}}"><view class="header data-v-09005178"><view class="type data-v-09005178"><text class="type-tag data-v-09005178">{{val.c}}</text></view><view class="time data-v-09005178"><u-text wx:if="{{val.e}}" class="data-v-09005178" u-i="{{val.d}}" bind:__l="__l" u-p="{{val.e}}"></u-text></view></view><view class="title data-v-09005178"><u-text wx:if="{{val.g}}" class="data-v-09005178" u-i="{{val.f}}" bind:__l="__l" u-p="{{val.g}}"></u-text></view><view wx:if="{{val.h}}" class="quote data-v-09005178"><view class="quote-label data-v-09005178">引用内容：</view><u-text wx:if="{{val.j}}" class="data-v-09005178" u-i="{{val.i}}" bind:__l="__l" u-p="{{val.j}}"></u-text></view></view><view class="arrow data-v-09005178"><u-icon wx:if="{{h}}" class="data-v-09005178" u-i="{{val.l}}" bind:__l="__l" u-p="{{h}}"></u-icon></view></view></view></mescroll-uni></view></view>