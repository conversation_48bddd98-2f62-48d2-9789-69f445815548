{"version": 3, "file": "create.js", "sources": ["../../../../../../../src/pages/bundle/world/author/create.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGF1dGhvclxjcmVhdGUudnVl"], "sourcesContent": ["<script setup>\nimport { ref, onMounted } from 'vue';\nimport { createAuthor, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 创建作者页面. Confirmed via 寸止 }}\nconst formData = ref({\n  name: '',\n  category: '',\n  description: '',\n  birth_year: '',\n  death_year: '',\n  nationality: '',\n  avatar: ''\n});\n\nconst isSubmitting = ref(false);\nconst avatarList = ref([]);\n\n// 获取页面参数\nconst pages = getCurrentPages();\nconst currentPageInstance = pages[pages.length - 1];\nconst isSelectMode = currentPageInstance.options.type === 'select';\nconst presetKeyword = currentPageInstance.options.keyword || '';\n\n// 常用作者类别\nconst categoryOptions = [\n  '作家', '诗人', '哲学家', '思想家', '政治家', \n  '科学家', '艺术家', '音乐家', '历史学家', '经济学家',\n  '心理学家', '社会学家', '教育家', '企业家', '其他'\n];\n\n// 处理头像上传\nconst handleAvatarRead = async (event) => {\n  let lists = [].concat(event.file);\n  \n  // 只能上传一张头像\n  if (avatarList.value.length > 0) {\n    uni.showToast({ title: '只能上传一张头像，将替换现有头像', icon: 'none' });\n    avatarList.value = [];\n  }\n  \n  if (lists.length > 1) {\n    uni.showToast({ title: '只能上传一张头像，已自动选择第一张', icon: 'none' });\n    lists = [lists[0]];\n  }\n  \n  let fileListLen = avatarList.value.length;\n  \n  lists.map((item) => {\n    avatarList.value.push({\n      ...item,\n      status: 'uploading',\n      message: '上传中'\n    });\n  });\n  \n  for (let i = 0; i < lists.length; i++) {\n    const currentFileIndex = fileListLen + i;\n    try {\n      const res = await upload_img(lists[i]);\n      if (res && res.url) {\n        avatarList.value[currentFileIndex].status = 'success';\n        avatarList.value[currentFileIndex].url = res.url;\n        formData.value.avatar = res.url;\n      } else {\n        throw new Error('上传失败');\n      }\n    } catch (error) {\n      avatarList.value[currentFileIndex].status = 'failed';\n      avatarList.value[currentFileIndex].message = '上传失败';\n      uni.showToast({ title: '头像上传失败', icon: 'none' });\n    }\n  }\n};\n\n// 删除头像\nconst handleDeleteAvatar = (event) => {\n  avatarList.value.splice(event.index, 1);\n  formData.value.avatar = '';\n};\n\n// 选择类别\nconst selectCategory = () => {\n  uni.showActionSheet({\n    itemList: categoryOptions,\n    success: (res) => {\n      formData.value.category = categoryOptions[res.tapIndex];\n    }\n  });\n};\n\n// 提交表单\nconst submitForm = async () => {\n  // 验证必填字段\n  if (!formData.value.name.trim()) {\n    uni.showToast({ title: '请输入作者姓名', icon: 'none' });\n    return;\n  }\n  \n  if (isSubmitting.value) return;\n  isSubmitting.value = true;\n  \n  try {\n    const params = {\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      name: formData.value.name.trim(),\n      category: formData.value.category.trim(),\n      description: formData.value.description.trim(),\n      birth_year: formData.value.birth_year.trim(),\n      death_year: formData.value.death_year.trim(),\n      nationality: formData.value.nationality.trim(),\n      avatar: formData.value.avatar\n    };\n    \n    const response = await createAuthor(params);\n    \n    if (response.status === 'ok') {\n      uni.showToast({ title: '创建成功', icon: 'success' });\n      \n      // 如果是选择模式，发送创建结果给父页面\n      if (isSelectMode) {\n        const newAuthor = {\n          id: response.data.author_id,\n          name: formData.value.name,\n          category: formData.value.category,\n          description: formData.value.description,\n          avatar: formData.value.avatar,\n          quote_count: 0\n        };\n        uni.$emit('authorCreated', newAuthor);\n      }\n      \n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1000);\n    } else if (response.status === 'relogin') {\n      uni.showToast({ title: '请先登录', icon: 'none' });\n    } else {\n      uni.showToast({ title: response.msg || '创建失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('创建作者失败:', error);\n    uni.showToast({ title: '创建失败，请稍后重试', icon: 'none' });\n  } finally {\n    isSubmitting.value = false;\n  }\n};\n\n// 页面加载时预填关键词\nonMounted(() => {\n  if (presetKeyword) {\n    formData.value.name = decodeURIComponent(presetKeyword);\n  }\n});\n</script>\n\n<template>\n  <view class=\"create-author-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar \n      :title=\"isSelectMode ? '创建作者' : '新建作者'\" \n      backIcon=\"arrow-left\"\n    />\n    \n    <!-- 表单内容 -->\n    <view class=\"form-container\">\n      <!-- 头像上传 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">头像 (选填)</view>\n        <view class=\"avatar-upload\">\n          <u-upload\n            :fileList=\"avatarList\"\n            @afterRead=\"handleAvatarRead\"\n            @delete=\"handleDeleteAvatar\"\n            name=\"file\"\n            :maxCount=\"1\"\n            :previewImage=\"true\"\n            width=\"160rpx\"\n            height=\"160rpx\"\n            uploadIconColor=\"#6AC086\"\n          ></u-upload>\n        </view>\n      </view>\n      \n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">基本信息</view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">姓名 *</view>\n          <input \n            v-model=\"formData.name\" \n            placeholder=\"请输入作者姓名\"\n            class=\"item-input\"\n            maxlength=\"50\"\n          />\n        </view>\n        \n        <view class=\"form-item\" @click=\"selectCategory\">\n          <view class=\"item-label\">类别</view>\n          <view class=\"item-input\" :class=\"{ 'placeholder': !formData.category }\">\n            {{ formData.category || '请选择作者类别' }}\n          </view>\n          <u-icon name=\"arrow-right\" size=\"16\" color=\"#ccc\"></u-icon>\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">国籍</view>\n          <input \n            v-model=\"formData.nationality\" \n            placeholder=\"请输入国籍\"\n            class=\"item-input\"\n            maxlength=\"30\"\n          />\n        </view>\n      </view>\n      \n      <!-- 时间信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">时间信息 (选填)</view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">出生年份</view>\n          <input \n            v-model=\"formData.birth_year\" \n            placeholder=\"如：1900\"\n            class=\"item-input\"\n            type=\"number\"\n            maxlength=\"4\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">逝世年份</view>\n          <input \n            v-model=\"formData.death_year\" \n            placeholder=\"如：1980（在世可不填）\"\n            class=\"item-input\"\n            type=\"number\"\n            maxlength=\"4\"\n          />\n        </view>\n      </view>\n      \n      <!-- 描述信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">描述信息 (选填)</view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">简介</view>\n          <textarea \n            v-model=\"formData.description\" \n            placeholder=\"请输入作者简介或主要成就\"\n            class=\"item-textarea\"\n            maxlength=\"500\"\n            auto-height\n          ></textarea>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <view class=\"submit-btn\" @click=\"submitForm\" :class=\"{ 'submitting': isSubmitting }\">\n        <u-loading-icon v-if=\"isSubmitting\" mode=\"spinner\" color=\"white\" size=\"20\"></u-loading-icon>\n        <text class=\"submit-text\" v-if=\"!isSubmitting\">创建作者</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.create-author-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  padding-bottom: 120rpx;\n  \n  .form-container {\n    padding: 32rpx;\n    \n    .form-section {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 32rpx;\n      \n      .section-title {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 32rpx;\n      }\n      \n      .avatar-upload {\n        display: flex;\n        justify-content: center;\n      }\n      \n      .form-item {\n        display: flex;\n        align-items: center;\n        padding: 24rpx 0;\n        border-bottom: 1rpx solid #f0f0f0;\n        min-height: 88rpx;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        .item-label {\n          width: 160rpx;\n          font-size: 32rpx;\n          color: #333;\n          flex-shrink: 0;\n        }\n        \n        .item-input {\n          flex: 1;\n          font-size: 32rpx;\n          color: #333;\n          \n          &.placeholder {\n            color: #999;\n          }\n        }\n        \n        .item-textarea {\n          flex: 1;\n          font-size: 32rpx;\n          color: #333;\n          min-height: 120rpx;\n          line-height: 1.6;\n        }\n      }\n    }\n  }\n  \n  .submit-section {\n    position: fixed;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    padding: 32rpx;\n    background-color: white;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .submit-btn {\n      width: 100%;\n      height: 88rpx;\n      background-color: #6AC086;\n      border-radius: 50rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      \n      &.submitting {\n        opacity: 0.7;\n      }\n      \n      .submit-text {\n        font-size: 32rpx;\n        color: white;\n        font-weight: 500;\n        margin-left: 16rpx;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/author/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "formData", "ref", "name", "category", "description", "birth_year", "death_year", "nationality", "avatar", "isSubmitting", "avatarList", "pages", "getCurrentPages", "currentPageInstance", "length", "isSelectMode", "options", "type", "presetKeyword", "keyword", "categoryOptions", "handleAvatarRead", "async", "event", "lists", "concat", "file", "value", "uni", "index", "showToast", "title", "icon", "fileListLen", "map", "item", "push", "status", "message", "i", "currentFileIndex", "res", "upload_img", "url", "Error", "error", "handleDeleteAvatar", "splice", "selectCategory", "showActionSheet", "itemList", "success", "tapIndex", "submitForm", "trim", "params", "uid", "store", "$state", "userInfo", "token", "response", "createAuthor", "new<PERSON><PERSON><PERSON>", "id", "data", "author_id", "quote_count", "$emit", "setTimeout", "navigateBack", "msg", "console", "onMounted", "decodeURIComponent", "wx", "createPage", "MiniProgramPage"], "mappings": "8wBAIA,MAAMA,EAAe,IAAW,qEAG1B,MAAAC,EAAWC,EAAAA,IAAI,CACnBC,KAAM,GACNC,SAAU,GACVC,YAAa,GACbC,WAAY,GACZC,WAAY,GACZC,YAAa,GACbC,OAAQ,KAGJC,EAAeR,EAAAA,KAAI,GACnBS,EAAaT,EAAAA,IAAI,IAGjBU,EAAQC,kBACRC,EAAsBF,EAAMA,EAAMG,OAAS,GAC3CC,EAAoD,WAArCF,EAAoBG,QAAQC,KAC3CC,EAAgBL,EAAoBG,QAAQG,SAAW,GAGvDC,EAAkB,CACtB,KAAM,KAAM,MAAO,MAAO,MAC1B,MAAO,MAAO,MAAO,OAAQ,OAC7B,OAAQ,OAAQ,MAAO,MAAO,MAI1BC,EAAmBC,MAAOC,IAC9B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MAGxBhB,EAAWiB,MAAMb,OAAS,IAC5Bc,EAAGC,MAACC,UAAU,CAAEC,MAAO,mBAAoBC,KAAM,SACjDtB,EAAWiB,MAAQ,IAGjBH,EAAMV,OAAS,IACjBc,EAAGC,MAACC,UAAU,CAAEC,MAAO,oBAAqBC,KAAM,SAC1CR,EAAA,CAACA,EAAM,KAGb,IAAAS,EAAcvB,EAAWiB,MAAMb,OAE7BU,EAAAU,KAAKC,IACTzB,EAAWiB,MAAMS,KAAK,IACjBD,EACHE,OAAQ,YACRC,QAAS,OACV,IAGH,IAAA,IAASC,EAAI,EAAGA,EAAIf,EAAMV,OAAQyB,IAAK,CACrC,MAAMC,EAAmBP,EAAcM,EACnC,IACF,MAAME,QAAYC,EAAAA,WAAWlB,EAAMe,IAC/B,IAAAE,IAAOA,EAAIE,IAKP,MAAA,IAAIC,MAAM,QAJLlC,EAAAiB,MAAMa,GAAkBH,OAAS,UAC5C3B,EAAWiB,MAAMa,GAAkBG,IAAMF,EAAIE,IACpC3C,EAAA2B,MAAMnB,OAASiC,EAAIE,GAI/B,OAAQE,GACInC,EAAAiB,MAAMa,GAAkBH,OAAS,SACjC3B,EAAAiB,MAAMa,GAAkBF,QAAU,OAC7CV,EAAGC,MAACC,UAAU,CAAEC,MAAO,SAAUC,KAAM,QACzC,CACF,GAIIc,EAAsBvB,IAC1Bb,EAAWiB,MAAMoB,OAAOxB,EAAMM,MAAO,GACrC7B,EAAS2B,MAAMnB,OAAS,EAAA,EAIpBwC,EAAiB,KACrBpB,EAAAA,MAAIqB,gBAAgB,CAClBC,SAAU9B,EACV+B,QAAUV,IACRzC,EAAS2B,MAAMxB,SAAWiB,EAAgBqB,EAAIW,SAAQ,GAEzD,EAIGC,EAAa/B,UAEjB,GAAKtB,EAAS2B,MAAMzB,KAAKoD,QAKzB,IAAI7C,EAAakB,MAAjB,CACAlB,EAAakB,OAAQ,EAEjB,IACF,MAAM4B,EAAS,CACbC,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/B1D,KAAMF,EAAS2B,MAAMzB,KAAKoD,OAC1BnD,SAAUH,EAAS2B,MAAMxB,SAASmD,OAClClD,YAAaJ,EAAS2B,MAAMvB,YAAYkD,OACxCjD,WAAYL,EAAS2B,MAAMtB,WAAWiD,OACtChD,WAAYN,EAAS2B,MAAMrB,WAAWgD,OACtC/C,YAAaP,EAAS2B,MAAMpB,YAAY+C,OACxC9C,OAAQR,EAAS2B,MAAMnB,QAGnBqD,QAAiBC,eAAaP,GAEhC,GAAoB,OAApBM,EAASxB,OAAiB,CAI5B,GAHAT,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,YAGjCjB,EAAc,CAChB,MAAMgD,EAAY,CAChBC,GAAIH,EAASI,KAAKC,UAClBhE,KAAMF,EAAS2B,MAAMzB,KACrBC,SAAUH,EAAS2B,MAAMxB,SACzBC,YAAaJ,EAAS2B,MAAMvB,YAC5BI,OAAQR,EAAS2B,MAAMnB,OACvB2D,YAAa,GAEfvC,EAAAA,MAAIwC,MAAM,gBAAiBL,EAC7B,CAEAM,YAAW,KACTzC,EAAGC,MAACyC,cAAY,GACf,IACT,KAAmC,YAApBT,EAASxB,OAClBT,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,iBAEjCF,UAAU,CAAEC,MAAO8B,EAASU,KAAO,OAAQvC,KAAM,QAExD,OAAQa,GACC2B,QAAA3B,MAAM,UAAWA,GACzBjB,EAAGC,MAACC,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC/C,CAAY,QACRvB,EAAakB,OAAQ,CACvB,CA/CwB,OAJtBC,EAAGC,MAACC,UAAU,CAAEC,MAAO,UAAWC,KAAM,QAmD1C,SAIFyC,EAAAA,WAAU,KACJvD,IACOlB,EAAA2B,MAAMzB,KAAOwE,mBAAmBxD,GAC3C,m0BC1JFyD,GAAGC,WAAWC"}