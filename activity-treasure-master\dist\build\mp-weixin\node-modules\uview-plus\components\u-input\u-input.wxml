<view class="{{['u-input', 'data-v-6a3de118', J]}}" style="{{K}}"><view class="u-input__content data-v-6a3de118"><view wx:if="{{a}}" class="u-input__content__prefix-icon data-v-6a3de118"><block wx:if="{{$slots.prefix}}"><slot name="prefix"></slot></block><block wx:else><u-icon wx:if="{{b}}" class="data-v-6a3de118" u-i="6a3de118-0" bind:__l="__l" u-p="{{b}}"></u-icon></block></view><view class="u-input__content__field-wrapper data-v-6a3de118" bindtap="{{D}}"><block wx:if="{{r0}}"><input ref="input-native" class="u-input__content__field-wrapper__field data-v-6a3de118" style="{{c}}" type="{{d}}" focus="{{e}}" cursor="{{f}}" value="{{g}}" auto-blur="{{h}}" disabled="{{i}}" maxlength="{{j}}" placeholder="{{k}}" placeholder-style="{{l}}" placeholder-class="{{m}}" confirm-type="{{n}}" confirm-hold="{{o}}" hold-keyboard="{{p}}" cursor-spacing="{{q}}" adjust-position="{{r}}" selection-end="{{s}}" selection-start="{{t}}" password="{{v}}" ignoreCompositionEvent="{{w}}" bindinput="{{x}}" bindblur="{{y}}" bindfocus="{{z}}" bindconfirm="{{A}}" bindkeyboardheightchange="{{B}}" bindnicknamereview="{{C}}"/></block></view><view wx:if="{{E}}" class="u-input__content__clear data-v-6a3de118" bindtap="{{G}}"><u-icon wx:if="{{F}}" class="data-v-6a3de118" u-i="6a3de118-1" bind:__l="__l" u-p="{{F}}"></u-icon></view><view wx:if="{{H}}" class="u-input__content__subfix-icon data-v-6a3de118"><block wx:if="{{$slots.suffix}}"><slot name="suffix"></slot></block><block wx:else><u-icon wx:if="{{I}}" class="data-v-6a3de118" u-i="6a3de118-2" bind:__l="__l" u-p="{{I}}"></u-icon></block></view></view></view>