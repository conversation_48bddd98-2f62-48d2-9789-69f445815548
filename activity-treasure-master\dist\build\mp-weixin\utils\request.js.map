{"version": 3, "file": "request.js", "sources": ["../../../../src/utils/request.js"], "sourcesContent": ["import { BaseUrl, gaodeKey } from \"./BaseUrl\";\nimport { store } from \"@/store\";\nimport { navto } from \"@/utils\";\nimport { isValidAuth } from \"@/utils/auth\";\nimport cacheManager, { CACHE_TYPES, setCache, getCache } from \"./cacheManager\";\n\n// 请求去重管理\nconst pendingRequests = new Map();\nconst pendingRequestsTime = new Map(); // 跟踪请求开始时间\n\n// 生成请求缓存键\n// 修复：优化缓存键生成，避免不同用户/业务场景的缓存冲突\nconst generateCacheKey = (url, data) => {\n  const cleanData = data || {};\n\n  // 提取关键参数用于缓存键生成，确保不同查询条件生成不同的键\n  const keyParams = {\n    uid: cleanData.uid || 'guest', // 改为guest，避免与实际API参数混淆\n    list_type: cleanData.list_type,\n    category: cleanData.category,\n    user_id: cleanData.user_id,\n    type_id: cleanData.type_id,\n    shi_id: cleanData.shi_id,\n    qu_id: cleanData.qu_id,\n    sort: cleanData.sort,\n    keyword: cleanData.keyword,\n    page: cleanData.page,\n    page_size: cleanData.page_size,\n    date: cleanData.date,\n    huodong_date: cleanData.huodong_date,\n    baoming_date: cleanData.baoming_date,\n    is_tuijian: cleanData.is_tuijian,\n    baoming_status: cleanData.baoming_status,\n    huodong_status: cleanData.huodong_status,\n    // 添加时间戳确保不同时间的请求有不同的键（分钟级精度）\n    time_key: Math.floor(Date.now() / (1000 * 60))\n  };\n\n  // 移除undefined值，只保留有效参数\n  const validParams = Object.fromEntries(\n    Object.entries(keyParams).filter(([_, value]) => value !== undefined)\n  );\n\n  // 生成更精确的缓存键\n  const paramString = Object.keys(validParams).length > 0\n    ? JSON.stringify(validParams)\n    : 'default';\n\n  return `api_${url}_${paramString}`;\n};\n\n// 智能缓存配置 - 只缓存静态资源，移除动态数据缓存\nconst cacheConfig = {\n  // 移除活动列表缓存，确保数据实时性\n  // \"huodong/get_list\": { ... },\n\n  // 保留静态资源缓存\n  \"lunbotu/index\": {\n    ttl: 30 * 60 * 1000,\n    type: CACHE_TYPES.STORAGE,\n    priority: 3\n  },\n  \"config/app\": {\n    ttl: 60 * 60 * 1000,\n    type: CACHE_TYPES.STORAGE,\n    priority: 3\n  },\n\n  // 移除活动分类缓存，确保分类数据实时性\n  // \"huodong/get_type\": { ... },\n\n  // 移除World模块缓存，确保内容实时性\n  // \"world/get_cards\": { ... },\n  // \"world/get_feeds\": { ... }\n};\n\n// 定义公开API列表 - 这些API无需登录也可访问\nconst publicApis = [\n  \"huodong/get_list\",\n  \"lunbotu/index\",\n  \"config/app\",\n  \"config/get_china\",\n  \"config/pop\",\n  \"user/login\",\n  \"huodong/get_type\",\n  // \"huodong/get_info\", // 移除：活动详情需要真实认证以获取用户状态\n  \"huodong/get_pingjia\", // 添加活动评价API\n  \"huodong/get_baoming_list_public\", // 添加公开报名列表API\n  \"config/get_deep_link\", // 添加深链接API\n  \"world/get_cards\",  // 日卡列表公开访问\n  \"world/get_card_detail\", // 日卡详情可公开访问\n  \"world/get_feeds\",  // 动态列表可公开访问\n  \"world/get_feed_detail\", // 动态详情可公开访问\n  \"user/get_trial_info\", // 获取体验会员分享信息可公开访问\n\n];\n\n// 需要登录但不自动跳转登录页的API - 这些API会返回relogin状态，但让页面自行决定如何提示用户\nconst authApisWithoutRedirect = [\n  \"world/get_daily_cards\", // 获取日期范围内的日卡\n  \"user/get_other_user_info\", // 获取他人信息\n  \"user/get_guanzhu_list\", // 获取关注列表\n  \"user/get_fans_list\", // 获取粉丝列表\n  \"user/guanzhu_check\", // 检查是否关注\n  \"userguanzhu_check\", // 添加用户关注检查API\n  \"user/claim_trial_member\", // 领取体验会员\n  \"user/get_share_records\", // 获取分享记录\n  \"user/get_unread_count\", // 获取未读通知数量\n  \"user/get_daijiesuan_status\" // 获取账户待结算状态\n];\n\n// 需要登录并自动跳转登录页的API - 这些是核心操作类API\nconst authApisWithRedirect = [\n  \"huodong/add_huodong\", // 发布活动\n  \"huodong/add_baoming\", // 活动报名\n  \"huodong/cancel_baoming\", // 取消报名\n  \"huodong/shoucang_add\", // 收藏活动\n  \"huodong/shoucang_del\", // 取消收藏\n  \"huodong/zan_add\", // 点赞活动\n  \"huodong/zan_del\", // 取消点赞\n  \"huodong/add_pingjia\", // 添加评价\n  \"world/publish_feed\", // 发布动态\n  \"world/publish_card\", // 发布日卡\n  \"world/like_feed\", // 点赞动态\n  \"world/like_card\", // 点赞日卡\n  \"world/favorite_feed\", // 收藏动态\n  \"world/favorite_card\", // 收藏日卡\n  \"world/comment_feed\", // 评论动态\n  \"world/comment_card\", // 评论日卡\n  \"world/like_comment\", // 点赞评论\n  \"world/create_quote\", // 创建摘录\n  \"world/get_quotes\", // 获取摘录列表\n  \"world/delete_feed\", // 删除动态\n  \"world/delete_comment\", // 删除评论\n  \"user/update\", // 更新用户信息\n  \"user/guanzhu_add\", // 添加关注\n  \"user/guanzhu_del\", // 取消关注\n  \"user/create_trial_share\" // 创建体验会员分享链接（需要管理员权限）\n];\n\n// 判断API是否公开\nconst isPublicApi = (url) => {\n  return publicApis.some(api => url.includes(api));\n};\n\n// 判断API是否需要登录但不自动跳转\nconst isAuthApiWithoutRedirect = (url) => {\n  return authApisWithoutRedirect.some(api => url.includes(api));\n};\n\n// 判断API是否需要登录并自动跳转\nconst isAuthApiWithRedirect = (url) => {\n  return authApisWithRedirect.some(api => url.includes(api));\n};\n\nuni.addInterceptor(\"request\", {\n  invoke: (invoke) => {\n    // 排除特定URL\n    if (\n      invoke.url != \"https://api.linqingkeji.com/user/update\" &&\n      invoke.url != \"https://api.linqingkeji.com/user/update_mobile\"\n    ) {\n      if (invoke.url.indexOf(BaseUrl) != -1 && invoke.method === \"POST\") {\n        // 判断是否是内部API请求\n        const apiPath = invoke.url.replace(BaseUrl, '');\n        const isPublicRequest = isPublicApi(apiPath);\n        const isAuthWithoutRedirect = isAuthApiWithoutRedirect(apiPath);\n        const isAuthWithRedirect = isAuthApiWithRedirect(apiPath);\n\n\n\n        // 修复：优先检查是否为公开请求，公开请求始终使用默认认证信息\n        if (isPublicRequest) {\n          console.log('公开API请求，使用默认token和uid', {\n            apiPath,\n            url: invoke.url,\n            userLoggedIn: isValidAuth()\n          });\n          invoke.data.token = \"00000000000000000000000000000000\";\n          invoke.data.uid = \"1\";\n          // 标记为公开请求，用于响应拦截器处理\n          invoke._isPublicRequest = true;\n        }\n        // 如果不是公开请求且用户已登录，添加真实的token和uid\n        else if (isValidAuth()) {\n          console.log('用户已登录，添加真实token和uid', {\n            apiPath,\n            uid: store().$state.userInfo.uid,\n            tokenLength: store().$state.userInfo.token.length,\n            isPublicRequest,\n            isAuthWithoutRedirect,\n            isAuthWithRedirect\n          });\n          invoke.data.token = store().$state.userInfo.token;\n          invoke.data.uid = store().$state.userInfo.uid;\n        }\n        // 如果用户未登录且不是公开请求\n        else {\n          // 对于需要登录的API请求，标记登录需求类型\n          if (!invoke.url.includes(\"user/login\")) {\n            // 标记是否需要自动跳转登录页\n            invoke._requiresAuth = true;\n            invoke._requiresRedirect = isAuthWithRedirect;\n          }\n        }\n      }\n    }\n  }\n});\n\nuni.addInterceptor(\"navigateTo\", {\n  invoke: (invoke) => {\n    if (uni.$u.page() != invoke.url) return true;\n    else return false;\n  }\n});\n\n/**\n @@param {request} request 封装请求\n */\nconst requset = {\n  request(options) {\n    return new Promise((resolve, reject) => {\n      // 确保options.data存在\n      if (!options.data) {\n        options.data = {};\n      }\n\n      // 生成缓存键\n      const cacheKey = generateCacheKey(options.url, options.data);\n\n      // 极简防抖机制：只阻止200ms内的完全相同请求，避免误杀正常请求\n      if (pendingRequests.has(cacheKey)) {\n        const requestStartTime = pendingRequestsTime.get(cacheKey) || Date.now();\n        const elapsedTime = Date.now() - requestStartTime;\n\n        // 只在200ms内阻止重复请求，确保用户操作响应及时\n        if (elapsedTime < 200) {\n          console.log(`防抖阻止重复请求 [${cacheKey}]，已等待 ${elapsedTime}ms`);\n          return pendingRequests.get(cacheKey);\n        } else {\n          // 超过200ms，清理旧请求，允许新请求\n          console.log(`清理旧请求 [${cacheKey}]，允许新请求`);\n          pendingRequests.delete(cacheKey);\n          pendingRequestsTime.delete(cacheKey);\n        }\n      }\n\n      // 暂时禁用智能缓存检查，确保动态数据实时性\n      // const config = cacheConfig[options.url];\n      // if (config) {\n      //   const cached = getCache(cacheKey, { type: config.type });\n      //   if (cached) {\n      //     return Promise.resolve(cached);\n      //   }\n      // }\n\n      // 添加请求超时处理\n      let isTimeout = false;\n      let timer = setTimeout(() => {\n        isTimeout = true;\n        pendingRequests.delete(cacheKey);\n        pendingRequestsTime.delete(cacheKey);\n        console.error(`请求超时 [${cacheKey}]，已清理缓存`);\n        reject(new Error('请求超时'));\n      }, 15000); // 15秒超时\n\n\n\n      // 如果是需要授权的API且用户未登录，则直接返回需要登录的错误\n      if (options._requiresAuth && (!store().$state.userInfo?.token || !store().$state.userInfo?.uid)) {\n        clearTimeout(timer);\n        const requiresAuthError = {\n          status: \"relogin\",\n          msg: \"请先登录后再操作\"\n        };\n        // 对于需要登录鉴权的请求操作，会返回relogin，但不会自动跳转\n        resolve(requiresAuthError);\n        return;\n      }\n\n      // 特定API强制使用form-urlencoded格式\n      const forceFormUrlencodedApis = [\n        \"huodong/add_huodong\",\n        \"huodong/update_huodong\"\n      ];\n\n      const shouldForceFormUrlencoded = forceFormUrlencodedApis.some(api => options.url.includes(api));\n\n      // 检查是否包含数组参数，如果有则使用JSON格式（除非强制使用form-urlencoded）\n      const hasArrayParam = options.data && Object.values(options.data).some(value => Array.isArray(value));\n      const contentType = shouldForceFormUrlencoded\n        ? \"application/x-www-form-urlencoded\"\n        : (hasArrayParam ? \"application/json\" : \"application/x-www-form-urlencoded\");\n\n\n\n      // 确保请求URL不为空\n      if (!options.url) {\n        clearTimeout(timer);\n\n        reject(new Error('请求URL为空'));\n        return;\n      }\n\n      // 记录请求开始时间\n      pendingRequestsTime.set(cacheKey, Date.now());\n      console.log(`开始新请求 [${cacheKey}]，当前时间: ${new Date().toISOString()}`);\n\n      // 将请求Promise加入待处理队列\n      pendingRequests.set(cacheKey, new Promise((requestResolve, requestReject) => {\n        uni.request({\n          url: BaseUrl + options.url,\n          method: options.method || 'GET',\n          data: options.data,\n          header: {\n            \"content-type\": contentType\n          },\n          success: async (res) => {\n            if (isTimeout) return; // 如果已超时，不处理响应\n            clearTimeout(timer); // 清除超时定时器\n            pendingRequests.delete(cacheKey); // 从待处理队列中移除\n            pendingRequestsTime.delete(cacheKey); // 清除时间戳记录\n\n            const elapsedTime = Date.now() - (pendingRequestsTime.get(cacheKey) || Date.now());\n            console.log(`请求成功 [${cacheKey}]，耗时: ${elapsedTime}ms`);\n\n          // 检查HTTP状态码\n          if (res.statusCode !== 200) {\n            console.error(`HTTP错误: ${res.statusCode}`);\n\n            // 记录错误到控制台\n              if (options.url && (options.url.includes('comment_card') || options.url.includes('comment_feed') ||\n                  options.url.includes('like_card') || options.url.includes('like_feed'))) {\n              console.error('API调用失败:', {\n                url: options.url,\n                statusCode: res.statusCode,\n                data: options.data\n              });\n            }\n\n            return reject(new Error(`HTTP错误: ${res.statusCode}`));\n          }\n\n          // 检查响应数据\n          if (!res.data) {\n            console.error('响应数据为空');\n            return reject(new Error('响应数据为空'));\n          }\n\n          const { status, msg } = res.data;\n\n          if (status === \"relogin\") {\n            // 获取API路径用于调试\n            const apiPath = options.url.replace(BaseUrl, '');\n            const isPublicRequest = isPublicApi(apiPath);\n            const isAuthWithoutRedirect = isAuthApiWithoutRedirect(apiPath);\n            const isAuthWithRedirect = isAuthApiWithRedirect(apiPath);\n            const userInfo = store().$state.userInfo;\n\n            console.log('API返回relogin状态，详细信息:', {\n              apiPath,\n              isPublicRequest,\n              isAuthWithoutRedirect,\n              isAuthWithRedirect,\n              hasUserInfo: !!(userInfo && userInfo.uid && userInfo.token),\n              uid: userInfo?.uid,\n              tokenLength: userInfo?.token?.length\n            });\n\n            // 特殊处理：如果是公开API返回relogin，可能是后端逻辑问题\n            if (options._isPublicRequest || isPublicRequest) {\n              console.warn('公开API返回relogin状态，这可能是后端逻辑问题', {\n                apiPath,\n                isPublicRequest,\n                hasDefaultToken: options.data?.token === \"00000000000000000000000000000000\"\n              });\n\n              // 对于公开API，如果返回relogin但使用的是默认token，直接返回数据\n              if (options.data?.token === \"00000000000000000000000000000000\") {\n                console.log('公开API使用默认token，忽略relogin状态');\n                console.log('原始响应数据:', res.data);\n\n                // 修复：如果响应是字符串，尝试提取JSON\n                let responseData = res.data;\n                if (typeof responseData === 'string') {\n                  console.log('检测到字符串响应，提取JSON...');\n                  try {\n                    const jsonStart = responseData.indexOf('{\"status\"');\n                    if (jsonStart !== -1) {\n                      const jsonStr = responseData.substring(jsonStart);\n                      responseData = JSON.parse(jsonStr);\n                      console.log('JSON解析成功，返回解析后的数据:', responseData);\n                    }\n                  } catch (parseError) {\n                    console.error('JSON解析失败:', parseError);\n                  }\n                }\n\n                return resolve(responseData);\n              }\n            }\n\n            // 如果用户确实已登录但API返回relogin，可能是token过期或其他问题\n            if (userInfo && userInfo.uid && userInfo.token) {\n              console.warn('用户已登录但API返回relogin，可能是token过期或权限问题');\n\n              // 对于不需要跳转的API，直接返回状态让页面处理\n              if (isAuthWithoutRedirect) {\n                console.log('API不需要自动跳转，返回relogin状态供页面处理');\n                return resolve(res.data);\n              }\n            }\n\n            console.log('需要重新登录');\n\n            // 修改登录跳转逻辑，仅对标记了需要跳转的API执行跳转\n            if (options._requiresRedirect || isAuthWithRedirect) {\n              // 对特定操作跳转登录页\n              if (!store().flag) {\n                store().flag = true;\n                console.log('跳转到登录页');\n                navto(\"/pages/bundle/common/login\");\n                setTimeout(() => (store().flag = false), 1500);\n              }\n            }\n            // 对于其他API，只返回需要登录的状态，由页面自行决定是否显示登录提示\n            return resolve(res.data);\n          } else if (status === \"empty\") {\n            console.log('服务器返回空数据');\n            return resolve(\"n\");\n          } else if (status === \"error\") {\n            console.error('服务器返回错误:', msg);\n\n            // 特殊处理缺少uid参数的错误\n            if (msg && msg.includes('Missing parameter [ uid ]')) {\n              console.log('检测到缺少uid参数，可能是用户未登录');\n              // 返回特殊的登录状态，让调用方处理\n              return resolve({\n                status: \"relogin\",\n                msg: \"请先登录后再操作\"\n              });\n            }\n\n            return resolve(res.data); // 返回原始响应，让调用方处理\n          }\n\n          // 暂时禁用智能缓存存储，确保数据实时性\n          // const config = cacheConfig[options.url];\n          // if (config && res.data.status === 'ok') {\n          //   setCache(cacheKey, res.data, {\n          //     ttl: config.ttl,\n          //     type: config.type,\n          //     priority: config.priority\n          //   });\n          // }\n\n          requestResolve(res.data);\n          resolve(res.data);\n        },\n        fail: (err) => {\n          if (isTimeout) return; // 如果已超时，不处理错误\n          clearTimeout(timer); // 清除超时定时器\n          pendingRequests.delete(cacheKey); // 从待处理队列中移除\n          pendingRequestsTime.delete(cacheKey); // 清除时间戳记录\n\n          console.error(`请求失败 [${cacheKey}]:`, err);\n          requestReject(err);\n          reject(err);\n        },\n        complete: () => {\n          // 可以在这里添加通用的完成处理逻辑\n        }\n      });\n      }));\n\n      // 返回待处理队列中的Promise\n      return pendingRequests.get(cacheKey);\n    });\n  },\n  getCoordinate(e) {\n    return new Promise((rs, rj) => {\n      uni.request({\n        url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${e.locations}&coordsys=gps&output=JSON&key=${gaodeKey}`,\n        success: (res) => rs(res.data),\n        fail: (error) => rj(error)\n      });\n    });\n  },\n  getAddr(e) {\n    return new Promise((rs, rj) => {\n      uni.request({\n        url: `https://restapi.amap.com/v3/geocode/regeo?output=JSON&location=${\n          e.location\n        }&key=${gaodeKey}&extensions=${e.extensions ? e.extensions : \"base\"}`,\n        success: (res) => rs(res.data),\n        fail: (error) => rj(error)\n      });\n    });\n  },\n  // 获取二进制\n  getArrayBuffer(options) {\n    return new Promise((resolve, reject) => {\n      // 检查是否包含数组参数，如果有则使用JSON格式\n      const hasArrayParam = Object.values(options.data).some(value => Array.isArray(value));\n      const contentType = hasArrayParam\n        ? \"application/json\"\n        : \"application/x-www-form-urlencoded\";\n\n      console.log(`ArrayBuffer请求内容类型: ${contentType}, 是否包含数组: ${hasArrayParam}`);\n\n      uni.request({\n        url: BaseUrl + options.url,\n        method: options.method,\n        data: options.data,\n        header: {\n          \"content-type\": contentType\n        },\n        responseType: \"arraybuffer\",\n        success: (res) => {\n          const { status, msg } = res.data;\n          if (status === \"relogin\") {\n            uni.$u.toast(\"请重新登录\");\n            uni.$u.debounce(navto(\"/pages/bundle/common/login\"), 1000);\n          } else if (status === \"error\") {\n            uni.$u.toast(msg);\n          } else if (status === \"empty\") {\n            uni.$u.toast(\"暂无数据\");\n            return resolve(\"n\");\n          } else {\n            msg ? uni.$u.toast(msg) : \"\";\n          }\n          resolve(res.data);\n        },\n        fail: (err) => {\n          reject(err);\n        }\n      });\n    });\n  },\n  upload(options) {\n    return new Promise((resolve, reject) => {\n      // 添加请求超时处理\n      let isTimeout = false;\n      let timer = setTimeout(() => {\n        isTimeout = true;\n        console.error('上传请求超时:', options.url);\n        reject(new Error('上传请求超时'));\n      }, 30000); // 30秒超时（上传可能需要更长时间）\n\n      console.log(`发起上传请求: ${options.url}, 文件路径: ${options.data}`);\n\n      // 确保formData包含用户信息\n      const formData = options.formData || {};\n      if (store().$state.userInfo?.uid) {\n        formData.uid = store().$state.userInfo.uid;\n      }\n      if (store().$state.userInfo?.token) {\n        formData.token = store().$state.userInfo.token;\n      }\n\n      uni.uploadFile({\n        url: BaseUrl + options.url,\n        filePath: options.data,\n        name: options.name,\n        formData: formData,\n        success: (res) => {\n          if (isTimeout) return; // 如果已超时，不处理响应\n          clearTimeout(timer); // 清除超时定时器\n\n          console.log(`上传成功: ${options.url}, 状态码: ${res.statusCode}, 响应:`, res.data);\n\n          // 检查HTTP状态码\n          if (res.statusCode !== 200) {\n            console.error(`上传HTTP错误: ${res.statusCode}`);\n            return reject(new Error(`上传HTTP错误: ${res.statusCode}`));\n          }\n\n          try {\n            const parsedData = JSON.parse(res.data);\n            const { status, msg } = parsedData;\n\n            if (status === \"relogin\") {\n              console.log('上传需要重新登录');\n              uni.showToast({ title: \"请先登录\", icon: \"none\" });\n              setTimeout(() => navto(\"/pages/bundle/common/login\"), 1000);\n              return resolve(parsedData);\n            } else if (status === \"error\") {\n              console.error('上传服务器返回错误:', msg);\n              uni.showToast({ title: msg || \"上传失败\", icon: \"none\" });\n              return resolve(parsedData);\n            } else if (status === \"empty\") {\n              console.log('上传服务器返回空数据');\n              uni.showToast({ title: \"暂无数据\", icon: \"none\" });\n              return resolve(\"n\");\n            }\n\n            resolve(parsedData);\n          } catch (error) {\n            console.error('解析上传响应失败:', error);\n            reject(new Error('解析上传响应失败'));\n          }\n        },\n        fail: (err) => {\n          if (isTimeout) return; // 如果已超时，不处理错误\n          clearTimeout(timer); // 清除超时定时器\n\n          console.error(`上传失败: ${options.url}`, err);\n          reject(err);\n        }\n      });\n    });\n  },\n  u(url, data, name, options) {\n    data = data ? data : {};\n    if (!options) {\n      options = {};\n    }\n    options.name = name;\n    options.url = url;\n    options.data = data;\n    // 使用传入的 formData，如果没有则使用空对象\n    options.formData = options.formData || {};\n    return this.upload(options);\n  },\n  aB(url, data, method, options) {\n    data = data ? data : {};\n    if (!options) {\n      options = {};\n    }\n    options.url = url;\n    options.data = data;\n    options.method = method ? method : \"POST\";\n    return this.getArrayBuffer(options);\n  },\n  g(url, data, options) {\n    if (!options) {\n      options = {};\n    }\n    options.url = url;\n    options.data = data;\n    options.method = \"GET\";\n    return this.request(options);\n  },\n  p(url, data, options) {\n    data = data ? data : {};\n    if (!options) {\n      options = {};\n    }\n    options.url = url;\n    options.data = data;\n    options.method = \"POST\";\n    return this.request(options);\n  },\n  put(url, data, options) {\n    data = data ? data : {};\n    if (!options) {\n      options = {};\n    }\n    options.url = url;\n    options.data = data;\n    options.method = \"PUT\";\n    return this.request(options);\n  },\n  delete(url, data, options) {\n    data = data ? data : {};\n    // data.token = store().$state.token;\n    // data.uid = store().$state.uid;\n    if (!options) {\n      options = {};\n    }\n    options.url = url;\n    options.data = data;\n    options.method = \"DELETE\";\n    return this.request(options);\n  }\n};\nexport default requset;\n"], "names": ["pendingRequests", "Map", "pendingRequestsTime", "publicApis", "authApisWithoutRedirect", "authApisWithRedirect", "isPublicApi", "url", "some", "api", "includes", "isAuthApiWithoutRedirect", "isAuthApiWithRedirect", "uni", "addInterceptor", "invoke", "indexOf", "BaseUrl", "method", "<PERSON><PERSON><PERSON><PERSON>", "replace", "isPublicRequest", "isAuthWithoutRedirect", "isAuthWithRedirect", "console", "log", "userLoggedIn", "isValidAuth", "data", "token", "uid", "_isPublicRequest", "store", "$state", "userInfo", "token<PERSON><PERSON>th", "length", "_requiresAuth", "_requiresRedirect", "$u", "page", "requset", "request", "options", "Promise", "resolve", "reject", "cache<PERSON>ey", "cleanData", "keyParams", "list_type", "category", "user_id", "type_id", "shi_id", "qu_id", "sort", "keyword", "page_size", "date", "huodong_date", "baoming_date", "is_tuijian", "baoming_status", "huodong_status", "time_key", "Math", "floor", "Date", "now", "validParams", "Object", "fromEntries", "entries", "filter", "_", "value", "keys", "JSON", "stringify", "generate<PERSON>ache<PERSON>ey", "has", "requestStartTime", "get", "elapsedTime", "delete", "isTimeout", "timer", "setTimeout", "error", "Error", "store_index", "_a", "clearTimeout", "status", "msg", "shouldForceFormUrlencoded", "hasArrayParam", "values", "Array", "isArray", "contentType", "set", "toISOString", "requestResolve", "requestReject", "header", "success", "async", "res", "statusCode", "hasUserInfo", "warn", "hasDefaultToken", "_b", "_c", "responseData", "jsonStart", "jsonStr", "substring", "parse", "parseError", "flag", "navto", "fail", "err", "complete", "getCoordinate", "e", "rs", "rj", "locations", "gaode<PERSON><PERSON>", "getAddr", "location", "extensions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType", "toast", "index", "debounce", "upload", "formData", "uploadFile", "filePath", "name", "parsedData", "showToast", "title", "icon", "u", "this", "aB", "g", "p", "put"], "mappings": "yLAOA,MAAMA,MAAsBC,IACtBC,MAA0BD,IAqE1BE,EAAa,CACjB,mBACA,gBACA,aACA,mBACA,aACA,aACA,mBAEA,sBACA,kCACA,uBACA,kBACA,wBACA,kBACA,wBACA,uBAKIC,EAA0B,CAC9B,wBACA,2BACA,wBACA,qBACA,qBACA,oBACA,0BACA,yBACA,wBACA,8BAIIC,EAAuB,CAC3B,sBACA,sBACA,yBACA,uBACA,uBACA,kBACA,kBACA,sBACA,qBACA,qBACA,kBACA,kBACA,sBACA,sBACA,qBACA,qBACA,qBACA,qBACA,mBACA,oBACA,uBACA,cACA,mBACA,mBACA,2BAIIC,EAAeC,GACZJ,EAAWK,MAAKC,GAAOF,EAAIG,SAASD,KAIvCE,EAA4BJ,GACzBH,EAAwBI,MAAKC,GAAOF,EAAIG,SAASD,KAIpDG,EAAyBL,GACtBF,EAAqBG,MAAKC,GAAOF,EAAIG,SAASD,KAGvDI,EAAAA,MAAIC,eAAe,UAAW,CAC5BC,OAASA,IAEP,GACgB,2CAAdA,EAAOR,KACO,kDAAdQ,EAAOR,MAE4B,GAA/BQ,EAAOR,IAAIS,QAAQC,EAAOA,UAA6B,SAAlBF,EAAOG,OAAmB,CAEjE,MAAMC,EAAUJ,EAAOR,IAAIa,QAAQH,EAAAA,QAAS,IACtCI,EAAkBf,EAAYa,GAC9BG,EAAwBX,EAAyBQ,GACjDI,EAAqBX,EAAsBO,GAK7CE,GACFG,QAAQC,IAAI,wBAAyB,CACnCN,UACAZ,IAAKQ,EAAOR,IACZmB,aAAcC,EAAAA,gBAEhBZ,EAAOa,KAAKC,MAAQ,mCACpBd,EAAOa,KAAKE,IAAM,IAElBf,EAAOgB,kBAAmB,GAGnBJ,EAAWA,eAClBH,QAAQC,IAAI,sBAAuB,CACjCN,UACAW,IAAKE,EAAKA,QAAGC,OAAOC,SAASJ,IAC7BK,YAAaH,EAAAA,QAAQC,OAAOC,SAASL,MAAMO,OAC3Cf,kBACAC,wBACAC,uBAEFR,EAAOa,KAAKC,MAAQG,EAAAA,QAAQC,OAAOC,SAASL,MAC5Cd,EAAOa,KAAKE,IAAME,EAAAA,QAAQC,OAAOC,SAASJ,KAKrCf,EAAOR,IAAIG,SAAS,gBAEvBK,EAAOsB,eAAgB,EACvBtB,EAAOuB,kBAAoBf,EAGjC,CACF,IAIJV,EAAAA,MAAIC,eAAe,aAAc,CAC/BC,OAASA,GACHF,EAAAA,MAAI0B,GAAGC,QAAUzB,EAAOR,MAQ3B,MAACkC,EAAU,CACdC,QAAQC,GACC,IAAIC,SAAQ,CAACC,EAASC,aAEtBH,EAAQf,OACXe,EAAQf,KAAO,IAIjB,MAAMmB,EAzNa,EAACxC,EAAKqB,KACvB,MAAAoB,EAAYpB,GAAQ,GAGpBqB,EAAY,CAChBnB,IAAKkB,EAAUlB,KAAO,QACtBoB,UAAWF,EAAUE,UACrBC,SAAUH,EAAUG,SACpBC,QAASJ,EAAUI,QACnBC,QAASL,EAAUK,QACnBC,OAAQN,EAAUM,OAClBC,MAAOP,EAAUO,MACjBC,KAAMR,EAAUQ,KAChBC,QAAST,EAAUS,QACnBjB,KAAMQ,EAAUR,KAChBkB,UAAWV,EAAUU,UACrBC,KAAMX,EAAUW,KAChBC,aAAcZ,EAAUY,aACxBC,aAAcb,EAAUa,aACxBC,WAAYd,EAAUc,WACtBC,eAAgBf,EAAUe,eAC1BC,eAAgBhB,EAAUgB,eAE1BC,SAAUC,KAAKC,MAAMC,KAAKC,YAItBC,EAAcC,OAAOC,YACzBD,OAAOE,QAAQxB,GAAWyB,QAAO,EAAEC,EAAGC,UAAqB,IAAVA,KAQnD,MAAO,OAAOrE,KAJMgE,OAAOM,KAAKP,GAAalC,OAAS,EAClD0C,KAAKC,UAAUT,GACf,WAEiB,EAqLAU,CAAiBrC,EAAQpC,IAAKoC,EAAQf,MAGnD,GAAA5B,EAAgBiF,IAAIlC,GAAW,CACjC,MAAMmC,EAAmBhF,EAAoBiF,IAAIpC,IAAaqB,KAAKC,MAC7De,EAAchB,KAAKC,MAAQa,EAGjC,GAAIE,EAAc,IAET,OADC5D,QAAAC,IAAI,aAAasB,UAAiBqC,OACnCpF,EAAgBmF,IAAIpC,GAGnBvB,QAAAC,IAAI,UAAUsB,YACtB/C,EAAgBqF,OAAOtC,GACvB7C,EAAoBmF,OAAOtC,EAE/B,CAYA,IAAIuC,GAAY,EACZC,EAAQC,YAAW,KACTF,GAAA,EACZtF,EAAgBqF,OAAOtC,GACvB7C,EAAoBmF,OAAOtC,GACnBvB,QAAAiE,MAAM,SAAS1C,YAChBD,EAAA,IAAI4C,MAAM,QAAO,GACvB,MAKH,GAAI/C,EAAQN,kBAAmBL,OAAAA,EAAK2D,EAAA3D,QAAGC,OAAOC,eAAU,EAAA0D,EAAA/D,UAAUG,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUJ,MAAM,CAC/F+D,aAAaN,GAOb,YADA1C,EAL0B,CACxBiD,OAAQ,UACRC,IAAK,YAKT,CAGA,MAKMC,EAL0B,CAC9B,sBACA,0BAGwDxF,MAAKC,GAAOkC,EAAQpC,IAAIG,SAASD,KAGrFwF,EAAgBtD,EAAQf,MAAQ2C,OAAO2B,OAAOvD,EAAQf,MAAMpB,MAAKoE,GAASuB,MAAMC,QAAQxB,KACxFyB,EAAcL,EAChB,oCACCC,EAAgB,mBAAqB,oCAKtC,OAACtD,EAAQpC,KAQbL,EAAoBoG,IAAIvD,EAAUqB,KAAKC,OACvC7C,QAAQC,IAAI,UAAUsB,aAAmB,IAAIqB,MAAOmC,iBAGpDvG,EAAgBsG,IAAIvD,EAAU,IAAIH,SAAQ,CAAC4D,EAAgBC,KACzD5F,EAAAA,MAAI6B,QAAQ,CACVnC,IAAKU,EAAAA,QAAU0B,EAAQpC,IACvBW,OAAQyB,EAAQzB,QAAU,MAC1BU,KAAMe,EAAQf,KACd8E,OAAQ,CACN,eAAgBL,GAElBM,QAASC,MAAOC,cACV,GAAAvB,EAAW,OACfO,aAAaN,GACbvF,EAAgBqF,OAAOtC,GACvB7C,EAAoBmF,OAAOtC,GAErB,MAAAqC,EAAchB,KAAKC,OAASnE,EAAoBiF,IAAIpC,IAAaqB,KAAKC,OAI1E,GAHM7C,QAAAC,IAAI,SAASsB,UAAiBqC,OAGjB,MAAnByB,EAAIC,WAaN,OAZQtF,QAAAiE,MAAM,WAAWoB,EAAIC,cAGvBnE,EAAQpC,MAAQoC,EAAQpC,IAAIG,SAAS,iBAAmBiC,EAAQpC,IAAIG,SAAS,iBAC7EiC,EAAQpC,IAAIG,SAAS,cAAgBiC,EAAQpC,IAAIG,SAAS,eAC9Dc,QAAQiE,MAAM,WAAY,CACxBlF,IAAKoC,EAAQpC,IACbuG,WAAYD,EAAIC,WAChBlF,KAAMe,EAAQf,OAIXkB,EAAO,IAAI4C,MAAM,WAAWmB,EAAIC,eAIrC,IAACD,EAAIjF,KAEP,OADAJ,QAAQiE,MAAM,UACP3C,EAAO,IAAI4C,MAAM,WAG1B,MAAMI,OAAEA,EAAAC,IAAQA,GAAQc,EAAIjF,KAE5B,GAAe,YAAXkE,EAAsB,CAExB,MAAM3E,EAAUwB,EAAQpC,IAAIa,QAAQH,EAAAA,QAAS,IACvCI,EAAkBf,EAAYa,GAC9BG,EAAwBX,EAAyBQ,GACjDI,EAAqBX,EAAsBO,GAC3Ce,EAAWF,EAAAA,QAAQC,OAAOC,SAa5B,GAXJV,QAAQC,IAAI,uBAAwB,CAClCN,UACAE,kBACAC,wBACAC,qBACAwF,eAAgB7E,GAAYA,EAASJ,KAAOI,EAASL,OACrDC,IAAe,MAAVI,OAAU,EAAAA,EAAAJ,IACfK,YAAa,OAAAyD,EAAU,MAAV1D,OAAU,EAAAA,EAAAL,cAAV+D,EAAiBxD,UAI5BO,EAAQZ,kBAAoBV,KAC9BG,QAAQwF,KAAK,8BAA+B,CAC1C7F,UACAE,kBACA4F,gBAAyC,sCAAxB,OAAAC,EAAAvE,EAAQf,WAAR,EAAAsF,EAAcrF,SAIL,sCAAxB,OAAAsF,EAAQxE,EAAAf,WAAM,EAAAuF,EAAAtF,QAA8C,CAC9DL,QAAQC,IAAI,8BACJD,QAAAC,IAAI,UAAWoF,EAAIjF,MAG3B,IAAIwF,EAAeP,EAAIjF,KACnB,GAAwB,iBAAjBwF,EAA2B,CACpC5F,QAAQC,IAAI,sBACR,IACI,MAAA4F,EAAYD,EAAapG,QAAQ,aACvC,IAAsB,IAAlBqG,EAAkB,CACd,MAAAC,EAAUF,EAAaG,UAAUF,GACxBD,EAAAtC,KAAK0C,MAAMF,GAClB9F,QAAAC,IAAI,qBAAsB2F,EACpC,CACD,OAAQK,GACCjG,QAAAiE,MAAM,YAAagC,EAC7B,CACF,CAEA,OAAO5E,EAAQuE,EACjB,CAIF,OAAIlF,GAAYA,EAASJ,KAAOI,EAASL,QACvCL,QAAQwF,KAAK,sCAGT1F,IACFE,QAAQC,IAAI,+BACLoB,EAAQgE,EAAIjF,QAIvBJ,QAAQC,IAAI,WAGRkB,EAAQL,mBAAqBf,KAE1BS,EAAAA,QAAQ0F,iBACHA,MAAO,EACflG,QAAQC,IAAI,UACZkG,EAAKA,MAAC,8BACNnC,YAAW,IAAOxD,EAAAA,QAAQ0F,MAAO,GAAQ,QAItC7E,EAAQgE,EAAIjF,MAC/B,CAAA,MAAgC,UAAXkE,GACTtE,QAAQC,IAAI,YACLoB,EAAQ,MACK,UAAXiD,GACDtE,QAAAiE,MAAM,WAAYM,GAGtBA,GAAOA,EAAIrF,SAAS,8BACtBc,QAAQC,IAAI,uBAELoB,EAAQ,CACbiD,OAAQ,UACRC,IAAK,cAIFlD,EAAQgE,EAAIjF,QAarB4E,EAAeK,EAAIjF,WACnBiB,EAAQgE,EAAIjF,MAAI,EAElBgG,KAAOC,IACDvC,IACJO,aAAaN,GACbvF,EAAgBqF,OAAOtC,GACvB7C,EAAoBmF,OAAOtC,GAEnBvB,QAAAiE,MAAM,SAAS1C,MAAc8E,GACrCpB,EAAcoB,GACd/E,EAAO+E,GAAG,EAEZC,SAAU,QAGX,KAIM9H,EAAgBmF,IAAIpC,KAlLzB8C,aAAaN,QAENzC,EAAA,IAAI4C,MAAM,YAgLgB,IAGvCqC,cAAcC,GACL,IAAIpF,SAAQ,CAACqF,EAAIC,KACtBrH,EAAAA,MAAI6B,QAAQ,CACVnC,IAAK,sEAAsEyH,EAAEG,0CAA0CC,EAAAA,WACvHzB,QAAUE,GAAQoB,EAAGpB,EAAIjF,MACzBgG,KAAOnC,GAAUyC,EAAGzC,IACrB,IAGL4C,QAAQL,GACC,IAAIpF,SAAQ,CAACqF,EAAIC,KACtBrH,EAAAA,MAAI6B,QAAQ,CACVnC,IAAK,kEACHyH,EAAEM,gBACIF,EAAAA,uBAAuBJ,EAAEO,WAAaP,EAAEO,WAAa,SAC7D5B,QAAUE,GAAQoB,EAAGpB,EAAIjF,MACzBgG,KAAOnC,GAAUyC,EAAGzC,IACrB,IAIL+C,eAAe7F,GACN,IAAIC,SAAQ,CAACC,EAASC,KAErB,MAAAmD,EAAgB1B,OAAO2B,OAAOvD,EAAQf,MAAMpB,MAAcoE,GAAAuB,MAAMC,QAAQxB,KACxEyB,EAAcJ,EAChB,mBACA,oCAEIzE,QAAAC,IAAI,sBAAsB4E,cAAwBJ,KAE1DpF,EAAAA,MAAI6B,QAAQ,CACVnC,IAAKU,EAAAA,QAAU0B,EAAQpC,IACvBW,OAAQyB,EAAQzB,OAChBU,KAAMe,EAAQf,KACd8E,OAAQ,CACN,eAAgBL,GAElBoC,aAAc,cACd9B,QAAUE,IACR,MAAMf,OAAEA,EAAAC,IAAQA,GAAQc,EAAIjF,KAC5B,GAAe,YAAXkE,EACFjF,EAAAA,MAAI0B,GAAGmG,MAAM,SACb7H,EAAG8H,MAACpG,GAAGqG,SAASjB,EAAKA,MAAC,8BAA+B,UACjE,GAAgC,UAAX7B,EACTjF,EAAAA,MAAI0B,GAAGmG,MAAM3C,OACzB,IAAgC,UAAXD,EAET,OADAjF,EAAAA,MAAI0B,GAAGmG,MAAM,QACN7F,EAAQ,KAEfkD,GAAMlF,EAAG8H,MAACpG,GAAGmG,MAAM3C,EACrB,CACAlD,EAAQgE,EAAIjF,KAAI,EAElBgG,KAAOC,IACL/E,EAAO+E,EAAG,GAEb,IAGLgB,OAAOlG,GACE,IAAIC,SAAQ,CAACC,EAASC,aAE3B,IAAIwC,GAAY,EACZC,EAAQC,YAAW,KACTF,GAAA,EACJ9D,QAAAiE,MAAM,UAAW9C,EAAQpC,KAC1BuC,EAAA,IAAI4C,MAAM,UAAS,GACzB,KAEHlE,QAAQC,IAAI,WAAWkB,EAAQpC,cAAcoC,EAAQf,QAG/C,MAAAkH,EAAWnG,EAAQmG,UAAY,IACjC9G,OAAAA,YAAQC,OAAOC,mBAAUJ,OAC3BgH,EAAShH,IAAME,EAAKA,QAAGC,OAAOC,SAASJ,MAErCE,OAAAA,YAAQC,OAAOC,mBAAUL,SAC3BiH,EAASjH,MAAQG,EAAKA,QAAGC,OAAOC,SAASL,OAG3ChB,EAAAA,MAAIkI,WAAW,CACbxI,IAAKU,EAAAA,QAAU0B,EAAQpC,IACvByI,SAAUrG,EAAQf,KAClBqH,KAAMtG,EAAQsG,KACdH,WACAnC,QAAUE,IACJ,IAAAvB,EAAA,CAMA,GALJO,aAAaN,GAEb/D,QAAQC,IAAI,SAASkB,EAAQpC,aAAasG,EAAIC,kBAAmBD,EAAIjF,MAG9C,MAAnBiF,EAAIC,WAEN,OADQtF,QAAAiE,MAAM,aAAaoB,EAAIC,cACxBhE,EAAO,IAAI4C,MAAM,aAAamB,EAAIC,eAGvC,IACF,MAAMoC,EAAapE,KAAK0C,MAAMX,EAAIjF,OAC5BkE,OAAEA,EAAQC,IAAAA,GAAQmD,EAExB,GAAe,YAAXpD,EAIF,OAHAtE,QAAQC,IAAI,YACZZ,EAAG8H,MAACQ,UAAU,CAAEC,MAAO,OAAQC,KAAM,SACrC7D,YAAW,IAAMmC,EAAKA,MAAC,+BAA+B,KAC/C9E,EAAQqG,GAC7B,GAAkC,UAAXpD,EAGT,OAFQtE,QAAAiE,MAAM,aAAcM,WACxBoD,UAAU,CAAEC,MAAOrD,GAAO,OAAQsD,KAAM,SACrCxG,EAAQqG,GAC7B,GAAkC,UAAXpD,EAGT,OAFAtE,QAAQC,IAAI,cACZZ,EAAG8H,MAACQ,UAAU,CAAEC,MAAO,OAAQC,KAAM,SAC9BxG,EAAQ,KAGjBA,EAAQqG,EACT,OAAQzD,GACCjE,QAAAiE,MAAM,YAAaA,GACpB3C,EAAA,IAAI4C,MAAM,YACnB,CAlCe,CAkCf,EAEFkC,KAAOC,IACDvC,IACJO,aAAaN,GAEb/D,QAAQiE,MAAM,SAAS9C,EAAQpC,MAAOsH,GACtC/E,EAAO+E,GAAG,GAEb,IAGL,CAAAyB,CAAE/I,EAAKqB,EAAMqH,EAAMtG,GAUV,OATAf,EAAAA,GAAc,GAChBe,IACHA,EAAU,CAAA,GAEZA,EAAQsG,KAAOA,EACftG,EAAQpC,IAAMA,EACdoC,EAAQf,KAAOA,EAEPe,EAAAmG,SAAWnG,EAAQmG,UAAY,CAAA,EAChCS,KAAKV,OAAOlG,EACpB,EACD,EAAA6G,CAAGjJ,EAAKqB,EAAMV,EAAQyB,GAQb,OAPAf,EAAAA,GAAc,GAChBe,IACHA,EAAU,CAAA,GAEZA,EAAQpC,IAAMA,EACdoC,EAAQf,KAAOA,EACPe,EAAAzB,OAASA,GAAkB,OAC5BqI,KAAKf,eAAe7F,EAC5B,EACD,CAAA8G,CAAElJ,EAAKqB,EAAMe,GAOJ,OANFA,IACHA,EAAU,CAAA,GAEZA,EAAQpC,IAAMA,EACdoC,EAAQf,KAAOA,EACfe,EAAQzB,OAAS,MACVqI,KAAK7G,QAAQC,EACrB,EACD,CAAA+G,CAAEnJ,EAAKqB,EAAMe,GAQJ,OAPAf,EAAAA,GAAc,GAChBe,IACHA,EAAU,CAAA,GAEZA,EAAQpC,IAAMA,EACdoC,EAAQf,KAAOA,EACfe,EAAQzB,OAAS,OACVqI,KAAK7G,QAAQC,EACrB,EACD,GAAAgH,CAAIpJ,EAAKqB,EAAMe,GAQN,OAPAf,EAAAA,GAAc,GAChBe,IACHA,EAAU,CAAA,GAEZA,EAAQpC,IAAMA,EACdoC,EAAQf,KAAOA,EACfe,EAAQzB,OAAS,MACVqI,KAAK7G,QAAQC,EACrB,EACD,OAAOpC,EAAKqB,EAAMe,GAUT,OATAf,EAAAA,GAAc,GAGhBe,IACHA,EAAU,CAAA,GAEZA,EAAQpC,IAAMA,EACdoC,EAAQf,KAAOA,EACfe,EAAQzB,OAAS,SACVqI,KAAK7G,QAAQC,EACtB"}