{"version": 3, "file": "systemInfo.js", "sources": ["../../../../src/utils/systemInfo.js"], "sourcesContent": ["/**\n * 系统信息获取工具 - 兼容新旧API\n * 解决微信小程序wx.getSystemInfoSync已废弃的问题\n */\n\n// 缓存系统信息，避免重复调用\nlet cachedSystemInfo = null;\nlet cacheTimestamp = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\n\n/**\n * 获取设备信息（替代wx.getSystemInfoSync的设备相关部分）\n */\nexport const getDeviceInfo = () => {\n  try {\n    // 优先使用新API\n    if (uni.getDeviceInfo) {\n      return uni.getDeviceInfo();\n    }\n    // 尝试使用微信原生新API\n    if (typeof wx !== 'undefined') {\n      try {\n        const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};\n        return {\n          brand: deviceInfo.brand || 'unknown',\n          model: deviceInfo.model || 'unknown',\n          platform: deviceInfo.platform || 'unknown',\n          system: deviceInfo.system || 'unknown'\n        };\n      } catch (wxError) {\n        console.warn('微信原生API获取设备信息失败:', wxError);\n      }\n    }\n\n    // 最后降级到旧API（仅在必要时）\n    console.warn('所有新API都不可用，降级使用getSystemInfoSync');\n    try {\n      const systemInfo = uni.getSystemInfoSync();\n      return {\n        brand: systemInfo.brand,\n        model: systemInfo.model,\n        platform: systemInfo.platform,\n        system: systemInfo.system\n      };\n    } catch (error) {\n      console.error('旧API也失败，使用默认值:', error);\n      return {\n        brand: 'unknown',\n        model: 'unknown',\n        platform: 'unknown',\n        system: 'unknown'\n      };\n    }\n  } catch (error) {\n    console.warn('获取设备信息失败:', error);\n    return {\n      brand: 'unknown',\n      model: 'unknown', \n      platform: 'unknown',\n      system: 'unknown'\n    };\n  }\n};\n\n/**\n * 获取应用基础信息\n */\nexport const getAppBaseInfo = () => {\n  try {\n    if (uni.getAppBaseInfo) {\n      return uni.getAppBaseInfo();\n    }\n    // 尝试使用微信原生新API\n    if (typeof wx !== 'undefined') {\n      try {\n        const appInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};\n        return {\n          version: appInfo.version || 'unknown',\n          language: appInfo.language || 'zh_CN',\n          theme: appInfo.theme || 'light'\n        };\n      } catch (wxError) {\n        console.warn('微信原生API获取应用信息失败:', wxError);\n      }\n    }\n\n    // 最后降级到旧API\n    console.warn('降级使用getSystemInfoSync获取应用信息');\n    try {\n      const systemInfo = uni.getSystemInfoSync();\n      return {\n        version: systemInfo.version,\n        language: systemInfo.language,\n        theme: systemInfo.theme\n      };\n    } catch (error) {\n      console.error('获取应用信息失败，使用默认值:', error);\n      return {\n        version: 'unknown',\n        language: 'zh_CN',\n        theme: 'light'\n      };\n    }\n  } catch (error) {\n    console.warn('获取应用基础信息失败:', error);\n    return {\n      version: 'unknown',\n      language: 'zh_CN',\n      theme: 'light'\n    };\n  }\n};\n\n/**\n * 获取窗口信息\n */\nexport const getWindowInfo = () => {\n  try {\n    if (uni.getWindowInfo) {\n      return uni.getWindowInfo();\n    }\n    // 尝试使用微信原生新API\n    if (typeof wx !== 'undefined') {\n      try {\n        const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};\n        return {\n          windowWidth: windowInfo.windowWidth || 375,\n          windowHeight: windowInfo.windowHeight || 667,\n          screenWidth: windowInfo.screenWidth || 375,\n          screenHeight: windowInfo.screenHeight || 667,\n          pixelRatio: windowInfo.pixelRatio || 2,\n          statusBarHeight: windowInfo.statusBarHeight || 20,\n          safeArea: windowInfo.safeArea || { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },\n          safeAreaInsets: windowInfo.safeAreaInsets || { top: 0, left: 0, right: 0, bottom: 0 }\n        };\n      } catch (wxError) {\n        console.warn('微信原生API获取窗口信息失败:', wxError);\n      }\n    }\n\n    // 最后降级到旧API\n    console.warn('降级使用getSystemInfoSync获取窗口信息');\n    try {\n      const systemInfo = uni.getSystemInfoSync();\n      return {\n        windowWidth: systemInfo.windowWidth,\n        windowHeight: systemInfo.windowHeight,\n        screenWidth: systemInfo.screenWidth,\n        screenHeight: systemInfo.screenHeight,\n        pixelRatio: systemInfo.pixelRatio,\n        statusBarHeight: systemInfo.statusBarHeight,\n        safeArea: systemInfo.safeArea,\n        safeAreaInsets: systemInfo.safeAreaInsets\n      };\n    } catch (error) {\n      console.error('获取窗口信息失败，使用默认值:', error);\n      return {\n        windowWidth: 375,\n        windowHeight: 667,\n        screenWidth: 375,\n        screenHeight: 667,\n        pixelRatio: 2,\n        statusBarHeight: 20,\n        safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },\n        safeAreaInsets: { top: 0, left: 0, right: 0, bottom: 0 }\n      };\n    }\n  } catch (error) {\n    console.warn('获取窗口信息失败:', error);\n    return {\n      windowWidth: 375,\n      windowHeight: 667,\n      screenWidth: 375,\n      screenHeight: 667,\n      pixelRatio: 2,\n      statusBarHeight: 20,\n      safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },\n      safeAreaInsets: { top: 0, left: 0, right: 0, bottom: 0 }\n    };\n  }\n};\n\n/**\n * 获取系统设置信息\n */\nexport const getSystemSetting = () => {\n  try {\n    if (uni.getSystemSetting) {\n      return uni.getSystemSetting();\n    }\n    // 降级处理\n    return {\n      bluetoothEnabled: false,\n      locationEnabled: false,\n      wifiEnabled: false\n    };\n  } catch (error) {\n    console.warn('获取系统设置失败:', error);\n    return {\n      bluetoothEnabled: false,\n      locationEnabled: false,\n      wifiEnabled: false\n    };\n  }\n};\n\n/**\n * 获取完整的系统信息（兼容旧API）\n * 这是主要的替代函数，用于替换wx.getSystemInfoSync()\n */\nexport const getSystemInfo = () => {\n  const now = Date.now();\n  \n  // 检查缓存\n  if (cachedSystemInfo && (now - cacheTimestamp) < CACHE_DURATION) {\n    return cachedSystemInfo;\n  }\n\n  try {\n    const deviceInfo = getDeviceInfo();\n    const appInfo = getAppBaseInfo();\n    const windowInfo = getWindowInfo();\n    const systemSetting = getSystemSetting();\n\n    // 合并所有信息，保持与旧API的兼容性\n    const systemInfo = {\n      // 设备信息\n      brand: deviceInfo.brand,\n      model: deviceInfo.model,\n      platform: deviceInfo.platform,\n      system: deviceInfo.system,\n      \n      // 应用信息\n      version: appInfo.version,\n      language: appInfo.language,\n      theme: appInfo.theme,\n      \n      // 窗口信息\n      windowWidth: windowInfo.windowWidth,\n      windowHeight: windowInfo.windowHeight,\n      screenWidth: windowInfo.screenWidth,\n      screenHeight: windowInfo.screenHeight,\n      pixelRatio: windowInfo.pixelRatio,\n      statusBarHeight: windowInfo.statusBarHeight,\n      safeArea: windowInfo.safeArea,\n      safeAreaInsets: windowInfo.safeAreaInsets,\n      \n      // 系统设置\n      bluetoothEnabled: systemSetting.bluetoothEnabled,\n      locationEnabled: systemSetting.locationEnabled,\n      wifiEnabled: systemSetting.wifiEnabled,\n      \n      // 添加时间戳\n      _timestamp: now\n    };\n\n    // 更新缓存\n    cachedSystemInfo = systemInfo;\n    cacheTimestamp = now;\n\n    return systemInfo;\n  } catch (error) {\n    console.error('获取系统信息失败，使用降级方案:', error);\n    \n    // 最后的降级方案\n    try {\n      console.warn('使用最后的降级方案：getSystemInfoSync');\n      const fallbackInfo = uni.getSystemInfoSync();\n      cachedSystemInfo = fallbackInfo;\n      cacheTimestamp = now;\n      return fallbackInfo;\n    } catch (fallbackError) {\n      console.error('所有方案都失败，使用硬编码默认值:', fallbackError);\n      // 返回默认值\n      const defaultInfo = {\n        brand: 'unknown',\n        model: 'unknown',\n        platform: 'unknown',\n        system: 'unknown',\n        version: 'unknown',\n        language: 'zh_CN',\n        theme: 'light',\n        windowWidth: 375,\n        windowHeight: 667,\n        screenWidth: 375,\n        screenHeight: 667,\n        pixelRatio: 2,\n        statusBarHeight: 20,\n        safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },\n        safeAreaInsets: { top: 0, left: 0, right: 0, bottom: 0 },\n        bluetoothEnabled: false,\n        locationEnabled: false,\n        wifiEnabled: false,\n        _timestamp: now\n      };\n      cachedSystemInfo = defaultInfo;\n      cacheTimestamp = now;\n      return defaultInfo;\n    }\n  }\n};\n\n/**\n * 清除缓存（在需要获取最新信息时调用）\n */\nexport const clearSystemInfoCache = () => {\n  cachedSystemInfo = null;\n  cacheTimestamp = 0;\n};\n\n/**\n * px转rpx工具函数（使用新的API）\n */\nexport const pxToRpx = (px) => {\n  const windowInfo = getWindowInfo();\n  return (750 * Number.parseInt(px)) / windowInfo.windowWidth;\n};\n\n/**\n * rpx转px工具函数\n */\nexport const rpxToPx = (rpx) => {\n  const windowInfo = getWindowInfo();\n  return (Number.parseInt(rpx) * windowInfo.windowWidth) / 750;\n};\n"], "names": ["cachedSystemInfo", "cacheTimestamp", "getWindowInfo", "uni", "wx", "wx$1", "windowInfo", "windowWidth", "windowHeight", "screenWidth", "screenHeight", "pixelRatio", "statusBarHeight", "safeArea", "top", "left", "right", "bottom", "width", "height", "safeAreaInsets", "wxError", "console", "warn", "systemInfo", "getSystemInfoSync", "error", "now", "Date", "deviceInfo", "getDeviceInfo", "brand", "model", "platform", "system", "appInfo", "getAppBaseInfo", "version", "language", "theme", "systemSetting", "getSystemSetting", "bluetoothEnabled", "locationEnabled", "wifiEnabled", "_timestamp", "fallbackInfo", "fallback<PERSON><PERSON>r", "defaultInfo", "px", "Number", "parseInt"], "mappings": "oDAMA,IAAIA,EAAmB,KACnBC,EAAiB,EACrB,MA4GaC,EAAgB,KACvB,IACEC,GAAAA,EAAAA,MAAID,cACCC,OAAAA,EAAAA,MAAID,gBAGT,QAAc,IAAPE,EAAEC,KACP,IACI,MAAAC,EAAaF,EAAAA,KAAGF,cAAgBE,EAAAA,KAAGF,gBAAkB,GACpD,MAAA,CACLK,YAAaD,EAAWC,aAAe,IACvCC,aAAcF,EAAWE,cAAgB,IACzCC,YAAaH,EAAWG,aAAe,IACvCC,aAAcJ,EAAWI,cAAgB,IACzCC,WAAYL,EAAWK,YAAc,EACrCC,gBAAiBN,EAAWM,iBAAmB,GAC/CC,SAAUP,EAAWO,UAAY,CAAEC,IAAK,EAAGC,KAAM,EAAGC,MAAO,IAAKC,OAAQ,IAAKC,MAAO,IAAKC,OAAQ,KACjGC,eAAgBd,EAAWc,gBAAkB,CAAEN,IAAK,EAAGC,KAAM,EAAGC,MAAO,EAAGC,OAAQ,GAErF,OAAQI,GACCC,QAAAC,KAAK,mBAAoBF,EACnC,CAIFC,QAAQC,KAAK,+BACT,IACI,MAAAC,EAAarB,QAAIsB,oBAChB,MAAA,CACLlB,YAAaiB,EAAWjB,YACxBC,aAAcgB,EAAWhB,aACzBC,YAAae,EAAWf,YACxBC,aAAcc,EAAWd,aACzBC,WAAYa,EAAWb,WACvBC,gBAAiBY,EAAWZ,gBAC5BC,SAAUW,EAAWX,SACrBO,eAAgBI,EAAWJ,eAE9B,OAAQM,GAEA,OADCJ,QAAAI,MAAM,kBAAmBA,GAC1B,CACLnB,YAAa,IACbC,aAAc,IACdC,YAAa,IACbC,aAAc,IACdC,WAAY,EACZC,gBAAiB,GACjBC,SAAU,CAAEC,IAAK,EAAGC,KAAM,EAAGC,MAAO,IAAKC,OAAQ,IAAKC,MAAO,IAAKC,OAAQ,KAC1EC,eAAgB,CAAEN,IAAK,EAAGC,KAAM,EAAGC,MAAO,EAAGC,OAAQ,GAEzD,CACD,OAAQS,GAEA,OADCJ,QAAAC,KAAK,YAAaG,GACnB,CACLnB,YAAa,IACbC,aAAc,IACdC,YAAa,IACbC,aAAc,IACdC,WAAY,EACZC,gBAAiB,GACjBC,SAAU,CAAEC,IAAK,EAAGC,KAAM,EAAGC,MAAO,IAAKC,OAAQ,IAAKC,MAAO,IAAKC,OAAQ,KAC1EC,eAAgB,CAAEN,IAAK,EAAGC,KAAM,EAAGC,MAAO,EAAGC,OAAQ,GAEzD,yBA+B2B,KACrB,MAAAU,EAAMC,KAAKD,MAGb,GAAA3B,GAAqB2B,EAAM1B,EA9MV,IA+MZ,OAAAD,EAGL,IACF,MAAM6B,EA9MmB,MACvB,IAEE1B,GAAAA,EAAAA,MAAI2B,cACC3B,OAAAA,EAAAA,MAAI2B,gBAGT,QAAc,IAAP1B,EAAEC,KACP,IACI,MAAAwB,EAAazB,EAAAA,KAAG0B,cAAgB1B,EAAAA,KAAG0B,gBAAkB,GACpD,MAAA,CACLC,MAAOF,EAAWE,OAAS,UAC3BC,MAAOH,EAAWG,OAAS,UAC3BC,SAAUJ,EAAWI,UAAY,UACjCC,OAAQL,EAAWK,QAAU,UAEhC,OAAQb,GACCC,QAAAC,KAAK,mBAAoBF,EACnC,CAIFC,QAAQC,KAAK,oCACT,IACI,MAAAC,EAAarB,QAAIsB,oBAChB,MAAA,CACLM,MAAOP,EAAWO,MAClBC,MAAOR,EAAWQ,MAClBC,SAAUT,EAAWS,SACrBC,OAAQV,EAAWU,OAEtB,OAAQR,GAEA,OADCJ,QAAAI,MAAM,iBAAkBA,GACzB,CACLK,MAAO,UACPC,MAAO,UACPC,SAAU,UACVC,OAAQ,UAEZ,CACD,OAAQR,GAEA,OADCJ,QAAAC,KAAK,YAAaG,GACnB,CACLK,MAAO,UACPC,MAAO,UACPC,SAAU,UACVC,OAAQ,UAEZ,GA8JqBJ,GACbK,EAzJoB,MACxB,IACEhC,GAAAA,EAAAA,MAAIiC,eACCjC,OAAAA,EAAAA,MAAIiC,iBAGT,QAAc,IAAPhC,EAAEC,KACP,IACI,MAAA8B,EAAU/B,EAAAA,KAAGgC,eAAiBhC,EAAAA,KAAGgC,iBAAmB,GACnD,MAAA,CACLC,QAASF,EAAQE,SAAW,UAC5BC,SAAUH,EAAQG,UAAY,QAC9BC,MAAOJ,EAAQI,OAAS,QAE3B,OAAQlB,GACCC,QAAAC,KAAK,mBAAoBF,EACnC,CAIFC,QAAQC,KAAK,+BACT,IACI,MAAAC,EAAarB,QAAIsB,oBAChB,MAAA,CACLY,QAASb,EAAWa,QACpBC,SAAUd,EAAWc,SACrBC,MAAOf,EAAWe,MAErB,OAAQb,GAEA,OADCJ,QAAAI,MAAM,kBAAmBA,GAC1B,CACLW,QAAS,UACTC,SAAU,QACVC,MAAO,QAEX,CACD,OAAQb,GAEA,OADCJ,QAAAC,KAAK,cAAeG,GACrB,CACLW,QAAS,UACTC,SAAU,QACVC,MAAO,QAEX,GA8GkBH,GACV9B,EAAaJ,IACbsC,EArCsB,MAC1B,IACErC,OAAAA,EAAAA,MAAIsC,iBACCtC,EAAAA,MAAIsC,mBAGN,CACLC,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EAEhB,OAAQlB,GAEA,OADCJ,QAAAC,KAAK,YAAaG,GACnB,CACLgB,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EAEjB,GAmBwBH,GAGhBjB,EAAa,CAEjBO,MAAOF,EAAWE,MAClBC,MAAOH,EAAWG,MAClBC,SAAUJ,EAAWI,SACrBC,OAAQL,EAAWK,OAGnBG,QAASF,EAAQE,QACjBC,SAAUH,EAAQG,SAClBC,MAAOJ,EAAQI,MAGfhC,YAAaD,EAAWC,YACxBC,aAAcF,EAAWE,aACzBC,YAAaH,EAAWG,YACxBC,aAAcJ,EAAWI,aACzBC,WAAYL,EAAWK,WACvBC,gBAAiBN,EAAWM,gBAC5BC,SAAUP,EAAWO,SACrBO,eAAgBd,EAAWc,eAG3BsB,iBAAkBF,EAAcE,iBAChCC,gBAAiBH,EAAcG,gBAC/BC,YAAaJ,EAAcI,YAG3BC,WAAYlB,GAOP,OAHY3B,EAAAwB,EACFvB,EAAA0B,EAEVH,CACR,OAAQE,GACCJ,QAAAI,MAAM,mBAAoBA,GAG9B,IACFJ,QAAQC,KAAK,+BACP,MAAAuB,EAAe3C,QAAIsB,oBAGlB,OAFYzB,EAAA8C,EACF7C,EAAA0B,EACVmB,CACR,OAAQC,GACCzB,QAAAI,MAAM,oBAAqBqB,GAEnC,MAAMC,EAAc,CAClBjB,MAAO,UACPC,MAAO,UACPC,SAAU,UACVC,OAAQ,UACRG,QAAS,UACTC,SAAU,QACVC,MAAO,QACPhC,YAAa,IACbC,aAAc,IACdC,YAAa,IACbC,aAAc,IACdC,WAAY,EACZC,gBAAiB,GACjBC,SAAU,CAAEC,IAAK,EAAGC,KAAM,EAAGC,MAAO,IAAKC,OAAQ,IAAKC,MAAO,IAAKC,OAAQ,KAC1EC,eAAgB,CAAEN,IAAK,EAAGC,KAAM,EAAGC,MAAO,EAAGC,OAAQ,GACrDyB,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EACbC,WAAYlB,GAIP,OAFY3B,EAAAgD,EACF/C,EAAA0B,EACVqB,CACT,CACF,2CAcsBC,IACtB,MAAM3C,EAAaJ,IACnB,OAAQ,IAAMgD,OAAOC,SAASF,GAAO3C,EAAWC,WAAA"}