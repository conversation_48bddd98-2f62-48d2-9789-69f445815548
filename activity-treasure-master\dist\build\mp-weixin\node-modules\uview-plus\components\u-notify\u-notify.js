"use strict";const t=require("../../../../common/vendor.js"),o={name:"u-notify",mixins:[t.mpMixin,t.mixin,t.props$30],data:()=>({open:!1,timer:null,config:{top:t.defProps.notify.top,type:t.defProps.notify.type,color:t.defProps.notify.color,bgColor:t.defProps.notify.bgColor,message:t.defProps.notify.message,duration:t.defProps.notify.duration,fontSize:t.defProps.notify.fontSize,safeAreaInsetTop:t.defProps.notify.safeAreaInsetTop},tmpConfig:{}}),computed:{containerStyle(){this.tmpConfig.top;return{top:t.addUnit(0===this.tmpConfig.top?0:this.tmpConfig.top),position:"fixed",left:0,right:0,zIndex:10076}},backgroundColor(){const t={};return this.tmpConfig.bgColor&&(t.backgroundColor=this.tmpConfig.bgColor),t},icon(){let t;return"success"===this.tmpConfig.type?t="checkmark-circle":"error"===this.tmpConfig.type?t="close-circle":"warning"===this.tmpConfig.type&&(t="error-circle"),t}},created(){["primary","success","error","warning"].map((t=>{this[t]=o=>this.show({type:t,message:o})}))},methods:{addStyle:t.addStyle,addUnit:t.addUnit,show(o){this.tmpConfig=t.deepMerge(this.config,o),this.clearTimer(),this.open=!0,this.tmpConfig.duration>0&&(this.timer=setTimeout((()=>{this.open=!1,this.clearTimer(),"function"==typeof this.tmpConfig.complete&&this.tmpConfig.complete()}),this.tmpConfig.duration))},close(){this.clearTimer()},clearTimer(){this.open=!1,clearTimeout(this.timer),this.timer=null}},beforeUnmount(){this.clearTimer()}};if(!Array){(t.resolveComponent("u-status-bar")+t.resolveComponent("u-icon")+t.resolveComponent("u-transition"))()}Math||((()=>"../u-status-bar/u-status-bar.js")+(()=>"../u-icon/u-icon.js")+(()=>"../u-transition/u-transition.js"))();const e=t._export_sfc(o,[["render",function(o,e,i,n,r,s){return t.e({a:r.tmpConfig.safeAreaInsetTop},(r.tmpConfig.safeAreaInsetTop,{}),{b:["success","warning","error"].includes(r.tmpConfig.type)},["success","warning","error"].includes(r.tmpConfig.type)?{c:t.p({name:r.tmpConfig.icon,color:r.tmpConfig.color,size:1.3*r.tmpConfig.fontSize,customStyle:{marginRight:"4px"}})}:{},{d:t.t(r.tmpConfig.message),e:s.addUnit(r.tmpConfig.fontSize),f:r.tmpConfig.color,g:t.n(`u-notify--${r.tmpConfig.type}`),h:t.s(s.backgroundColor),i:t.s(s.addStyle(o.customStyle)),j:t.p({mode:"slide-down",customStyle:s.containerStyle,show:r.open})})}],["__scopeId","data-v-d7826d86"]]);wx.createComponent(e);
//# sourceMappingURL=u-notify.js.map
