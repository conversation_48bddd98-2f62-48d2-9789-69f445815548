{"version": 3, "file": "u-textarea.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-textarea/u-textarea.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXRleHRhcmVhL3UtdGV4dGFyZWEudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"u-textarea\" :class=\"textareaClass\" :style=\"[textareaStyle]\">\r\n        <textarea\r\n            class=\"u-textarea__field\"\r\n            :value=\"innerValue\"\r\n            :style=\"{ height: addUnit(height) }\"\r\n            :placeholder=\"placeholder\"\r\n            :placeholder-style=\"addStyle(placeholderStyle, 'string')\"\r\n            :placeholder-class=\"placeholderClass\"\r\n            :disabled=\"disabled\"\r\n            :focus=\"focus\"\r\n            :autoHeight=\"autoHeight\"\r\n            :fixed=\"fixed\"\r\n            :cursorSpacing=\"cursorSpacing\"\r\n            :cursor=\"cursor\"\r\n            :showConfirmBar=\"showConfirmBar\"\r\n            :selectionStart=\"selectionStart\"\r\n            :selectionEnd=\"selectionEnd\"\r\n            :adjustPosition=\"adjustPosition\"\r\n            :disableDefaultPadding=\"disableDefaultPadding\"\r\n            :holdKeyboard=\"holdKeyboard\"\r\n            :maxlength=\"maxlength\"\r\n            :confirm-type=\"confirmType\"\r\n            :ignoreCompositionEvent=\"ignoreCompositionEvent\"\r\n            @focus=\"onFocus\"\r\n            @blur=\"onBlur\"\r\n            @linechange=\"onLinechange\"\r\n            @input=\"onInput\"\r\n            @confirm=\"onConfirm\"\r\n            @keyboardheightchange=\"onKeyboardheightchange\"\r\n        ></textarea>\r\n\t\t<!-- #ifndef MP-ALIPAY -->\r\n        <text\r\n            class=\"u-textarea__count\"\r\n            :style=\"{\r\n                'background-color': disabled ? 'transparent' : '#fff',\r\n            }\"\r\n            v-if=\"count\"\r\n            >{{ innerValue.length }}/{{ maxlength }}</text\r\n        >\r\n\t\t<!-- #endif -->\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport { props } from \"./props.js\";\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { addStyle, addUnit, deepMerge, formValidate, os } from '../../libs/function/index';\r\n/**\r\n * Textarea 文本域\r\n * @description 文本域此组件满足了可能出现的表单信息补充，编辑等实际逻辑的功能，内置了字数校验等\r\n * @tutorial https://ijry.github.io/uview-plus/components/textarea.html\r\n *\r\n * @property {String | Number} \t\tvalue\t\t\t\t\t输入框的内容\r\n * @property {String | Number}\t\tplaceholder\t\t\t\t输入框为空时占位符\r\n * @property {String}\t\t\t    placeholderClass\t\t指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）\r\n * @property {String | Object}\t    placeholderStyle\t\t指定placeholder的样式，字符串/对象形式，如\"color: red;\"\r\n * @property {String | Number}\t\theight\t\t\t\t\t输入框高度（默认 70 ）\r\n * @property {String}\t\t\t\tconfirmType\t\t\t\t设置键盘右下角按钮的文字，仅微信小程序，App-vue和H5有效（默认 'done' ）\r\n * @property {Boolean}\t\t\t\tdisabled\t\t\t\t是否禁用（默认 false ）\r\n * @property {Boolean}\t\t\t\tcount\t\t\t\t\t是否显示统计字数（默认 false ）\r\n * @property {Boolean}\t\t\t\tfocus\t\t\t\t\t是否自动获取焦点，nvue不支持，H5取决于浏览器的实现（默认 false ）\r\n * @property {Boolean | Function}\tautoHeight\t\t\t\t是否自动增加高度（默认 false ）\r\n * @property {Boolean}\t\t\t\tfixed\t\t\t\t\t如果textarea是在一个position:fixed的区域，需要显示指定属性fixed为true（默认 false ）\r\n * @property {Number}\t\t\t\tcursorSpacing\t\t\t指定光标与键盘的距离（默认 0 ）\r\n * @property {String | Number}\t\tcursor\t\t\t\t\t指定focus时的光标位置\r\n * @property {Function}\t\t\t    formatter\t\t\t    内容式化函数\r\n * @property {Boolean}\t\t\t\tshowConfirmBar\t\t\t是否显示键盘上方带有”完成“按钮那一栏，（默认 true ）\r\n * @property {Number}\t\t\t\tselectionStart\t\t\t光标起始位置，自动聚焦时有效，需与selection-end搭配使用，（默认 -1 ）\r\n * @property {Number | Number}\t\tselectionEnd\t\t\t光标结束位置，自动聚焦时有效，需与selection-start搭配使用（默认 -1 ）\r\n * @property {Boolean}\t\t\t\tadjustPosition\t\t\t键盘弹起时，是否自动上推页面（默认 true ）\r\n * @property {Boolean | Number}\t\tdisableDefaultPadding\t是否去掉 iOS 下的默认内边距，只微信小程序有效（默认 false ）\r\n * @property {Boolean}\t\t\t\tholdKeyboard\t\t\tfocus时，点击页面的时候不收起键盘，只微信小程序有效（默认 false ）\r\n * @property {String | Number}\t\tmaxlength\t\t\t\t最大输入长度，设置为 -1 的时候不限制最大长度（默认 140 ）\r\n * @property {String}\t\t\t\tborder\t\t\t\t\t边框类型，surround-四周边框，none-无边框，bottom-底部边框（默认 'surround' ）\r\n * @property {Boolean}\t\t\t\tignoreCompositionEvent\t是否忽略组件内对文本合成系统事件的处理\r\n *\r\n * @event {Function(e)} focus\t\t\t\t\t输入框聚焦时触发，event.detail = { value, height }，height 为键盘高度\r\n * @event {Function(e)} blur\t\t\t\t\t输入框失去焦点时触发，event.detail = {value, cursor}\r\n * @event {Function(e)} linechange\t\t\t\t输入框行数变化时调用，event.detail = {height: 0, heightRpx: 0, lineCount: 0}\r\n * @event {Function(e)} input\t\t\t\t\t当键盘输入时，触发 input 事件\r\n * @event {Function(e)} confirm\t\t\t\t\t点击完成时， 触发 confirm 事件\r\n * @event {Function(e)} keyboardheightchange\t键盘高度发生变化的时候触发此事件\r\n * @example <up-textarea v-model=\"value1\" placeholder=\"请输入内容\" ></up-textarea>\r\n */\r\nexport default {\r\n    name: \"u-textarea\",\r\n    mixins: [mpMixin, mixin, props],\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 输入框的值\r\n\t\t\tinnerValue: \"\",\r\n\t\t\t// 是否处于获得焦点状态\r\n\t\t\tfocused: false,\r\n\t\t\t// value是否第一次变化，在watch中，由于加入immediate属性，会在第一次触发，此时不应该认为value发生了变化\r\n\t\t\tfirstChange: true,\r\n\t\t\t// value绑定值的变化是由内部还是外部引起的\r\n\t\t\tchangeFromInner: false,\r\n\t\t\t// 过滤处理方法\r\n\t\t\tinnerFormatter: value => value\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t},\r\n\twatch: {\r\n        // #ifdef VUE2\r\n\t    value: {\r\n\t        immediate: true,\r\n\t        handler(newVal, oldVal) {\r\n\t            this.innerValue = newVal;\r\n\t            /* #ifdef H5 */\r\n\t            // 在H5中，外部value变化后，修改input中的值，不会触发@input事件，此时手动调用值变化方法\r\n\t            if (\r\n\t                this.firstChange === false &&\r\n\t                this.changeFromInner === false\r\n\t            ) {\r\n\t                this.valueChange();\r\n\t            }\r\n\t            /* #endif */\r\n\t            this.firstChange = false;\r\n\t            // 重置changeFromInner的值为false，标识下一次引起默认为外部引起的\r\n\t            this.changeFromInner = false;\r\n\t        },\r\n\t    },\r\n        // #endif\r\n        // #ifdef VUE3\r\n        modelValue: {\r\n\t        immediate: true,\r\n\t        handler(newVal, oldVal) {\r\n\t            this.innerValue = newVal;\r\n\t            /* #ifdef H5 */\r\n\t            // 在H5中，外部value变化后，修改input中的值，不会触发@input事件，此时手动调用值变化方法\r\n\t            if (\r\n\t                this.firstChange === false &&\r\n\t                this.changeFromInner === false\r\n\t            ) {\r\n\t                this.valueChange();\r\n\t            }\r\n\t            /* #endif */\r\n\t            this.firstChange = false;\r\n\t            // 重置changeFromInner的值为false，标识下一次引起默认为外部引起的\r\n\t            this.changeFromInner = false;\r\n\t        },\r\n\t    }\r\n        // #endif\r\n\t},\r\n    computed: {\r\n        // 组件的类名\r\n        textareaClass() {\r\n            let classes = [],\r\n                { border, disabled } = this;\r\n            border === \"surround\" &&\r\n                (classes = classes.concat([\"u-border\", \"u-textarea--radius\"]));\r\n            border === \"bottom\" &&\r\n                (classes = classes.concat([\r\n                    \"u-border-bottom\",\r\n                    \"u-textarea--no-radius\",\r\n                ]));\r\n            disabled && classes.push(\"u-textarea--disabled\");\r\n            return classes.join(\" \");\r\n        },\r\n        // 组件的样式\r\n        textareaStyle() {\r\n            const style = {};\r\n            // #ifdef APP-NVUE\r\n            // 由于textarea在安卓nvue上的差异性，需要额外再调整其内边距\r\n            if (os() === \"android\") {\r\n                style.paddingTop = \"6px\";\r\n                style.paddingLeft = \"9px\";\r\n                style.paddingBottom = \"3px\";\r\n                style.paddingRight = \"6px\";\r\n            }\r\n            // #endif\r\n            return deepMerge(style, addStyle(this.customStyle));\r\n        },\r\n    },\r\n    // #ifdef VUE3\r\n    emits: ['update:modelValue', 'linechange', 'focus', 'blur', 'change', 'confirm', 'keyboardheightchange'],\r\n    // #endif\r\n    methods: {\r\n        addStyle,\r\n        addUnit,\r\n\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\r\n\t\tsetFormatter(e) {\r\n\t\t\tthis.innerFormatter = e\r\n\t\t},\r\n        onFocus(e) {\r\n            this.$emit(\"focus\", e);\r\n        },\r\n        onBlur(e) {\r\n            this.$emit(\"blur\", e);\r\n            // 尝试调用u-form的验证方法\r\n            formValidate(this, \"blur\");\r\n        },\r\n        onLinechange(e) {\r\n            this.$emit(\"linechange\", e);\r\n        },\r\n        onInput(e) {\r\n\t\t\tlet { value = \"\" } = e.detail || {};\r\n\t\t\t// 格式化过滤方法\r\n\t\t\tconst formatter = this.formatter || this.innerFormatter\r\n\t\t\tconst formatValue = formatter(value)\r\n\t\t\t// 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效\r\n\t\t\tthis.innerValue = value\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.innerValue = formatValue;\r\n\t\t\t\tthis.valueChange();\r\n\t\t\t})\r\n        },\r\n\t\t// 内容发生变化，进行处理\r\n\t\tvalueChange() {\r\n\t\t    const value = this.innerValue;\r\n\t\t    this.$nextTick(() => {\r\n                // #ifdef VUE3\r\n                this.$emit(\"update:modelValue\", value);\r\n                // #endif\r\n                // #ifdef VUE2\r\n                this.$emit(\"input\", value);\r\n                // #endif\r\n\t\t        // 标识value值的变化是由内部引起的\r\n\t\t        this.changeFromInner = true;\r\n\t\t        this.$emit(\"change\", value);\r\n\t\t        // 尝试调用u-form的验证方法\r\n\t\t        formValidate(this, \"change\");\r\n\t\t    });\r\n\t\t},\r\n        onConfirm(e) {\r\n            this.$emit(\"confirm\", e);\r\n        },\r\n        onKeyboardheightchange(e) {\r\n            this.$emit(\"keyboardheightchange\", e);\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/components.scss\";\r\n\r\n.u-textarea {\r\n    border-radius: 4px;\r\n    background-color: #fff;\r\n    position: relative;\r\n    @include flex;\r\n    flex: 1;\r\n\tpadding: 9px;\r\n\r\n    &--radius {\r\n        border-radius: 4px;\r\n    }\r\n\r\n    &--no-radius {\r\n        border-radius: 0;\r\n    }\r\n\r\n    &--disabled {\r\n        background-color: #f5f7fa;\r\n    }\r\n\r\n    &__field {\r\n        flex: 1;\r\n        font-size: 15px;\r\n        color: $u-content-color;\r\n\t\twidth: 100%;\r\n    }\r\n\r\n    &__count {\r\n        position: absolute;\r\n        right: 5px;\r\n        bottom: 2px;\r\n        font-size: 12px;\r\n        color: $u-tips-color;\r\n        background-color: #ffffff;\r\n        padding: 1px 4px;\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-textarea/u-textarea.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "innerValue", "focused", "firstChange", "changeFromInner", "innerFormatter", "value", "created", "watch", "modelValue", "immediate", "handler", "newVal", "oldVal", "this", "computed", "textareaClass", "classes", "border", "disabled", "concat", "push", "join", "textareaStyle", "deepMerge", "addStyle", "customStyle", "emits", "methods", "addUnit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "onFocus", "$emit", "onBlur", "onLinechange", "onInput", "detail", "formatValue", "formatter", "$nextTick", "valueChange", "onConfirm", "onKeyboardheightchange", "wx", "createComponent", "Component"], "mappings": "6DAsFKA,EAAU,CACXC,KAAM,aACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YAC5BC,KAAO,KACC,CAENC,WAAY,GAEZC,SAAS,EAETC,aAAa,EAEbC,iBAAiB,EAEjBC,eAAyBC,GAAAA,IAG3B,OAAAC,GACC,EACDC,MAAO,CAsBAC,WAAY,CACXC,WAAW,EACX,OAAAC,CAAQC,EAAQC,GACZC,KAAKb,WAAaW,EAUlBE,KAAKX,aAAc,EAEnBW,KAAKV,iBAAkB,CAC1B,IAINW,SAAU,CAEN,aAAAC,GACI,IAAIC,EAAU,IACVC,OAAEA,EAAAC,SAAQA,GAAaL,KASpB,MARI,aAAXI,IACKD,EAAUA,EAAQG,OAAO,CAAC,WAAY,wBAChC,WAAAF,IACND,EAAUA,EAAQG,OAAO,CACtB,kBACA,2BAEID,GAAAF,EAAQI,KAAK,wBAClBJ,EAAQK,KAAK,IACvB,EAED,aAAAC,GAWI,OAAOC,EAAAA,UAVO,CAAA,EAUUC,EAAAA,SAASX,KAAKY,aACzC,GAGLC,MAAO,CAAC,oBAAqB,aAAc,QAAS,OAAQ,SAAU,UAAW,wBAEjFC,QAAS,CACLH,SAAAA,EAAQA,SACRI,QAAAA,EAAOA,QAEb,YAAAC,CAAaC,GACZjB,KAAKT,eAAiB0B,CACtB,EACK,OAAAC,CAAQD,GACCjB,KAAAmB,MAAM,QAASF,EACvB,EACD,MAAAG,CAAOH,GACEjB,KAAAmB,MAAM,OAAQF,kBAENjB,KAAM,OACtB,EACD,YAAAqB,CAAaJ,GACJjB,KAAAmB,MAAM,aAAcF,EAC5B,EACD,OAAAK,CAAQL,GACb,IAAIzB,MAAEA,EAAQ,IAAOyB,EAAEM,QAAU,CAAA,EAE3B,MACAC,GADYxB,KAAKyB,WAAazB,KAAKT,gBACXC,GAE9BQ,KAAKb,WAAaK,EAClBQ,KAAK0B,WAAU,KACd1B,KAAKb,WAAaqC,EAClBxB,KAAK2B,aAAW,GAEX,EAEP,WAAAA,GACI,MAAMnC,EAAQQ,KAAKb,WACnBa,KAAK0B,WAAU,KAEA1B,KAAAmB,MAAM,oBAAqB3B,GAMtCQ,KAAKV,iBAAkB,EAClBU,KAAAmB,MAAM,SAAU3B,kBAERQ,KAAM,SAAQ,GAElC,EACK,SAAA4B,CAAUX,GACDjB,KAAAmB,MAAM,UAAWF,EACzB,EACD,sBAAAY,CAAuBZ,GACdjB,KAAAmB,MAAM,uBAAwBF,EACtC,u6BCvOTa,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}