{"version": 3, "file": "search.js", "sources": ["../../../../../../../src/pages/bundle/world/author/search.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGF1dGhvclxzZWFyY2gudnVl"], "sourcesContent": ["<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue';\nimport { searchAuthors, createAuthor } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 创建作者搜索选择页面. Confirmed via 寸止 }}\nconst searchKeyword = ref('');\nconst authorList = ref([]);\nconst isLoading = ref(false);\nconst isLoadingMore = ref(false);\nconst hasMore = ref(true);\nconst currentPage = ref(1);\nconst pageSize = 20;\n\n// 获取页面参数\nconst pages = getCurrentPages();\nconst currentPageInstance = pages[pages.length - 1];\nconst isSelectMode = currentPageInstance.options.type === 'select';\n\n// 搜索作者\nconst searchAuthorList = async (isLoadMore = false) => {\n  if (isLoading.value || (isLoadMore && isLoadingMore.value)) return;\n  \n  if (isLoadMore) {\n    isLoadingMore.value = true;\n  } else {\n    isLoading.value = true;\n    currentPage.value = 1;\n    authorList.value = [];\n    hasMore.value = true;\n  }\n  \n  try {\n    const response = await searchAuthors({\n      uid: store.userInfo.uid,\n      token: store.userInfo.token,\n      keyword: searchKeyword.value,\n      page: currentPage.value,\n      page_size: pageSize\n    });\n    \n    if (response.status === 'ok') {\n      const newAuthors = response.data.authors || [];\n      \n      if (isLoadMore) {\n        authorList.value = [...authorList.value, ...newAuthors];\n      } else {\n        authorList.value = newAuthors;\n      }\n      \n      hasMore.value = newAuthors.length === pageSize;\n      if (hasMore.value) {\n        currentPage.value++;\n      }\n    } else {\n      uni.showToast({ title: response.msg || '搜索失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('搜索作者失败:', error);\n    uni.showToast({ title: '搜索失败，请稍后重试', icon: 'none' });\n  } finally {\n    isLoading.value = false;\n    isLoadingMore.value = false;\n  }\n};\n\n// 选择作者\nconst selectAuthor = (author) => {\n  if (isSelectMode) {\n    // 发送选择结果给父页面\n    uni.$emit('authorSelected', author);\n    uni.navigateBack();\n  } else {\n    // 跳转到作者详情页\n    uni.navigateTo({\n      url: `/pages/bundle/world/author/detail?id=${author.id}`\n    });\n  }\n};\n\n// 创建新作者\nconst createNewAuthor = () => {\n  if (isSelectMode) {\n    uni.navigateTo({\n      url: `/pages/bundle/world/author/create?type=select&keyword=${encodeURIComponent(searchKeyword.value)}`\n    });\n  } else {\n    uni.navigateTo({\n      url: '/pages/bundle/world/author/create'\n    });\n  }\n};\n\n// 搜索防抖\nlet searchTimer = null;\nconst handleSearch = () => {\n  clearTimeout(searchTimer);\n  searchTimer = setTimeout(() => {\n    searchAuthorList();\n  }, 500);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (hasMore.value && !isLoadingMore.value) {\n    searchAuthorList(true);\n  }\n};\n\n// 页面加载\nonMounted(() => {\n  searchAuthorList();\n});\n\n// 监听创建作者成功事件\nuni.$on('authorCreated', (newAuthor) => {\n  if (isSelectMode) {\n    selectAuthor(newAuthor);\n  } else {\n    // 刷新列表\n    searchAuthorList();\n  }\n});\n\n// 组件销毁时移除事件监听\nonUnmounted(() => {\n  uni.$off('authorCreated');\n});\n</script>\n\n<template>\n  <view class=\"author-search-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar \n      :title=\"isSelectMode ? '选择作者' : '搜索作者'\" \n      backIcon=\"arrow-left\"\n    />\n    \n    <!-- 搜索框 -->\n    <view class=\"search-section\">\n      <view class=\"search-box\">\n        <u-icon name=\"search\" size=\"20\" color=\"#999\" class=\"search-icon\"></u-icon>\n        <input \n          v-model=\"searchKeyword\" \n          @input=\"handleSearch\"\n          placeholder=\"搜索作者姓名、类别...\"\n          class=\"search-input\"\n          confirm-type=\"search\"\n          @confirm=\"searchAuthorList\"\n        />\n        <view v-if=\"searchKeyword\" @click=\"searchKeyword = ''; searchAuthorList()\" class=\"clear-btn\">\n          <u-icon name=\"close-circle-fill\" size=\"18\" color=\"#ccc\"></u-icon>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 作者列表 -->\n    <view class=\"author-list\">\n      <view v-if=\"isLoading && authorList.length === 0\" class=\"loading-wrapper\">\n        <u-loading-icon mode=\"spinner\" color=\"#6AC086\" size=\"40\"></u-loading-icon>\n        <text class=\"loading-text\">搜索中...</text>\n      </view>\n      \n      <view v-else-if=\"authorList.length === 0 && !isLoading\" class=\"empty-wrapper\">\n        <u-icon name=\"account\" size=\"80\" color=\"#ddd\"></u-icon>\n        <text class=\"empty-text\">暂无相关作者</text>\n        <view class=\"create-btn\" @click=\"createNewAuthor\">\n          <u-icon name=\"plus\" size=\"16\" color=\"#6AC086\" style=\"margin-right: 8rpx;\"></u-icon>\n          <text class=\"create-text\">创建新作者</text>\n        </view>\n      </view>\n      \n      <view v-else>\n        <view \n          v-for=\"author in authorList\" \n          :key=\"author.id\"\n          class=\"author-item\"\n          @click=\"selectAuthor(author)\"\n        >\n          <image \n            v-if=\"author.avatar\" \n            :src=\"author.avatar\" \n            class=\"avatar\"\n            mode=\"aspectFill\"\n          ></image>\n          <view v-else class=\"avatar-placeholder\">\n            {{ author.name.charAt(0) }}\n          </view>\n          \n          <view class=\"author-info\">\n            <text class=\"name\">{{ author.name }}</text>\n            <text v-if=\"author.category\" class=\"category\">{{ author.category }}</text>\n            <text v-if=\"author.description\" class=\"description\">{{ author.description }}</text>\n            <view class=\"stats\">\n              <text class=\"quote-count\">{{ author.quote_count || 0 }} 条摘录</text>\n            </view>\n          </view>\n          \n          <u-icon name=\"arrow-right\" size=\"16\" color=\"#ccc\" class=\"arrow\"></u-icon>\n        </view>\n        \n        <!-- 加载更多 -->\n        <view v-if=\"hasMore\" class=\"load-more\" @click=\"loadMore\">\n          <u-loading-icon v-if=\"isLoadingMore\" mode=\"spinner\" color=\"#6AC086\" size=\"20\"></u-loading-icon>\n          <text class=\"load-more-text\">{{ isLoadingMore ? '加载中...' : '加载更多' }}</text>\n        </view>\n        \n        <!-- 创建新作者按钮 -->\n        <view class=\"create-new-section\">\n          <view class=\"create-btn\" @click=\"createNewAuthor\">\n            <u-icon name=\"plus\" size=\"16\" color=\"#6AC086\" style=\"margin-right: 8rpx;\"></u-icon>\n            <text class=\"create-text\">创建新作者</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.author-search-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  \n  .search-section {\n    padding: 32rpx;\n    background-color: white;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .search-box {\n      display: flex;\n      align-items: center;\n      background-color: #f8f9fa;\n      border-radius: 50rpx;\n      padding: 24rpx 32rpx;\n      \n      .search-icon {\n        margin-right: 16rpx;\n        flex-shrink: 0;\n      }\n      \n      .search-input {\n        flex: 1;\n        font-size: 32rpx;\n        color: #333;\n        \n        &::placeholder {\n          color: #999;\n        }\n      }\n      \n      .clear-btn {\n        margin-left: 16rpx;\n        flex-shrink: 0;\n      }\n    }\n  }\n  \n  .author-list {\n    flex: 1;\n    \n    .loading-wrapper,\n    .empty-wrapper {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 120rpx 32rpx;\n      \n      .loading-text,\n      .empty-text {\n        margin-top: 32rpx;\n        font-size: 28rpx;\n        color: #999;\n      }\n      \n      .create-btn {\n        margin-top: 48rpx;\n        display: flex;\n        align-items: center;\n        padding: 24rpx 48rpx;\n        background-color: #6AC086;\n        border-radius: 50rpx;\n        \n        .create-text {\n          font-size: 28rpx;\n          color: white;\n          font-weight: 500;\n        }\n      }\n    }\n    \n    .author-item {\n      display: flex;\n      align-items: center;\n      padding: 32rpx;\n      background-color: white;\n      border-bottom: 1rpx solid #f0f0f0;\n      min-height: 88rpx;\n      \n      &:active {\n        background-color: #f8f9fa;\n      }\n      \n      .avatar {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: 50%;\n        margin-right: 32rpx;\n        flex-shrink: 0;\n      }\n      \n      .avatar-placeholder {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: 50%;\n        background-color: #6AC086;\n        color: white;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 32rpx;\n        font-weight: 500;\n        margin-right: 32rpx;\n        flex-shrink: 0;\n      }\n      \n      .author-info {\n        flex: 1;\n        \n        .name {\n          display: block;\n          font-size: 32rpx;\n          color: #333;\n          font-weight: 500;\n          line-height: 1.4;\n          margin-bottom: 8rpx;\n        }\n        \n        .category {\n          display: block;\n          font-size: 24rpx;\n          color: #6AC086;\n          margin-bottom: 8rpx;\n        }\n        \n        .description {\n          display: block;\n          font-size: 24rpx;\n          color: #666;\n          line-height: 1.4;\n          margin-bottom: 8rpx;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n        \n        .stats {\n          .quote-count {\n            font-size: 24rpx;\n            color: #999;\n          }\n        }\n      }\n      \n      .arrow {\n        margin-left: 16rpx;\n        flex-shrink: 0;\n      }\n    }\n    \n    .load-more {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 48rpx 32rpx;\n      background-color: white;\n      border-bottom: 1rpx solid #f0f0f0;\n      \n      .load-more-text {\n        margin-left: 16rpx;\n        font-size: 28rpx;\n        color: #666;\n      }\n    }\n    \n    .create-new-section {\n      padding: 48rpx 32rpx;\n      background-color: white;\n      display: flex;\n      justify-content: center;\n      \n      .create-btn {\n        display: flex;\n        align-items: center;\n        padding: 24rpx 48rpx;\n        background-color: #6AC086;\n        border-radius: 50rpx;\n        \n        .create-text {\n          font-size: 28rpx;\n          color: white;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/author/search.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "searchKeyword", "ref", "authorList", "isLoading", "isLoadingMore", "hasMore", "currentPage", "pages", "getCurrentPages", "isSelectMode", "length", "options", "type", "searchAuthorList", "async", "isLoadMore", "value", "response", "searchAuthors", "uid", "store", "userInfo", "token", "keyword", "page", "page_size", "status", "newAuthors", "data", "authors", "showToast", "title", "msg", "icon", "error", "console", "uni", "index", "select<PERSON><PERSON><PERSON>", "author", "$emit", "navigateBack", "navigateTo", "url", "id", "createNewAuthor", "encodeURIComponent", "searchTimer", "handleSearch", "clearTimeout", "setTimeout", "loadMore", "onMounted", "$on", "new<PERSON><PERSON><PERSON>", "onUnmounted", "$off", "wx", "createPage", "MiniProgramPage"], "mappings": "mqBAIA,MAAMA,EAAe,IAAW,qEAG1B,MAAAC,EAAgBC,EAAAA,IAAI,IACpBC,EAAaD,EAAAA,IAAI,IACjBE,EAAYF,EAAAA,KAAI,GAChBG,EAAgBH,EAAAA,KAAI,GACpBI,EAAUJ,EAAAA,KAAI,GACdK,EAAcL,EAAAA,IAAI,GAIlBM,EAAQC,kBAERC,EAAoD,WAD9BF,EAAMA,EAAMG,OAAS,GACRC,QAAQC,KAG3CC,EAAmBC,MAAOC,GAAa,KACvC,KAAAZ,EAAUa,OAAUD,GAAcX,EAAcY,OAAhD,CAEAD,EACFX,EAAcY,OAAQ,GAEtBb,EAAUa,OAAQ,EAClBV,EAAYU,MAAQ,EACpBd,EAAWc,MAAQ,GACnBX,EAAQW,OAAQ,GAGd,IACI,MAAAC,QAAiBC,gBAAc,CACnCC,IAAKC,EAAAA,MAAMC,SAASF,IACpBG,MAAOF,EAAAA,MAAMC,SAASC,MACtBC,QAASvB,EAAcgB,MACvBQ,KAAMlB,EAAYU,MAClBS,UA1BW,KA6BT,GAAoB,OAApBR,EAASS,OAAiB,CAC5B,MAAMC,EAAaV,EAASW,KAAKC,SAAW,GAG1C3B,EAAWc,MADTD,EACiB,IAAIb,EAAWc,SAAUW,GAEzBA,EAGbtB,EAAAW,MAtCG,KAsCKW,EAAWjB,OACvBL,EAAQW,OACEV,EAAAU,OAEpB,cACUc,UAAU,CAAEC,MAAOd,EAASe,KAAO,OAAQC,KAAM,QAExD,OAAQC,GACCC,QAAAD,MAAM,UAAWA,GACzBE,EAAGC,MAACP,UAAU,CAAEC,MAAO,aAAcE,KAAM,QAC/C,CAAY,QACR9B,EAAUa,OAAQ,EAClBZ,EAAcY,OAAQ,CACxB,CA1C4D,CA0C5D,EAIIsB,EAAgBC,IAChB9B,GAEF2B,EAAAA,MAAII,MAAM,iBAAkBD,GAC5BH,EAAGC,MAACI,gBAGJL,EAAAA,MAAIM,WAAW,CACbC,IAAK,wCAAwCJ,EAAOK,MAExD,EAIIC,EAAkB,KAClBpC,EACF2B,EAAAA,MAAIM,WAAW,CACbC,IAAK,yDAAyDG,mBAAmB9C,EAAcgB,WAGjGoB,EAAAA,MAAIM,WAAW,CACbC,IAAK,qCAET,EAIF,IAAII,EAAc,KAClB,MAAMC,EAAe,KACnBC,aAAaF,GACbA,EAAcG,YAAW,WAEtB,IAAG,EAIFC,EAAW,KACX9C,EAAQW,QAAUZ,EAAcY,OAClCH,GAAiB,EACnB,SAIFuC,EAAAA,WAAU,YAKVhB,EAAAA,MAAIiB,IAAI,iBAAkBC,IACpB7C,EACF6B,EAAagB,MAIf,IAIFC,EAAAA,aAAY,aACNC,KAAK,gBAAe,woCC9H1BC,GAAGC,WAAWC"}