"use strict";const e=require("../../../common/vendor.js"),a=require("../../../api/index.js"),o=require("../../../utils/request.js"),t=require("../../../store/index.js"),s=require("../../../utils/index.js");if(require("../../../utils/BaseUrl.js"),require("../../../utils/systemInfo.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-sticky")+e.resolveComponent("u-input")+e.resolveComponent("u-icon")+e.resolveComponent("u-textarea")+e.resolveComponent("u-upload")+e.resolveComponent("u-gap")+e.resolveComponent("u-button")+e.resolveComponent("u-safe-bottom")+e.resolveComponent("u-picker")+e.resolveComponent("u-datetime-picker")+e.resolveComponent("u-popup"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-sticky/u-sticky.js")+(()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-safe-bottom/u-safe-bottom.js")+(()=>"../../../node-modules/uview-plus/components/u-picker/u-picker.js")+(()=>"../../../node-modules/uview-plus/components/u-datetime-picker/u-datetime-picker.js")+(()=>"../../../node-modules/uview-plus/components/u-popup/u-popup.js"))();const l={__name:"edit",setup(l){const u=e.ref({avatar:"",nickname:"",birthday:"",mobile:"",sex:"",gexingqianming:"",city:"",labels:[],imgs:[]}),n=e.ref([]),i=e.ref([["男","女"]]),r=e.ref(!1),d=e.ref(!1),p=e.ref(Date.now()),c=e.ref(!1),m=e.ref(""),v=e.ref(0),f=e.ref(0);e.onPageScroll((e=>{f.value=e.scrollTop})),e.onLoad((()=>{u.value.avatar=t.store().$state.userInfo.avatar,u.value.nickname=t.store().$state.userInfo.nickname,u.value.birthday=t.store().$state.userInfo.birthday,u.value.mobile=t.store().$state.userInfo.mobile,u.value.sex=t.store().$state.userInfo.sex,u.value.gexingqianming=t.store().$state.userInfo.gexingqianming,u.value.city=t.store().$state.userInfo.city||"",u.value.labels=t.store().$state.userInfo.labels,u.value.imgs=t.store().$state.userInfo.imgs,u.value.imgs.map((e=>n.value.push({url:e.img_url})))})),e.onReady((async()=>{const e=await s.getListHeight("bottomBox");v.value=e.height}));const g=async o=>{const l=await a.setPhone({code:o.detail.code,token:t.store().$state.userInfo.token,uid:t.store().$state.userInfo.uid});"ok"===l.status?(e.index.$u.toast(l.msg),s.getUserInfo()):e.index.$u.toast(l.msg)},x=async e=>{n.value.splice(e.index,1);"ok"===(await a.userdel_img({ids:u.value.imgs[e.index].id})).status&&t.store().$state.userInfo.imgs.forEach((a=>{a.id===u.value.imgs[e.index].id&&t.store().$state.userInfo.imgs.splice(t.store().$state.userInfo.imgs.indexOf(a),1)}))},h=async e=>{let o=[].concat(e.file),s=n.value.length;o.map((e=>{n.value.push({...e,status:"uploading",message:"上传中"})}));for(let l=0;l<o.length;l++){const e=await a.upload_img(o[l].url);let u=n.value[s];if(n.value.splice(s,1,{...u,status:"success",message:"",url:e.data}),s++,"ok"===e.status){const o=await a.useradd_img({img_url:e.data});"ok"===o.status&&t.store().$state.userInfo.imgs.push({id:o.data,img_url:e.data})}}},y=async e=>{u.value.sex=e.indexs[0]+1,r.value=!1},b=a=>{u.value.birthday=e.index.$u.timeFormat(a.value,"yyyy-mm-dd"),d.value=!1},k=async()=>{const o=await a.useradd_label({label:m.value});"ok"==o.status?t.store().$state.userInfo.labels.unshift({label:m.value,id:o.data}):e.index.$u.toast(o.msg)},w=async o=>{const t=await a.upload_img(o.detail.avatarUrl);"ok"===t.status?u.value.avatar=null==t?void 0:t.data:e.index.$u.toast(t.msg)},$=e=>u.value.nickname=e,I=e=>u.value.nickname=e,j=async()=>{"ok"===(await o.requset.p("user/update",{avatar:u.value.avatar,nickname:u.value.nickname,sex:u.value.sex,birthday:u.value.birthday,gexingqianming:u.value.gexingqianming,city:u.value.city,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token})).status&&s.back({tip:"修改成功，即将返回上级页面",time:2e3})};return(o,s)=>({a:e.p({bgColor:"#f8f8f8",title:"修改个人信息",height:"200rpx"}),b:e.p({"z-index":"2"}),c:u.value.avatar,d:f.value>17?"1":"20",e:`${e.unref(t.store)().$state.url}editMyInfo.png`,f:e.o(w),g:e.o(w),h:f.value>17?"1":"20",i:f.value>17?"1":"20",j:e.o($),k:e.o(I),l:e.o((e=>u.value.nickname=e)),m:e.p({type:"nickname","input-align":"right",placeholder:"请输入昵称",border:"none","placeholder-style":"font-size:26rpx;color:#aaa",customStyle:{fontSize:"26rpx"},modelValue:u.value.nickname}),n:e.t(u.value.birthday||"请选择出生日期"),o:u.value.birthday?"":"#aaa",p:e.o((e=>d.value=!0)),q:e.o((a=>e.unref(t.store)().$state.userInfo.uid=a)),r:e.p({disabled:!0,"disabled-color":"#fff","input-align":"right",placeholder:"请输入ID",border:"none","placeholder-style":"font-size:26rpx;color:#aaa",customStyle:{fontSize:"26rpx"},modelValue:e.unref(t.store)().$state.userInfo.uid}),s:e.o((e=>u.value.mobile=e)),t:e.p({"input-align":"right",maxlength:"11",placeholder:"绑定手机号",disabled:!0,border:"none","placeholder-style":"font-size:26rpx;color:#aaa",customStyle:{fontSize:"26rpx",background:"transparent"},modelValue:u.value.mobile}),v:e.o(g),w:e.t(1==u.value.sex?"男":2==u.value.sex?"女":"请选择性别"),x:u.value.sex?"":"#aaa",y:e.o((e=>r.value=!0)),z:e.o((e=>u.value.city=e)),A:e.p({"input-align":"right",placeholder:"请输入常住城市",border:"none","placeholder-style":"font-size:26rpx;color:#aaa",customStyle:{fontSize:"26rpx"},maxlength:"50",modelValue:u.value.city}),B:e.p({name:"arrow-right"}),C:e.o((e=>c.value=!0)),D:e.o((e=>u.value.gexingqianming=e)),E:e.p({radius:"20",height:"143rpx",placeholder:"说点什么，让大家更了解你~","placeholder-style":"font-size:26rpx;color:#aaa",modelValue:u.value.gexingqianming}),F:e.p({color:"#BFBFBF",size:"80rpx",name:"plus"}),G:e.o(h),H:e.o(x),I:e.p({fileList:n.value,name:"1",multiple:!0,maxCount:9,width:"215rpx",height:"215rpx"}),J:e.p({height:v.value}),K:e.o(j),L:e.p({text:"保存",shape:"circle",color:"linear-gradient(90deg, #88D7A0 0%, #6AC086 100%)",customStyle:{marginTop:"20rpx",height:"90rpx",color:"#ffffff",fontSize:"30rpx"}}),M:e.o((e=>r.value=!1)),N:e.o((e=>r.value=!1)),O:e.o(y),P:e.p({show:r.value,columns:i.value,"close-on-click-overlay":!0}),Q:e.o((e=>d.value=!1)),R:e.o((e=>d.value=!1)),S:e.o(b),T:e.o((e=>p.value=e)),U:e.p({show:d.value,mode:"date",minDate:1*new Date(1900,0,1),"close-on-click-overlay":!0,modelValue:p.value}),V:e.o((e=>m.value=e)),W:e.p({placeholder:"请输入标签（最多10字）",border:"none",maxlength:"10",customStyle:{width:"500rpx"},modelValue:m.value}),X:e.o(k),Y:e.p({shape:"circle",text:"添加",color:"linear-gradient(90deg, #88D7A0 0%, #6AC086 100%)",customStyle:{margin:"0",width:"88rpx",border:"none",color:"#ffffff",fontSize:"28rpx"}}),Z:e.f(u.value.labels,((o,s,l)=>({a:e.t(o.label),b:e.o((s=>(async o=>{const s=await a.userdel_label({id:o});"ok"==s.status?t.store().$state.userInfo.labels.forEach((e=>{e.id===o&&t.store().$state.userInfo.labels.splice(t.store().$state.userInfo.labels.indexOf(e),1)})):e.index.$u.toast(s.msg)})(o.id)),s),c:"4b3c3e68-18-"+l+",4b3c3e68-15",d:s}))),aa:e.p({size:"30rpx",name:"close-circle-fill"}),ab:e.o((e=>c.value=!1)),ac:e.p({color:"linear-gradient(90deg, #88D7A0 0%, #6AC086 100%)",text:"完成",customStyle:{height:"90rpx",color:"#ffffff",fontWeight:"500"}}),ad:e.o((e=>c.value=!1)),ae:e.p({show:c.value,"close-on-click-overlay":!0,round:"20"})})},__runtimeHooks:1};wx.createPage(l);
//# sourceMappingURL=edit.js.map
