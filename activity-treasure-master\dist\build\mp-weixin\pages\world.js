"use strict";const e=require("../common/vendor.js"),o=require("../api/index.js"),a=require("../store/index.js"),t=require("../utils/index.js"),l=require("../utils/auth.js");if(require("../utils/request.js"),require("../utils/BaseUrl.js"),require("../utils/cacheManager.js"),require("../utils/systemInfo.js"),require("../store/counter.js"),!Array){(e.resolveComponent("u-tabs")+e.resolveComponent("u-sticky")+e.resolveComponent("u-icon"))()}Math||((()=>"../node-modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../node-modules/uview-plus/components/u-sticky/u-sticky.js")+r+s+n+d+(()=>"../node-modules/uview-plus/components/u-icon/u-icon.js")+u)();const r=()=>"./bundle/world/card/index.js",s=()=>"./bundle/world/feed/index.js",n=()=>"./bundle/world/diary/index.js",d=()=>"./bundle/world/quote/index.js",u=()=>"../components/CustomTabBar.js",c={__name:"world",setup(u){const c=e.ref(null),i=e.ref(null),v=e.ref(null);console.log("world.vue: 组件导入检查",{CardIndex:!!r,FeedIndex:!!s,DiaryIndex:!!n,QuoteIndex:!!d});const g=e.ref(0),w=e.ref([{name:"日卡"},{name:"动态"},{name:"日记"},{name:"摘录"}]),f=e.ref([]),p=e.ref(!0),y=e.ref(""),b=e.ref(!1),m=e.ref(!1),x=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,h=async()=>{var e,t;console.log("world.vue: loadCardData triggered"),p.value=!0,y.value="";try{const r=new Date,s=new Date;s.setDate(r.getDate()-15);const n={startDate:x(s),endDate:x(r),uid:(null==(e=a.store().$state.userInfo)?void 0:e.uid)||0,token:a.store().$state.token||""};console.log("world.vue: Calling getDailyCards with params:",n);let d=0;const u=3;let c;for(;d<u;)try{c=await o.getDailyCards(n),console.log("world.vue: API response:",c);break}catch(l){if(d++,console.error(`world.vue: Retry ${d}/${u} failed:`,l),d>=u)throw l;await new Promise((e=>setTimeout(e,1e3)))}"ok"===c.status&&(null==(t=c.data)?void 0:t.cards)?(console.log("world.vue: Successfully received card data:",c.data.cards),console.log("world.vue: Card data length:",c.data.cards.length),c.data.cards.length>0&&console.log("world.vue: First card sample:",c.data.cards[0]),f.value=c.data.cards.sort(((e,o)=>new Date(e.date)-new Date(o.date)))):"empty"===c.status?(console.log("world.vue: API returned empty status"),f.value=[],y.value="暂无日卡数据"):(console.warn("world.vue: API returned error status:",c.status,c.msg),f.value=[],y.value=c.msg||"获取日卡失败")}catch(r){console.error("world.vue: Error loading card data:",r),f.value=[],r.message.includes("getDailyCards is not a function")||r.message.includes("undefined")?y.value="API函数缺失，请手动修复api/index.js":y.value="加载失败，请稍后重试",f.value=[]}finally{p.value=!1}},D=e=>{const o="number"==typeof e?e:e.index;if(g.value!==o)switch(console.log(`Tab切换: ${g.value} -> ${o}`),g.value=o,o){case 0:console.log("切换到日卡tab，加载日卡数据"),h();break;case 1:console.log("切换到动态tab，加载动态数据"),I();break;case 2:console.log("切换到日记tab，加载日记数据"),k();break;case 3:console.log("切换到摘录tab，加载摘录数据"),j();break;default:console.warn(`未知的tab索引: ${o}`)}else console.log(`Tab ${o} 已经是当前选中状态，跳过切换`)},I=async()=>{try{console.log("开始加载动态数据");const e=a.store().$state.userInfo;e&&e.uid||console.warn("用户未登录，但仍可查看动态数据"),c.value&&c.value.loadFeedData?(console.log("调用FeedIndex组件的loadFeedData方法"),c.value.loadFeedData()):console.warn("FeedIndex组件ref未准备好")}catch(o){console.error("加载动态数据失败:",o),e.index.showToast({title:"加载失败",icon:"none"})}},k=async()=>{try{console.log("world.vue: 切换到日记tab，加载日记数据");const e=a.store().$state.userInfo;e&&e.uid||console.warn("world.vue: 用户未登录，但仍可查看日记数据"),i.value&&i.value.loadDiaryData?(console.log("调用DiaryIndex组件的loadDiaryData方法"),i.value.loadDiaryData()):console.warn("DiaryIndex组件ref未准备好")}catch(o){console.error("world.vue: 加载日记数据失败:",o),e.index.showToast({title:"加载失败",icon:"none"})}},j=async()=>{try{console.log("world.vue: 切换到摘录tab，加载摘录数据");const e=a.store().$state.userInfo;e&&e.uid||console.warn("world.vue: 用户未登录，但仍可查看摘录数据"),v.value&&v.value.loadQuoteData?(console.log("调用QuoteIndex组件的loadQuoteData方法"),v.value.loadQuoteData()):console.warn("QuoteIndex组件ref未准备好")}catch(o){console.error("world.vue: 加载摘录数据失败:",o),e.index.showToast({title:"加载失败",icon:"none"})}},q=()=>{console.log("FAB按钮被点击，当前状态:",{isFabRotated:m.value,showFabOptions:b.value}),m.value=!m.value,b.value=!b.value,console.log("FAB按钮状态更新后:",{isFabRotated:m.value,showFabOptions:b.value})},F=()=>{m.value=!1,b.value=!1},C=o=>{if(console.log("FAB Option Clicked:",o),F(),l.requireLogin("","请先登录后再发布内容"))switch(o){case"feed":try{t.navto("/pages/bundle/world/feed/post")}catch(a){console.error("导航到动态发布页面失败:",a),e.index.showToast({title:"页面跳转失败",icon:"none"})}break;case"diary":try{console.log("跳转到日记发布页面"),t.navto("/pages/bundle/world/diary/post")}catch(a){console.error("导航到日记发布页面失败:",a),e.index.showToast({title:"页面跳转失败",icon:"none"})}break;case"quote":try{console.log("跳转到摘录发布页面"),t.navto("/pages/bundle/world/quote/post")}catch(a){console.error("导航到摘录发布页面失败:",a),e.index.showToast({title:"页面跳转失败",icon:"none"})}break;default:console.warn("未知的FAB选项:",o)}};return e.onLoad((()=>{var e;console.log("world.vue: onLoad triggered. Current tab:",g.value),console.log("world.vue: Store state:",{userInfo:a.store().$state.userInfo,token:a.store().$state.token,uid:null==(e=a.store().$state.userInfo)?void 0:e.uid}),console.log("world.vue: Loading card data on onLoad (default)"),h()})),(o,a)=>e.e({a:e.o(D),b:e.p({list:w.value,current:g.value,lineColor:"#FFD700",activeStyle:{color:"#000000",fontWeight:"bold"},inactiveStyle:{color:"#666666"},itemStyle:"padding-left: 15px; padding-right: 15px; height: 48px;"}),c:e.p({"offset-top":"0"}),d:e.p({cards:f.value,loading:p.value,error:y.value}),e:0===g.value,f:e.sr(c,"237ddecc-3",{k:"feedIndexRef"}),g:1===g.value,h:e.sr(i,"237ddecc-4",{k:"diaryIndexRef"}),i:2===g.value,j:e.sr(v,"237ddecc-5",{k:"quoteIndexRef"}),k:3===g.value,l:b.value},b.value?{m:e.p({name:"chat",color:"#6AC086",size:"20"}),n:b.value?1:"",o:e.o((e=>C("feed"))),p:e.p({name:"edit-pen",color:"#6AC086",size:"20"}),q:b.value?1:"",r:e.o((e=>C("diary"))),s:e.p({name:"bookmark",color:"#6AC086",size:"20"}),t:b.value?1:"",v:e.o((e=>C("quote")))}:{},{w:e.p({name:"edit-pen",color:"#ffffff",size:"28"}),x:m.value?1:"",y:e.o(q),z:e.p({current:1}),A:e.o(F)})}},i=e._export_sfc(c,[["__scopeId","data-v-237ddecc"]]);wx.createPage(i);
//# sourceMappingURL=world.js.map
