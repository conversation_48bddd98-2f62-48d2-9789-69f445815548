{"version": 3, "file": "create.js", "sources": ["../../../../../../../src/pages/bundle/world/source/create.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXHNvdXJjZVxjcmVhdGUudnVl"], "sourcesContent": ["<script setup>\nimport { ref, onMounted } from 'vue';\nimport { createSource, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 创建出处页面. Confirmed via 寸止 }}\nconst formData = ref({\n  name: '',\n  category: '',\n  description: '',\n  publisher: '',\n  publish_year: '',\n  isbn: '',\n  url: '',\n  cover_image: ''\n});\n\nconst isSubmitting = ref(false);\nconst coverList = ref([]);\n\n// 获取页面参数\nconst pages = getCurrentPages();\nconst currentPageInstance = pages[pages.length - 1];\nconst isSelectMode = currentPageInstance.options.type === 'select';\nconst presetKeyword = currentPageInstance.options.keyword || '';\n\n// 常用出处类别\nconst categoryOptions = [\n  '图书', '期刊', '报纸', '网站', '博客', \n  '学术论文', '演讲', '电影', '纪录片', '播客',\n  '社交媒体', '访谈', '会议', '其他'\n];\n\n// 处理封面上传\nconst handleCoverRead = async (event) => {\n  let lists = [].concat(event.file);\n  \n  // 只能上传一张封面\n  if (coverList.value.length > 0) {\n    uni.showToast({ title: '只能上传一张封面，将替换现有封面', icon: 'none' });\n    coverList.value = [];\n  }\n  \n  if (lists.length > 1) {\n    uni.showToast({ title: '只能上传一张封面，已自动选择第一张', icon: 'none' });\n    lists = [lists[0]];\n  }\n  \n  let fileListLen = coverList.value.length;\n  \n  lists.map((item) => {\n    coverList.value.push({\n      ...item,\n      status: 'uploading',\n      message: '上传中'\n    });\n  });\n  \n  for (let i = 0; i < lists.length; i++) {\n    const currentFileIndex = fileListLen + i;\n    try {\n      const res = await upload_img(lists[i]);\n      if (res && res.url) {\n        coverList.value[currentFileIndex].status = 'success';\n        coverList.value[currentFileIndex].url = res.url;\n        formData.value.cover_image = res.url;\n      } else {\n        throw new Error('上传失败');\n      }\n    } catch (error) {\n      coverList.value[currentFileIndex].status = 'failed';\n      coverList.value[currentFileIndex].message = '上传失败';\n      uni.showToast({ title: '封面上传失败', icon: 'none' });\n    }\n  }\n};\n\n// 删除封面\nconst handleDeleteCover = (event) => {\n  coverList.value.splice(event.index, 1);\n  formData.value.cover_image = '';\n};\n\n// 选择类别\nconst selectCategory = () => {\n  uni.showActionSheet({\n    itemList: categoryOptions,\n    success: (res) => {\n      formData.value.category = categoryOptions[res.tapIndex];\n    }\n  });\n};\n\n// 提交表单\nconst submitForm = async () => {\n  // 验证必填字段\n  if (!formData.value.name.trim()) {\n    uni.showToast({ title: '请输入出处名称', icon: 'none' });\n    return;\n  }\n  \n  // 验证URL格式\n  if (formData.value.url && !isValidUrl(formData.value.url)) {\n    uni.showToast({ title: '请输入正确的网址格式', icon: 'none' });\n    return;\n  }\n  \n  if (isSubmitting.value) return;\n  isSubmitting.value = true;\n  \n  try {\n    const params = {\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      name: formData.value.name.trim(),\n      category: formData.value.category.trim(),\n      description: formData.value.description.trim(),\n      publisher: formData.value.publisher.trim(),\n      publish_year: formData.value.publish_year.trim(),\n      isbn: formData.value.isbn.trim(),\n      url: formData.value.url.trim(),\n      cover_image: formData.value.cover_image\n    };\n    \n    const response = await createSource(params);\n    \n    if (response.status === 'ok') {\n      uni.showToast({ title: '创建成功', icon: 'success' });\n      \n      // 如果是选择模式，发送创建结果给父页面\n      if (isSelectMode) {\n        const newSource = {\n          id: response.data.source_id,\n          name: formData.value.name,\n          category: formData.value.category,\n          description: formData.value.description,\n          publisher: formData.value.publisher,\n          cover_image: formData.value.cover_image,\n          quote_count: 0\n        };\n        uni.$emit('sourceCreated', newSource);\n      }\n      \n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1000);\n    } else if (response.status === 'relogin') {\n      uni.showToast({ title: '请先登录', icon: 'none' });\n    } else {\n      uni.showToast({ title: response.msg || '创建失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('创建出处失败:', error);\n    uni.showToast({ title: '创建失败，请稍后重试', icon: 'none' });\n  } finally {\n    isSubmitting.value = false;\n  }\n};\n\n// 验证URL格式\nconst isValidUrl = (url) => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return /^https?:\\/\\/.+/.test(url);\n  }\n};\n\n// 页面加载时预填关键词\nonMounted(() => {\n  if (presetKeyword) {\n    formData.value.name = decodeURIComponent(presetKeyword);\n  }\n});\n</script>\n\n<template>\n  <view class=\"create-source-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar \n      :title=\"isSelectMode ? '创建出处' : '新建出处'\" \n      backIcon=\"arrow-left\"\n    />\n    \n    <!-- 表单内容 -->\n    <view class=\"form-container\">\n      <!-- 封面上传 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">封面 (选填)</view>\n        <view class=\"cover-upload\">\n          <u-upload\n            :fileList=\"coverList\"\n            @afterRead=\"handleCoverRead\"\n            @delete=\"handleDeleteCover\"\n            name=\"file\"\n            :maxCount=\"1\"\n            :previewImage=\"true\"\n            width=\"200rpx\"\n            height=\"260rpx\"\n            uploadIconColor=\"#6AC086\"\n          ></u-upload>\n        </view>\n      </view>\n      \n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">基本信息</view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">名称 *</view>\n          <input \n            v-model=\"formData.name\" \n            placeholder=\"请输入出处名称\"\n            class=\"item-input\"\n            maxlength=\"100\"\n          />\n        </view>\n        \n        <view class=\"form-item\" @click=\"selectCategory\">\n          <view class=\"item-label\">类别</view>\n          <view class=\"item-input\" :class=\"{ 'placeholder': !formData.category }\">\n            {{ formData.category || '请选择出处类别' }}\n          </view>\n          <u-icon name=\"arrow-right\" size=\"16\" color=\"#ccc\"></u-icon>\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">出版社</view>\n          <input \n            v-model=\"formData.publisher\" \n            placeholder=\"请输入出版社或发布机构\"\n            class=\"item-input\"\n            maxlength=\"100\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">出版年份</view>\n          <input \n            v-model=\"formData.publish_year\" \n            placeholder=\"如：2023\"\n            class=\"item-input\"\n            type=\"number\"\n            maxlength=\"4\"\n          />\n        </view>\n      </view>\n      \n      <!-- 标识信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">标识信息 (选填)</view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">ISBN</view>\n          <input \n            v-model=\"formData.isbn\" \n            placeholder=\"请输入ISBN号码\"\n            class=\"item-input\"\n            maxlength=\"20\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">网址</view>\n          <input \n            v-model=\"formData.url\" \n            placeholder=\"请输入相关网址\"\n            class=\"item-input\"\n            maxlength=\"500\"\n          />\n        </view>\n      </view>\n      \n      <!-- 描述信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">描述信息 (选填)</view>\n        \n        <view class=\"form-item\">\n          <view class=\"item-label\">简介</view>\n          <textarea \n            v-model=\"formData.description\" \n            placeholder=\"请输入出处简介或主要内容\"\n            class=\"item-textarea\"\n            maxlength=\"500\"\n            auto-height\n          ></textarea>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <view class=\"submit-btn\" @click=\"submitForm\" :class=\"{ 'submitting': isSubmitting }\">\n        <u-loading-icon v-if=\"isSubmitting\" mode=\"spinner\" color=\"white\" size=\"20\"></u-loading-icon>\n        <text class=\"submit-text\" v-if=\"!isSubmitting\">创建出处</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.create-source-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  padding-bottom: 120rpx;\n  \n  .form-container {\n    padding: 32rpx;\n    \n    .form-section {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 32rpx;\n      \n      .section-title {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 32rpx;\n      }\n      \n      .cover-upload {\n        display: flex;\n        justify-content: center;\n      }\n      \n      .form-item {\n        display: flex;\n        align-items: center;\n        padding: 24rpx 0;\n        border-bottom: 1rpx solid #f0f0f0;\n        min-height: 88rpx;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        .item-label {\n          width: 160rpx;\n          font-size: 32rpx;\n          color: #333;\n          flex-shrink: 0;\n        }\n        \n        .item-input {\n          flex: 1;\n          font-size: 32rpx;\n          color: #333;\n          \n          &.placeholder {\n            color: #999;\n          }\n        }\n        \n        .item-textarea {\n          flex: 1;\n          font-size: 32rpx;\n          color: #333;\n          min-height: 120rpx;\n          line-height: 1.6;\n        }\n      }\n    }\n  }\n  \n  .submit-section {\n    position: fixed;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    padding: 32rpx;\n    background-color: white;\n    border-top: 1rpx solid #f0f0f0;\n    \n    .submit-btn {\n      width: 100%;\n      height: 88rpx;\n      background-color: #6AC086;\n      border-radius: 50rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      \n      &.submitting {\n        opacity: 0.7;\n      }\n      \n      .submit-text {\n        font-size: 32rpx;\n        color: white;\n        font-weight: 500;\n        margin-left: 16rpx;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/source/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "formData", "ref", "name", "category", "description", "publisher", "publish_year", "isbn", "url", "cover_image", "isSubmitting", "coverList", "pages", "getCurrentPages", "currentPageInstance", "length", "isSelectMode", "options", "type", "presetKeyword", "keyword", "categoryOptions", "handleCoverRead", "async", "event", "lists", "concat", "file", "value", "uni", "index", "showToast", "title", "icon", "fileListLen", "map", "item", "push", "status", "message", "i", "currentFileIndex", "res", "upload_img", "Error", "error", "handleDeleteCover", "splice", "selectCategory", "showActionSheet", "itemList", "success", "tapIndex", "submitForm", "trim", "isValidUrl", "params", "uid", "store", "$state", "userInfo", "token", "response", "createSource", "newSource", "id", "data", "source_id", "quote_count", "$emit", "setTimeout", "navigateBack", "msg", "console", "URL", "test", "onMounted", "decodeURIComponent", "wx", "createPage", "MiniProgramPage"], "mappings": "8wBAIA,MAAMA,EAAe,IAAW,qEAG1B,MAAAC,EAAWC,EAAAA,IAAI,CACnBC,KAAM,GACNC,SAAU,GACVC,YAAa,GACbC,UAAW,GACXC,aAAc,GACdC,KAAM,GACNC,IAAK,GACLC,YAAa,KAGTC,EAAeT,EAAAA,KAAI,GACnBU,EAAYV,EAAAA,IAAI,IAGhBW,EAAQC,kBACRC,EAAsBF,EAAMA,EAAMG,OAAS,GAC3CC,EAAoD,WAArCF,EAAoBG,QAAQC,KAC3CC,EAAgBL,EAAoBG,QAAQG,SAAW,GAGvDC,EAAkB,CACtB,KAAM,KAAM,KAAM,KAAM,KACxB,OAAQ,KAAM,KAAM,MAAO,KAC3B,OAAQ,KAAM,KAAM,MAIhBC,EAAkBC,MAAOC,IAC7B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MAGxBhB,EAAUiB,MAAMb,OAAS,IAC3Bc,EAAGC,MAACC,UAAU,CAAEC,MAAO,mBAAoBC,KAAM,SACjDtB,EAAUiB,MAAQ,IAGhBH,EAAMV,OAAS,IACjBc,EAAGC,MAACC,UAAU,CAAEC,MAAO,oBAAqBC,KAAM,SAC1CR,EAAA,CAACA,EAAM,KAGb,IAAAS,EAAcvB,EAAUiB,MAAMb,OAE5BU,EAAAU,KAAKC,IACTzB,EAAUiB,MAAMS,KAAK,IAChBD,EACHE,OAAQ,YACRC,QAAS,OACV,IAGH,IAAA,IAASC,EAAI,EAAGA,EAAIf,EAAMV,OAAQyB,IAAK,CACrC,MAAMC,EAAmBP,EAAcM,EACnC,IACF,MAAME,QAAYC,EAAAA,WAAWlB,EAAMe,IAC/B,IAAAE,IAAOA,EAAIlC,IAKP,MAAA,IAAIoC,MAAM,QAJNjC,EAAAiB,MAAMa,GAAkBH,OAAS,UAC3C3B,EAAUiB,MAAMa,GAAkBjC,IAAMkC,EAAIlC,IACnCR,EAAA4B,MAAMnB,YAAciC,EAAIlC,GAIpC,OAAQqC,GACGlC,EAAAiB,MAAMa,GAAkBH,OAAS,SACjC3B,EAAAiB,MAAMa,GAAkBF,QAAU,OAC5CV,EAAGC,MAACC,UAAU,CAAEC,MAAO,SAAUC,KAAM,QACzC,CACF,GAIIa,EAAqBtB,IACzBb,EAAUiB,MAAMmB,OAAOvB,EAAMM,MAAO,GACpC9B,EAAS4B,MAAMnB,YAAc,EAAA,EAIzBuC,EAAiB,KACrBnB,EAAAA,MAAIoB,gBAAgB,CAClBC,SAAU7B,EACV8B,QAAUT,IACR1C,EAAS4B,MAAMzB,SAAWkB,EAAgBqB,EAAIU,SAAQ,GAEzD,EAIGC,EAAa9B,UAEjB,GAAKvB,EAAS4B,MAAM1B,KAAKoD,OAMrB,IAAAtD,EAAS4B,MAAMpB,KAAQ+C,EAAWvD,EAAS4B,MAAMpB,MAKrD,IAAIE,EAAakB,MAAjB,CACAlB,EAAakB,OAAQ,EAEjB,IACF,MAAM4B,EAAS,CACbC,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/B3D,KAAMF,EAAS4B,MAAM1B,KAAKoD,OAC1BnD,SAAUH,EAAS4B,MAAMzB,SAASmD,OAClClD,YAAaJ,EAAS4B,MAAMxB,YAAYkD,OACxCjD,UAAWL,EAAS4B,MAAMvB,UAAUiD,OACpChD,aAAcN,EAAS4B,MAAMtB,aAAagD,OAC1C/C,KAAMP,EAAS4B,MAAMrB,KAAK+C,OAC1B9C,IAAKR,EAAS4B,MAAMpB,IAAI8C,OACxB7C,YAAaT,EAAS4B,MAAMnB,aAGxBqD,QAAiBC,eAAaP,GAEhC,GAAoB,OAApBM,EAASxB,OAAiB,CAI5B,GAHAT,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,YAGjCjB,EAAc,CAChB,MAAMgD,EAAY,CAChBC,GAAIH,EAASI,KAAKC,UAClBjE,KAAMF,EAAS4B,MAAM1B,KACrBC,SAAUH,EAAS4B,MAAMzB,SACzBC,YAAaJ,EAAS4B,MAAMxB,YAC5BC,UAAWL,EAAS4B,MAAMvB,UAC1BI,YAAaT,EAAS4B,MAAMnB,YAC5B2D,YAAa,GAEfvC,EAAAA,MAAIwC,MAAM,gBAAiBL,EAC7B,CAEAM,YAAW,KACTzC,EAAGC,MAACyC,cAAY,GACf,IACT,KAAmC,YAApBT,EAASxB,OAClBT,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,iBAEjCF,UAAU,CAAEC,MAAO8B,EAASU,KAAO,OAAQvC,KAAM,QAExD,OAAQY,GACC4B,QAAA5B,MAAM,UAAWA,GACzBhB,EAAGC,MAACC,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC/C,CAAY,QACRvB,EAAakB,OAAQ,CACvB,CAjDwB,OAJtBC,EAAGC,MAACC,UAAU,CAAEC,MAAO,aAAcC,KAAM,cAN3CJ,EAAGC,MAACC,UAAU,CAAEC,MAAO,UAAWC,KAAM,QA2D1C,EAIIsB,EAAc/C,IACd,IAEK,OADP,IAAIkE,IAAIlE,IACD,CACX,CAAI,MACO,MAAA,iBAAiBmE,KAAKnE,EAC/B,UAIFoE,EAAAA,WAAU,KACJzD,IACOnB,EAAA4B,MAAM1B,KAAO2E,mBAAmB1D,GAC3C,42BC7KF2D,GAAGC,WAAWC"}