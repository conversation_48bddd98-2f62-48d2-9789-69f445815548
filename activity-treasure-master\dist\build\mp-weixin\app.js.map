{"version": 3, "file": "app.js", "sources": ["../../../src/App.vue", "../../../src/main.js"], "sourcesContent": ["<script setup>\nimport { onLaunch, onShow, onHide } from \"@dcloudio/uni-app\";\nimport { configapp, configpop } from \"@/api\";\nimport { store } from \"@/store\";\nimport { restoreLoginState } from \"@/utils/auth\";\n\n// {{ AURA-X: Add - 修复uView组件库使用已废弃API的警告. Confirmed via 寸止. }}\n// 全局替换已废弃的getSystemInfoSync API\nimport { getSystemInfo } from \"@/utils/systemInfo\";\n\n// 在应用启动前替换已废弃的API\nconst originalGetSystemInfoSync = uni.getSystemInfoSync;\nuni.getSystemInfoSync = () => {\n  try {\n    // 使用新的API替换方案\n    return getSystemInfo();\n  } catch (error) {\n    console.warn('新API获取系统信息失败，使用原始API:', error);\n    // 降级到原始API但不显示警告\n    return originalGetSystemInfoSync();\n  }\n};\n\n// 同时替换uView可能使用的$u.sys()方法\nif (uni.$u && uni.$u.sys) {\n  const originalSys = uni.$u.sys;\n  uni.$u.sys = () => {\n    try {\n      return getSystemInfo();\n    } catch (error) {\n      console.warn('$u.sys()新API失败，使用原始方法:', error);\n      return originalSys();\n    }\n  };\n}\n\nonLaunch(async () => {\n  try {\n    // 应用启动时首先恢复登录状态\n    console.log('应用启动，尝试恢复登录状态...');\n    const restored = restoreLoginState();\n    if (restored) {\n      console.log('登录状态恢复成功');\n    } else {\n      console.log('无有效的登录状态需要恢复');\n    }\n\n    const res = await configapp();\n\n    // 检查配置获取是否成功\n    if (res?.status === \"ok\" && res?.data) {\n      // 保存配置数据到store\n      store().changeConfig(res.data);\n    }\n\n    // 获取弹出公告\n    const popRes = await configpop();\n    if (popRes?.status == \"ok\") {\n      store().setPopContent(popRes.data);\n    }\n  } catch (error) {\n    console.error('获取App配置出错:', error);\n  }\n});\n\nonShow(() => {\n  // App显示时的处理\n});\n\nonHide(() => {\n  // App隐藏时的处理\n});\n\n\n</script>\n\n<template>\n  <!-- 主应用内容 -->\n  <view>\n    <slot></slot>\n  </view>\n</template>\n\n<style lang=\"scss\">\n@import \"uview-plus/index.scss\";\n@import \"./style/judu-theme.scss\";\n\n/* {{ AURA-X: Modify - 修复小程序不支持标签选择器的问题，改为class选择器. Confirmed via 寸止. }} */\n/* 修复小程序不支持标签选择器的问题 */\n.ql-editor {\n  /* 覆盖 ul 标签选择器样式 - 改为class选择器 */\n  .ul-style,\n  .ql-list-ul {\n    margin: 0;\n    padding: 0;\n    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n  }\n\n  /* 覆盖 ul > li 标签选择器样式 - 改为class选择器 */\n  .ul-li-style,\n  .ql-list-ul .ql-list-item {\n    list-style-type: none;\n  }\n\n  .ul-li-style:before,\n  .ql-list-ul .ql-list-item:before {\n    content: \"•\";\n  }\n}\n</style>\n<style lang=\"less\">\n@import url(\"style/base.less\");\n/*每个页面公共css */\n\n/* {{ AURA-X: Modify - 修复小程序不支持标签选择器问题，改为class选择器. Confirmed via 寸止. }} */\n/* 全局字体栈声明 - 微信小程序兼容版本 */\n.page-font-family {\n  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n  line-height: 1.6;\n}\n\n/* 具体组件字体声明 - 修复小程序不支持标签选择器问题 */\n.font-inherit {\n  font-family: inherit;\n}\n\n/* 为常用组件添加字体继承类 */\n.uni-view, .uni-text, .uni-button, .uni-input, .uni-textarea, .uni-picker, .uni-navigator {\n  font-family: inherit;\n}\n\n/* 确保所有文本元素继承字体 */\n.uni-input-input, .uni-textarea-textarea {\n  font-family: inherit;\n}\n\n\n\n\n\n\n\n/* 应用基础样式 */\n\n/* 全局色彩系统 - 基于新配色方案 */\n:root {\n  /* 主色调系统 */\n  --primary-color: #245D3C;        /* 深绿色 - 主色 */\n  --primary-light: #3AEE55;        /* 亮绿色 - 强调色 */\n  --primary-medium: #73C088;       /* 中绿色 - 辅助色 */\n  --primary-soft: #A5E1B8;         /* 浅绿色 - 柔和色 */\n  --primary-bg: #C8E6D1;           /* 极浅绿色 - 背景色 */\n\n  /* 中性色系统 */\n  --neutral-white: #FFFFFF;\n  --neutral-light: #F8F9FA;\n  --neutral-gray: #E5E5E5;\n  --neutral-dark: #333333;\n  --neutral-text: #666666;\n\n  /* 功能色系统 */\n  --success-color: #3AEE55;\n  --warning-color: #FF9500;\n  --error-color: #FF4757;\n  --info-color: #245D3C;\n\n  /* 阴影系统 */\n  --shadow-light: 0 4px 10px rgba(36, 93, 60, 0.05);\n  --shadow-medium: 0 8px 15px rgba(36, 93, 60, 0.1);\n  --shadow-heavy: 0 12px 24px rgba(36, 93, 60, 0.15);\n\n  /* 渐变系统 */\n  --gradient-primary: linear-gradient(135deg, #3AEE55 0%, #73C088 100%);\n  --gradient-soft: linear-gradient(135deg, #A5E1B8 0%, #C8E6D1 100%);\n  --gradient-bg: linear-gradient(180deg, #C8E6D1 0%, #F8F9FA 50%);\n}\n\n/* 全局按钮呼吸感阴影效果 */\n.btn-enhanced,\n.u-button,\n.publish-btn,\n.submit-btn,\n.action-btn,\n.primary-btn {\n  box-shadow: var(--shadow-light);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.btn-enhanced:hover,\n.u-button:hover,\n.publish-btn:hover,\n.submit-btn:hover,\n.action-btn:hover,\n.primary-btn:hover {\n  transform: scale(1.05);\n  box-shadow: var(--shadow-medium);\n}\n\n/* 玻璃拟态效果 */\n.glass-effect,\n.navbar-glass,\n.card-glass,\n.popup-glass {\n  background: rgba(255,255,255,0.6);\n  border: 1px solid rgba(255,255,255,0.8);\n  backdrop-filter: blur(12px);\n  -webkit-backdrop-filter: blur(12px);\n}\n\n/* 关键卡片玻璃效果 */\n.activity-card,\n.user-card,\n.world-card,\n.feed-card {\n  background: rgba(255,255,255,0.6);\n  border: 1px solid rgba(255,255,255,0.8);\n  backdrop-filter: blur(12px);\n  -webkit-backdrop-filter: blur(12px);\n  box-shadow: var(--shadow-light);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.activity-card:hover,\n.user-card:hover,\n.world-card:hover,\n.feed-card:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-medium);\n}\n\n/* 主题色彩应用 */\n.primary-color { color: var(--primary-color) !important; }\n.primary-bg { background-color: var(--primary-color) !important; }\n.primary-light-color { color: var(--primary-light) !important; }\n.primary-light-bg { background-color: var(--primary-light) !important; }\n.primary-gradient { background: var(--gradient-primary) !important; }\n.primary-soft-bg { background-color: var(--primary-soft) !important; }\n</style>\n", "import {\n\tcreateApp\n} from 'vue';\nimport App from './App.vue';\nimport {\n\tcreatePinia\n} from 'pinia';\nimport {\n\tcreateUnistorage\n} from 'pinia-plugin-unistorage'\nimport uviewPlus from \"uview-plus\";\nimport errorHandler, { handleError, ERROR_TYPES, ERROR_LEVELS } from './utils/errorHandler'\n\n// 移除字体阻止逻辑，恢复正常字体加载\n\n// 增强的全局错误处理\nconst enhancedErrorHandler = (err, vm, info) => {\n\tconsole.error('全局捕获的错误:', err);\n\tconsole.error('错误组件:', vm);\n\tconsole.error('错误详情:', info);\n\n\t// 使用新的错误处理系统\n\thandleError(err, ERROR_TYPES.SYSTEM, ERROR_LEVELS.ERROR, {\n\t\tcomponent: vm?.$options.name || 'Unknown',\n\t\terrorInfo: info\n\t});\n};\n\nconst app = createApp(App);\n\n// 配置全局错误处理\napp.config.errorHandler = enhancedErrorHandler;\n\n// 全局警告处理\napp.config.warnHandler = (msg, instance, trace) => {\n\tif (process.env.NODE_ENV === 'development') {\n\t\tconsole.warn('Vue警告:', msg, trace);\n\t}\n\thandleError(msg, ERROR_TYPES.SYSTEM, ERROR_LEVELS.WARNING, {\n\t\tcomponent: instance?.$options.name || 'Unknown',\n\t\ttrace\n\t});\n};\n\n\n\nconst store = createPinia();\nstore.use(createUnistorage())\napp.use(store).use(uviewPlus).mount('#app');\n\n// 未捕获的Promise错误处理（兼容小程序环境）\nif (typeof window !== 'undefined' && window.addEventListener) {\n\t// H5环境\n\twindow.addEventListener('unhandledrejection', (event) => {\n\t\tconsole.error('未捕获的Promise错误:', event.reason);\n\t\thandleError(event.reason, ERROR_TYPES.SYSTEM, ERROR_LEVELS.ERROR, {\n\t\t\ttype: 'unhandledrejection'\n\t\t});\n\t\tevent.preventDefault();\n\t});\n} else {\n\t// 小程序环境 - 使用uni-app的错误处理\n\tuni.onError && uni.onError((error) => {\n\t\tconsole.error('小程序错误:', error);\n\t\thandleError(error, ERROR_TYPES.SYSTEM, ERROR_LEVELS.ERROR, {\n\t\t\ttype: 'miniprogram_error'\n\t\t});\n\t});\n}"], "names": ["originalGetSystemInfoSync", "uni", "index", "getSystemInfoSync", "common_vendor", "getSystemInfo", "error", "console", "warn", "$u", "sys", "originalSys", "onLaunch", "async", "log", "restoreLoginState", "res", "configapp", "status", "data", "store", "changeConfig", "popRes", "configpop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onShow", "onHide", "app", "createApp", "App", "config", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "info", "handleError", "ERROR_TYPES", "SYSTEM", "ERROR_LEVELS", "ERROR", "component", "$options", "name", "errorInfo", "warn<PERSON><PERSON>ler", "msg", "instance", "trace", "WARNING", "createPinia", "use", "createUnistorage", "uviewPlus", "mount", "window", "addEventListener", "event", "reason", "utils_errorHandler", "type", "preventDefault", "onError"], "mappings": "+YAWM,MAAAA,EAA4BC,EAAGC,MAACC,kBAatC,GAZGC,EAAAF,MAACC,kBAAoB,KAClB,IAEF,OAAOE,EAAaA,eACrB,OAAQC,GAGP,OAFQC,QAAAC,KAAK,wBAAyBF,GAE/BN,GACT,GAIEC,EAAAA,MAAIQ,IAAMR,QAAIQ,GAAGC,IAAK,CAClB,MAAAC,EAAcV,EAAAA,MAAIQ,GAAGC,YACvBD,GAAGC,IAAM,KACP,IACF,OAAOL,EAAaA,eACrB,OAAQC,GAEP,OADQC,QAAAC,KAAK,yBAA0BF,GAChCK,GACT,EAEJ,QAEAC,EAAAA,UAASC,UACH,IAEFN,QAAQO,IAAI,oBACKC,EAAAA,oBAEfR,QAAQO,IAAI,YAEZP,QAAQO,IAAI,gBAGR,MAAAE,QAAYC,EAAAA,YAGE,QAAX,MAALD,OAAK,EAAAA,EAAAE,UAAmB,MAAAF,OAAA,EAAAA,EAAKG,OAE/BC,EAAAA,QAAQC,aAAaL,EAAIG,MAIrB,MAAAG,QAAeC,EAAAA,YACC,OAAlB,MAAAD,OAAA,EAAAA,EAAQJ,SACVE,EAAAA,QAAQI,cAAcF,EAAOH,KAEhC,OAAQb,GACCC,QAAAD,MAAM,aAAcA,EAC9B,KAGFmB,EAAAA,QAAO,SAIPC,EAAAA,QAAO,uBCzCDC,EAAMC,EAAAA,UAAUC,GAGtBF,EAAIG,OAAOC,aAfkB,CAACC,EAAKC,EAAIC,KAC9B3B,QAAAD,MAAM,WAAY0B,GAClBzB,QAAAD,MAAM,QAAS2B,GACf1B,QAAAD,MAAM,QAAS4B,GAGvBC,EAAAA,YAAYH,EAAKI,EAAAA,YAAYC,OAAQC,EAAAA,aAAaC,MAAO,CACxDC,WAAe,MAAJP,OAAI,EAAAA,EAAAQ,SAASC,OAAQ,UAChCC,UAAWT,GACX,EASFP,EAAIG,OAAOc,YAAc,CAACC,EAAKC,EAAUC,KAIxCZ,EAAAA,YAAYU,EAAKT,EAAAA,YAAYC,OAAQC,EAAAA,aAAaU,QAAS,CAC1DR,WAAqB,MAAVM,OAAU,EAAAA,EAAAL,SAASC,OAAQ,UACtCK,SACA,EAKF,MAAM3B,EAAQ6B,EAAWA,cACzB7B,EAAM8B,IAAIC,EAAAA,oBACVxB,EAAIuB,IAAI9B,GAAO8B,IAAIE,EAAAA,WAAWC,MAAM,QAGd,oBAAXC,QAA0BA,OAAOC,iBAEpCD,OAAAC,iBAAiB,sBAAuBC,IACtCjD,QAAAD,MAAM,iBAAkBkD,EAAMC,QAC3BC,EAAAvB,YAACqB,EAAMC,OAAQrB,EAAWA,YAACC,OAAQC,EAAYA,aAACC,MAAO,CACjEoB,KAAM,uBAEPH,EAAMI,gBAAc,IAIrB3D,EAAAA,MAAI4D,SAAW5D,EAAAA,MAAI4D,SAASvD,IACnBC,QAAAD,MAAM,SAAUA,GACxB6B,EAAAA,YAAY7B,EAAO8B,EAAAA,YAAYC,OAAQC,EAAAA,aAAaC,MAAO,CAC1DoB,KAAM,qBACN"}