"use strict";const e=require("../../../common/vendor.js"),o=require("../../../store/index.js"),t=require("../../../api/index.js");if(require("../../../store/counter.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-search")+e.resolveComponent("u-button")+e.resolveComponent("u-avatar")+e.resolveComponent("u-icon")+e.resolveComponent("u-empty")+e.resolveComponent("u-loadmore"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-search/u-search.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../../node-modules/uview-plus/components/u-loadmore/u-loadmore.js"))();const a={__name:"branch_members",setup(a){const l=e.ref(!1),r=e.ref([]),s=e.ref(""),n=e.ref("all"),u=e.ref(1),i=e.ref(!0),c=e.ref("loadmore"),m=e.reactive({total:0,active:0,new:0}),v=e.computed((()=>m.total)),f=e.computed((()=>m.active)),p=e.computed((()=>m.new));e.onLoad((()=>{d(!0)})),e.onPullDownRefresh((()=>{d(!0),setTimeout((()=>{e.index.stopPullDownRefresh()}),1e3)})),e.onReachBottom((()=>{i.value&&!l.value&&w()}));const d=async(a=!1)=>{try{if(a)u.value=1,i.value=!0,l.value=!0,c.value="loading";else if(!i.value)return;const v=o.store().$state.userInfo,f=await t.branch_presidentget_members({uid:v.uid,token:v.token,page:u.value,page_size:20,keyword:s.value,filter:n.value});if("ok"===f.status){const e=f.data||[];a?r.value=e:r.value.push(...e),f.stats&&(m.total=f.stats.total||0,m.active=f.stats.active||0,m.new=f.stats.new||0),i.value=20===e.length,u.value++,c.value=i.value?"loadmore":"nomore"}else"relogin"===f.status?e.index.showToast({title:"登录已过期，请重新登录",icon:"none"}):(e.index.showToast({title:f.msg||"加载失败",icon:"none"}),c.value="loadmore")}catch(v){console.error("加载成员列表失败:",v),e.index.showToast({title:"网络错误，请重试",icon:"none"}),c.value="loadmore"}finally{l.value=!1}},h=()=>{d(!0)},g=e=>{n.value=e,d(!0)},w=()=>{!l.value&&i.value&&(c.value="loading",d(!1))},y=e=>"system"===e.assignment_type,x=e=>e?e.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):"未绑定",j=e=>{if(!e)return"未知";const o=new Date(1e3*e),t=new Date-o,a=Math.floor(t/864e5);return 0===a?"今天加入":1===a?"昨天加入":a<30?`${a}天前加入`:a<365?`${Math.floor(a/30)}个月前加入`:`${Math.floor(a/365)}年前加入`};return(o,t)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"分会成员管理",color:"#ffffff",blod:!0}),b:e.t(e.unref(v)),c:e.t(e.unref(f)),d:e.t(e.unref(p)),e:e.o(h),f:e.o(h),g:e.o((e=>s.value=e)),h:e.p({placeholder:"搜索成员昵称或手机号","show-action":!1,"bg-color":"#f5f5f5","border-color":"transparent",modelValue:s.value}),i:e.o((e=>g("all"))),j:e.p({type:"all"===n.value?"primary":"info",size:"small",customStyle:"border-radius: 30rpx; margin-right: 20rpx;",customTextStyle:"all"===n.value?"color: #ffffff":"color: #6AC086"}),k:e.o((e=>g("active"))),l:e.p({type:"active"===n.value?"primary":"info",size:"small",customStyle:"border-radius: 30rpx; margin-right: 20rpx;",customTextStyle:"active"===n.value?"color: #ffffff":"color: #6AC086"}),m:e.o((e=>g("new"))),n:e.p({type:"new"===n.value?"primary":"info",size:"small",customStyle:"border-radius: 30rpx;",customTextStyle:"new"===n.value?"color: #ffffff":"color: #6AC086"}),o:r.value.length>0},r.value.length>0?{p:e.f(r.value,((o,t,a)=>e.e({a:"9f110556-5-"+a,b:e.p({src:o.avatar||"/static/default-avatar.png",size:"80",shape:"circle"}),c:o.is_active},o.is_active?{d:"9f110556-6-"+a,e:e.p({name:"checkmark-circle-fill",color:"#6AC086",size:"20"})}:{},{f:e.t(o.nickname||"未设置昵称"),g:y(o)},(y(o),{}),{h:e.t(x(o.mobile)),i:e.t(j(o.time)),j:e.t(o.activity_count||0),k:"9f110556-7-"+a,l:o.uid,m:e.o((t=>(o=>{e.index.showModal({title:"成员详情",content:`昵称：${o.nickname}\n手机：${x(o.mobile)}\n加入时间：${j(o.time)}\n参与活动：${o.activity_count||0}次`,showCancel:!1,confirmText:"知道了"})})(o)),o.uid)}))),q:e.p({name:"arrow-right",color:"#999",size:"24"})}:l.value?{}:{s:e.p({mode:"list",text:"暂无成员数据",textColor:"#999999",textSize:"28"})},{r:!l.value,t:i.value&&r.value.length>0},i.value&&r.value.length>0?{v:e.o(w),w:e.p({status:c.value})}:{})}},l=e._export_sfc(a,[["__scopeId","data-v-9f110556"]]);wx.createPage(l);
//# sourceMappingURL=branch_members.js.map
