"use strict";const e=require("../../../../common/vendor.js"),u=require("../../../../api/index.js"),a=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon"))()}Math||(l+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js"))();const l=()=>"../../../../components/customNavbar.js",r={__name:"detail",setup(l){const r=e.ref(null),t=e.ref(!0),s=e.ref(""),o=getCurrentPages(),i=o[o.length-1].options.id,n=async()=>{var e,l;if(!i)return s.value="出处ID无效",void(t.value=!1);try{t.value=!0;const o={uid:(null==(e=a.store().$state.userInfo)?void 0:e.uid)||0,token:(null==(l=a.store().$state.userInfo)?void 0:l.token)||"",source_id:i},n=await u.getSourceDetail(o);"ok"===n.status?r.value=n.data.source:s.value=n.msg||"获取出处详情失败"}catch(o){console.error("获取出处详情失败:",o),s.value="网络错误，请稍后重试"}finally{t.value=!1}};return e.onMounted((()=>{n()})),(u,a)=>{var l;return e.e({a:e.p({title:(null==(l=r.value)?void 0:l.name)||"出处详情",backIcon:"arrow-left"}),b:t.value},t.value?{c:e.p({mode:"spinner",color:"#6AC086",size:"40"})}:s.value?{e:e.p({name:"error-circle",size:"60",color:"#ff4757"}),f:e.t(s.value),g:e.o(n)}:r.value?e.e({i:r.value.cover_image},r.value.cover_image?{j:r.value.cover_image}:{k:e.p({name:"bookmark-fill",size:"60",color:"#ccc"})},{l:e.t(r.value.name),m:r.value.category},r.value.category?{n:e.t(r.value.category)}:{},{o:r.value.publisher},r.value.publisher?{p:e.t(r.value.publisher)}:{},{q:r.value.publish_year},r.value.publish_year?{r:e.t(r.value.publish_year)}:{},{s:e.t(r.value.quote_count||0),t:r.value.description},r.value.description?{v:e.t(r.value.description)}:{},{w:r.value.publisher},r.value.publisher?{x:e.t(r.value.publisher)}:{},{y:r.value.publish_year},r.value.publish_year?{z:e.t(r.value.publish_year)}:{},{A:r.value.isbn},r.value.isbn?{B:e.t(r.value.isbn)}:{},{C:r.value.category},r.value.category?{D:e.t(r.value.category)}:{},{E:r.value.url},r.value.url?{F:e.t(r.value.url),G:e.p({name:"arrow-right",size:"16",color:"#6AC086"}),H:e.o((u=>(u=>{if(!u)return;let a=u;u.startsWith("http://")||u.startsWith("https://")||(a="https://"+u),e.index.showModal({title:"打开链接",content:`是否要打开链接：${a}`,success:u=>{u.confirm&&e.index.setClipboardData({data:a,success:()=>{e.index.showToast({title:"链接已复制到剪贴板",icon:"success"})}})}})})(r.value.url)))}:{}):{},{d:s.value,h:r.value})}}},t=e._export_sfc(r,[["__scopeId","data-v-9a4b314a"]]);wx.createPage(t);
//# sourceMappingURL=detail.js.map
