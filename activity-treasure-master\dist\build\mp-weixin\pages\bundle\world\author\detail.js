"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../api/index.js"),t=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon"))()}Math||(u+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js"))();const u=()=>"../../../../components/customNavbar.js",r={__name:"detail",setup(u){const r=e.ref(null),l=e.ref(!0),o=e.ref(""),i=getCurrentPages(),n=i[i.length-1].options.id,s=async()=>{var e,u;if(!n)return o.value="作者ID无效",void(l.value=!1);try{l.value=!0;const i={uid:(null==(e=t.store().$state.userInfo)?void 0:e.uid)||0,token:(null==(u=t.store().$state.userInfo)?void 0:u.token)||"",author_id:n},s=await a.getAuthorDetail(i);"ok"===s.status?r.value=s.data.author:o.value=s.msg||"获取作者详情失败"}catch(i){console.error("获取作者详情失败:",i),o.value="网络错误，请稍后重试"}finally{l.value=!1}},v=(e,a)=>e||a?e&&a?`${e} - ${a}`:e&&!a?`${e} - 至今`:!e&&a?`? - ${a}`:"":"";return e.onMounted((()=>{s()})),(a,t)=>{var u;return e.e({a:e.p({title:(null==(u=r.value)?void 0:u.name)||"作者详情",backIcon:"arrow-left"}),b:l.value},l.value?{c:e.p({mode:"spinner",color:"#6AC086",size:"40"})}:o.value?{e:e.p({name:"error-circle",size:"60",color:"#ff4757"}),f:e.t(o.value),g:e.o(s)}:r.value?e.e({i:r.value.avatar},r.value.avatar?{j:r.value.avatar}:{k:e.p({name:"account-fill",size:"60",color:"#ccc"})},{l:e.t(r.value.name),m:r.value.category},r.value.category?{n:e.t(r.value.category)}:{},{o:r.value.nationality},r.value.nationality?{p:e.t(r.value.nationality)}:{},{q:v(r.value.birth_year,r.value.death_year)},v(r.value.birth_year,r.value.death_year)?{r:e.t(v(r.value.birth_year,r.value.death_year))}:{},{s:e.t(r.value.quote_count||0),t:r.value.description},r.value.description?{v:e.t(r.value.description)}:{},{w:r.value.birth_year},r.value.birth_year?{x:e.t(r.value.birth_year)}:{},{y:r.value.death_year},r.value.death_year?{z:e.t(r.value.death_year)}:{},{A:r.value.nationality},r.value.nationality?{B:e.t(r.value.nationality)}:{},{C:r.value.category},r.value.category?{D:e.t(r.value.category)}:{}):{},{d:o.value,h:r.value})}}},l=e._export_sfc(r,[["__scopeId","data-v-273c6f7a"]]);wx.createPage(l);
//# sourceMappingURL=detail.js.map
