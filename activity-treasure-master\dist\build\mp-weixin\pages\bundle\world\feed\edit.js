"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),a=require("../../../../store/index.js"),t=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/cacheManager.js"),require("../../../../store/counter.js"),!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-textarea")+e.resolveComponent("u-upload")+e.resolveComponent("u-icon")+e.resolveComponent("u-input")+e.resolveComponent("u-switch")+e.resolveComponent("u-button"))()}Math||(s+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../../node-modules/uview-plus/components/u-button/u-button.js"))();const s=()=>"../../../../components/customNavbar.js",n={__name:"edit",setup(s){const n=e.ref(""),i=e.ref([]),u=e.ref(null),l=e.ref(""),r=e.ref("public"),c=e.ref(!1),d=e.ref(!1),v=e.ref(!0),m=e.ref(null),p=e.computed((()=>{const e=a.store().$state.userInfo;return 1===(null==e?void 0:e.role_type)||2===(null==e?void 0:e.role_type)?4:1})),g=e.computed((()=>u.value?u.value.name||u.value.address:"添加位置"));e.onLoad((async o=>{if(!o.id)return e.index.showToast({title:"参数错误",icon:"none"}),void e.index.navigateBack();m.value=o.id,await f()}));const f=async()=>{try{v.value=!0;const t=a.store().$state.userInfo,s=await o.getFeedDetail({id:m.value,uid:t.uid,token:t.token});if("ok"===s.status&&s.data){const o=s.data;if(o.user_id!==t.uid)return e.index.showToast({title:"您无权编辑此动态",icon:"none"}),void e.index.navigateBack();n.value=o.content||"",l.value=o.tags||"",r.value=o.privacy||"public",c.value="private"===o.privacy,o.location&&(u.value={name:o.location}),o.images&&o.images.length>0&&(i.value=o.images.map(((e,o)=>({url:e,status:"success",message:"",uid:Date.now()+o}))))}else e.index.showToast({title:s.msg||"加载失败",icon:"none"}),e.index.navigateBack()}catch(t){console.error("加载动态数据失败:",t),e.index.showToast({title:"加载失败",icon:"none"}),e.index.navigateBack()}finally{v.value=!1}},h=async e=>{let o=[].concat(e.file),a=i.value.length;o.map((e=>{i.value.push({...e,status:"uploading",message:"上传中"})}));for(let t=0;t<o.length;t++){const e=await x(o[t].file);let s=i.value[a];i.value.splice(a,1,Object.assign(s,{status:e.success?"success":"failed",message:e.success?"":e.message,url:e.success?e.data:""})),a++}},x=e=>new Promise(((a,t)=>{let s=new FormData;s.append("file",e),o.upload_img(s).then((e=>{"ok"===e.status?a({success:!0,data:e.data.url}):a({success:!1,message:e.msg||"上传失败"})})).catch((e=>{a({success:!1,message:"上传失败"})}))})),w=e=>{i.value.splice(e.index,1)},j=e=>{r.value=e?"private":"public"},y=()=>{e.index.chooseLocation({success:e=>{u.value={name:e.name,address:e.address,latitude:e.latitude,longitude:e.longitude}},fail:e=>{console.log("选择位置失败:",e)}})},k=async()=>{if(t.requireLogin())if(n.value.trim()){if(!d.value)try{d.value=!0,e.index.showLoading({title:"保存中..."});const t=a.store().$state.userInfo,s=i.value.filter((e=>"success"===e.status&&e.url)).map((e=>e.url)),c={uid:t.uid,token:t.token,feed_id:m.value,content:n.value.trim(),images:s,location:u.value?u.value.name:"",tags:l.value.trim(),privacy:r.value},v=await o.editFeed(c);"ok"===v.status?(e.index.hideLoading(),e.index.showToast({title:"保存成功",icon:"success"}),e.index.$emit("feed-updated",{id:m.value}),setTimeout((()=>{e.index.navigateBack()}),1500)):(e.index.hideLoading(),e.index.showToast({title:v.msg||"保存失败",icon:"none"}))}catch(s){console.error("保存动态失败:",s),e.index.hideLoading(),e.index.showToast({title:"保存失败，请重试",icon:"none"})}finally{d.value=!1}}else e.index.showToast({title:"请输入动态内容",icon:"none"})},q=()=>{e.index.navigateBack()};return(o,a)=>e.e({a:e.o(q),b:e.p({title:"编辑动态",showBack:!0}),c:v.value},v.value?{d:e.p({mode:"spinner",color:"#6AC086",size:"40"})}:{e:e.o((e=>n.value=e)),f:e.p({placeholder:"分享你的动态...",maxlength:5e3,showWordLimit:!0,autoHeight:!0,minHeight:200,modelValue:n.value}),g:e.o(h),h:e.o(w),i:e.o((()=>o.uni.showToast({title:"图片大小不能超过400KB",icon:"none"}))),j:e.p({fileList:i.value,maxCount:e.unref(p),multiple:!0,previewFullImage:!0,accept:"image",maxSize:409600}),k:e.t(e.unref(p)),l:e.p({name:"map",color:"#6AC086",size:"20"}),m:e.t(e.unref(g)),n:e.p({name:"arrow-right",color:"#999",size:"16"}),o:e.o(y),p:e.o((e=>l.value=e)),q:e.p({placeholder:"添加标签，用逗号分隔",maxlength:100,modelValue:l.value}),r:e.o(j),s:e.o((e=>c.value=e)),t:e.p({activeColor:"#6AC086",modelValue:c.value})},{v:e.t(d.value?"保存中...":"保存"),w:e.o(k),x:e.p({type:"primary",loading:d.value,disabled:!n.value.trim()||d.value,color:"#6AC086"})})}},i=e._export_sfc(n,[["__scopeId","data-v-c8f4244e"]]);wx.createPage(i);
//# sourceMappingURL=edit.js.map
