"use strict";const t=require("../../../../common/vendor.js"),o={name:"u-back-top",mixins:[t.mpMixin,t.mixin,t.props$8],computed:{backTopStyle(){return{bottom:t.addUnit(this.bottom),right:t.addUnit(this.right),width:"40px",height:"40px",position:"fixed",zIndex:10}},show(){return t.getPx(this.scrollTop)>t.getPx(this.top)},contentStyle(){const o={};let e=0;return e="circle"===this.mode?"100px":"4px",o.borderTopLeftRadius=e,o.borderTopRightRadius=e,o.borderBottomLeftRadius=e,o.borderBottomRightRadius=e,t.deepMerge(o,t.addStyle(this.customStyle))}},emits:["click"],methods:{backToTop(){t.index.pageScrollTo({scrollTop:0,duration:this.duration}),this.$emit("click")}}};if(!Array){(t.resolveComponent("u-icon")+t.resolveComponent("u-transition"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-transition/u-transition.js"))();const e=t._export_sfc(o,[["render",function(o,e,i,s,n,r){return t.e({a:!o.$slots.default&&!o.$slots.$default},o.$slots.default||o.$slots.$default?{}:t.e({b:t.p({name:o.icon,"custom-style":o.iconStyle}),c:o.text},o.text?{d:t.t(o.text)}:{},{e:t.s(r.contentStyle),f:t.o(((...t)=>r.backToTop&&r.backToTop(...t)))}),{g:t.p({mode:"fade",customStyle:r.backTopStyle,show:r.show})})}],["__scopeId","data-v-5177c26f"]]);wx.createComponent(e);
//# sourceMappingURL=u-back-top.js.map
