"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),o=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),a=require("../../../store/index.js"),r=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-tabs")+e.resolveComponent("u-text")+e.resolveComponent("u-icon")+e.resolveComponent("mescroll-uni"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js"))();const n={__name:"userActivity",setup(n){const l=e.ref(""),s=e.ref(""),i=e.ref("feed"),u=e.ref([]),{mescrollInit:d,downCallback:c,getMescroll:m}=o.useMescroll(e.onPageScroll,e.onReachBottom),p=e.ref(""),y=[{name:"动态"},{name:"日卡"},{name:"摘录"},{name:"日记"}],v=["feed","card","quote","diary"];e.onLoad((e=>{l.value=e.type||"likes";s.value={likes:"我赞过的",favorites:"我收藏的",published:"我发布的",comments:"我评论的"}[l.value]||"我的活动"})),e.onReady((async()=>{p.value="auto",console.log("使用自动高度布局")})),e.onShow((()=>{const e=m();e&&e.resetUpScroll()}));const f=e=>{const t=v[e.index];if(i.value!==t){i.value=t,u.value=[];const e=m();e&&e.resetUpScroll()}},g=async o=>{try{let a;const r={page:o.num,page_size:o.size,type:_()};switch(console.log("API调用参数:",r,`活动类型: ${l.value}`),l.value){case"likes":a=await t.userget_user_likes(r);break;case"favorites":a=await t.userget_user_favorites(r);break;case"published":a=await t.userget_user_published(r);break;case"comments":a=await t.userget_user_comments(r);break;default:a={status:"error",msg:"未知类型"}}if(console.log("API响应结果:",a),"ok"===a.status){let e=[];"feed"===i.value?"likes"===l.value||"favorites"===l.value||"published"===l.value?e=a.data.feeds.map((e=>({...e,type:"feed"}))):"comments"===l.value&&(e=a.data.feed_comments.map((e=>({...e,type:"feed_comment"})))):"card"===i.value?"likes"===l.value||"favorites"===l.value?e=a.data.cards.map((e=>({...e,type:"card"}))):"published"===l.value?e=[]:"comments"===l.value&&(e=a.data.card_comments.map((e=>({...e,type:"card_comment"})))):"quote"===i.value?"likes"===l.value||"favorites"===l.value?e=a.data.quotes?a.data.quotes.map((e=>({...e,type:"quote"}))):[]:"published"===l.value?e=a.data.quotes.map((e=>({...e,type:"quote"}))):"comments"===l.value&&(e=a.data.quote_comments?a.data.quote_comments.map((e=>({...e,type:"quote_comment"}))):[]):"diary"===i.value&&("likes"===l.value||"favorites"===l.value||"published"===l.value?e=a.data.diaries?a.data.diaries.map((e=>({...e,type:"diary"}))):[]:"comments"===l.value&&(e=a.data.diary_comments?a.data.diary_comments.map((e=>({...e,type:"diary_comment"}))):[])),1==o.num&&(u.value=[]),u.value=u.value.concat(e),o.endBySize(e.length,a.count)}else"empty"===a.status||"n"===a?(1==o.num&&(u.value=[]),o.endBySize(0,0)):("empty"!==a.status&&"n"!==a&&(console.error("获取数据失败:",a),!a.msg||a.msg.includes("暂无")||a.msg.includes("没有")||e.index.$u.toast(a.msg)),o.endErr())}catch(a){console.error("获取用户活动错误:",a),("NetworkError"===a.name||a.message.includes("network")||a.message.includes("timeout"))&&e.index.$u.toast("网络连接失败，请检查网络后重试"),o.endErr()}},_=()=>({feed:"feed",card:"card",quote:"quote",diary:"diary"}[i.value]||""),q=e=>{var t,o,a;return"feed"===e.type?(null==(t=e.feed)?void 0:t.content)||e.content||"动态内容":"card"===e.type?(null==(o=e.card)?void 0:o.content)||e.content||"日卡内容":"quote"===e.type?e.content||"摘录内容":"diary"===e.type?(null==(a=e.diary)?void 0:a.content)||e.content||"日记内容":"feed_comment"===e.type||"card_comment"===e.type||"quote_comment"===e.type||"diary_comment"===e.type?e.content||"评论内容":"内容"},x=e=>"feed_comment"===e.type&&e.feed?e.feed.display_content||e.feed.content:"card_comment"===e.type&&e.card?e.card.display_content||e.card.content:"quote_comment"===e.type&&e.quote?e.quote.display_content||e.quote.content:"diary_comment"===e.type&&e.diary?e.diary.display_content||e.diary.content:null,h=e=>{var t,o,a;if("feed"===e.type){const o=(null==(t=e.feed)?void 0:t.images)||e.images||[];return o.length>0?o[0]:null}if("card"===e.type)return(null==(o=e.card)?void 0:o.image_url)||e.image_url;if("diary"===e.type){const t=(null==(a=e.diary)?void 0:a.images)||e.images||[];return t.length>0?t[0]:null}return null};return(t,o)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:s.value,color:"#ffffff",blod:!0}),b:e.o(f),c:e.p({current:v.indexOf(i.value),list:y,"active-style":{borderRadius:"25rpx",textAlign:"center",lineHeight:"56rpx",fontSize:"28rpx",color:"#ffffff",fontWeight:"600",backgroundColor:"#6AC086",boxShadow:"0 8rpx 24rpx rgba(106, 192, 134, 0.3), 0 4rpx 12rpx rgba(106, 192, 134, 0.2)",transition:"all 0.3s ease",transform:"translateY(-2rpx)"},inactiveStyle:{fontSize:"26rpx",color:"#6c757d",fontWeight:"400",borderRadius:"20rpx",backgroundColor:"transparent"},itemStyle:{padding:"20rpx 32rpx",margin:"0 8rpx",minWidth:"120rpx"},lineWidth:"0"}),d:0===u.value.length},0===u.value.length?{e:`${e.unref(a.store)().$state.url}empty.png`,f:e.t(s.value.replace("我",""))}:{g:e.f(u.value,((t,o,a)=>{return e.e({a:h(t)},h(t)?{b:h(t)}:{},{c:e.t((n=t,{feed:"动态",card:"日卡",quote:"摘录",diary:"日记",feed_comment:"动态评论",card_comment:"日卡评论",quote_comment:"摘录评论",diary_comment:"日记评论"}[n.type]||"内容")),d:"09005178-3-"+a+",09005178-2",e:e.p({size:"20rpx",color:"#999",text:t.created_at||t.time||""}),f:"09005178-4-"+a+",09005178-2",g:e.p({size:"28rpx",bold:!0,color:"#333",lines:"2",text:q(t)}),h:t.type.includes("comment")&&x(t)},t.type.includes("comment")&&x(t)?{i:"09005178-5-"+a+",09005178-2",j:e.p({size:"24rpx",color:"#666",lines:"1",text:x(t)})}:{},{k:h(t)?"":1,l:"09005178-6-"+a+",09005178-2",m:o,n:e.o((e=>(e=>{var t,o,a,n;if("feed"===e.type||"feed_comment"===e.type){const o=(null==(t=e.feed)?void 0:t.id)||e.id;r.navto(`/pages/bundle/world/feed/detail?feedId=${o}`)}else if("card"===e.type||"card_comment"===e.type){const t=(null==(o=e.card)?void 0:o.id)||e.id;r.navto(`/pages/bundle/world/card/detail?cardId=${t}`)}else if("quote"===e.type||"quote_comment"===e.type){const t=(null==(a=e.quote)?void 0:a.id)||e.id;r.navto(`/pages/bundle/world/quote/detail?quoteId=${t}`)}else if("diary"===e.type||"diary_comment"===e.type){const t=(null==(n=e.diary)?void 0:n.id)||e.id;r.navto(`/pages/bundle/world/diary/detail?diaryId=${t}`)}})(t)),o)});var n})),h:e.p({name:"arrow-right",color:"#999",size:"32rpx"})},{i:e.o(e.unref(d)),j:e.o(e.unref(c)),k:e.o(g),l:e.o((e=>e.scrollTo(0))),m:e.p({height:p.value})})}},l=e._export_sfc(n,[["__scopeId","data-v-09005178"]]);n.__runtimeHooks=1,wx.createPage(l);
//# sourceMappingURL=userActivity.js.map
