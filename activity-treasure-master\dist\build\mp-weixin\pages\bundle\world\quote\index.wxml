<view class="quote-container data-v-0a8ef25b"><view wx:if="{{a}}" class="loading-container data-v-0a8ef25b"><u-loading-icon wx:if="{{b}}" class="data-v-0a8ef25b" u-i="0a8ef25b-0" bind:__l="__l" u-p="{{b}}"/><text class="loading-text data-v-0a8ef25b">加载中...</text></view><scroll-view wx:else class="quote-scroll data-v-0a8ef25b" scroll-y refresher-enabled refresher-triggered="{{j}}" bindrefresherrefresh="{{k}}" bindscrolltolower="{{l}}"><view wx:if="{{c}}" class="empty-container data-v-0a8ef25b"><u-empty wx:if="{{d}}" class="data-v-0a8ef25b" u-i="0a8ef25b-1" bind:__l="__l" u-p="{{d}}"/></view><view wx:else class="quote-list data-v-0a8ef25b"><view wx:for="{{e}}" wx:for-item="quote" wx:key="B" class="quote-card data-v-0a8ef25b" bindtap="{{quote.C}}"><view class="quote-content data-v-0a8ef25b"><text class="quote-text data-v-0a8ef25b">{{quote.a}}</text></view><view wx:if="{{quote.b}}" class="quote-image-container data-v-0a8ef25b"><image src="{{quote.c}}" class="quote-image data-v-0a8ef25b" mode="aspectFill" catchtap="{{quote.d}}"></image></view><view wx:if="{{quote.e}}" class="quote-meta data-v-0a8ef25b"><text wx:if="{{quote.f}}" class="quote-author data-v-0a8ef25b">— {{quote.g}}</text><text wx:if="{{quote.h}}" class="quote-source data-v-0a8ef25b">《{{quote.i}}》</text></view><view class="quote-footer data-v-0a8ef25b"><view class="user-info data-v-0a8ef25b"><image src="{{quote.j}}" class="user-avatar data-v-0a8ef25b" mode="aspectFill"></image><text class="user-nickname data-v-0a8ef25b">{{quote.k}}</text></view><view wx:if="{{quote.l}}" class="privacy-badge data-v-0a8ef25b"><u-icon wx:if="{{quote.n}}" class="data-v-0a8ef25b" u-i="{{quote.m}}" bind:__l="__l" u-p="{{quote.n}}"/></view></view><view class="quote-bottom data-v-0a8ef25b"><view class="quote-time data-v-0a8ef25b"><text class="time-text data-v-0a8ef25b">{{quote.o}}</text></view><view class="quote-actions data-v-0a8ef25b"><view class="action-btn data-v-0a8ef25b" bindtap="{{quote.s}}"><u-icon wx:if="{{quote.q}}" class="data-v-0a8ef25b" u-i="{{quote.p}}" bind:__l="__l" u-p="{{quote.q}}"/><text class="action-count data-v-0a8ef25b">{{quote.r}}</text></view><view class="action-btn data-v-0a8ef25b" bindtap="{{quote.x}}"><u-icon wx:if="{{quote.v}}" class="data-v-0a8ef25b" u-i="{{quote.t}}" bind:__l="__l" u-p="{{quote.v}}"/><text class="action-count data-v-0a8ef25b">{{quote.w}}</text></view><view class="action-btn data-v-0a8ef25b" bindtap="{{quote.A}}"><u-icon wx:if="{{f}}" class="data-v-0a8ef25b" u-i="{{quote.y}}" bind:__l="__l" u-p="{{f}}"/><text class="action-count data-v-0a8ef25b">{{quote.z}}</text></view></view></view></view></view><view wx:if="{{g}}" class="loading-more data-v-0a8ef25b"><u-loading-icon wx:if="{{h}}" class="data-v-0a8ef25b" u-i="0a8ef25b-6" bind:__l="__l" u-p="{{h}}"/><text class="loading-more-text data-v-0a8ef25b">加载更多...</text></view><view wx:if="{{i}}" class="no-more data-v-0a8ef25b"><text class="no-more-text data-v-0a8ef25b">没有更多了</text></view></scroll-view></view>