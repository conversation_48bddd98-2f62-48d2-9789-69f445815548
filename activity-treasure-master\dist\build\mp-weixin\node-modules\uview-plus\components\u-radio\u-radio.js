"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-radio",mixins:[e.mpMixin,e.mixin,e.props$19],data:()=>({checked:!1,parentData:{iconSize:12,labelDisabled:null,disabled:null,shape:null,activeColor:null,inactiveColor:null,size:18,value:null,modelValue:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}),computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize(){return e.addUnit(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor(){const e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.checked?this.elInactiveColor:"transparent":this.checked?e:"transparent"},iconClasses(){let e=[];return e.push("u-radio__icon-wrap--"+this.elShape),this.elDisabled&&e.push("u-radio__icon-wrap--disabled"),this.checked&&this.elDisabled&&e.push("u-radio__icon-wrap--disabled--checked"),e},iconWrapStyle(){const t={};return t.backgroundColor=this.checked&&!this.elDisabled?this.elActiveColor:"#ffffff",t.borderColor=this.checked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,t.width=e.addUnit(this.elSize),t.height=e.addUnit(this.elSize),"right"===this.parentData.iconPlacement&&(t.marginRight=0),t},radioStyle(){const t={};return this.parentData.borderBottom&&this.parentData.placement,this.parentData.borderBottom&&"column"===this.parentData.placement&&(t.paddingBottom="ios"===e.os()?"12px":"8px"),e.deepMerge(t,e.addStyle(this.customStyle))}},mounted(){this.init()},emits:["change"],methods:{init(){this.updateParentData(),this.parent,this.checked=this.name===this.parentData.modelValue},updateParentData(){this.getParentData("u-radio-group")},iconClickHandler(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},wrapperClickHandler(e){"right"===this.parentData.iconPlacement&&this.iconClickHandler(e)},labelClickHandler(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.checked||(this.$emit("change",this.name),this.$nextTick((()=>{e.formValidate(this,"change")})))},setRadioCheckedStatus(){this.emitEvent(),this.checked=!0,"function"==typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}}};if(!Array){e.resolveComponent("u-icon")()}Math;const a=e._export_sfc(t,[["render",function(t,a,i,l,n,r){return{a:e.p({name:"checkbox-mark",size:r.elIconSize,color:r.elIconColor}),b:e.o(((...e)=>r.iconClickHandler&&r.iconClickHandler(...e))),c:e.n(r.iconClasses),d:e.s(r.iconWrapStyle),e:e.t(t.label),f:e.o(((...e)=>r.labelClickHandler&&r.labelClickHandler(...e))),g:r.elDisabled?r.elInactiveColor:r.elLabelColor,h:r.elLabelSize,i:r.elLabelSize,j:e.o(((...e)=>r.wrapperClickHandler&&r.wrapperClickHandler(...e))),k:e.s(r.radioStyle),l:e.n(`u-radio-label--${n.parentData.iconPlacement}`),m:e.n(n.parentData.borderBottom&&"column"===n.parentData.placement&&"u-border-bottom")}}],["__scopeId","data-v-6cdd0a08"]]);wx.createComponent(a);
//# sourceMappingURL=u-radio.js.map
