{"version": 3, "file": "u-line.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-line/u-line.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWxpbmUvdS1saW5lLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-line\"\n\t    :style=\"[lineStyle]\"\n\t>\n\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * line 线条\n\t * @description 此组件一般用于显示一根线条，用于分隔内容块，有横向和竖向两种模式，且能设置0.5px线条，使用也很简单\n\t * @tutorial https://ijry.github.io/uview-plus/components/line.html\n\t * @property {String}\t\t\tcolor\t\t线条的颜色 ( 默认 '#d6d7d9' )\n\t * @property {String | Number}\tlength\t\t长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带px单位的值等 ( 默认 '100%' )\n\t * @property {String}\t\t\tdirection\t线条的方向，row-横向，col-竖向 (默认 'row' )\n\t * @property {Boolean}\t\t\thairline\t是否显示细线条 (默认 true )\n\t * @property {String | Number}\tmargin\t\t线条与上下左右元素的间距，字符串形式，如\"30px\"  (默认 0 )\n\t * @property {Boolean}\t\t\tdashed\t\t是否虚线，true-虚线，false-实线 (默认 false )\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * @example <u-line color=\"red\"></u-line>\n\t */\n\texport default {\n\t\tname: 'u-line',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tlineStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tstyle.margin = this.margin\n\t\t\t\t// 如果是水平线条，边框高度为1px，再通过transform缩小一半，就是0.5px了\n\t\t\t\tif (this.direction === 'row') {\n\t\t\t\t\t// 此处采用兼容分开写，兼容nvue的写法\n\t\t\t\t\tstyle.borderBottomWidth = '1px'\n\t\t\t\t\tstyle.borderBottomStyle = this.dashed ? 'dashed' : 'solid'\n\t\t\t\t\tstyle.width = addUnit(this.length)\n\t\t\t\t\tif (this.hairline) style.transform = 'scaleY(0.5)'\n\t\t\t\t} else {\n\t\t\t\t\t// 如果是竖向线条，边框宽度为1px，再通过transform缩小一半，就是0.5px了\n\t\t\t\t\tstyle.borderLeftWidth = '1px'\n\t\t\t\t\tstyle.borderLeftStyle = this.dashed ? 'dashed' : 'solid'\n\t\t\t\t\tstyle.height = addUnit(this.length)\n\t\t\t\t\tif (this.hairline) style.transform = 'scaleX(0.5)'\n\t\t\t\t}\n\n\t\t\t\tstyle.borderColor = this.color\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-line {\n\t\t/* #ifndef APP-NVUE */\n\t\tvertical-align: middle;\n\t\t/* #endif */\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-line/u-line.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "computed", "lineStyle", "style", "margin", "this", "direction", "borderBottomWidth", "borderBottomStyle", "dashed", "width", "addUnit", "length", "hairline", "transform", "borderLeftWidth", "borderLeftStyle", "height", "borderColor", "color", "deepMerge", "addStyle", "customStyle", "wx", "createComponent", "Component"], "mappings": "6DA2BMA,EAAU,CACdC,KAAM,SACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,WACzBC,SAAU,CACT,SAAAC,GACC,MAAMC,EAAQ,CAAC,EAkBf,OAjBAA,EAAMC,OAASC,KAAKD,OAEG,QAAnBC,KAAKC,WAERH,EAAMI,kBAAoB,MACpBJ,EAAAK,kBAAoBH,KAAKI,OAAS,SAAW,QACnDN,EAAMO,MAAQC,UAAQN,KAAKO,QACvBP,KAAKQ,WAAUV,EAAMW,UAAY,iBAGrCX,EAAMY,gBAAkB,MAClBZ,EAAAa,gBAAkBX,KAAKI,OAAS,SAAW,QACjDN,EAAMc,OAASN,UAAQN,KAAKO,QACxBP,KAAKQ,WAAUV,EAAMW,UAAY,gBAGtCX,EAAMe,YAAcb,KAAKc,MAClBC,EAASA,UAACjB,EAAOkB,EAAQA,SAAChB,KAAKiB,aACvC,4HClDHC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}