{"version": 3, "file": "album.js", "sources": ["../../../../../../src/pages/bundle/activity/album.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGFjdGl2aXR5XGFsYnVtLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"album-page\">\n    <!-- 顶部导航栏 -->\n    <view class=\"nav-bar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <u-icon name=\"arrow-left\" color=\"#333\" size=\"20\"></u-icon>\n      </view>\n      <view class=\"nav-title\">活动相册</view>\n      <view class=\"nav-right\"></view>\n    </view>\n    \n    <!-- 活动信息卡片 -->\n    <view class=\"activity-info\" v-if=\"activityInfo.name\">\n      <text class=\"activity-name\">{{ activityInfo.name }}</text>\n      <text class=\"photo-count\">共{{ totalPhotos }}张图片</text>\n    </view>\n    \n    <!-- 图片网格 -->\n    <view class=\"photo-grid\" v-if=\"photos.length > 0\">\n      <view \n        class=\"photo-item\" \n        v-for=\"(photo, index) in photos\" \n        :key=\"photo.id\"\n        @click=\"previewImage(index)\"\n        @longpress=\"showDeleteMenu(photo)\"\n      >\n        <image \n          :src=\"photo.photo_url\" \n          mode=\"aspectFill\"\n          class=\"photo-image\"\n          lazy-load\n        ></image>\n        \n        <!-- 用户信息 -->\n        <view class=\"photo-user\">\n          <image \n            :src=\"photo.user?.avatar || '/static/default-avatar.png'\" \n            class=\"user-avatar\"\n            mode=\"aspectFill\"\n          ></image>\n          <text class=\"user-name\">{{ photo.user?.nickname || '匿名用户' }}</text>\n        </view>\n        \n        <!-- 删除按钮（仅对有权限的用户显示） -->\n        <view \n          v-if=\"canDeletePhoto(photo)\" \n          class=\"delete-btn\"\n          @click.stop=\"confirmDelete(photo)\"\n        >\n          <u-icon name=\"close-circle-fill\" color=\"#ff4757\" size=\"16\"></u-icon>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 空状态 -->\n    <view class=\"empty-state\" v-else-if=\"!loading\">\n      <u-icon name=\"photo\" color=\"#ccc\" size=\"60\"></u-icon>\n      <text class=\"empty-text\">暂无图片</text>\n      <text class=\"empty-hint\">活动开始后可以上传图片</text>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view class=\"loading-state\" v-if=\"loading\">\n      <u-loading-icon mode=\"flower\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n    \n    <!-- 底部上传按钮 -->\n    <view class=\"upload-fab\" v-if=\"canUpload\" @click=\"showUploadMenu\">\n      <u-icon name=\"plus\" color=\"#fff\" size=\"24\"></u-icon>\n    </view>\n    \n    <!-- 上传选择弹窗 -->\n    <view v-if=\"showUploadDialog\" class=\"upload-dialog-overlay\" @click=\"closeUploadDialog\">\n      <view class=\"upload-dialog\" @click.stop>\n        <view class=\"upload-option\" @click=\"chooseFromCamera\">\n          <u-icon name=\"camera\" color=\"#6AC086\" size=\"20\"></u-icon>\n          <text class=\"option-text\">拍照</text>\n        </view>\n        <view class=\"upload-option\" @click=\"chooseFromAlbum\">\n          <u-icon name=\"photo\" color=\"#6AC086\" size=\"20\"></u-icon>\n          <text class=\"option-text\">从相册选择</text>\n        </view>\n        <view class=\"upload-cancel\" @click=\"closeUploadDialog\">\n          <text class=\"cancel-text\">取消</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { huodongget_activity_photos, huodongupload_activity_photo, huodongdelete_activity_photo, upload_img } from '@/api'\nimport { store } from '@/store/index.js'\n\n// 页面数据\nconst activityId = ref(0)\nconst photos = ref([])\nconst activityInfo = ref({ name: '', publisher_uid: 0 }) // {{ AURA-X: Fix - 初始化activityInfo结构. Confirmed via 寸止. }}\nconst loading = ref(false)\nconst showUploadDialog = ref(false)\nconst totalPhotos = ref(0)\nconst userInfo = computed(() => store().$state.userInfo)\n\n// 页面加载\nonLoad((options) => {\n  if (options.activity_id) {\n    activityId.value = parseInt(options.activity_id)\n    loadPhotos()\n  } else {\n    uni.showToast({\n      title: '参数错误',\n      icon: 'error'\n    })\n    setTimeout(() => {\n      uni.navigateBack()\n    }, 1500)\n  }\n})\n\n// 加载图片列表\nconst loadPhotos = async () => {\n  try {\n    loading.value = true\n    const res = await huodongget_activity_photos({\n      activity_id: activityId.value,\n      page: 1,\n      page_size: 100 // 一次加载所有图片\n    })\n\n    // {{ AURA-X: Modify - 修复空数据处理，避免报错. Confirmed via 寸止. }}\n    if (res.status === 'ok') {\n      photos.value = res.data || []\n      totalPhotos.value = res.count || 0\n\n      // 获取活动基本信息用于显示\n      if (photos.value.length > 0) {\n        // 可以从图片数据中获取活动信息，或者调用活动详情API\n        activityInfo.value.name = `活动相册`; // 临时设置，实际应该获取真实活动名称\n      }\n    } else if (res.status === 'empty' || res === 'n' || !res.data) {\n      // 处理空数据情况，不显示错误提示\n      photos.value = []\n      totalPhotos.value = 0\n      console.log('活动相册暂无图片')\n    } else {\n      uni.showToast({\n        title: res.msg || '加载失败',\n        icon: 'error'\n      })\n    }\n  } catch (error) {\n    console.error('加载图片失败:', error)\n    uni.showToast({\n      title: '加载失败',\n      icon: 'error'\n    })\n  } finally {\n    loading.value = false\n  }\n}\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack()\n}\n\n// 预览图片\nconst previewImage = (index) => {\n  const urls = photos.value.map(photo => photo.photo_url)\n  uni.previewImage({\n    urls: urls,\n    current: index\n  })\n}\n\n// 检查是否可以上传图片\nconst canUpload = computed(() => {\n  // 需要用户登录且活动已开始\n  return userInfo.value && userInfo.value.uid\n})\n\n// 检查是否可以删除图片\nconst canDeletePhoto = (photo) => {\n  if (!userInfo.value || !userInfo.value.uid) return false\n  \n  // 图片上传者或活动发布者可以删除\n  return photo.user_id === userInfo.value.uid || \n         activityInfo.value.publisher_uid === userInfo.value.uid\n}\n\n// 显示上传菜单\nconst showUploadMenu = () => {\n  showUploadDialog.value = true\n}\n\n// 关闭上传弹窗\nconst closeUploadDialog = () => {\n  showUploadDialog.value = false\n}\n\n// 从相机拍照\nconst chooseFromCamera = () => {\n  closeUploadDialog()\n  chooseImage('camera')\n}\n\n// 从相册选择\nconst chooseFromAlbum = () => {\n  closeUploadDialog()\n  chooseImage('album')\n}\n\n// 选择图片\nconst chooseImage = (sourceType) => {\n  uni.chooseImage({\n    count: 1,\n    sourceType: [sourceType],\n    success: (res) => {\n      uploadImage(res.tempFilePaths[0])\n    },\n    fail: (error) => {\n      console.error('选择图片失败:', error)\n      uni.showToast({\n        title: '选择图片失败',\n        icon: 'error'\n      })\n    }\n  })\n}\n\n// 上传图片\nconst uploadImage = async (tempFilePath) => {\n  try {\n    uni.showLoading({\n      title: '上传中...'\n    })\n\n    // {{ AURA-X: Fix - 修复上传参数，使用正确的字段名. Confirmed via 寸止. }}\n    // 1. 先上传图片文件到服务器\n    const uploadRes = await upload_img(tempFilePath)\n\n    if (uploadRes.status !== 'ok') {\n      throw new Error(uploadRes.msg || '图片上传失败')\n    }\n\n    // 2. 调用活动相册API保存图片记录\n    const res = await huodongupload_activity_photo({\n      uid: userInfo.value.uid,\n      token: userInfo.value.token,\n      activity_id: activityId.value,\n      photo_url: uploadRes.data // 修复：直接使用data字段，不是data.url\n    })\n\n    if (res.status === 'ok') {\n      uni.showToast({\n        title: '上传成功',\n        icon: 'success'\n      })\n      // 重新加载图片列表\n      loadPhotos()\n    } else {\n      uni.showToast({\n        title: res.msg || '上传失败',\n        icon: 'error'\n      })\n    }\n  } catch (error) {\n    console.error('上传图片失败:', error)\n    uni.showToast({\n      title: error.message || '上传失败',\n      icon: 'error'\n    })\n  } finally {\n    uni.hideLoading()\n  }\n}\n\n// 确认删除图片\nconst confirmDelete = (photo) => {\n  uni.showModal({\n    title: '确认删除',\n    content: '确定要删除这张图片吗？',\n    success: (res) => {\n      if (res.confirm) {\n        deletePhoto(photo.id)\n      }\n    }\n  })\n}\n\n// 删除图片\nconst deletePhoto = async (photoId) => {\n  try {\n    uni.showLoading({\n      title: '删除中...'\n    })\n    \n    const res = await huodongdelete_activity_photo({\n      uid: userInfo.value.uid,\n      token: userInfo.value.token,\n      photo_id: photoId\n    })\n    \n    if (res.status === 'ok') {\n      uni.showToast({\n        title: '删除成功',\n        icon: 'success'\n      })\n      // 重新加载图片列表\n      loadPhotos()\n    } else {\n      uni.showToast({\n        title: res.msg || '删除失败',\n        icon: 'error'\n      })\n    }\n  } catch (error) {\n    console.error('删除图片失败:', error)\n    uni.showToast({\n      title: '删除失败',\n      icon: 'error'\n    })\n  } finally {\n    uni.hideLoading()\n  }\n}\n\n// 显示删除菜单（长按）\nconst showDeleteMenu = (photo) => {\n  if (!canDeletePhoto(photo)) return\n  \n  uni.showActionSheet({\n    itemList: ['删除图片'],\n    success: (res) => {\n      if (res.tapIndex === 0) {\n        confirmDelete(photo)\n      }\n    }\n  })\n}\n</script>\n\n<style scoped>\n.album-page {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 120rpx;\n  margin-top: 100rpx; /* {{ AURA-X: Add - 整体页面下移100rpx. Confirmed via 寸止. }} */\n}\n\n/* 导航栏样式 */\n.nav-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 32rpx;\n  background: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n  position: fixed;\n  top: 100rpx; /* 导航栏下移100rpx */\n  left: 0;\n  right: 0;\n  z-index: 100;\n}\n\n.nav-left, .nav-right {\n  width: 88rpx;\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n/* 活动信息卡片 */\n.activity-info {\n  margin: 208rpx 32rpx 32rpx; /* 调整上边距：100rpx(页面下移) + 88rpx(导航栏高度) + 20rpx(间距) */\n  padding: 32rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.activity-name {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n  flex: 1;\n}\n\n.photo-count {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 图片网格样式 */\n.photo-grid {\n  padding: 0 32rpx;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 24rpx;\n}\n\n.photo-item {\n  position: relative;\n  aspect-ratio: 1;\n  border-radius: 16rpx;\n  overflow: hidden;\n  background: #fff;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.photo-image {\n  width: 100%;\n  height: 100%;\n}\n\n.photo-user {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n  padding: 16rpx;\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.user-avatar {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  border: 2rpx solid #fff;\n}\n\n.user-name {\n  font-size: 24rpx;\n  color: #fff;\n  font-weight: 500;\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 12rpx;\n  right: 12rpx;\n  width: 48rpx;\n  height: 48rpx;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n  text-align: center;\n}\n\n.empty-text {\n  font-size: 30rpx;\n  color: #999;\n  margin-top: 24rpx;\n  font-weight: 500;\n}\n\n.empty-hint {\n  font-size: 26rpx;\n  color: #ccc;\n  margin-top: 12rpx;\n}\n\n/* 加载状态样式 */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999;\n  margin-top: 24rpx;\n}\n\n/* 上传按钮样式 */\n.upload-fab {\n  position: fixed;\n  bottom: 40rpx;\n  right: 40rpx;\n  width: 120rpx;\n  height: 120rpx;\n  background: #6AC086;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.3);\n  z-index: 50;\n  transition: all 0.3s ease;\n}\n\n.upload-fab:active {\n  transform: scale(0.95);\n}\n\n/* 上传弹窗样式 */\n.upload-dialog-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  display: flex;\n  align-items: flex-end;\n  justify-content: center;\n}\n\n.upload-dialog {\n  width: 100%;\n  max-width: 750rpx;\n  background: #fff;\n  border-radius: 32rpx 32rpx 0 0;\n  padding: 40rpx 32rpx;\n  animation: slideUp 0.3s ease-out;\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n.upload-option {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 0;\n  border-bottom: 1rpx solid #f8f8f8;\n}\n\n.upload-option:last-of-type {\n  border-bottom: none;\n}\n\n.option-text {\n  margin-left: 24rpx;\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.upload-cancel {\n  text-align: center;\n  padding: 32rpx 0 16rpx;\n  border-top: 1rpx solid #f0f0f0;\n  margin-top: 24rpx;\n}\n\n.cancel-text {\n  font-size: 30rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 750rpx) {\n  .photo-grid {\n    padding: 0 24rpx;\n    gap: 16rpx;\n  }\n\n  .activity-info {\n    margin: 108rpx 24rpx 24rpx;\n    padding: 24rpx;\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/activity/album.vue'\nwx.createPage(MiniProgramPage)"], "names": ["activityId", "ref", "photos", "activityInfo", "name", "publisher_uid", "loading", "showUploadDialog", "totalPhotos", "userInfo", "computed", "store", "$state", "common_vendor", "onLoad", "options", "activity_id", "value", "parseInt", "loadPhotos", "uni", "showToast", "title", "icon", "setTimeout", "navigateBack", "async", "res", "huodongget_activity_photos", "page", "page_size", "status", "data", "count", "length", "msg", "console", "log", "error", "goBack", "canUpload", "uid", "canDeletePhoto", "photo", "user_id", "showUploadMenu", "closeUploadDialog", "chooseFromCamera", "chooseImage", "chooseFromAlbum", "sourceType", "success", "uploadImage", "tempFilePaths", "fail", "tempFile<PERSON>ath", "showLoading", "uploadRes", "upload_img", "Error", "huodongupload_activity_photo", "token", "photo_url", "message", "hideLoading", "confirmDelete", "showModal", "content", "confirm", "deletePhoto", "id", "photoId", "huodongdelete_activity_photo", "photo_id", "index", "urls", "map", "previewImage", "current", "showActionSheet", "itemList", "tapIndex", "wx", "createPage", "MiniProgramPage"], "mappings": "8pBAkGM,MAAAA,EAAaC,EAAGA,IAAC,GACjBC,EAASD,EAAGA,IAAC,IACbE,EAAeF,EAAGA,IAAC,CAAEG,KAAM,GAAIC,cAAe,IAC9CC,EAAUL,EAAGA,KAAC,GACdM,EAAmBN,EAAGA,KAAC,GACvBO,EAAcP,EAAGA,IAAC,GAClBQ,EAAWC,EAAAA,UAAS,IAAMC,EAAKA,QAAGC,OAAOH,WAGzCI,EAAAC,QAAEC,IACFA,EAAQC,aACChB,EAAAiB,MAAQC,SAASH,EAAQC,aACzBG,MAEXC,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,UAERC,YAAW,KACTJ,EAAAA,MAAIK,cAAa,GAChB,MACL,IAIF,MAAMN,EAAaO,UACb,IACFpB,EAAQW,OAAQ,EACV,MAAAU,QAAYC,6BAA2B,CAC3CZ,YAAahB,EAAWiB,MACxBY,KAAM,EACNC,UAAW,MAIM,OAAfH,EAAII,QACC7B,EAAAe,MAAQU,EAAIK,MAAQ,GACfxB,EAAAS,MAAQU,EAAIM,OAAS,EAG7B/B,EAAOe,MAAMiB,OAAS,IAExB/B,EAAac,MAAMb,KAAO,SAEJ,UAAfuB,EAAII,QAA8B,MAARJ,GAAgBA,EAAIK,KAMvDZ,EAAAA,MAAIC,UAAU,CACZC,MAAOK,EAAIQ,KAAO,OAClBZ,KAAM,WANRrB,EAAOe,MAAQ,GACfT,EAAYS,MAAQ,EACpBmB,QAAQC,IAAI,YAOf,OAAQC,GACCF,QAAAE,MAAM,UAAWA,GACzBlB,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,SAEZ,CAAY,QACRjB,EAAQW,OAAQ,CAClB,GAIIsB,EAAS,KACbnB,EAAAA,MAAIK,cAAa,EAabe,EAAY9B,EAAQA,UAAC,IAElBD,EAASQ,OAASR,EAASQ,MAAMwB,MAIpCC,EAAkBC,MACjBlC,EAASQ,QAAUR,EAASQ,MAAMwB,OAGhCE,EAAMC,UAAYnC,EAASQ,MAAMwB,KACjCtC,EAAac,MAAMZ,gBAAkBI,EAASQ,MAAMwB,KAIvDI,EAAiB,KACrBtC,EAAiBU,OAAQ,CAAA,EAIrB6B,EAAoB,KACxBvC,EAAiBU,OAAQ,CAAA,EAIrB8B,EAAmB,KACLD,IAClBE,EAAY,SAAQ,EAIhBC,EAAkB,KACJH,IAClBE,EAAY,QAAO,EAIfA,EAAeE,IACnB9B,EAAAA,MAAI4B,YAAY,CACdf,MAAO,EACPiB,WAAY,CAACA,GACbC,QAAUxB,IACIyB,EAAAzB,EAAI0B,cAAc,GAAE,EAElCC,KAAOhB,IACGF,QAAAE,MAAM,UAAWA,GACzBlB,EAAAA,MAAIC,UAAU,CACZC,MAAO,SACPC,KAAM,SACP,GAEJ,EAIG6B,EAAc1B,MAAO6B,IACrB,IACFnC,EAAAA,MAAIoC,YAAY,CACdlC,MAAO,WAKT,MAAMmC,QAAkBC,EAAUA,WAACH,GAE/B,GAAqB,OAArBE,EAAU1B,OACZ,MAAM,IAAI4B,MAAMF,EAAUtB,KAAO,UAI7B,MAAAR,QAAYiC,+BAA6B,CAC7CnB,IAAKhC,EAASQ,MAAMwB,IACpBoB,MAAOpD,EAASQ,MAAM4C,MACtB7C,YAAahB,EAAWiB,MACxB6C,UAAWL,EAAUzB,OAGJ,OAAfL,EAAII,QACNX,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,YAGGJ,KAEXC,EAAAA,MAAIC,UAAU,CACZC,MAAOK,EAAIQ,KAAO,OAClBZ,KAAM,SAGX,OAAQe,GACCF,QAAAE,MAAM,UAAWA,GACzBlB,EAAAA,MAAIC,UAAU,CACZC,MAAOgB,EAAMyB,SAAW,OACxBxC,KAAM,SAEZ,CAAY,QACRH,EAAAA,MAAI4C,aACN,GAIIC,EAAiBtB,IACrBvB,EAAAA,MAAI8C,UAAU,CACZ5C,MAAO,OACP6C,QAAS,cACThB,QAAUxB,IACJA,EAAIyC,SACNC,EAAY1B,EAAM2B,GACpB,GAEH,EAIGD,EAAc3C,MAAO6C,IACrB,IACFnD,EAAAA,MAAIoC,YAAY,CACdlC,MAAO,WAGH,MAAAK,QAAY6C,+BAA6B,CAC7C/B,IAAKhC,EAASQ,MAAMwB,IACpBoB,MAAOpD,EAASQ,MAAM4C,MACtBY,SAAUF,IAGO,OAAf5C,EAAII,QACNX,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,YAGGJ,KAEXC,EAAAA,MAAIC,UAAU,CACZC,MAAOK,EAAIQ,KAAO,OAClBZ,KAAM,SAGX,OAAQe,GACCF,QAAAE,MAAM,UAAWA,GACzBlB,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,SAEZ,CAAY,QACRH,EAAAA,MAAI4C,aACN,sfA7JmB,CAACU,IACpB,MAAMC,EAAOzE,EAAOe,MAAM2D,KAAIjC,GAASA,EAAMmB,YAC7C1C,EAAAA,MAAIyD,aAAa,CACfF,OACAG,QAASJ,GACV,uBA4JoB,CAAC/B,IACjBD,EAAeC,IAEpBvB,EAAAA,MAAI2D,gBAAgB,CAClBC,SAAU,CAAC,QACX7B,QAAUxB,IACa,IAAjBA,EAAIsD,UACNhB,EAActB,EAChB,GAEH,kcCpVHuC,GAAGC,WAAWC"}