<view class="page data-v-861b5b94"><view class="container data-v-861b5b94"><view class="page-header data-v-861b5b94"><u-navbar wx:if="{{b}}" class="data-v-861b5b94" bindleftClick="{{a}}" u-i="861b5b94-0" bind:__l="__l" u-p="{{b}}"></u-navbar></view><view class="steps-container data-v-861b5b94"><view class="{{['step-item', 'data-v-861b5b94', c && 'active', d && 'completed']}}"><view class="step-number data-v-861b5b94">1</view><text class="step-text data-v-861b5b94">选择对象</text></view><view class="{{['step-line', 'data-v-861b5b94', e && 'active']}}"></view><view class="{{['step-item', 'data-v-861b5b94', f && 'active', g && 'completed']}}"><view class="step-number data-v-861b5b94">2</view><text class="step-text data-v-861b5b94">选择理由</text></view><view class="{{['step-line', 'data-v-861b5b94', h && 'active']}}"></view><view class="{{['step-item', 'data-v-861b5b94', i && 'active']}}"><view class="step-number data-v-861b5b94">3</view><text class="step-text data-v-861b5b94">提交详情</text></view></view><view wx:if="{{j}}" class="step-content data-v-861b5b94"><view class="section-title data-v-861b5b94"><u-text wx:if="{{k}}" class="data-v-861b5b94" u-i="861b5b94-1" bind:__l="__l" u-p="{{k}}"></u-text></view><view wx:if="{{l}}" class="loading-container data-v-861b5b94"><u-loading-icon wx:if="{{m}}" class="data-v-861b5b94" u-i="861b5b94-2" bind:__l="__l" u-p="{{m}}"></u-loading-icon><text class="loading-text data-v-861b5b94">加载中...</text></view><view wx:elif="{{n}}" class="empty-container data-v-861b5b94"><u-empty wx:if="{{o}}" class="data-v-861b5b94" u-i="861b5b94-3" bind:__l="__l" u-p="{{o}}"></u-empty></view><view wx:else class="participants-list data-v-861b5b94"><view wx:for="{{p}}" wx:for-item="participant" wx:key="f" class="participant-item data-v-861b5b94" bindtap="{{participant.g}}"><u-avatar wx:if="{{participant.b}}" class="data-v-861b5b94" u-i="{{participant.a}}" bind:__l="__l" u-p="{{participant.b}}"></u-avatar><view class="participant-info data-v-861b5b94"><text class="participant-name data-v-861b5b94">{{participant.c}}</text><text class="participant-role data-v-861b5b94">{{participant.d}}</text></view><u-icon wx:if="{{q}}" class="data-v-861b5b94" u-i="{{participant.e}}" bind:__l="__l" u-p="{{q}}"></u-icon></view></view></view><view wx:if="{{r}}" class="step-content data-v-861b5b94"><view class="section-title data-v-861b5b94"><u-text wx:if="{{s}}" class="data-v-861b5b94" u-i="861b5b94-6" bind:__l="__l" u-p="{{s}}"></u-text></view><view class="selected-target data-v-861b5b94"><text class="target-label data-v-861b5b94">举报对象：</text><text class="target-name data-v-861b5b94">{{t}}</text></view><view wx:if="{{v}}" class="loading-container data-v-861b5b94"><u-loading-icon wx:if="{{w}}" class="data-v-861b5b94" u-i="861b5b94-7" bind:__l="__l" u-p="{{w}}"></u-loading-icon><text class="loading-text data-v-861b5b94">加载举报选项...</text></view><view wx:else class="report-options-list data-v-861b5b94"><view wx:for="{{x}}" wx:for-item="option" wx:key="d" class="report-option-item data-v-861b5b94" bindtap="{{option.e}}"><view class="option-content data-v-861b5b94"><text class="option-title data-v-861b5b94">{{option.a}}</text><text class="option-description data-v-861b5b94">{{option.b}}</text></view><u-icon wx:if="{{y}}" class="data-v-861b5b94" u-i="{{option.c}}" bind:__l="__l" u-p="{{y}}"></u-icon></view></view><view class="step-buttons data-v-861b5b94"><u-button wx:if="{{A}}" class="data-v-861b5b94" bindclick="{{z}}" u-i="861b5b94-9" bind:__l="__l" u-p="{{A}}"></u-button></view></view><view wx:if="{{B}}" class="step-content data-v-861b5b94"><view class="section-title data-v-861b5b94"><u-text wx:if="{{C}}" class="data-v-861b5b94" u-i="861b5b94-10" bind:__l="__l" u-p="{{C}}"></u-text></view><view class="report-summary data-v-861b5b94"><view class="summary-item data-v-861b5b94"><text class="summary-label data-v-861b5b94">举报对象：</text><text class="summary-value data-v-861b5b94">{{D}}</text></view><view class="summary-item data-v-861b5b94"><text class="summary-label data-v-861b5b94">举报理由：</text><text class="summary-value data-v-861b5b94">{{E}}</text></view></view><view class="detail-form data-v-861b5b94"><view class="form-item data-v-861b5b94"><text class="form-label data-v-861b5b94">详细说明 <text class="required data-v-861b5b94">*</text></text><u-textarea wx:if="{{G}}" class="data-v-861b5b94" u-i="861b5b94-11" bind:__l="__l" bindupdateModelValue="{{F}}" u-p="{{G}}"></u-textarea><text class="char-count data-v-861b5b94">{{H}}/500</text></view></view><view class="step-buttons data-v-861b5b94"><u-button wx:if="{{J}}" class="data-v-861b5b94" bindclick="{{I}}" u-i="861b5b94-12" bind:__l="__l" u-p="{{J}}"></u-button><u-button wx:if="{{L}}" class="data-v-861b5b94" bindclick="{{K}}" u-i="861b5b94-13" bind:__l="__l" u-p="{{L}}"></u-button></view></view></view></view>