{"version": 3, "file": "city-select.js", "sources": ["../../../../src/pages/city-select.vue", "../../../../uniPage:/cGFnZXMvY2l0eS1zZWxlY3QudnVl"], "sourcesContent": ["<template>\n  <view class=\"city-select-page\">\n    <!-- 导航栏 -->\n    <view class=\"nav-bar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <u-icon name=\"arrow-left\" size=\"40rpx\" color=\"#333\"></u-icon>\n      </view>\n      <view class=\"nav-title\">选择城市</view>\n      <view class=\"nav-right\"></view>\n    </view>\n\n    <!-- 搜索框 -->\n    <view class=\"search-container\">\n      <view class=\"search-box\">\n        <u-icon name=\"search\" size=\"32rpx\" color=\"#999\" class=\"search-icon\"></u-icon>\n        <input\n          class=\"search-input\"\n          placeholder=\"搜索城市名称\"\n          v-model=\"searchKeyword\"\n          @input=\"onSearchInput\"\n          confirm-type=\"search\"\n          @confirm=\"onSearch\"\n        />\n        <view v-if=\"searchKeyword\" class=\"clear-btn\" @click=\"clearSearch\">\n          <u-icon name=\"close-circle-fill\" size=\"32rpx\" color=\"#ccc\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 当前定位城市 -->\n    <view v-if=\"currentCity && !searchKeyword\" class=\"current-location\">\n      <view class=\"section-title\">当前定位</view>\n      <view class=\"city-item\" @click=\"selectCity(currentCity)\">\n        <u-icon name=\"map-pin\" size=\"32rpx\" color=\"#6AC086\" class=\"location-icon\"></u-icon>\n        <text class=\"city-name\">{{ currentCity.name }}</text>\n      </view>\n    </view>\n\n    <!-- 城市列表容器 -->\n    <view class=\"city-list-container\">\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <u-loading-icon mode=\"spinner\" size=\"60rpx\" color=\"#6AC086\"></u-loading-icon>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n\n      <!-- 城市列表主体 -->\n      <view v-else class=\"city-content\">\n        <!-- 搜索结果列表 -->\n        <scroll-view\n          v-if=\"searchKeyword\"\n          class=\"city-scroll\"\n          scroll-y\n          :style=\"{ height: scrollHeight + 'rpx' }\"\n        >\n          <view v-if=\"searchResults.length > 0\" class=\"search-results\">\n            <view class=\"section-title\">搜索结果</view>\n            <view\n              v-for=\"city in searchResults\"\n              :key=\"city.adcode\"\n              class=\"city-item\"\n              @click=\"selectCity(city)\"\n            >\n              <text class=\"city-name\">{{ city.name }}</text>\n              <text class=\"city-pinyin\">{{ city.pinyin }}</text>\n            </view>\n          </view>\n\n          <!-- 搜索无结果 -->\n          <view v-else class=\"no-data\">\n            <u-icon name=\"search\" size=\"80rpx\" color=\"#ccc\"></u-icon>\n            <text class=\"no-data-text\">未找到相关城市</text>\n          </view>\n        </scroll-view>\n\n        <!-- 字母分组城市列表 -->\n        <view v-else class=\"grouped-city-container\">\n          <!-- 城市列表滚动区域 -->\n          <scroll-view\n            class=\"city-scroll\"\n            scroll-y\n            :style=\"{ height: scrollHeight + 'rpx' }\"\n            :scroll-into-view=\"scrollIntoView\"\n            @scroll=\"onScroll\"\n          >\n            <view v-if=\"Object.keys(groupedCities).length > 0\" class=\"grouped-city-list\">\n              <view\n                v-for=\"(cities, letter) in groupedCities\"\n                :key=\"letter\"\n                :id=\"`letter-${letter}`\"\n                class=\"letter-group\"\n              >\n                <!-- 字母标题 -->\n                <view class=\"letter-header\">{{ letter }}</view>\n\n                <!-- 该字母下的城市列表 -->\n                <view class=\"cities-in-letter\">\n                  <view\n                    v-for=\"city in cities\"\n                    :key=\"city.adcode\"\n                    class=\"city-item\"\n                    @click=\"selectCity(city)\"\n                  >\n                    <text class=\"city-name\">{{ city.name }}</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n\n            <!-- 无数据提示 -->\n            <view v-else class=\"no-data\">\n              <u-icon name=\"list\" size=\"80rpx\" color=\"#ccc\"></u-icon>\n              <text class=\"no-data-text\">暂无城市数据</text>\n            </view>\n          </scroll-view>\n\n          <!-- 右侧字母索引导航 -->\n          <view class=\"letter-index\">\n            <view\n              v-for=\"letter in availableLetters\"\n              :key=\"letter\"\n              class=\"letter-index-item\"\n              :class=\"{ active: currentLetter === letter }\"\n              @click=\"scrollToLetter(letter)\"\n            >\n              {{ letter }}\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 字母提示浮层 -->\n    <view v-if=\"showLetterTip\" class=\"letter-tip\">\n      {{ currentLetter }}\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, nextTick } from 'vue'\nimport { onLoad, onShow } from '@dcloudio/uni-app'\nimport { getCityList, getAddr } from '@/api'\n\n// {{ AURA-X: Modify - 重构城市选择页面，支持字母索引导航和分组显示. Confirmed via 寸止. }}\n// 响应式数据\nconst searchKeyword = ref('')\nconst cities = ref([])\nconst groupedCities = ref({})\nconst currentCity = ref(null)\nconst loading = ref(false)\nconst scrollHeight = ref(1000)\nconst scrollIntoView = ref('')\nconst currentLetter = ref('A')\nconst showLetterTip = ref(false)\nconst letterTipTimer = ref(null)\n\n// 计算属性\nconst searchResults = computed(() => {\n  if (!searchKeyword.value) {\n    return []\n  }\n  return cities.value.filter(city =>\n    city.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||\n    (city.pinyin && city.pinyin.toLowerCase().includes(searchKeyword.value.toLowerCase()))\n  )\n})\n\n// 可用的字母列表\nconst availableLetters = computed(() => {\n  return Object.keys(groupedCities.value).sort()\n})\n\n// 页面加载\nonLoad(() => {\n  calculateScrollHeight()\n  getCurrentLocation()\n  loadCityList()\n})\n\nonShow(() => {\n  calculateScrollHeight()\n})\n\n// 计算滚动区域高度 - 修复废弃API警告\nconst calculateScrollHeight = async () => {\n  await nextTick()\n  // 使用新的API替代已废弃的getSystemInfoSync\n  const windowInfo = uni.getWindowInfo()\n  const windowHeight = windowInfo.windowHeight\n  // 导航栏(88rpx) + 搜索框(120rpx) + 当前定位(可选,100rpx) + 标题(80rpx) + 底部安全区域(40rpx)\n  const usedHeight = 88 + 120 + (currentCity.value ? 100 : 0) + 80 + 40\n  scrollHeight.value = (windowHeight * 2) - usedHeight // 转换为rpx\n}\n\n// 获取当前定位\nconst getCurrentLocation = () => {\n  uni.getLocation({\n    type: 'wgs84',\n    isHighAccuracy: true,\n    success: async (res) => {\n      try {\n        const addrRes = await getAddr({\n          longitude: res.longitude,\n          latitude: res.latitude\n        })\n        \n        if (addrRes && addrRes.status == 1) {\n          const add = addrRes.regeocode.addressComponent\n          const cityName = uni.$u.test.isEmpty(add.city) ? add.province : add.city\n          // {{ AURA-X: Modify - 统一使用市级adcode格式，包括直辖市. Confirmed via 寸止. }}\n          if (add.adcode && add.adcode.length >= 4) {\n            const provinceCode = add.adcode.substring(0, 2) + '0000';\n            // 直辖市映射到对应的市级adcode\n            const municipalityMap = {\n              '110000': '110100', // 北京市 → 北京城区\n              '120000': '120100', // 天津市 → 天津城区\n              '310000': '310100', // 上海市 → 上海城区\n              '500000': '500100'  // 重庆市 → 重庆城区\n            };\n\n            let cityCode;\n            if (municipalityMap[provinceCode]) {\n              // 直辖市使用对应的市级adcode\n              cityCode = municipalityMap[provinceCode];\n            } else {\n              // 其他城市使用市级adcode（前4位+00）\n              cityCode = add.adcode.substring(0, 4) + '00';\n            }\n\n            currentCity.value = {\n              adcode: cityCode,\n              name: cityName\n            }\n            calculateScrollHeight()\n          }\n        }\n      } catch (error) {\n        console.log('获取定位失败:', error)\n      }\n    },\n    fail: (error) => {\n      console.log('定位失败:', error)\n    }\n  })\n}\n\n// 加载城市列表\nconst loadCityList = async () => {\n  try {\n    loading.value = true\n    // 请求按首字母分组的城市数据\n    const res = await getCityList('', 1) // 第二个参数为1表示按首字母分组\n\n    if (res.status === 'ok') {\n      if (res.data && typeof res.data === 'object') {\n        // 分组数据\n        groupedCities.value = res.data\n        // 同时保存平铺的城市列表用于搜索\n        cities.value = []\n        Object.values(res.data).forEach(letterCities => {\n          cities.value.push(...letterCities)\n        })\n      } else {\n        // 兼容旧版本API返回的数组格式\n        cities.value = res.data || []\n        groupCitiesByLetter()\n      }\n    } else {\n      uni.$u.toast(res.msg || '获取城市列表失败')\n    }\n  } catch (error) {\n    console.error('加载城市列表失败:', error)\n    uni.$u.toast('网络错误，请稍后重试')\n  } finally {\n    loading.value = false\n  }\n}\n\n// 手动按首字母分组（兼容旧版本API）\nconst groupCitiesByLetter = () => {\n  const grouped = {}\n  cities.value.forEach(city => {\n    const letter = city.first_letter || getFirstLetter(city.name)\n    if (!grouped[letter]) {\n      grouped[letter] = []\n    }\n    grouped[letter].push(city)\n  })\n  groupedCities.value = grouped\n}\n\n// 获取中文首字母（简单实现）\nconst getFirstLetter = (name) => {\n  const firstChar = name.charAt(0)\n  // 简单的中文首字母映射\n  const letterMap = {\n    '阿': 'A', '安': 'A', '鞍': 'A',\n    '北': 'B', '保': 'B', '包': 'B', '蚌': 'B', '滨': 'B', '亳': 'B', '白': 'B', '百': 'B',\n    '成': 'C', '重': 'C', '长': 'C', '常': 'C', '沧': 'C', '承': 'C', '赤': 'C',\n    '大': 'D', '东': 'D', '丹': 'D', '德': 'D',\n    '佛': 'F', '福': 'F', '抚': 'F', '阜': 'F', '防': 'F',\n    '广': 'G', '贵': 'G', '桂': 'G', '赣': 'G',\n    '杭': 'H', '哈': 'H', '合': 'H', '海': 'H', '惠': 'H', '邯': 'H', '衡': 'H', '呼': 'H', '葫': 'H', '鹤': 'H', '黑': 'H', '淮': 'H', '湖': 'H', '黄': 'H', '菏': 'H', '怀': 'H', '贺': 'H',\n    '济': 'J', '吉': 'J', '锦': 'J', '佳': 'J', '荆': 'J', '九': 'J', '吉': 'J', '景': 'J', '金': 'J', '嘉': 'J',\n    '昆': 'K', '开': 'K',\n    '兰': 'L', '拉': 'L', '洛': 'L', '柳': 'L', '廊': 'L', '聊': 'L', '临': 'L', '莱': 'L', '辽': 'L', '连': 'L', '丽': 'L', '泸': 'L', '乐': 'L', '凉': 'L', '娄': 'L',\n    '绵': 'M', '牡': 'M', '马': 'M', '梅': 'M', '眉': 'M',\n    '南': 'N', '宁': 'N', '内': 'N',\n    '平': 'P', '盘': 'P', '萍': 'P', '莆': 'P',\n    '青': 'Q', '秦': 'Q', '齐': 'Q', '泉': 'Q', '曲': 'Q',\n    '日': 'R',\n    '上': 'S', '深': 'S', '沈': 'S', '石': 'S', '苏': 'S', '三': 'S', '绍': 'S', '宿': 'S', '遂': 'S',\n    '天': 'T', '太': 'T', '唐': 'T', '铁': 'T', '台': 'T', '通': 'T', '泰': 'T',\n    '武': 'W', '无': 'W', '温': 'W', '潍': 'W', '威': 'W', '芜': 'W', '梧': 'W',\n    '西': 'X', '厦': 'X', '西': 'X', '徐': 'X', '邢': 'X', '信': 'X', '新': 'X', '许': 'X', '孝': 'X', '襄': 'X', '湘': 'X',\n    '银': 'Y', '烟': 'Y', '扬': 'Y', '盐': 'Y', '岳': 'Y', '益': 'Y', '永': 'Y', '玉': 'Y', '宜': 'Y', '雅': 'Y',\n    '郑': 'Z', '珠': 'Z', '湛': 'Z', '中': 'Z', '张': 'Z', '淄': 'Z', '枣': 'Z', '镇': 'Z', '舟': 'Z', '株': 'Z', '遵': 'Z', '自': 'Z', '资': 'Z'\n  }\n  return letterMap[firstChar] || 'Z'\n}\n\n// 搜索输入处理\nconst onSearchInput = (e) => {\n  searchKeyword.value = e.detail.value\n}\n\n// 搜索确认\nconst onSearch = () => {\n  // 实时搜索，无需额外处理\n}\n\n// 清除搜索\nconst clearSearch = () => {\n  searchKeyword.value = ''\n}\n\n// 选择城市\nconst selectCity = (city) => {\n  // 存储选择的城市到全局状态\n  uni.setStorageSync('selectedCity', {\n    adcode: city.adcode,\n    name: city.name\n  })\n  \n  // 返回首页并传递城市信息\n  uni.navigateBack({\n    success: () => {\n      // 通过事件总线通知首页更新城市\n      uni.$emit('citySelected', city)\n    }\n  })\n}\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack()\n}\n\n// 滚动到指定字母\nconst scrollToLetter = (letter) => {\n  scrollIntoView.value = `letter-${letter}`\n  currentLetter.value = letter\n  showLetterTip.value = true\n\n  // 清除之前的定时器\n  if (letterTipTimer.value) {\n    clearTimeout(letterTipTimer.value)\n  }\n\n  // 1秒后隐藏字母提示\n  letterTipTimer.value = setTimeout(() => {\n    showLetterTip.value = false\n  }, 1000)\n}\n\n// 监听滚动事件，更新当前字母\nconst onScroll = (e) => {\n  // 这里可以根据滚动位置计算当前显示的字母\n  // 由于微信小程序的限制，这里简化处理\n}\n\n// 滚动到底部\nconst onReachBottom = () => {\n  // 如果需要分页加载，在这里实现\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.city-select-page {\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n.nav-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 32rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n  \n  .nav-left, .nav-right {\n    width: 80rpx;\n    height: 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333;\n  }\n}\n\n.search-container {\n  padding: 24rpx 32rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n  \n  .search-box {\n    position: relative;\n    display: flex;\n    align-items: center;\n    height: 72rpx;\n    background-color: #f5f5f5;\n    border-radius: 36rpx;\n    padding: 0 32rpx;\n    \n    .search-icon {\n      margin-right: 16rpx;\n    }\n    \n    .search-input {\n      flex: 1;\n      font-size: 28rpx;\n      color: #333;\n      \n      &::placeholder {\n        color: #999;\n      }\n    }\n    \n    .clear-btn {\n      margin-left: 16rpx;\n      padding: 8rpx;\n    }\n  }\n}\n\n.current-location {\n  background-color: #fff;\n  margin-bottom: 16rpx;\n  \n  .section-title {\n    padding: 24rpx 32rpx 16rpx;\n    font-size: 24rpx;\n    color: #666;\n    background-color: #f8f9fa;\n  }\n  \n  .city-item {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 32rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    .location-icon {\n      margin-right: 16rpx;\n    }\n    \n    .city-name {\n      font-size: 28rpx;\n      color: #333;\n    }\n  }\n}\n\n.city-list-container {\n  background-color: #fff;\n  flex: 1;\n\n  .loading-container {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 80rpx 0;\n\n    .loading-text {\n      margin-top: 16rpx;\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n\n  .city-content {\n    height: 100%;\n\n    // 搜索结果样式\n    .search-results {\n      .section-title {\n        padding: 24rpx 32rpx 16rpx;\n        font-size: 24rpx;\n        color: #666;\n        background-color: #f8f9fa;\n      }\n\n      .city-item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 24rpx 32rpx;\n        border-bottom: 1rpx solid #f0f0f0;\n\n        &:active {\n          background-color: #f5f5f5;\n        }\n\n        .city-name {\n          font-size: 28rpx;\n          color: #333;\n          flex: 1;\n        }\n\n        .city-pinyin {\n          font-size: 24rpx;\n          color: #999;\n          margin-left: 16rpx;\n        }\n      }\n    }\n\n    // 分组城市列表容器\n    .grouped-city-container {\n      position: relative;\n      height: 100%;\n\n      .city-scroll {\n        height: 100%;\n\n        .grouped-city-list {\n          .letter-group {\n            .letter-header {\n              position: sticky;\n              top: 0;\n              z-index: 10;\n              padding: 16rpx 32rpx;\n              font-size: 24rpx;\n              font-weight: 600;\n              color: #666;\n              background-color: #f8f9fa;\n              border-bottom: 1rpx solid #e0e0e0;\n            }\n\n            .cities-in-letter {\n              .city-item {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                padding: 32rpx;\n                border-bottom: 1rpx solid #f0f0f0;\n                min-height: 88rpx; // 确保最小触摸区域\n\n                &:active {\n                  background-color: #f5f5f5;\n                }\n\n                .city-name {\n                  font-size: 30rpx;\n                  color: #333;\n                  font-weight: 500;\n                }\n              }\n            }\n          }\n        }\n      }\n\n      // 右侧字母索引导航\n      .letter-index {\n        position: fixed;\n        right: 16rpx;\n        top: 50%;\n        transform: translateY(-50%);\n        z-index: 100;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        background-color: rgba(255, 255, 255, 0.9);\n        border-radius: 24rpx;\n        padding: 16rpx 8rpx;\n        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);\n\n        .letter-index-item {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 48rpx;\n          height: 48rpx;\n          margin: 2rpx 0;\n          font-size: 22rpx;\n          font-weight: 600;\n          color: #666;\n          border-radius: 50%;\n          transition: all 0.2s ease;\n\n          &.active {\n            background-color: #6AC086;\n            color: #fff;\n            transform: scale(1.1);\n          }\n\n          &:active {\n            background-color: #6AC086;\n            color: #fff;\n            transform: scale(0.95);\n          }\n        }\n      }\n    }\n\n    // 无数据提示\n    .no-data {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 120rpx 0;\n\n      .no-data-text {\n        margin-top: 24rpx;\n        font-size: 28rpx;\n        color: #999;\n      }\n    }\n  }\n}\n\n// 字母提示浮层\n.letter-tip {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 120rpx;\n  height: 120rpx;\n  background-color: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  font-size: 48rpx;\n  font-weight: 600;\n  border-radius: 16rpx;\n  pointer-events: none;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/city-select.vue'\nwx.createPage(MiniProgramPage)"], "names": ["searchKeyword", "ref", "cities", "groupedCities", "currentCity", "loading", "scrollHeight", "scrollIntoView", "currentLetter", "showLetterTip", "letterTipTimer", "searchResults", "computed", "value", "filter", "city", "name", "toLowerCase", "includes", "pinyin", "availableLetters", "Object", "keys", "sort", "onLoad", "calculateScrollHeight", "getCurrentLocation", "loadCityList", "onShow", "async", "nextTick", "windowHeight", "uni", "index", "getWindowInfo", "usedHeight", "getLocation", "type", "isHighAccuracy", "success", "res", "addrRes", "getAddr", "longitude", "latitude", "status", "add", "regeocode", "addressComponent", "cityName", "$u", "test", "isEmpty", "province", "adcode", "length", "provinceCode", "substring", "municipalityMap", "cityCode", "error", "console", "log", "fail", "getCityList", "data", "values", "for<PERSON>ach", "letterCities", "push", "groupCitiesByLetter", "toast", "msg", "grouped", "letter", "first_letter", "getFirstLetter", "char<PERSON>t", "onSearchInput", "e", "detail", "onSearch", "clearSearch", "selectCity", "common_vendor", "setStorageSync", "navigateBack", "$emit", "goBack", "onScroll", "clearTimeout", "setTimeout", "wx", "createPage", "MiniProgramPage"], "mappings": "0lBAkJM,MAAAA,EAAgBC,EAAGA,IAAC,IACpBC,EAASD,EAAGA,IAAC,IACbE,EAAgBF,EAAGA,IAAC,IACpBG,EAAcH,EAAGA,IAAC,MAClBI,EAAUJ,EAAGA,KAAC,GACdK,EAAeL,EAAGA,IAAC,KACnBM,EAAiBN,EAAGA,IAAC,IACrBO,EAAgBP,EAAGA,IAAC,KACpBQ,EAAgBR,EAAGA,KAAC,GACpBS,EAAiBT,EAAGA,IAAC,MAGrBU,EAAgBC,EAAQA,UAAC,IACxBZ,EAAca,MAGZX,EAAOW,MAAMC,QAAOC,GACzBA,EAAKC,KAAKC,cAAcC,SAASlB,EAAca,MAAMI,gBACpDF,EAAKI,QAAUJ,EAAKI,OAAOF,cAAcC,SAASlB,EAAca,MAAMI,iBAJhE,KASLG,EAAmBR,EAAQA,UAAC,IACzBS,OAAOC,KAAKnB,EAAcU,OAAOU,SAI1CC,EAAAA,QAAO,KACiBC,IACHC,IACNC,GAAA,IAGfC,EAAAA,QAAO,KACiBH,GAAA,IAIxB,MAAMA,EAAwBI,gBACtBC,eAEA,MACAC,EADaC,EAAGC,MAACC,gBACSH,aAE1BI,EAAa,KAAY/B,EAAYS,MAAQ,IAAM,GAAK,GAAK,GACtDP,EAAAO,MAAwB,EAAfkB,EAAoBI,CAAA,EAItCT,EAAqB,KACzBM,EAAAA,MAAII,YAAY,CACdC,KAAM,QACNC,gBAAgB,EAChBC,QAASV,MAAOW,IACV,IACI,MAAAC,QAAgBC,UAAQ,CAC5BC,UAAWH,EAAIG,UACfC,SAAUJ,EAAII,WAGZ,GAAAH,GAA6B,GAAlBA,EAAQI,OAAa,CAC5B,MAAAC,EAAML,EAAQM,UAAUC,iBACxBC,EAAWjB,EAAAA,MAAIkB,GAAGC,KAAKC,QAAQN,EAAI/B,MAAQ+B,EAAIO,SAAWP,EAAI/B,KAEpE,GAAI+B,EAAIQ,QAAUR,EAAIQ,OAAOC,QAAU,EAAG,CACxC,MAAMC,EAAeV,EAAIQ,OAAOG,UAAU,EAAG,GAAK,OAE5CC,EAAkB,CACtB,KAAU,SACV,KAAU,SACV,KAAU,SACV,IAAU,UAGR,IAAAC,EAGFA,EAFED,EAAgBF,GAEPE,EAAgBF,GAGhBV,EAAIQ,OAAOG,UAAU,EAAG,GAAK,KAG1CrD,EAAYS,MAAQ,CAClByC,OAAQK,EACR3C,KAAMiC,GAEcxB,GACxB,CACF,CACD,OAAQmC,GACCC,QAAAC,IAAI,UAAWF,EACzB,GAEFG,KAAOH,IACGC,QAAAC,IAAI,QAASF,EAAK,GAE7B,EAIGjC,EAAeE,UACf,IACFxB,EAAQQ,OAAQ,EAEhB,MAAM2B,QAAYwB,cAAY,GAAI,GAEf,OAAfxB,EAAIK,OACFL,EAAIyB,MAA4B,iBAAbzB,EAAIyB,MAEzB9D,EAAcU,MAAQ2B,EAAIyB,KAE1B/D,EAAOW,MAAQ,GACfQ,OAAO6C,OAAO1B,EAAIyB,MAAME,SAAwBC,IACvClE,EAAAW,MAAMwD,QAAQD,EAAY,MAI5BlE,EAAAW,MAAQ2B,EAAIyB,MAAQ,GACPK,KAGtBtC,EAAAA,MAAIkB,GAAGqB,MAAM/B,EAAIgC,KAAO,WAE3B,OAAQZ,GACCC,QAAAD,MAAM,YAAaA,WACvBV,GAAGqB,MAAM,aACjB,CAAY,QACRlE,EAAQQ,OAAQ,CAClB,GAIIyD,EAAsB,KAC1B,MAAMG,EAAU,CAAC,EACVvE,EAAAW,MAAMsD,SAAgBpD,IAC3B,MAAM2D,EAAS3D,EAAK4D,cAAgBC,EAAe7D,EAAKC,MACnDyD,EAAQC,KACHD,EAAAC,GAAU,IAEZD,EAAAC,GAAQL,KAAKtD,EAAI,IAE3BZ,EAAcU,MAAQ4D,CAAA,EAIlBG,EAAkB5D,IAGJ,CAChB,IAAK,IAAK,IAAK,IAAK,IAAK,IACzB,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC3E,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACnC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7C,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACnC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/F,IAAK,IAAK,IAAK,IACf,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjJ,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7C,IAAK,IAAK,IAAK,IAAK,IAAK,IACzB,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACnC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC7C,IAAK,IACL,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACrF,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACjE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACzG,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAC/F,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAvB7GA,EAAK6D,OAAO,KAyBC,KAI3BC,EAAiBC,IACP/E,EAAAa,MAAQkE,EAAEC,OAAOnE,KAAA,EAI3BoE,EAAW,OAKXC,EAAc,KAClBlF,EAAca,MAAQ,EAAA,EAIlBsE,EAAcpE,IAEfqE,EAAAnD,MAACoD,eAAe,eAAgB,CACjC/B,OAAQvC,EAAKuC,OACbtC,KAAMD,EAAKC,OAIbgB,EAAAA,MAAIsD,aAAa,CACf/C,QAAS,aAEHgD,MAAM,eAAgBxE,EAAI,GAEjC,EAIGyE,EAAS,KACbxD,EAAAA,MAAIsD,cAAa,EAqBbG,EAAYV,IAAD,4kCAjBM,CAACL,IACtBnE,EAAeM,MAAQ,UAAU6D,IACjClE,EAAcK,MAAQ6D,EACtBjE,EAAcI,OAAQ,EAGlBH,EAAeG,OACjB6E,aAAahF,EAAeG,OAIfH,EAAAG,MAAQ8E,YAAW,KAChClF,EAAcI,OAAQ,CAAA,GACrB,IAAI,iHCpXT+E,GAAGC,WAAWC"}