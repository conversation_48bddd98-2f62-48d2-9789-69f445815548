{"version": 3, "file": "manage.js", "sources": ["../../../../../../src/pages/bundle/branch_president/manage.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGJyYW5jaF9wcmVzaWRlbnRcbWFuYWdlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page branch-management\">\n    <myTitle\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      title=\"分会管理\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <!-- 分会信息卡片 -->\n      <view class=\"branch-info-card\">\n        <view class=\"card-header\">\n          <view class=\"branch-name\">{{ branchInfo.branch_name || '加载中...' }}</view>\n          <view class=\"branch-location\">{{ branchInfo.branch_location || '' }}</view>\n        </view>\n        <view class=\"stats-container\">\n          <view class=\"stat-item\">\n            <view class=\"stat-number\">{{ stats.pending_activities || 0 }}</view>\n            <view class=\"stat-label\">待审核活动</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-number\">{{ stats.total_members || 0 }}</view>\n            <view class=\"stat-label\">总成员</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-number\">{{ stats.vip_members || 0 }}</view>\n            <view class=\"stat-label\">会员用户</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-number\">{{ stats.normal_members || 0 }}</view>\n            <view class=\"stat-label\">普通用户</view>\n          </view>\n          <view class=\"stat-item\">\n            <view class=\"stat-number\">{{ stats.month_commission || '0.00' }}</view>\n            <view class=\"stat-label\">本月佣金</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 功能菜单 -->\n      <view class=\"menu-container\">\n        <view class=\"menu-title\">管理功能</view>\n        <view class=\"menu-grid\">\n          <view \n            class=\"menu-item\"\n            @click=\"navto('/pages/bundle/branch_president/pending_activities')\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon name=\"list\" size=\"40\" color=\"#6AC086\"></u-icon>\n            </view>\n            <view class=\"menu-text\">待审核活动</view>\n            <view class=\"menu-badge\" v-if=\"stats.pending_activities > 0\">\n              {{ stats.pending_activities }}\n            </view>\n          </view>\n          \n          <view \n            class=\"menu-item\"\n            @click=\"navto('/pages/bundle/branch_president/commission')\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon name=\"rmb-circle\" size=\"40\" color=\"#6AC086\"></u-icon>\n            </view>\n            <view class=\"menu-text\">运营佣金</view>\n          </view>\n          \n          <view \n            class=\"menu-item\"\n            @click=\"viewBranchMembers\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon name=\"account\" size=\"40\" color=\"#6AC086\"></u-icon>\n            </view>\n            <view class=\"menu-text\">分会成员</view>\n          </view>\n          \n          <view \n            class=\"menu-item\"\n            @click=\"viewBranchActivities\"\n          >\n            <view class=\"menu-icon\">\n              <u-icon name=\"calendar\" size=\"40\" color=\"#6AC086\"></u-icon>\n            </view>\n            <view class=\"menu-text\">分会活动</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 快捷操作 -->\n      <view class=\"quick-actions\">\n        <view class=\"section-title\">快捷操作</view>\n        <view class=\"action-list\">\n          <view class=\"action-item\" @click=\"refreshData\">\n            <u-icon name=\"reload\" size=\"32\" color=\"#6AC086\"></u-icon>\n            <text class=\"action-text\">刷新数据</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <u-loading-page \n      :loading=\"loading\" \n      loading-text=\"加载中...\"\n      bg-color=\"#f8f9fa\"\n    ></u-loading-page>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive } from \"vue\";\nimport { onLoad, onShow } from \"@dcloudio/uni-app\";\nimport { navto } from \"@/utils\";\nimport { requireLogin } from \"@/utils/auth\";\nimport { branch_presidentpending_activities, branch_presidentget_stats } from \"@/api\";\nimport { store } from \"@/store\";\n// 修复：使用统一的权限检查工具函数\nimport { hasBranchManagementPermission } from \"@/utils/permissions\";\n\n// 分会信息\nconst branchInfo = reactive({\n  branch_name: '',\n  branch_location: '',\n  branch_id: null\n});\n\n// 统计数据\nconst stats = reactive({\n  pending_activities: 0,\n  total_members: 0,\n  normal_members: 0,\n  vip_members: 0,\n  month_commission: '0.00'\n});\n\nconst loading = ref(true);\n\n// 页面加载\nonLoad(() => {\n  if (!requireLogin()) {\n    return;\n  }\n  \n  // 修复：使用统一的权限检查工具函数\n  if (!hasBranchManagementPermission()) {\n    uni.showModal({\n      title: '权限不足',\n      content: '您没有分会管理权限，无法访问此页面',\n      showCancel: false,\n      success: () => {\n        uni.navigateBack();\n      }\n    });\n    return;\n  }\n  \n  loadData();\n});\n\nonShow(() => {\n  // 每次显示页面时刷新数据\n  refreshData();\n});\n\n// 加载数据\nconst loadData = async () => {\n  try {\n    loading.value = true;\n    \n    const userInfo = store().$state.userInfo;\n    \n    // 设置分会信息（从用户信息中获取）\n    branchInfo.branch_name = userInfo.branch_name || '未知分会';\n    branchInfo.branch_location = userInfo.branch_location || '';\n    branchInfo.branch_id = userInfo.branch_id;\n    \n    // 获取待审核活动数量\n    await loadPendingActivitiesCount();\n\n    // 获取分会统计数据\n    await loadBranchStats();\n    \n  } catch (error) {\n    console.error('加载数据失败:', error);\n    uni.showToast({\n      title: '加载失败',\n      icon: 'none'\n    });\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 获取待审核活动数量\nconst loadPendingActivitiesCount = async () => {\n  try {\n    const userInfo = store().$state.userInfo;\n    const res = await branch_presidentpending_activities({\n      uid: userInfo.uid,\n      token: userInfo.token,\n      page: 1,\n      page_size: 1\n    });\n    \n    if (res.status === 'ok') {\n      stats.pending_activities = res.count || 0;\n    }\n  } catch (error) {\n    console.error('获取待审核活动数量失败:', error);\n  }\n};\n\n// 获取分会统计数据\nconst loadBranchStats = async () => {\n  try {\n    const userInfo = store().$state.userInfo;\n    const res = await branch_presidentget_stats({\n      uid: userInfo.uid,\n      token: userInfo.token\n    });\n\n    if (res.status === 'ok') {\n      stats.total_members = res.data.total_members || 0;\n      stats.normal_members = res.data.normal_members || 0;\n      stats.vip_members = res.data.vip_members || 0;\n      stats.month_commission = res.data.month_commission || '0.00';\n    }\n  } catch (error) {\n    console.error('获取分会统计数据失败:', error);\n  }\n};\n\n// 刷新数据\nconst refreshData = () => {\n  loadData();\n};\n\n// 查看分会成员\nconst viewBranchMembers = () => {\n  uni.navigateTo({\n    url: '/pages/bundle/branch_president/branch_members'\n  });\n};\n\n// 查看分会活动\nconst viewBranchActivities = () => {\n  uni.navigateTo({\n    url: '/pages/bundle/branch_president/branch_activities'\n  });\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/style/wcag-colors.scss';\n.page {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.content-container {\n  padding: 30rpx;\n}\n\n.branch-info-card {\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  color: #ffffff;\n}\n\n.card-header {\n  margin-bottom: 30rpx;\n}\n\n.branch-name {\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.branch-location {\n  font-size: 26rpx;\n  color: rgba(255, 255, 255, 0.95); /* 提高对比度，从opacity: 0.8改为更高对比度的颜色 */\n}\n\n.stats-container {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 20rpx;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 20rpx 10rpx;\n  background: rgba(106, 192, 134, 0.05);\n  border-radius: 12rpx;\n  border: 1rpx solid rgba(106, 192, 134, 0.1);\n}\n\n.stat-number {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 6rpx;\n  color: #ffffff; /* 在绿色背景上使用白色，确保足够对比度 */\n}\n\n.stat-label {\n  font-size: 22rpx;\n  color: rgba(255, 255, 255, 0.9); /* 提高对比度，确保在绿色背景上可读 */\n}\n\n.menu-container {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.menu-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #1a1a1a; /* 加深颜色，提高与白色背景的对比度 */\n  margin-bottom: 30rpx;\n}\n\n.menu-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30rpx;\n}\n\n.menu-item {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30rpx 20rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  transition: all 0.3s ease;\n}\n\n.menu-item:active {\n  transform: scale(0.95);\n  background: #e9ecef;\n}\n\n.menu-icon {\n  margin-bottom: 16rpx;\n}\n\n.menu-text {\n  font-size: 26rpx;\n  color: #1a1a1a; /* 加深颜色，提高对比度 */\n  text-align: center;\n}\n\n.menu-badge {\n  position: absolute;\n  top: 10rpx;\n  right: 10rpx;\n  background: #ff4757;\n  color: #ffffff;\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 20rpx;\n  min-width: 32rpx;\n  text-align: center;\n}\n\n.quick-actions {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #1a1a1a; /* 加深颜色，提高对比度 */\n  margin-bottom: 30rpx;\n}\n\n.action-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.action-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  transition: all 0.3s ease;\n}\n\n.action-item:active {\n  transform: scale(0.98);\n  background: #e9ecef;\n}\n\n.action-text {\n  margin-left: 20rpx;\n  font-size: 28rpx;\n  color: #1a1a1a; /* 加深颜色，提高对比度 */\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/branch_president/manage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["branchInfo", "reactive", "branch_name", "branch_location", "branch_id", "stats", "pending_activities", "total_members", "normal_members", "vip_members", "month_commission", "loading", "ref", "onLoad", "requireLogin", "hasBranchManagementPermission", "uni", "showModal", "title", "content", "showCancel", "success", "index", "navigateBack", "onShow", "loadData", "async", "value", "userInfo", "store", "$state", "loadPendingActivitiesCount", "loadBranchStats", "error", "console", "showToast", "icon", "res", "branch_presidentpending_activities", "uid", "token", "page", "page_size", "status", "count", "branch_presidentget_stats", "data", "refreshData", "viewBranchMembers", "navigateTo", "url", "viewBranchActivities", "wx", "createPage", "MiniProgramPage"], "mappings": "mxBA0HM,MAAAA,EAAaC,EAAAA,SAAS,CAC1BC,YAAa,GACbC,gBAAiB,GACjBC,UAAW,OAIPC,EAAQJ,EAAAA,SAAS,CACrBK,mBAAoB,EACpBC,cAAe,EACfC,eAAgB,EAChBC,YAAa,EACbC,iBAAkB,SAGdC,EAAUC,EAAAA,KAAI,GAGpBC,EAAAA,QAAO,KACAC,EAAYA,iBAKZC,EAA6BA,oCAChCC,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,QAAS,oBACTC,YAAY,EACZC,QAAS,KACPL,EAAGM,MAACC,cAAY,QASxBC,EAAAA,QAAO,YAMP,MAAMC,EAAWC,UACX,IACFf,EAAQgB,OAAQ,EAEhB,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAGrB5B,EAAAE,YAAc0B,EAAS1B,aAAe,OACtCF,EAAAG,gBAAkByB,EAASzB,iBAAmB,GACzDH,EAAWI,UAAYwB,EAASxB,gBAG1B2B,UAGAC,GAEP,OAAQC,GACCC,QAAAD,MAAM,UAAWA,GACzBjB,EAAAA,MAAImB,UAAU,CACZjB,MAAO,OACPkB,KAAM,QAEZ,CAAY,QACRzB,EAAQgB,OAAQ,CAClB,GAIII,EAA6BL,UAC7B,IACF,MAAME,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BS,QAAYC,qCAAmC,CACnDC,IAAKX,EAASW,IACdC,MAAOZ,EAASY,MAChBC,KAAM,EACNC,UAAW,IAGM,OAAfL,EAAIM,SACAtC,EAAAC,mBAAqB+B,EAAIO,OAAS,EAE3C,OAAQX,GACCC,QAAAD,MAAM,eAAgBA,EAChC,GAIID,EAAkBN,UAClB,IACF,MAAME,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BS,QAAYQ,4BAA0B,CAC1CN,IAAKX,EAASW,IACdC,MAAOZ,EAASY,QAGC,OAAfH,EAAIM,SACAtC,EAAAE,cAAgB8B,EAAIS,KAAKvC,eAAiB,EAC1CF,EAAAG,eAAiB6B,EAAIS,KAAKtC,gBAAkB,EAC5CH,EAAAI,YAAc4B,EAAIS,KAAKrC,aAAe,EACtCJ,EAAAK,iBAAmB2B,EAAIS,KAAKpC,kBAAoB,OAEzD,OAAQuB,GACCC,QAAAD,MAAM,cAAeA,EAC/B,GAIIc,EAAc,UAKdC,EAAoB,KACxBhC,EAAAA,MAAIiC,WAAW,CACbC,IAAK,iDACN,EAIGC,EAAuB,KAC3BnC,EAAAA,MAAIiC,WAAW,CACbC,IAAK,oDACN,29BCzPHE,GAAGC,WAAWC"}