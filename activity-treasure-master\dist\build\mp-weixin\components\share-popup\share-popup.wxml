<view class="{{['share-popup-wrapper', 'data-v-84f55cf7', o && 'show']}}" bindtap="{{p}}"><view class="{{['share-popup', 'data-v-84f55cf7', e && 'show']}}"><view class="share-popup-header data-v-84f55cf7"><text class="share-popup-title data-v-84f55cf7">{{a}}</text><view class="share-popup-close data-v-84f55cf7" bindtap="{{c}}"><u-icon wx:if="{{b}}" class="data-v-84f55cf7" u-i="84f55cf7-0" bind:__l="__l" u-p="{{b}}"></u-icon></view></view><view class="share-options data-v-84f55cf7"><view wx:for="{{d}}" wx:for-item="option" wx:key="c" class="share-option-item data-v-84f55cf7" bindtap="{{option.d}}"><image class="share-option-icon data-v-84f55cf7" src="{{option.a}}" mode="aspectFit"></image><text class="share-option-name data-v-84f55cf7">{{option.b}}</text></view></view></view><painter wx:if="{{f}}" class="data-v-84f55cf7" bindimgOK="{{g}}" bindimgErr="{{h}}" binddidShow="{{i}}" bindviewUpdate="{{j}}" bindtouchStart="{{k}}" bindtouchMove="{{l}}" bindtouchEnd="{{m}}" u-i="84f55cf7-1" bind:__l="__l" u-p="{{n}}"/></view>