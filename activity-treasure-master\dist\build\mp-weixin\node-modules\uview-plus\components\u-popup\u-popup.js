"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-popup",mixins:[e.mpMixin,e.mixin,e.props$13],data(){return{overlayDuration:this.duration+50}},watch:{show(e,t){if(!0===e){const e=this.$children;this.retryComputedComponentRect(e)}}},computed:{transitionStyle(){const t={zIndex:this.zIndex,position:"fixed",display:"flex"};return t[this.mode]=0,"left"===this.mode||"right"===this.mode?e.deepMerge(t,{bottom:0,top:0}):"top"===this.mode||"bottom"===this.mode?e.deepMerge(t,{left:0,right:0}):"center"===this.mode?e.deepMerge(t,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0}):void 0},contentStyle(){const t={};if(e.sys(),"center"!==this.mode&&(t.flex=1),this.bgColor&&(t.backgroundColor=this.bgColor),this.round){const o=e.addUnit(this.round);"top"===this.mode?(t.borderBottomLeftRadius=o,t.borderBottomRightRadius=o):"bottom"===this.mode?(t.borderTopLeftRadius=o,t.borderTopRightRadius=o):"center"===this.mode&&(t.borderRadius=o)}return e.deepMerge(t,e.addStyle(this.customStyle))},position(){return"center"===this.mode?this.zoom?"fade-zoom":"fade":"left"===this.mode?"slide-left":"right"===this.mode?"slide-right":"bottom"===this.mode?"slide-up":"top"===this.mode?"slide-down":void 0}},emits:["open","close","click","update:show"],methods:{overlayClick(){this.closeOnClickOverlay&&(this.$emit("update:show",!1),this.$emit("close"))},close(e){this.$emit("update:show",!1),this.$emit("close")},afterEnter(){this.$emit("open")},clickHandler(){"center"===this.mode&&this.overlayClick(),this.$emit("click")},retryComputedComponentRect(t){const o=["u-calendar-month","u-album","u-collapse-item","u-dropdown","u-index-item","u-index-list","u-line-progress","u-list-item","u-rate","u-read-more","u-row","u-row-notice","u-scroll-list","u-skeleton","u-slider","u-steps-item","u-sticky","u-subsection","u-swipe-action-item","u-tabbar","u-tabs","u-tooltip"];for(let s=0;s<t.length;s++){const i=t[s],n=i.$children;o.includes(i.$options.name)&&"function"==typeof(null==i?void 0:i.init)&&e.sleep(50).then((()=>{i.init()})),n.length&&this.retryComputedComponentRect(n)}}}};if(!Array){(e.resolveComponent("u-overlay")+e.resolveComponent("u-status-bar")+e.resolveComponent("u-icon")+e.resolveComponent("u-safe-bottom")+e.resolveComponent("u-transition"))()}Math||((()=>"../u-overlay/u-overlay.js")+(()=>"../u-status-bar/u-status-bar.js")+(()=>"../u-icon/u-icon.js")+(()=>"../u-safe-bottom/u-safe-bottom.js")+(()=>"../u-transition/u-transition.js"))();const o=e._export_sfc(t,[["render",function(t,o,s,i,n,r){return e.e({a:t.overlay},t.overlay?{b:e.o(r.overlayClick),c:e.p({show:t.show,zIndex:t.zIndex,duration:n.overlayDuration,customStyle:t.overlayStyle,opacity:t.overlayOpacity})}:{},{d:t.safeAreaInsetTop},(t.safeAreaInsetTop,{}),{e:t.closeable},t.closeable?{f:e.p({name:"close",color:"#909399",size:"18",bold:!0}),g:e.o(((...e)=>r.close&&r.close(...e))),h:e.n("u-popup__content__close--"+t.closeIconPos)}:{},{i:t.safeAreaInsetBottom},(t.safeAreaInsetBottom,{}),{j:e.s(r.contentStyle),k:e.o(((...e)=>t.noop&&t.noop(...e))),l:e.o(r.afterEnter),m:e.o(r.clickHandler),n:e.p({show:t.show,customStyle:r.transitionStyle,mode:r.position,duration:t.duration}),o:e.n(t.customClass)})}],["__scopeId","data-v-f4fdd9de"]]);wx.createComponent(o);
//# sourceMappingURL=u-popup.js.map
