"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-safe-bottom",mixins:[e.mpMixin,e.mixin,e.props$21],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return e.deepMerge({},e.addStyle(this.customStyle))}},mounted(){}};const o=e._export_sfc(t,[["render",function(t,o,s,n,r,a){return{a:e.s(a.style),b:e.n(!r.isNvue&&"u-safe-area-inset-bottom")}}],["__scopeId","data-v-fcb47c48"]]);wx.createComponent(o);
//# sourceMappingURL=u-safe-bottom.js.map
