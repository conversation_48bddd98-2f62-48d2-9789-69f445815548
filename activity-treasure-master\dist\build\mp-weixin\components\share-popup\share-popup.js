"use strict";const e=require("../../common/vendor.js"),o=require("../../utils/painterShare.js"),l=require("../../utils/uniShareConfig.js"),a=require("../../utils/painterConfig.js");if(require("./uni-share.js"),require("./uni-image-menu.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("painter"))()}Math;const n={__name:"share-popup",props:{show:{type:Boolean,default:!1},title:{type:String,default:"分享"},shareData:{type:Object,default:()=>({})},showMemberInvite:{type:Boolean,default:!1}},emits:["close","select","share-success","share-error"],setup(n,{emit:t}){const s=n,r=e.ref(!1),i=e.ref(""),c=e.ref(null),u=e.ref(null),g=3e4,v=e.ref(!1),h=e.ref(!1),d=e.ref(null),m=e.ref(null),w=[{id:"wechat",name:"微信",icon:"/static/weixin.svg"},{id:"moments",name:"朋友圈",icon:"/static/pengyouquan.svg"},{id:"image",name:"分享图片",icon:"/static/kapian.svg"},{id:"save",name:"保存图片",icon:"/static/baocun.svg"}],p=e.computed((()=>{const e=[...w];return s.showMemberInvite&&e.push({id:"member-invite",name:"邀请体验",icon:"/static/gift.svg"}),e})),f=async o=>{try{switch(o.id){case"wechat":await y();break;case"moments":await x();break;case"image":await T();break;case"save":await P();break;case"member-invite":await b();break;case"uni-share":await k();break;default:t("select",o)}}catch(l){console.error("分享操作失败:",l),t("share-error",l);const a=l.message||"分享失败";e.index.showModal({title:"分享失败",content:a,showCancel:!0,cancelText:"取消",confirmText:"重试",success:e=>{e.confirm&&f(o)}})}},y=async()=>{try{e.index.showToast({title:"请点击右上角分享给好友",icon:"none",duration:2e3}),t("share-success",{type:"wechat"}),M()}catch(o){throw console.error("微信分享失败:",o),o}},x=async()=>{try{e.index.showToast({title:"请点击右上角分享到朋友圈",icon:"none",duration:2e3}),t("share-success",{type:"moments"}),M()}catch(o){throw console.error("朋友圈分享失败:",o),o}},T=async()=>{var o,l;try{if(r.value)return void console.warn("图片正在生成中，请勿重复操作");console.log("=== 开始分享图片生成流程 ==="),r.value=!0,i.value="",e.index.showLoading({title:"生成图片中..."});const a=S.value;if(!a)throw new Error("无法生成分享配置");if(console.log("✅ Painter配置生成成功:",JSON.stringify(a,null,2)),!a.width||!a.height)throw new Error(`Painter配置缺少尺寸信息: width=${a.width}, height=${a.height}`);if(!a.views||!Array.isArray(a.views))throw new Error("Painter配置缺少views数组");console.log("✅ Painter配置验证通过，views数量:",a.views.length),D(),console.log("✅ 超时计时器已启动，超时时间:",g,"ms"),console.log("🛡️ 启用配置保护机制..."),v.value=!0,h.value=!0,d.value=setTimeout((()=>{console.log("🛡️ 配置保护超时，自动解除保护"),v.value=!1,h.value=!1}),5e3),console.log("🎨 设置Painter配置，触发图片生成..."),console.log("🎨 配置中的背景图片URL:",null==(l=null==(o=a.views)?void 0:o[0])?void 0:l.url);const n={...a};m.value=n,c.value=n,console.log("🎨 Painter配置已设置，等待组件渲染..."),console.log("🛡️ 配置保护状态 - locked:",v.value,"rendering:",h.value),setTimeout((()=>{console.log("🎨 Painter组件渲染检查..."),console.log("🎨 当前painterConfig状态:",!!c.value),console.log("🎨 当前isGeneratingImage状态:",r.value),console.log("🛡️ 当前保护状态 - locked:",v.value,"rendering:",h.value),c.value||(console.error("❌ Painter配置被意外清理！"),console.error("❌ 尝试恢复配置..."),v.value=!0,h.value=!0,c.value=n,console.log("✅ 配置已恢复，重新启动保护机制"),console.log("🛡️ 恢复后保护状态 - locked:",v.value,"rendering:",h.value),d.value&&clearTimeout(d.value),d.value=setTimeout((()=>{console.log("🛡️ 恢复后配置保护超时，自动解除保护"),v.value=!1,h.value=!1}),5e3))}),100),setTimeout((()=>{console.log("🔍 深度检查 Painter 组件状态..."),console.log("🔍 小程序环境，跳过DOM检查");try{e.index.createSelectorQuery().selectAll("painter").boundingClientRect((e=>{console.log("🔍 小程序中找到的 Painter 组件数量:",e?e.length:0),e&&0!==e.length?(console.log("✅ Painter 组件在小程序中存在"),e.forEach(((e,o)=>{console.log(`🔍 Painter 组件 ${o+1} 位置:`,e)}))):(console.warn("⚠️ 小程序中未找到 Painter 组件，可能渲染失败"),t())})).exec()}catch(o){console.error("🔍 组件检查失败:",o),t()}}),500);const t=()=>{console.log("🔄 尝试强制重新渲染 Painter 组件...");const o=c.value;c.value=null,e.nextTick$1((()=>{c.value=o,console.log("🔄 Painter 组件已重新渲染")}))}}catch(a){console.error("❌ 生成分享图片失败:",a),C(!0,"generate-error");const o=a.message||"生成分享图片失败";throw e.index.showToast({title:o.length>20?"生成图片失败":o,icon:"none",duration:3e3}),a}},P=async()=>{var o,l;try{if(r.value)return void console.warn("图片正在生成中，请勿重复操作");r.value=!0,i.value="save-mode",e.index.showLoading({title:"生成图片中..."});const a=S.value;if(!a)throw new Error("无法生成分享配置");console.log("开始生成保存图片:",a),D(),console.log("🎨 保存模式 - 配置中的背景图片URL:",null==(l=null==(o=a.views)?void 0:o[0])?void 0:l.url),c.value={...a}}catch(a){console.error("生成保存图片失败:",a),C(!0,"save-error");const o=a.message||"生成保存图片失败";throw e.index.showToast({title:o.length>20?"生成图片失败":o,icon:"none",duration:3e3}),a}},b=async()=>{try{e.index.showToast({title:"请点击右上角分享体验券",icon:"none",duration:2e3}),t("share-success",{type:"member-invite"}),M()}catch(o){throw console.error("体验会员分享失败:",o),o}},k=async()=>{try{let o;if(console.log("开始uni-share分享，数据:",s.shareData),"card"===s.shareData.template){const{createCardShareConfig:e}=await"../../utils/uniShareConfig.js";o=e(s.shareData)}else if("feed"===s.shareData.template||"dynamic"===s.shareData.template){const{createFeedShareConfig:e}=await"../../utils/uniShareConfig.js";o=e(s.shareData)}else o={content:{type:0,href:s.shareData.href||"",title:s.shareData.content||"分享内容",summary:`${s.shareData.author||"匿名用户"}分享了内容`,imageUrl:s.shareData.image||""},menus:[{img:"/static/app-plus/sharemenu/wechatfriend.png",text:"微信好友",share:{provider:"weixin",scene:"WXSceneSession"}},{img:"/static/app-plus/sharemenu/wechatmoments.png",text:"微信朋友圈",share:{provider:"weixin",scene:"WXSceneTimeline"}},{img:"/static/app-plus/sharemenu/copyurl.png",text:"复制链接",share:"copyurl"}],cancelText:"取消分享"};const a=await l.showShareMenu(o);a.success?(console.log("uni-share分享成功:",a),e.index.showToast({title:"分享成功",icon:"success"}),t("share-success",{type:"uni-share",result:a}),M()):console.log("用户取消分享")}catch(o){console.error("uni-share分享失败:",o),e.index.showToast({title:"分享失败",icon:"none"}),t("share-error",o)}},C=(o=!1,l="unknown")=>{if(console.log("🧹 清理资源请求 - 原因:",l,"强制清理:",o),console.log("🧹 当前状态 - configLocked:",v.value,"isRenderingInProgress:",h.value),console.log("🧹 调用栈:",(new Error).stack),!v.value||o){if(h.value&&!o)return console.log("🛡️ 正在渲染中，延迟清理 - 原因:",l),void setTimeout((()=>{C(!1,`delayed-${l}`)}),1e3);console.log("🧹 开始执行资源清理..."),u.value&&(clearTimeout(u.value),u.value=null),d.value&&(clearTimeout(d.value),d.value=null),r.value=!1,v.value=!1,h.value=!1,c.value&&(console.log("🧹 清理 Painter 配置"),c.value=null),i.value&&"save-mode"!==i.value&&(i.value=""),e.index.hideLoading(),(()=>{console.log("🧹 开始内存清理...");try{void 0!==e.wx$1&&e.wx$1.triggerGC&&(console.log("🧹 触发微信小程序垃圾回收"),e.wx$1.triggerGC()),"undefined"!=typeof global&&global.gc&&(console.log("🧹 触发全局垃圾回收"),global.gc()),console.log("🧹 内存清理完成")}catch(o){console.error("🧹 内存清理失败:",o)}})(),console.log("🧹 资源清理完成")}else console.log("🛡️ 配置被保护，拒绝清理 - 原因:",l)},D=()=>{u.value&&(console.log("⏰ 清理之前的超时计时器"),clearTimeout(u.value)),console.log("⏰ 启动新的超时计时器，超时时间:",g,"ms"),u.value=setTimeout((()=>{console.error("⏰ 图片生成超时，超时时间:",g,"ms"),console.error("⏰ 当前状态 - isGeneratingImage:",r.value),console.error("⏰ 当前状态 - painterConfig:",!!c.value),console.error("⏰ 当前状态 - generatedImagePath:",i.value),console.error("⏰ 当前保护状态 - locked:",v.value,"rendering:",h.value),C(!0,"timeout"),e.index.showToast({title:"图片生成超时，请重试",icon:"none",duration:3e3})}),g)},S=e.computed((()=>{var e;if(console.log("🔧 计算 Painter 配置，shareData:",s.shareData),!s.shareData)return console.warn("⚠️ shareData 为空，无法生成 Painter 配置"),null;try{let o;switch(console.log("🔧 根据模板类型生成配置，template:",s.shareData.template),s.shareData.template){case"card":console.log("🔧 使用日卡模板生成配置"),o=a.createCardShareConfig(s.shareData);break;case"feed":case"dynamic":console.log("🔧 使用动态模板生成配置"),o=a.createFeedShareConfig(s.shareData);break;default:console.log("🔧 使用默认模板生成配置"),o=a.createDefaultShareConfig(s.shareData)}return console.log("✅ Painter 配置生成成功，配置预览:",{width:null==o?void 0:o.width,height:null==o?void 0:o.height,viewsCount:null==(e=null==o?void 0:o.views)?void 0:e.length,background:null==o?void 0:o.background}),o}catch(o){return console.error("❌ 生成Painter配置失败:",o),console.error("❌ 错误详情:",o.stack),null}})),I=l=>{var a,n;console.log("🎉 Painter success event 触发:",l),console.log("🎉 Event detail:",l.detail),console.log("🎉 Event 完整结构:",JSON.stringify(l,null,2)),console.log("🛡️ 解除配置保护 - 成功回调"),v.value=!1,h.value=!1,d.value&&(clearTimeout(d.value),d.value=null),u.value&&(console.log("✅ 清理超时计时器"),clearTimeout(u.value),u.value=null),r.value=!1,e.index.hideLoading();const s=(null==(a=l.detail)?void 0:a.path)||(null==(n=l.detail)?void 0:n.tempFilePath)||l.path;console.log("🎉 提取的图片路径:",s),s?(console.log("✅ 图片生成成功，路径:",s),console.log("🔍 开始验证图片文件..."),e.index.getImageInfo({src:s,success:l=>{console.log("✅ 图片验证成功:",l),console.log("✅ 图片尺寸:",l.width,"x",l.height),"save-mode"===i.value?(console.log("💾 进入保存模式"),(async l=>{try{await o.saveImageToAlbum(l),e.index.showToast({title:"保存成功",icon:"success"}),t("share-success",{type:"save",path:l}),M()}catch(a){console.error("保存图片失败:",a);const o=a.message||"保存图片失败";throw e.index.showToast({title:o.length>20?"保存失败":o,icon:"none",duration:3e3}),a}})(s)):(console.log("📤 进入分享模式"),A(s)),i.value=s},fail:o=>{console.error("❌ 图片验证失败:",o),C(!0,"image-validation-failed"),e.index.showToast({title:"生成的图片无效",icon:"none"})}})):(console.error("❌ 图片生成成功但未获取到路径"),console.error("❌ Event 详情:",l),C(!0,"no-image-path"),e.index.showToast({title:"图片生成失败",icon:"none"}))},E=o=>{var l,a,n,t;console.error("❌ Painter组件生成失败 event 触发:",o),console.error("❌ Event detail:",o.detail),console.error("❌ Event 完整结构:",JSON.stringify(o,null,2)),console.log("🛡️ 解除配置保护 - 失败回调"),v.value=!1,h.value=!1,d.value&&(clearTimeout(d.value),d.value=null),u.value&&(console.log("✅ 清理超时计时器"),clearTimeout(u.value),u.value=null);const s=(null==(a=null==(l=o.detail)?void 0:l.error)?void 0:a.message)||(null==(n=o.detail)?void 0:n.message)||(null==(t=o.detail)?void 0:t.errMsg)||o.errMsg||"图片生成失败";if(console.error("❌ 分享图片生成失败，错误信息:",s),c.value&&!c.value._fallbackAttempted)return console.log("🔄 尝试降级到传统 Canvas API..."),c.value._fallbackAttempted=!0,void setTimeout((()=>{console.log("🔄 开始降级重试...");const e={...c.value};delete e._fallbackAttempted,c.value=null,setTimeout((()=>{console.log("🔄 设置传统 Canvas API 配置..."),c.value=e}),100)}),500);console.error("❌ 所有重试方案都已失败，清理资源"),C(!0,"all-retries-failed"),e.index.showToast({title:"图片生成失败",icon:"none"})},A=o=>{try{e.index.showShareImageMenu({path:o,success:()=>{console.log("分享图片成功"),t("share-success",{type:"image",path:o}),M()},fail:o=>{console.error("分享图片失败:",o),e.index.showToast({title:"分享失败",icon:"none"})}})}catch(l){console.error("分享图片失败:",l),e.index.showToast({title:"分享失败",icon:"none"})}},M=()=>{if(console.log("🚪 弹窗关闭请求"),console.log("🛡️ 当前保护状态 - locked:",v.value,"rendering:",h.value),console.log("🎨 当前生成状态 - isGeneratingImage:",r.value),r.value&&(v.value||h.value))return console.log("⚠️ 正在生成图片，延迟关闭"),void e.index.showModal({title:"提示",content:"图片正在生成中，确定要关闭吗？",success:e=>{e.confirm?(console.log("👤 用户确认关闭，强制清理资源"),C(!0,"user-confirmed-close"),i.value="",t("close")):console.log("👤 用户取消关闭")}});console.log("🚪 执行正常关闭流程"),C(!0,"popup-close"),i.value="",t("close")};e.watch(c,((e,o)=>{!e&&o&&(v.value||h.value)&&(console.warn("🛡️ 检测到配置被意外清理，尝试恢复"),console.log("🛡️ 保护状态 - locked:",v.value,"rendering:",h.value),m.value&&(console.log("🔄 使用最后有效配置恢复"),setTimeout((()=>{c.value||!v.value&&!h.value||(c.value={...m.value},console.log("✅ 配置已自动恢复"))}),50)))}),{deep:!0});const j=()=>{console.log("🎨 Painter didShow 事件 - 组件已显示"),console.log("🎨 Painter 组件状态检查"),console.log("🎨 - painterConfig:",!!c.value),console.log("🎨 - isGeneratingImage:",r.value),console.log("🎨 - configLocked:",v.value)},L=e=>{console.log("🎨 Painter viewUpdate 事件:",e),console.log("🎨 ViewUpdate 详情:",JSON.stringify(e,null,2))},$=e=>{console.log("🎨 Painter touchStart 事件:",e)},_=e=>{console.log("🎨 Painter touchMove 事件:",e)},q=e=>{console.log("🎨 Painter touchEnd 事件:",e)};return(o,l)=>e.e({a:e.t(n.title),b:e.p({name:"close",size:"24",color:"#999"}),c:e.o(M),d:e.f(e.unref(p),((o,l,a)=>({a:o.icon,b:e.t(o.name),c:o.id,d:e.o((e=>f(o)),o.id)}))),e:n.show?1:"",f:c.value},c.value?{g:e.o(I),h:e.o(E),i:e.o(j),j:e.o(L),k:e.o($),l:e.o(_),m:e.o(q),n:e.p({palette:c.value,use2D:!0,LRU:!1,dirty:!1,disableAction:!0,widthPixels:750,customStyle:"position: fixed; top: -9999px; left: -9999px; width: 750rpx; height: 1334rpx; z-index: -1;"})}:{},{o:n.show?1:"",p:e.o(M)})}},t=e._export_sfc(n,[["__scopeId","data-v-84f55cf7"]]);wx.createComponent(t);
//# sourceMappingURL=share-popup.js.map
