"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js");if(require("../../../store/index.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-text")+e.resolveComponent("u-parse"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-parse/u-parse.js"))();const r={__name:"xieyi",setup(r){const s=e.ref({});return e.onLoad((async r=>{var u;const i=await t.htmlindex({type:r.type});"ok"===(null==i?void 0:i.status)&&(s.value=i.data),e.index.setNavigationBarTitle({title:(null==(u=null==i?void 0:i.data)?void 0:u.name)||"相关协议"})})),(t,r)=>{var u,i;return{a:e.p({margin:"0 0 20rpx",color:"#ccc",size:"24rpx",text:`发布时间：${null==(u=s.value)?void 0:u.time}`}),b:e.p({content:null==(i=s.value)?void 0:i.contents})}}},__runtimeHooks:1};wx.createPage(r);
//# sourceMappingURL=xieyi.js.map
