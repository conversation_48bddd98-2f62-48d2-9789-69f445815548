"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),t=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+a+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const a=()=>"../../../../components/customNavbar.js",r={__name:"list",setup(a){const r=e.ref([]),n=e.ref(!1),l=e.ref(!1),u=e.ref(!0),s=e.ref(1),i=async(a=!1)=>{var i,c;if(!n.value||a){n.value=!0;try{const n={page:a?1:s.value,page_size:10,uid:(null==(i=t.store().$state.userInfo)?void 0:i.uid)||0,token:(null==(c=t.store().$state.userInfo)?void 0:c.token)||"",type:"diary"};console.log("请求日记列表参数:",n);const l=await o.getDiaryList(n);if(console.log("日记列表API响应:",l),"ok"===l.status&&l.data){const e=l.data.list||[];console.log("获取到的日记列表:",e),a?(r.value=e,s.value=1):r.value=[...r.value,...e],u.value=10===e.length,a||s.value++}else"empty"===l.status?(console.log("服务器返回空数据:",l.msg),a&&(r.value=[]),u.value=!1):(console.error("API返回错误:",l),e.index.showToast({title:l.msg||"获取日记列表失败",icon:"none"}))}catch(d){console.error("获取日记列表失败:",d),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}finally{n.value=!1,l.value=!1}}},c=()=>{l.value=!0,i(!0)},d=()=>{u.value&&!n.value&&i()},v=()=>{e.index.navigateTo({url:"/pages/bundle/world/diary/post"})},f=e=>{if(!e)return"";let o;if("string"==typeof e)o=/^\d+$/.test(e)?new Date(1e3*parseInt(e)):new Date(e);else{if("number"!=typeof e)return"";o=new Date(1e3*e)}if(isNaN(o.getTime()))return console.warn("无效的时间格式:",e),"";const t=new Date-o;if(t<6e4)return"刚刚";if(t<36e5)return`${Math.floor(t/6e4)}分钟前`;if(t<864e5)return`${Math.floor(t/36e5)}小时前`;if(t<6048e5)return`${Math.floor(t/864e5)}天前`;return`${o.getFullYear()}-${String(o.getMonth()+1).padStart(2,"0")}-${String(o.getDate()).padStart(2,"0")}`};return e.onMounted((()=>{i()})),(o,t)=>e.e({a:e.p({name:"plus",size:"44rpx",color:"#ffffff"}),b:e.o(v),c:e.p({title:"我的日记"}),d:e.f(r.value,((o,t,a)=>e.e({a:e.t(o.content),b:e.t(f(o.created_at)),c:o.mood},o.mood?{d:e.t(o.mood)}:{},{e:o.cover_image},o.cover_image?{f:o.cover_image}:{},{g:o.id,h:e.o((t=>(o=>{e.index.navigateTo({url:`/pages/bundle/world/diary/detail?id=${o.id}`})})(o)),o.id)}))),e:n.value&&!l.value},n.value&&!l.value?{f:e.p({size:"40rpx",color:"#6AC086"})}:{},{g:!u.value&&r.value.length>0},(!u.value&&r.value.length,{}),{h:!n.value&&0===r.value.length},n.value||0!==r.value.length?{}:{i:e.p({name:"edit-pen",size:"120rpx",color:"#cccccc"}),j:e.o(v)},{k:e.o(d),l:l.value,m:e.o(c)})}},n=e._export_sfc(r,[["__scopeId","data-v-1d06032b"]]);wx.createPage(n);
//# sourceMappingURL=list.js.map
