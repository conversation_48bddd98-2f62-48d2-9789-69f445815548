"use strict";const i=require("../../../../common/vendor.js"),t={name:"u-switch",mixins:[i.mpMixin,i.mixin,i.props$23],watch:{modelValue:{immediate:!0,handler(i){i!==this.inactiveValue&&this.activeValue}}},data:()=>({bgColor:"#ffffff"}),computed:{isActive(){return this.modelValue===this.activeValue},switchStyle(){let t={};return t.width=i.addUnit(2*this.size+2),t.height=i.addUnit(Number(this.size)+2),this.customInactiveColor&&(t.borderColor="rgba(0, 0, 0, 0)"),t.backgroundColor=this.isActive?this.activeColor:this.inactiveColor,t},nodeStyle(){let t={};t.width=i.addUnit(this.size-this.space),t.height=i.addUnit(this.size-this.space);const e=this.isActive?i.addUnit(this.space):i.addUnit(this.size);return t.transform=`translateX(-${e})`,t},bgStyle(){let t={};return t.width=i.addUnit(2*Number(this.size)-this.size/2),t.height=i.addUnit(this.size),t.backgroundColor=this.inactiveColor,t.transform=`scale(${this.isActive?0:1})`,t},customInactiveColor(){return"#fff"!==this.inactiveColor&&"#ffffff"!==this.inactiveColor}},emits:["update:modelValue","change"],methods:{addStyle:i.addStyle,clickHandler(){if(!this.disabled&&!this.loading){const i=this.isActive?this.inactiveValue:this.activeValue;this.asyncChange||this.$emit("update:modelValue",i),this.$nextTick((()=>{this.$emit("change",i)}))}}}};if(!Array){i.resolveComponent("u-loading-icon")()}Math;const e=i._export_sfc(t,[["render",function(t,e,s,a,o,n){return{a:i.s(n.bgStyle),b:i.p({show:t.loading,mode:"circle",timingFunction:"linear",color:t.modelValue?t.activeColor:"#AAABAD",size:.6*t.size}),c:i.n(t.modelValue&&"u-switch__node--on"),d:i.s(n.nodeStyle),e:i.n(t.disabled&&"u-switch--disabled"),f:i.s(n.switchStyle),g:i.s(n.addStyle(t.customStyle)),h:i.o(((...i)=>n.clickHandler&&n.clickHandler(...i)))}}],["__scopeId","data-v-6e53bc96"]]);wx.createComponent(e);
//# sourceMappingURL=u-switch.js.map
