"use strict";const o=require("../../../../common/vendor.js"),e={name:"u-toolbar",mixins:[o.mpMixin,o.mixin,o.props$42],emits:["confirm","cancel"],created(){},methods:{cancel(){this.$emit("cancel")},confirm(){this.$emit("confirm")}}};const c=o._export_sfc(e,[["render",function(e,c,t,n,i,r){return o.e({a:e.show},e.show?o.e({b:o.t(e.cancelText),c:o.o(((...o)=>r.cancel&&r.cancel(...o))),d:e.cancelColor,e:e.title},e.title?{f:o.t(e.title)}:{},{g:!e.rightSlot},e.rightSlot?{}:{h:o.t(e.confirmText),i:o.o(((...o)=>r.confirm&&r.confirm(...o))),j:e.confirmColor},{k:o.o(((...o)=>e.noop&&e.noop(...o)))}):{})}],["__scopeId","data-v-adbc4cad"]]);wx.createComponent(c);
//# sourceMappingURL=u-toolbar.js.map
