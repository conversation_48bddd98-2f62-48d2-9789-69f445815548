<view class="page"><mescroll-uni wx:if="{{f}}" u-s="{{['d']}}" class="list" bindinit="{{b}}" binddown="{{c}}" bindup="{{d}}" bindtopclick="{{e}}" u-i="48e18fad-0" bind:__l="__l" u-p="{{f}}"><view wx:for="{{a}}" wx:for-item="val" wx:key="q" class="p30 df aic borderBottom"><u-avatar wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></u-avatar><view class="ml20 df fdc f1"><view class="df f1 aic"><text style="color:#414141" class="df aic jcc mr10 x30 fb">{{val.c}}</text><u-text wx:if="{{val.e}}" u-i="{{val.d}}" bind:__l="__l" u-p="{{val.e}}"></u-text><u-tag wx:if="{{val.g}}" style="margin-left:10rpx" u-i="{{val.f}}" bind:__l="__l" u-p="{{val.g}}"></u-tag></view><u-text wx:if="{{val.i}}" u-i="{{val.h}}" bind:__l="__l" u-p="{{val.i}}"></u-text><u-text wx:if="{{val.k}}" u-i="{{val.j}}" bind:__l="__l" u-p="{{val.k}}"></u-text><u-text wx:if="{{val.l}}" style="margin-top:4rpx" u-i="{{val.m}}" bind:__l="__l" u-p="{{val.n}}"></u-text></view><view><u-text wx:if="{{val.p}}" u-i="{{val.o}}" bind:__l="__l" u-p="{{val.p}}"></u-text></view></view></mescroll-uni></view>