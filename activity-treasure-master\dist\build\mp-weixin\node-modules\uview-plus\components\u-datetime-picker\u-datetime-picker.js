"use strict";const e=require("../../../../common/vendor.js");const t={name:"up-datetime-picker",mixins:[e.mpMixin,e.mixin,e.props$29],data:()=>({inputValue:"",showByClickInput:!1,columns:[],innerDefaultIndex:[],innerFormatter:(e,t)=>t}),watch:{show(e,t){e&&this.updateColumnValue(this.innerValue)},modelValue(e){this.init()},propsChange(){this.init()}},computed:{propsChange(){return[this.mode,this.maxDate,this.minDate,this.minHour,this.maxHour,this.minMinute,this.maxMinute,this.filter]}},mounted(){this.init()},emits:["close","cancel","confirm","change","update:modelValue"],methods:{getInputValue(t){if(""!=t&&t&&null!=t)if("time"==this.mode)this.inputValue=t;else if(this.format)this.inputValue=e.dayjs(t).format(this.format);else{let i="";switch(this.mode){case"date":i="YYYY-MM-DD";break;case"year-month":i="YYYY-MM";break;case"datetime":i="YYYY-MM-DD HH:mm";break;case"time":i="HH:mm"}this.inputValue=e.dayjs(t).format(i)}else this.inputValue=""},init(){this.innerValue=this.correctValue(this.modelValue),this.updateColumnValue(this.innerValue),this.getInputValue(this.innerValue)},setFormatter(e){this.innerFormatter=e},close(){this.closeOnClickOverlay&&this.$emit("close")},cancel(){this.hasInput&&(this.showByClickInput=!1),this.$emit("cancel")},confirm(){this.$emit("update:modelValue",this.innerValue),this.hasInput&&(this.getInputValue(this.innerValue),this.showByClickInput=!1),this.$emit("confirm",{value:this.innerValue,mode:this.mode})},intercept(e,t){let i=e.match(/\d+/g);return i.length>1?0:t&&4==i[0].length?i[0]:i[0].length>2?0:i[0]},change(t){const{indexs:i,values:n}=t;let a="";if("time"===this.mode)a=`${this.intercept(n[0][i[0]])}:${this.intercept(n[1][i[1]])}`;else{const t=parseInt(this.intercept(n[0][i[0]],"year")),s=parseInt(this.intercept(n[1][i[1]]));let r=parseInt(n[2]?this.intercept(n[2][i[2]]):1),o=0,u=0;const h=e.dayjs(`${t}-${s}`).daysInMonth();"year-month"===this.mode&&(r=1),r=Math.min(h,r),"datetime"===this.mode&&(o=parseInt(this.intercept(n[3][i[3]])),u=parseInt(this.intercept(n[4][i[4]]))),a=Number(new Date(t,s-1,r,o,u))}a=this.correctValue(a),this.innerValue=a,this.updateColumnValue(a),this.$emit("change",{value:a,mode:this.mode})},updateColumnValue(e){this.innerValue=e,this.updateColumns(),setTimeout((()=>{this.updateIndexs(e)}),0)},updateIndexs(t){let i=[];const n=this.formatter||this.innerFormatter;if("time"===this.mode){const e=t.split(":");i=[n("hour",e[0]),n("minute",e[1])]}else i=[n("year",`${e.dayjs(t).year()}`),n("month",e.padZero(e.dayjs(t).month()+1))],"date"===this.mode&&i.push(n("day",e.padZero(e.dayjs(t).date()))),"datetime"===this.mode&&i.push(n("day",e.padZero(e.dayjs(t).date())),n("hour",e.padZero(e.dayjs(t).hour())),n("minute",e.padZero(e.dayjs(t).minute())));const a=this.columns.map(((e,t)=>Math.max(0,e.findIndex((e=>e===i[t])))));this.innerDefaultIndex=a},updateColumns(){const e=this.formatter||this.innerFormatter,t=this.getOriginColumns().map((t=>t.values.map((i=>e(t.type,i)))));this.columns=t},getOriginColumns(){return this.getRanges().map((({type:t,range:i})=>{let n=function(e,t){let i=-1;const n=Array(e<0?0:e);for(;++i<e;)n[i]=t(i);return n}(i[1]-i[0]+1,(n=>{let a=i[0]+n;return a="year"===t?`${a}`:e.padZero(a),a}));return this.filter&&(n=this.filter(t,n),(!n||n&&0==n.length)&&console.log("日期filter结果不能为空")),{type:t,values:n}}))},generateArray:(e,t)=>Array.from(new Array(t+1).keys()).slice(e),correctValue(t){const i="time"!==this.mode;if(i&&!e.dayjs.unix(t).isValid()?t=this.minDate:i||t||(t=`${e.padZero(this.minHour)}:${e.padZero(this.minMinute)}`),i)return t=e.dayjs(t).isBefore(e.dayjs(this.minDate))?this.minDate:t,t=e.dayjs(t).isAfter(e.dayjs(this.maxDate))?this.maxDate:t;{if(-1===String(t).indexOf(":"))return e.error();let[i,n]=t.split(":");return i=e.padZero(e.range(this.minHour,this.maxHour,Number(i))),n=e.padZero(e.range(this.minMinute,this.maxMinute,Number(n))),`${i}:${n}`}},getRanges(){if("time"===this.mode)return[{type:"hour",range:[this.minHour,this.maxHour]},{type:"minute",range:[this.minMinute,this.maxMinute]}];const{maxYear:e,maxDate:t,maxMonth:i,maxHour:n,maxMinute:a}=this.getBoundary("max",this.innerValue),{minYear:s,minDate:r,minMonth:o,minHour:u,minMinute:h}=this.getBoundary("min",this.innerValue),m=[{type:"year",range:[s,e]},{type:"month",range:[o,i]},{type:"day",range:[r,t]},{type:"hour",range:[u,n]},{type:"minute",range:[h,a]}];return"date"===this.mode&&m.splice(3,2),"year-month"===this.mode&&m.splice(2,3),m},getBoundary(t,i){const n=new Date(i),a=new Date(this[`${t}Date`]),s=e.dayjs(a).year();let r=1,o=1,u=0,h=0;return"max"===t&&(r=12,o=e.dayjs(n).daysInMonth(),u=23,h=59),e.dayjs(n).year()===s&&(r=e.dayjs(a).month()+1,e.dayjs(n).month()+1===r&&(o=e.dayjs(a).date(),e.dayjs(n).date()===o&&(u=e.dayjs(a).hour(),e.dayjs(n).hour()===u&&(h=e.dayjs(a).minute())))),{[`${t}Year`]:s,[`${t}Month`]:r,[`${t}Date`]:o,[`${t}Hour`]:u,[`${t}Minute`]:h}}}};if(!Array){(e.resolveComponent("up-input")+e.resolveComponent("u-picker"))()}Math||((()=>"../u-input/u-input.js")+(()=>"../u-picker/u-picker.js"))();const i=e._export_sfc(t,[["render",function(t,i,n,a,s,r){return e.e({a:t.hasInput},t.hasInput?{b:e.o((e=>s.inputValue=e)),c:e.p({placeholder:t.placeholder,readonly:!!s.showByClickInput,border:"surround",modelValue:s.inputValue}),d:e.o((e=>s.showByClickInput=!s.showByClickInput))}:{},{e:e.sr("picker","c0db4b1c-1"),f:e.o(r.close),g:e.o(r.cancel),h:e.o(r.confirm),i:e.o(r.change),j:e.p({show:t.show||t.hasInput&&s.showByClickInput,popupMode:t.popupMode,closeOnClickOverlay:t.closeOnClickOverlay,columns:s.columns,title:t.title,itemHeight:t.itemHeight,showToolbar:t.showToolbar,visibleItemCount:t.visibleItemCount,defaultIndex:s.innerDefaultIndex,cancelText:t.cancelText,confirmText:t.confirmText,cancelColor:t.cancelColor,confirmColor:t.confirmColor,toolbarRightSlot:t.toolbarRightSlot})})}],["__scopeId","data-v-c0db4b1c"]]);wx.createComponent(i);
//# sourceMappingURL=u-datetime-picker.js.map
