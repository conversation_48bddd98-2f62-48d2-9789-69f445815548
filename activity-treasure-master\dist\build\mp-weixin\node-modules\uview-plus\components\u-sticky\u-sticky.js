"use strict";const t=require("../../../../common/vendor.js"),e={name:"u-sticky",mixins:[t.mpMixin,t.mixin,t.props$3],data:()=>({cssSticky:!1,stickyTop:0,elId:t.guid(),left:0,width:"auto",height:"auto",fixed:!1}),computed:{style(){const e={};return this.disabled?e.position="static":this.cssSticky?(e.position="sticky",e.zIndex=this.uZindex,e.top=t.addUnit(this.stickyTop)):e.height=this.fixed?this.height+"px":"auto",e.backgroundColor=this.bgColor,t.deepMerge(t.addStyle(this.customStyle),e)},stickyContent(){const t={};return this.cssSticky||(t.position=this.fixed?"fixed":"static",t.top=this.stickyTop+"px",t.left=this.left+"px",t.width="auto"==this.width?"auto":this.width+"px",t.zIndex=this.uZindex),t},uZindex(){return this.zIndex?this.zIndex:t.zIndex.sticky}},mounted(){this.init()},methods:{init(){this.getStickyTop(),this.checkSupportCssSticky(),this.cssSticky||!this.disabled&&this.initObserveContent()},initObserveContent(){this.$uGetRect("#"+this.elId).then((t=>{this.height=t.height,this.left=t.left,this.width=t.width,this.$nextTick((()=>{this.observeContent()}))}))},observeContent(){this.disconnectObserver("contentObserver");const e=t.index.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe(`#${this.elId}`,(t=>{this.setFixed(t.boundingClientRect.top)})),this.contentObserver=e},setFixed(t){const e=t<=this.stickyTop;this.fixed=e},disconnectObserver(t){const e=this[t];e&&e.disconnect()},getStickyTop(){this.stickyTop=t.getPx(this.offsetTop)+t.getPx(this.customNavHeight)},async checkSupportCssSticky(){"android"===t.os()&&Number(t.sys().system)>8&&(this.cssSticky=!0),this.cssSticky=await this.checkComputedStyle(),"ios"===t.os()&&(this.cssSticky=!0)},checkComputedStyle(){return new Promise((e=>{t.index.createSelectorQuery().in(this).select(".u-sticky").fields({computedStyle:["position"]}).exec((t=>{e("sticky"===t[0].position)}))}))},checkCssStickyForH5(){}},beforeUnmount(){this.disconnectObserver("contentObserver")}};const i=t._export_sfc(e,[["render",function(e,i,s,o,c,n){return{a:t.s(n.stickyContent),b:c.elId,c:t.s(n.style)}}],["__scopeId","data-v-a4e72af3"]]);wx.createComponent(i);
//# sourceMappingURL=u-sticky.js.map
