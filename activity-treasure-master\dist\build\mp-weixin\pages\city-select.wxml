<view class="city-select-page data-v-220e068e"><view class="nav-bar data-v-220e068e"><view class="nav-left data-v-220e068e" bindtap="{{b}}"><u-icon wx:if="{{a}}" class="data-v-220e068e" u-i="220e068e-0" bind:__l="__l" u-p="{{a}}"></u-icon></view><view class="nav-title data-v-220e068e">选择城市</view><view class="nav-right data-v-220e068e"></view></view><view class="search-container data-v-220e068e"><view class="search-box data-v-220e068e"><u-icon wx:if="{{c}}" class="search-icon data-v-220e068e" u-i="220e068e-1" bind:__l="__l" u-p="{{c}}"></u-icon><input class="search-input data-v-220e068e" placeholder="搜索城市名称" bindinput="{{d}}" confirm-type="search" bindconfirm="{{e}}" value="{{f}}"/><view wx:if="{{g}}" class="clear-btn data-v-220e068e" bindtap="{{i}}"><u-icon wx:if="{{h}}" class="data-v-220e068e" u-i="220e068e-2" bind:__l="__l" u-p="{{h}}"></u-icon></view></view></view><view wx:if="{{j}}" class="current-location data-v-220e068e"><view class="section-title data-v-220e068e">当前定位</view><view class="city-item data-v-220e068e" bindtap="{{m}}"><u-icon wx:if="{{k}}" class="location-icon data-v-220e068e" u-i="220e068e-3" bind:__l="__l" u-p="{{k}}"></u-icon><text class="city-name data-v-220e068e">{{l}}</text></view></view><view class="city-list-container data-v-220e068e"><view wx:if="{{n}}" class="loading-container data-v-220e068e"><u-loading-icon wx:if="{{o}}" class="data-v-220e068e" u-i="220e068e-4" bind:__l="__l" u-p="{{o}}"></u-loading-icon><text class="loading-text data-v-220e068e">加载中...</text></view><view wx:else class="city-content data-v-220e068e"><scroll-view wx:if="{{p}}" class="city-scroll data-v-220e068e" scroll-y style="{{'height:' + t}}"><view wx:if="{{q}}" class="search-results data-v-220e068e"><view class="section-title data-v-220e068e">搜索结果</view><view wx:for="{{r}}" wx:for-item="city" wx:key="c" class="city-item data-v-220e068e" bindtap="{{city.d}}"><text class="city-name data-v-220e068e">{{city.a}}</text><text class="city-pinyin data-v-220e068e">{{city.b}}</text></view></view><view wx:else class="no-data data-v-220e068e"><u-icon wx:if="{{s}}" class="data-v-220e068e" u-i="220e068e-5" bind:__l="__l" u-p="{{s}}"></u-icon><text class="no-data-text data-v-220e068e">未找到相关城市</text></view></scroll-view><view wx:else class="grouped-city-container data-v-220e068e"><scroll-view class="city-scroll data-v-220e068e" scroll-y style="{{'height:' + y}}" scroll-into-view="{{z}}" bindscroll="{{A}}"><view wx:if="{{v}}" class="grouped-city-list data-v-220e068e"><view wx:for="{{w}}" wx:for-item="cities" wx:key="c" id="{{cities.d}}" class="letter-group data-v-220e068e"><view class="letter-header data-v-220e068e">{{cities.a}}</view><view class="cities-in-letter data-v-220e068e"><view wx:for="{{cities.b}}" wx:for-item="city" wx:key="b" class="city-item data-v-220e068e" bindtap="{{city.c}}"><text class="city-name data-v-220e068e">{{city.a}}</text></view></view></view></view><view wx:else class="no-data data-v-220e068e"><u-icon wx:if="{{x}}" class="data-v-220e068e" u-i="220e068e-6" bind:__l="__l" u-p="{{x}}"></u-icon><text class="no-data-text data-v-220e068e">暂无城市数据</text></view></scroll-view><view class="letter-index data-v-220e068e"><view wx:for="{{B}}" wx:for-item="letter" wx:key="b" class="{{['letter-index-item', 'data-v-220e068e', letter.c && 'active']}}" bindtap="{{letter.d}}">{{letter.a}}</view></view></view></view></view><view wx:if="{{C}}" class="letter-tip data-v-220e068e">{{D}}</view></view>