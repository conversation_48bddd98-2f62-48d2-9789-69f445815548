<view class="page"><view class="p30"><my-line wx:if="{{c}}" u-s="{{['d']}}" u-i="aba4c00a-0" bind:__l="__l" u-p="{{c}}"><u-button wx:if="{{b}}" bindclick="{{a}}" u-i="aba4c00a-1,aba4c00a-0" bind:__l="__l" u-p="{{b}}"></u-button></my-line><view class="mt30 df aic fw"><view wx:for="{{d}}" wx:for-item="val" wx:key="d" class="pr m20 p20 x24" style="background-color:#eeeeee;border-radius:26rpx">{{val.a}} <view class="pa" style="top:-5rpx;right:-5rpx"><u-icon wx:if="{{e}}" bindclick="{{val.b}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{e}}"></u-icon></view></view></view><u-gap wx:if="{{f}}" u-i="aba4c00a-3" bind:__l="__l" u-p="{{f}}"></u-gap><my-line wx:if="{{i}}" u-s="{{['d']}}" u-i="aba4c00a-4" bind:__l="__l" u-p="{{i}}"><u-button wx:if="{{h}}" bindclick="{{g}}" u-i="aba4c00a-5,aba4c00a-4" bind:__l="__l" u-p="{{h}}"></u-button></my-line></view><u-popup wx:if="{{o}}" u-s="{{['d']}}" bindclose="{{n}}" u-i="aba4c00a-6" bind:__l="__l" u-p="{{o}}"><view class="p30"><view class="df aic jcsb px20 pt10 pb10 r20" style="border:2rpx #aaa solid"><u-input wx:if="{{k}}" u-i="aba4c00a-7,aba4c00a-6" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"></u-input><u-button wx:if="{{m}}" bindclick="{{l}}" u-i="aba4c00a-8,aba4c00a-6" bind:__l="__l" u-p="{{m}}"></u-button></view></view></u-popup></view>