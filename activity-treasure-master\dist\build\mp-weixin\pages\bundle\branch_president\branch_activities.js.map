{"version": 3, "file": "branch_activities.js", "sources": ["../../../../../../src/pages/bundle/branch_president/branch_activities.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGJyYW5jaF9wcmVzaWRlbnRcYnJhbmNoX2FjdGl2aXRpZXMudnVl"], "sourcesContent": ["<template>\n  <view class=\"page branch-management\">\n    <myTitle\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      title=\"分会活动管理\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <!-- 统计卡片 -->\n      <view class=\"stats-card\">\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ totalActivities }}</view>\n          <view class=\"stats-label\">总活动数</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ ongoingActivities }}</view>\n          <view class=\"stats-label\">进行中</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ completedActivities }}</view>\n          <view class=\"stats-label\">已完成</view>\n        </view>\n      </view>\n      \n      <!-- 筛选器 -->\n      <view class=\"filter-container\">\n        <u-button\n          :type=\"filterStatus === 'all' ? 'primary' : 'info'\"\n          size=\"mini\"\n          @click=\"setFilter('all')\"\n          customStyle=\"border-radius: 30rpx; margin-right: 15rpx; width: 140rpx; height: 60rpx; font-size: 24rpx;\"\n          :customTextStyle=\"filterStatus === 'all' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          全部活动\n        </u-button>\n        <u-button\n          :type=\"filterStatus === 'ongoing' ? 'primary' : 'info'\"\n          size=\"mini\"\n          @click=\"setFilter('ongoing')\"\n          customStyle=\"border-radius: 30rpx; margin-right: 15rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;\"\n          :customTextStyle=\"filterStatus === 'ongoing' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          进行中\n        </u-button>\n        <u-button\n          :type=\"filterStatus === 'completed' ? 'primary' : 'info'\"\n          size=\"mini\"\n          @click=\"setFilter('completed')\"\n          customStyle=\"border-radius: 30rpx; margin-right: 15rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;\"\n          :customTextStyle=\"filterStatus === 'completed' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          已完成\n        </u-button>\n        <u-button\n          :type=\"filterStatus === 'cancelled' ? 'primary' : 'info'\"\n          size=\"mini\"\n          @click=\"setFilter('cancelled')\"\n          customStyle=\"border-radius: 30rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;\"\n          :customTextStyle=\"filterStatus === 'cancelled' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          已取消\n        </u-button>\n      </view>\n      \n      <!-- 活动列表 -->\n      <view class=\"activity-list\" v-if=\"activityList.length > 0\">\n        <view \n          class=\"activity-item\"\n          v-for=\"activity in activityList\"\n          :key=\"activity.id\"\n          @click=\"viewActivityDetail(activity)\"\n        >\n          <view class=\"activity-header\">\n            <image \n              class=\"activity-image\"\n              :src=\"activity.img_url || '/static/default-activity.png'\"\n              mode=\"aspectFill\"\n            />\n            <view class=\"activity-status\">\n              <u-tag\n                :text=\"getStatusText(activity)\"\n                :type=\"getStatusType(activity)\"\n                size=\"mini\"\n              ></u-tag>\n            </view>\n          </view>\n          \n          <view class=\"activity-content\">\n            <view class=\"activity-name\">{{ activity.name }}</view>\n            <view class=\"activity-title\">{{ activity.title }}</view>\n            \n            <view class=\"activity-meta\">\n              <view class=\"meta-item\">\n                <u-icon name=\"clock\" color=\"#999\" size=\"24\"></u-icon>\n                <text>{{ formatTime(activity.start_time) }}</text>\n              </view>\n              <view class=\"meta-item\">\n                <u-icon name=\"account\" color=\"#999\" size=\"24\"></u-icon>\n                <text>{{ activity.signup_count || 0 }}人参与</text>\n              </view>\n            </view>\n            \n            <view class=\"activity-organizer\">\n              <text>组织者：{{ activity.organizer_name }}</text>\n            </view>\n          </view>\n          \n          <view class=\"activity-actions\">\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"24\"></u-icon>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-else-if=\"!loading\">\n        <u-empty\n          mode=\"list\"\n          text=\"暂无活动数据\"\n          textColor=\"#999999\"\n          textSize=\"28\"\n        ></u-empty>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMore && activityList.length > 0\">\n        <u-loadmore \n          :status=\"loadStatus\"\n          @loadmore=\"loadMore\"\n        ></u-loadmore>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';\nimport { store } from '@/store';\nimport { branch_presidentget_activities } from '@/api';\n\n// 响应式数据\nconst loading = ref(false);\nconst activityList = ref([]);\nconst filterStatus = ref('all');\nconst currentPage = ref(1);\nconst hasMore = ref(true);\nconst loadStatus = ref('loadmore');\nconst pageSize = 20;\n\n// 统计数据\nconst stats = reactive({\n  total: 0,\n  ongoing: 0,\n  completed: 0,\n  cancelled: 0\n});\n\n// 计算属性\nconst totalActivities = computed(() => stats.total);\nconst ongoingActivities = computed(() => stats.ongoing);\nconst completedActivities = computed(() => stats.completed);\n\n// 页面加载\nonLoad(() => {\n  loadActivities(true);\n});\n\n// 下拉刷新\nonPullDownRefresh(() => {\n  loadActivities(true);\n  setTimeout(() => {\n    uni.stopPullDownRefresh();\n  }, 1000);\n});\n\n// 上拉加载更多\nonReachBottom(() => {\n  if (hasMore.value && !loading.value) {\n    loadMore();\n  }\n});\n\n// 加载活动列表\nconst loadActivities = async (isRefresh = false) => {\n  try {\n    if (isRefresh) {\n      currentPage.value = 1;\n      hasMore.value = true;\n      loading.value = true;\n      loadStatus.value = 'loading';\n    } else if (!hasMore.value) {\n      return;\n    }\n    \n    const userInfo = store().$state.userInfo;\n    const res = await branch_presidentget_activities({\n      uid: userInfo.uid,\n      token: userInfo.token,\n      page: currentPage.value,\n      page_size: pageSize,\n      status: filterStatus.value\n    });\n    \n    if (res.status === 'ok') {\n      const newActivities = res.data || [];\n      \n      if (isRefresh) {\n        activityList.value = newActivities;\n      } else {\n        activityList.value.push(...newActivities);\n      }\n      \n      // 更新统计数据\n      if (res.stats) {\n        stats.total = res.stats.total || 0;\n        stats.ongoing = res.stats.ongoing || 0;\n        stats.completed = res.stats.completed || 0;\n        stats.cancelled = res.stats.cancelled || 0;\n      }\n      \n      // 判断是否还有更多数据\n      hasMore.value = newActivities.length === pageSize;\n      currentPage.value++;\n      \n      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';\n      \n    } else if (res.status === 'relogin') {\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n    } else {\n      uni.showToast({\n        title: res.msg || '加载失败',\n        icon: 'none'\n      });\n      loadStatus.value = 'loadmore';\n    }\n  } catch (error) {\n    console.error('加载活动列表失败:', error);\n    uni.showToast({\n      title: '网络错误，请重试',\n      icon: 'none'\n    });\n    loadStatus.value = 'loadmore';\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 设置筛选器\nconst setFilter = (status) => {\n  filterStatus.value = status;\n  loadActivities(true);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (!loading.value && hasMore.value) {\n    loadStatus.value = 'loading';\n    loadActivities(false);\n  }\n};\n\n// 查看活动详情\nconst viewActivityDetail = (activity) => {\n  uni.navigateTo({\n    url: `/pages/bundle/index/activeInfo?id=${activity.id}`\n  });\n};\n\n// 获取状态文本\nconst getStatusText = (activity) => {\n  // 使用后端返回的display_status或根据activity数据判断\n  const status = activity.display_status || activity.status;\n  const statusMap = {\n    1: '进行中',\n    2: '已完成',\n    3: '已取消'\n  };\n  return statusMap[status] || '未知';\n};\n\n// 获取状态类型\nconst getStatusType = (activity) => {\n  const status = activity.display_status || activity.status;\n  const typeMap = {\n    1: 'success',\n    2: 'info',\n    3: 'error'\n  };\n  return typeMap[status] || 'info';\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  if (!timestamp || timestamp === 0) return '时间待定';\n\n  // 确保timestamp是数字类型\n  const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;\n  if (isNaN(ts)) return '时间格式错误';\n\n  const date = new Date(ts * 1000);\n  if (isNaN(date.getTime())) return '时间格式错误';\n\n  const month = date.getMonth() + 1;\n  const day = date.getDate();\n  const hour = date.getHours();\n  const minute = date.getMinutes();\n  return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/style/wcag-colors.scss';\n.page {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.content-container {\n  padding: 30rpx;\n}\n\n.stats-card {\n  display: flex;\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 40rpx 20rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(106, 192, 134, 0.1);\n}\n\n.stats-item {\n  flex: 1;\n  text-align: center;\n  \n  .stats-number {\n    font-size: 48rpx;\n    font-weight: bold;\n    color: #6AC086;\n    margin-bottom: 10rpx;\n  }\n  \n  .stats-label {\n    font-size: 24rpx;\n    color: #666666; /* 提高对比度，从#999改为#666666 */\n  }\n}\n\n.filter-container {\n  display: flex;\n  margin-bottom: 30rpx;\n  flex-wrap: nowrap;\n  justify-content: flex-start;\n  align-items: center;\n  overflow-x: auto;\n  padding: 10rpx 0;\n}\n\n.activity-list {\n  .activity-item {\n    background: #ffffff;\n    border-radius: 20rpx;\n    margin-bottom: 20rpx;\n    overflow: hidden;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n    \n    .activity-header {\n      position: relative;\n      height: 300rpx;\n      \n      .activity-image {\n        width: 100%;\n        height: 100%;\n      }\n      \n      .activity-status {\n        position: absolute;\n        top: 20rpx;\n        right: 20rpx;\n      }\n    }\n    \n    .activity-content {\n      padding: 30rpx;\n      \n      .activity-name {\n        font-size: 32rpx;\n        font-weight: bold;\n        color: #1a1a1a; /* 提高对比度 */\n        margin-bottom: 10rpx;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n\n      .activity-title {\n        font-size: 28rpx;\n        color: #4a4a4a; /* 提高对比度，从#666改为#4a4a4a */\n        margin-bottom: 20rpx;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n      \n      .activity-meta {\n        display: flex;\n        margin-bottom: 20rpx;\n        \n        .meta-item {\n          display: flex;\n          align-items: center;\n          margin-right: 40rpx;\n          font-size: 24rpx;\n          color: #666666; /* 提高对比度，从#999改为#666666 */\n\n          text {\n            margin-left: 10rpx;\n          }\n        }\n      }\n\n      .activity-organizer {\n        font-size: 24rpx;\n        color: #666666; /* 提高对比度，从#999改为#666666 */\n      }\n    }\n    \n    .activity-actions {\n      position: absolute;\n      top: 50%;\n      right: 30rpx;\n      transform: translateY(-50%);\n    }\n  }\n}\n\n.empty-state {\n  padding: 100rpx 0;\n}\n\n.load-more {\n  padding: 30rpx 0;\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/branch_president/branch_activities.vue'\nwx.createPage(MiniProgramPage)"], "names": ["loading", "ref", "activityList", "filterStatus", "currentPage", "hasMore", "loadStatus", "stats", "reactive", "total", "ongoing", "completed", "cancelled", "totalActivities", "computed", "ongoingActivities", "completedActivities", "onLoad", "loadActivities", "onPullDownRefresh", "setTimeout", "uni", "index", "stopPullDownRefresh", "onReachBottom", "value", "async", "isRefresh", "userInfo", "store", "$state", "res", "branch_presidentget_activities", "uid", "token", "page", "page_size", "status", "newActivities", "data", "push", "length", "showToast", "title", "icon", "msg", "error", "console", "setFilter", "loadMore", "getStatusText", "activity", "display_status", "getStatusType", "formatTime", "timestamp", "ts", "parseInt", "isNaN", "date", "Date", "getTime", "month", "getMonth", "day", "getDate", "hour", "getHours", "minute", "getMinutes", "toString", "padStart", "navigateTo", "url", "id", "wx", "createPage", "MiniProgramPage"], "mappings": "+gCAgJM,MAAAA,EAAUC,EAAAA,KAAI,GACdC,EAAeD,EAAAA,IAAI,IACnBE,EAAeF,EAAAA,IAAI,OACnBG,EAAcH,EAAAA,IAAI,GAClBI,EAAUJ,EAAAA,KAAI,GACdK,EAAaL,EAAAA,IAAI,YAIjBM,EAAQC,EAAAA,SAAS,CACrBC,MAAO,EACPC,QAAS,EACTC,UAAW,EACXC,UAAW,IAIPC,EAAkBC,EAAQA,UAAC,IAAMP,EAAME,QACvCM,EAAoBD,EAAQA,UAAC,IAAMP,EAAMG,UACzCM,EAAsBF,EAAQA,UAAC,IAAMP,EAAMI,YAGjDM,EAAAA,QAAO,KACLC,GAAe,EAAI,IAIrBC,EAAAA,mBAAkB,KAChBD,GAAe,GACfE,YAAW,KACTC,EAAGC,MAACC,qBAAmB,GACtB,IAAI,IAITC,EAAAA,eAAc,KACRnB,EAAQoB,QAAUzB,EAAQyB,UAE9B,IAII,MAAAP,EAAiBQ,MAAOC,GAAY,KACpC,IACF,GAAIA,EACFvB,EAAYqB,MAAQ,EACpBpB,EAAQoB,OAAQ,EAChBzB,EAAQyB,OAAQ,EAChBnB,EAAWmB,MAAQ,eACzB,IAAgBpB,EAAQoB,MAClB,OAGF,MAAMG,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BG,QAAYC,iCAA+B,CAC/CC,IAAKL,EAASK,IACdC,MAAON,EAASM,MAChBC,KAAM/B,EAAYqB,MAClBW,UApDW,GAqDXC,OAAQlC,EAAasB,QAGnB,GAAe,OAAfM,EAAIM,OAAiB,CACjB,MAAAC,EAAgBP,EAAIQ,MAAQ,GAE9BZ,EACFzB,EAAauB,MAAQa,EAERpC,EAAAuB,MAAMe,QAAQF,GAIzBP,EAAIxB,QACAA,EAAAE,MAAQsB,EAAIxB,MAAME,OAAS,EAC3BF,EAAAG,QAAUqB,EAAIxB,MAAMG,SAAW,EAC/BH,EAAAI,UAAYoB,EAAIxB,MAAMI,WAAa,EACnCJ,EAAAK,UAAYmB,EAAIxB,MAAMK,WAAa,GAInCP,EAAAoB,MA1EG,KA0EKa,EAAcG,OAClBrC,EAAAqB,QAEDnB,EAAAmB,MAAQpB,EAAQoB,MAAQ,WAAa,QAEtD,KAA8B,YAAfM,EAAIM,OACbhB,EAAAA,MAAIqB,UAAU,CACZC,MAAO,cACPC,KAAM,UAGRvB,EAAAA,MAAIqB,UAAU,CACZC,MAAOZ,EAAIc,KAAO,OAClBD,KAAM,SAERtC,EAAWmB,MAAQ,WAEtB,OAAQqB,GACCC,QAAAD,MAAM,YAAaA,GAC3BzB,EAAAA,MAAIqB,UAAU,CACZC,MAAO,WACPC,KAAM,SAERtC,EAAWmB,MAAQ,UACvB,CAAY,QACRzB,EAAQyB,OAAQ,CAClB,GAIIuB,EAAaX,IACjBlC,EAAasB,MAAQY,EACrBnB,GAAe,EAAI,EAIf+B,EAAW,MACVjD,EAAQyB,OAASpB,EAAQoB,QAC5BnB,EAAWmB,MAAQ,UACnBP,GAAe,GACjB,EAWIgC,EAAiBC,IAGH,CAChB,EAAG,MACH,EAAG,MACH,EAAG,OAJUA,EAASC,gBAAkBD,EAASd,SAMvB,MAIxBgB,EAAiBF,IAEL,CACd,EAAG,UACH,EAAG,OACH,EAAG,SAJUA,EAASC,gBAAkBD,EAASd,SAMzB,QAItBiB,EAAcC,IACd,IAACA,GAA2B,IAAdA,EAAwB,MAAA,OAG1C,MAAMC,EAA0B,iBAAdD,EAAyBE,SAASF,GAAaA,EACjE,GAAIG,MAAMF,GAAY,MAAA,SAEtB,MAAMG,EAAO,IAAIC,KAAU,IAALJ,GAClB,GAAAE,MAAMC,EAAKE,WAAmB,MAAA,SAE5B,MAAAC,EAAQH,EAAKI,WAAa,EAC1BC,EAAML,EAAKM,UACXC,EAAOP,EAAKQ,WACZC,EAAST,EAAKU,aACpB,MAAO,GAAGP,KAASE,MAAQE,EAAKI,WAAWC,SAAS,EAAG,QAAQH,EAAOE,WAAWC,SAAS,EAAG,MAAG,giDA5CvE,CAACpB,IAC1B9B,EAAAA,MAAImD,WAAW,CACbC,IAAK,qCAAqCtB,EAASuB,MACpD,oZC9QHC,GAAGC,WAAWC"}