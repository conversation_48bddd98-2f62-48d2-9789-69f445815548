"use strict";const t=require("../../../../common/vendor.js"),e={name:"u-album",mixins:[t.mpMixin,t.mixin,t.props$11],data:()=>({singleWidth:0,singleHeight:0,singlePercent:.6}),watch:{urls:{immediate:!0,handler(t){1===t.length&&this.getImageRect()}}},emits:["albumWidth"],computed:{imageStyle(){return(e,i)=>{const{space:s,rowCount:h,multipleSize:n,urls:r}=this,{addUnit:l,addStyle:o}=t.index.$u,a=this.showUrls.length;this.urls.length;const g={marginRight:l(s),marginBottom:l(s)};return e!==a||this.autoWrap||(g.marginBottom=0),this.autoWrap||(i===h||e===a&&i===this.showUrls[e-1].length)&&(g.marginRight=0),g}},showUrls(){if(this.autoWrap)return[this.urls.slice(0,this.maxCount)];{const t=[];return this.urls.map(((e,i)=>{if(i+1<=this.maxCount){const s=Math.floor(i/this.rowCount);t[s]||(t[s]=[]),t[s].push(e)}})),t}},imageWidth(){return t.addUnit(1===this.urls.length?this.singleWidth:this.multipleSize,this.unit)},imageHeight(){return t.addUnit(1===this.urls.length?this.singleHeight:this.multipleSize,this.unit)},albumWidth(){let t=0;return t=1===this.urls.length?this.singleWidth:this.showUrls[0].length*this.multipleSize+this.space*(this.showUrls[0].length-1),this.$emit("albumWidth",t),t}},methods:{addUnit:t.addUnit,onPreviewTap(e,i){const s=this.urls.map((t=>this.getSrc(t)));t.index.previewImage({current:i,urls:s}),this.stop&&this.preventEvent(e)},getSrc(e){return t.test.object(e)?this.keyName&&e[this.keyName]||e.src:e},getImageRect(){const e=this.getSrc(this.urls[0]);t.index.getImageInfo({src:e,success:t=>{const e=t.width>=t.height;this.singleWidth=e?this.singleSize:t.width/t.height*this.singleSize,this.singleHeight=e?t.height/t.width*this.singleWidth:this.singleSize},fail:()=>{this.getComponentWidth()}})},async getComponentWidth(){await t.sleep(30),this.$uGetRect(".u-album__row").then((t=>{this.singleWidth=t.width*this.singlePercent}))}}};if(!Array){t.resolveComponent("up-text")()}Math;const i=t._export_sfc(e,[["render",function(e,i,s,h,n,r){return{a:t.f(r.showUrls,((i,s,h)=>({a:t.f(i,((i,n,l)=>t.e({a:r.getSrc(i),b:e.showMore&&e.urls.length>e.rowCount*r.showUrls.length&&s===r.showUrls.length-1&&n===r.showUrls[r.showUrls.length-1].length-1},e.showMore&&e.urls.length>e.rowCount*r.showUrls.length&&s===r.showUrls.length-1&&n===r.showUrls[r.showUrls.length-1].length-1?{c:"20e13790-0-"+h+"-"+l,d:t.p({text:"+"+(e.urls.length-e.maxCount),color:"#fff",size:.3*e.multipleSize,align:"center",customStyle:"justify-content: center"}),e:"circle"==e.shape?"50%":r.addUnit(e.radius)}:{},{f:n,g:t.s(r.imageStyle(s+1,n+1)),h:t.o((t=>e.previewFullImage?r.onPreviewTap(t,r.getSrc(i)):""),n)}))),b:s}))),b:1===e.urls.length?r.imageHeight>0?e.singleMode:"widthFix":e.multipleMode,c:t.s({width:r.imageWidth,height:r.imageHeight,borderRadius:"circle"==e.shape?"10000px":r.addUnit(e.radius)}),d:r.albumWidth,e:e.autoWrap?"wrap":"nowrap"}}],["__scopeId","data-v-20e13790"]]);wx.createComponent(i);
//# sourceMappingURL=u-album.js.map
