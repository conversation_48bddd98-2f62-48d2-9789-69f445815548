"use strict";const e=require("../common/vendor.js");require("../utils/request.js"),require("../store/index.js");const r=require("../utils/index.js");if(require("../utils/BaseUrl.js"),require("../utils/auth.js"),require("../store/counter.js"),require("../api/index.js"),require("../utils/systemInfo.js"),require("../utils/cacheManager.js"),!Array){e.resolveComponent("u-icon")()}Math;const t={__name:"myLine",props:{w:{type:String||Number,default:"5"},h:{type:String||Number,default:"25"},bg:{type:String,default:"#333"},r:{type:String||Number,default:"2"},title:{type:String,default:"标题"},size:{type:String||Number,default:"34"},c:{type:String,default:"#000"},mr:{type:String||Number,default:"10"},right:{type:Boolean,default:!1},label:{type:String,default:""},iconSize:{type:Number||String,default:15},url:{type:String,default:""}},setup:t=>(i,l)=>e.e({a:t.w+"rpx",b:t.h+"rpx",c:t.bg,d:t.r+"rpx",e:t.mr+"rpx",f:e.t(t.title),g:t.size+"rpx",h:t.c,i:t.size>=28?"bold":"normal",j:t.right},t.right?{k:e.o((i=>e.unref(r.navto)(t.url))),l:e.p({label:t.label,"label-pos":"left","label-color":"#AAAAAA","label-size":"20rpx",size:t.iconSize,color:"#AAAAAA",name:"arrow-right"})}:{})};wx.createComponent(t);
//# sourceMappingURL=myLine.js.map
