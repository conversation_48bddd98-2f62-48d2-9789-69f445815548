<u-popup wx:if="{{z}}" class="data-v-15aca955" u-s="{{['d']}}" bindclose="{{y}}" u-i="15aca955-0" bind:__l="__l" u-p="{{z}}"><view class="u-action-sheet data-v-15aca955"><view wx:if="{{a}}" class="u-action-sheet__header data-v-15aca955"><text class="u-action-sheet__header__title u-line-1 data-v-15aca955">{{b}}</text><view class="u-action-sheet__header__icon-wrap data-v-15aca955" catchtap="{{d}}"><u-icon wx:if="{{c}}" class="data-v-15aca955" u-i="15aca955-1,15aca955-0" bind:__l="__l" u-p="{{c}}"></u-icon></view></view><text wx:if="{{e}}" class="u-action-sheet__description data-v-15aca955" style="{{g}}">{{f}}</text><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><u-line wx:if="{{h}}" class="data-v-15aca955" u-i="15aca955-2,15aca955-0" bind:__l="__l"></u-line><scroll-view scroll-y class="u-action-sheet__item-wrap data-v-15aca955" style="{{'max-height:' + q}}"><view wx:for="{{i}}" wx:for-item="item" wx:key="v" class="data-v-15aca955"><button class="u-reset-button data-v-15aca955" openType="{{item.j}}" bindgetuserinfo="{{item.k}}" bindcontact="{{item.l}}" bindgetphonenumber="{{item.m}}" binderror="{{item.n}}" bindlaunchapp="{{item.o}}" bindopensetting="{{item.p}}" lang="{{j}}" session-from="{{k}}" send-message-title="{{l}}" send-message-path="{{m}}" send-message-img="{{n}}" show-message-card="{{o}}" app-parameter="{{p}}" bindtap="{{item.q}}" hover-class="{{item.r}}"><view class="u-action-sheet__item-wrap__item data-v-15aca955" catchtap="{{item.h}}" hover-class="{{item.i}}" hover-stay-time="{{150}}"><block wx:if="{{item.a}}"><text class="u-action-sheet__item-wrap__item__name data-v-15aca955" style="{{item.c}}">{{item.b}}</text><text wx:if="{{item.d}}" class="u-action-sheet__item-wrap__item__subname data-v-15aca955">{{item.e}}</text></block><u-loading-icon wx:else class="data-v-15aca955" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g||''}}"/></view></button><u-line wx:if="{{item.s}}" class="data-v-15aca955" u-i="{{item.t}}" bind:__l="__l"></u-line></view></scroll-view></block><u-gap wx:if="{{r}}" class="data-v-15aca955" u-i="15aca955-5,15aca955-0" bind:__l="__l" u-p="{{s}}"></u-gap><view wx:if="{{t}}" class="u-action-sheet__item-wrap__item u-action-sheet__cancel data-v-15aca955" hover-class="u-action-sheet--hover" bindtap="{{x}}"><text catchtouchmove="{{w}}" hover-stay-time="{{150}}" class="u-action-sheet__cancel-text data-v-15aca955">{{v}}</text></view></view></u-popup>