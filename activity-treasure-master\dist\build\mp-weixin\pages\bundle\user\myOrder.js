"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),o=require("../../../store/index.js"),s=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),r=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-tabs")+e.resolveComponent("u-text")+e.resolveComponent("u-image")+e.resolveComponent("u-button")+e.resolveComponent("mescroll-uni")+e.resolveComponent("u-action-sheet"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-image/u-image.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js")+(()=>"../../../node-modules/uview-plus/components/u-action-sheet/u-action-sheet.js"))();const a={__name:"myOrder",setup(a){const i=e.ref([{name:"全部"},{name:"待付款"},{name:"待发货"},{name:"待收货"},{name:"已完成"},{name:"退款/售后"},{name:"待评价"}]),n=e.ref({status:"all"}),u=e.ref([]),{mescrollInit:l,downCallback:p,getMescroll:c}=s.useMescroll(e.onPageScroll,e.onReachBottom),d=e.ref(""),f=e.ref([{name:"余额支付",color:"#ffaa7f",fontSize:"18"},{name:"微信支付",color:"#ffaa7f",fontSize:"18"}]),x=e.ref(!1),m=e.ref({order_id:null,money:null}),g=e.ref(!1);e.onLoad((e=>{})),e.onShow((()=>{c().resetUpScroll(!0)})),e.onReady((async()=>{d.value=await r.setListHeight()+"px"}));const b=async e=>{(0,t.goodsget_order_list)({page:e.num,page_size:e.size,...n.value}).then((t=>{const o=t.data||[];1==e.num&&(u.value=[]),u.value=u.value.concat(o),e.endBySize(o.length,t.count)})).catch((()=>{e.endErr()}))},h=async o=>{let s;if("余额支付"===o.name)s=await t.payyue_pay({...m.value,type:g.value?4:1});else{const e=await t.payweixin_pay({...m.value,type:g.value?5:1});if("ok"===e.status){const o=await t.payget_weixinpay_sign({prepay_id:e.prepay_id});s=await r.pay(o),"requestPayment:ok"===s.errMsg?(s.status="ok",s.msg="支付成功"):s.msg="支付失败"}}"ok"===(null==s?void 0:s.status)?(e.index.$u.toast("支付成功"),c().resetUpScroll(!0)):e.index.$u.toast(null==s?void 0:s.msg)},v=e=>{switch(Number(e.index)){case 0:n.value.status="all";break;case 1:case 2:case 3:case 4:n.value.status=e.index-1+"";break;case 5:n.value.status="5,6,7";break;case 6:n.value.status="8,9,10"}u.value=[],c().resetUpScroll(!0)};return(s,a)=>({a:e.o(v),b:e.p({list:i.value,"active-style":{borderRadius:"26rpx",textAlign:"center",lineHeight:"52rpx",fontSize:"28rpx",color:"#333333"},inactiveStyle:{fontSize:"24rpx",color:"#999999"},itemStyle:{padding:"20rpx 30rpx"},lineWidth:"0"}),c:e.f(u.value,((s,a,i)=>e.e({a:"41f1b930-2-"+i+",41f1b930-1",b:e.p({size:"24rpx",color:"#aaa",text:s.create_time}),c:"41f1b930-3-"+i+",41f1b930-1",d:e.p({align:"rigth",text:e.unref(r.getItem)(["等待买家付款","等待卖家发货","卖家已发货","交易成功","交易关闭","退款中","退款成功","退款失败"],s.status)}),e:e.o((t=>e.unref(o.store)().setGoodInfo(s,!1,g.value)),a)},g.value?{f:"41f1b930-4-"+i+",41f1b930-1",g:e.p({size:"26rpx",text:"商品名称："}),h:"41f1b930-5-"+i+",41f1b930-1",i:e.p({size:"26rpx",text:s.goods_name}),j:"41f1b930-6-"+i+",41f1b930-1",k:e.p({size:"26rpx",text:s.order_id}),l:"41f1b930-7-"+i+",41f1b930-1",m:e.p({mode:"price",color:"#EF6227",size:"26rpx",align:"right",text:s.money}),n:"41f1b930-8-"+i+",41f1b930-1",o:e.p({size:"18rpx",color:"#AAAAAA",align:"right",text:"x1"}),p:e.o((t=>e.unref(o.store)().setGoodInfo(s,!1,!0)),a)}:{q:e.f(s.goods_info,((t,r,a)=>({a:"41f1b930-9-"+i+"-"+a+",41f1b930-1",b:e.p({width:"188rpx",height:"188rpx",src:t.img_url}),c:"41f1b930-10-"+i+"-"+a+",41f1b930-1",d:e.p({size:"28rpx",text:t.goods_name}),e:"41f1b930-11-"+i+"-"+a+",41f1b930-1",f:e.f(t.guige_info,((t,o,s)=>({a:"41f1b930-12-"+i+"-"+a+"-"+s+",41f1b930-1",b:e.p({size:"26rpx",text:`${o}:${t},`}),c:o}))),g:"41f1b930-13-"+i+"-"+a+",41f1b930-1",h:e.p({color:"#AAAAAA",align:"right",size:"18rpx",text:`x${t.num}`}),i:"41f1b930-14-"+i+"-"+a+",41f1b930-1",j:e.p({mode:"price",size:"18rpx",color:"#EF6227",text:t.money}),k:e.o((t=>e.unref(o.store)().setGoodInfo(s)),r),l:r}))),r:e.p({size:"26rpx",text:"规格："})},{s:0===s.status},0===s.status?{t:e.o((o=>(async o=>{const s=t.goodscancel_order,r=await s({order_id:o});"ok"===r.status?(e.index.$u.toast("取消成功"),c().resetUpScroll(!0)):e.index.$u.toast(r.msg)})(s.order_id)),a),v:"41f1b930-15-"+i+",41f1b930-1",w:e.p({shape:"circle",color:"#aaa",plain:!0,text:"取消订单",customStyle:{margin:"10rpx 10rpx 0 0",width:"140rpx",height:"48rpx",fontSize:"22rpx"}})}:{},{x:0===s.status},0===s.status?{y:e.o((e=>{x.value=!0,m.value.type=1,m.value.order_id=s.order_id,m.value.money=s.money}),a),z:"41f1b930-16-"+i+",41f1b930-1",A:e.p({shape:"circle",color:"#FA8700",plain:!0,text:"继续付款",customStyle:{margin:"10rpx 0 0",width:"140rpx",height:"48rpx",fontSize:"22rpx"}})}:{},{B:1===s.status&&!g.value||2===s.status&&!g.value},1===s.status&&!g.value||2===s.status&&!g.value?{C:e.o((t=>e.unref(r.navto)(`/pages/bundle/user/returnGood?order_id=${s.order_id}`)),a),D:"41f1b930-17-"+i+",41f1b930-1",E:e.p({shape:"circle",color:"#aaa",plain:!0,text:s.is_shenqing_tuikuan?"退款中":"申请退款",disabled:!!s.is_shenqing_tuikuan,customStyle:{margin:"10rpx 10rpx 0",width:"140rpx",height:"48rpx",fontSize:"22rpx"}})}:{},{F:2===s.status},2===s.status?{G:e.o((o=>(async o=>{const s=g.value?dalibaoqueren_shouhuo:t.goodsqueren_shouhuo,r=await s({order_id:o});"ok"===r.status?(e.index.$u.toast("确认成功"),c().resetUpScroll(!0)):e.index.$u.toast(r.msg)})(s.order_id)),a),H:"41f1b930-18-"+i+",41f1b930-1",I:e.p({shape:"circle",color:"#FA8700",plain:!0,text:"确认收货",customStyle:{margin:"10rpx 0 0",width:"140rpx",height:"48rpx",fontSize:"22rpx"}})}:{},{J:0==s.is_pingjia&&3===s.status&&!g.value},0!=s.is_pingjia||3!==s.status||g.value?{}:{K:e.o((t=>e.unref(o.store)().setGoodInfo(s,!0)),a),L:"41f1b930-19-"+i+",41f1b930-1",M:e.p({shape:"circle",color:"#FA8700",plain:!0,text:"去评价",customStyle:{margin:"10rpx 0 0",width:"140rpx",height:"48rpx",fontSize:"22rpx"}})},{N:a}))),d:g.value,e:e.o(e.unref(l)),f:e.o(e.unref(p)),g:e.o(b),h:e.o((e=>e.scrollTo(0))),i:e.p({down:{auto:!1},height:d.value}),j:e.o((e=>x.value=!1)),k:e.o(h),l:e.p({title:"支付方式选择","close-on-click-action":!0,actions:f.value,show:x.value})})},__runtimeHooks:1};wx.createPage(a);
//# sourceMappingURL=myOrder.js.map
