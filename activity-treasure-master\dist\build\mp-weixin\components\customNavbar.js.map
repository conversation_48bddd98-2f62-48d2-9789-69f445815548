{"version": 3, "file": "customNavbar.js", "sources": ["../../../../src/components/customNavbar.vue", "../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvY3VzdG9tTmF2YmFyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"custom-navbar\" :style=\"{ background: bgColor, height: height }\">\n    <u-status-bar :bgColor=\"bgColor\"></u-status-bar>\n    <view class=\"navbar-content\">\n      <!-- 左侧返回按钮 -->\n      <view class=\"navbar-left\" @click=\"handleBack\" v-if=\"showBack\">\n        <u-icon \n          :name=\"backIcon\" \n          size=\"44rpx\" \n          :color=\"iconColor\"\n        ></u-icon>\n      </view>\n      \n      <!-- 中间标题 -->\n      <view class=\"navbar-center\">\n        <text class=\"navbar-title\" :style=\"{ color: titleColor, fontWeight: bold ? '700' : '400' }\">\n          {{ title }}\n        </text>\n      </view>\n      \n      <!-- 右侧操作区域 -->\n      <view class=\"navbar-right\">\n        <slot name=\"right\"></slot>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { defineProps, defineEmits } from 'vue';\n\nconst props = defineProps({\n  // 标题文字\n  title: {\n    type: String,\n    default: ''\n  },\n  // 背景色\n  bgColor: {\n    type: String,\n    default: 'linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)'\n  },\n  // 导航栏高度\n  height: {\n    type: String,\n    default: '200rpx'\n  },\n  // 标题颜色\n  titleColor: {\n    type: String,\n    default: '#ffffff'\n  },\n  // 图标颜色\n  iconColor: {\n    type: String,\n    default: '#ffffff'\n  },\n  // 是否显示返回按钮\n  showBack: {\n    type: Boolean,\n    default: true\n  },\n  // 返回按钮图标\n  backIcon: {\n    type: String,\n    default: 'arrow-left'\n  },\n  // 返回类型：'back'返回上一页，'home'返回首页\n  backType: {\n    type: String,\n    default: 'back'\n  },\n  // 标题是否加粗\n  bold: {\n    type: Boolean,\n    default: true\n  }\n});\n\nconst emit = defineEmits(['back']);\n\nconst handleBack = () => {\n  if (props.backType === 'home') {\n    uni.reLaunch({ url: '/pages/index' });\n  } else {\n    uni.navigateBack({\n      delta: 1,\n      fail: () => uni.reLaunch({ url: '/pages/index' })\n    });\n  }\n  emit('back');\n};\n</script>\n\n<style scoped lang=\"less\">\n.custom-navbar {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);\n}\n\n.navbar-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 80rpx;\n  padding: 0 var(--spacing-lg, 32rpx);\n  position: relative;\n}\n\n.navbar-left {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 80rpx;\n  height: 80rpx;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.navbar-left:active {\n  transform: scale(0.95);\n  opacity: 0.7;\n}\n\n.navbar-center {\n  position: absolute;\n  left: 50%;\n  transform: translateX(-50%);\n  max-width: 400rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  text-align: center;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  min-width: 80rpx;\n  height: 80rpx;\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/customNavbar.vue'\nwx.createComponent(Component)"], "names": ["handleBack", "props", "backType", "uni", "reLaunch", "url", "navigateBack", "delta", "fail", "emit", "wx", "createComponent", "Component"], "mappings": "yuBAiFMA,EAAa,KACM,SAAnBC,EAAMC,SACRC,EAAAA,MAAIC,SAAS,CAAEC,IAAK,iBAEpBF,EAAAA,MAAIG,aAAa,CACfC,MAAO,EACPC,KAAM,IAAML,EAAAA,MAAIC,SAAS,CAAEC,IAAK,mBAGpCI,EAAK,OAAM,kRCzFbC,GAAGC,gBAAgBC"}