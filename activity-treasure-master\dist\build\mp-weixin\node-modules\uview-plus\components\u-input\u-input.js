"use strict";const e=require("../../../../common/vendor.js"),n={name:"u-input",mixins:[e.mpMixin,e.mixin,e.props$2],data:()=>({clearInput:!1,innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:e=>e}),created(){this.formatter&&(this.innerFormatter=this.formatter)},watch:{modelValue:{immediate:!0,handler(n,t){this.changeFromInner||this.innerValue===n||(this.innerValue=n,!1===this.firstChange&&!1===this.changeFromInner?this.valueChange(this.innerValue,!0):this.firstChange||e.formValidate(this,"change"),this.firstChange=!1),this.changeFromInner=!1}}},computed:{isShowClear(){const{clearable:e,readonly:n,focused:t,innerValue:i}=this;return!!e&&!n&&!!t&&""!==i},inputClass(){let e=[],{border:n,disabled:t,shape:i}=this;return"surround"===n&&(e=e.concat(["u-border","u-input--radius"])),e.push(`u-input--${i}`),"bottom"===n&&(e=e.concat(["u-border-bottom","u-input--no-radius"])),e.join(" ")},wrapperStyle(){const n={};return this.disabled&&(n.backgroundColor=this.disabledColor),"none"===this.border?n.padding="0":(n.paddingTop="6px",n.paddingBottom="6px",n.paddingLeft="9px",n.paddingRight="9px"),e.deepMerge(n,e.addStyle(this.customStyle))},inputStyle(){return{color:this.color,fontSize:e.addUnit(this.fontSize),textAlign:this.inputAlign}}},emits:["update:modelValue","focus","blur","change","confirm","clear","keyboardheightchange","nicknamereview"],methods:{setFormatter(e){this.innerFormatter=e},onInput(e){let{value:n=""}=e.detail||{};this.innerValue=n,this.$nextTick((()=>{let e=this.innerFormatter(n);this.innerValue=e,this.valueChange(e)}))},onBlur(n){this.$emit("blur",n.detail.value),e.sleep(150).then((()=>{this.focused=!1})),e.formValidate(this,"blur")},onFocus(e){this.focused=!0,this.$emit("focus")},doFocus(){this.$refs["input-native"].focus()},doBlur(){this.$refs["input-native"].blur()},onConfirm(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},onnicknamereview(e){this.$emit("nicknamereview",e)},valueChange(n,t=!1){this.clearInput&&(this.innerValue="",this.clearInput=!1),this.$nextTick((()=>{t&&!this.clearInput||(this.changeFromInner=!0,this.$emit("change",n),this.$emit("update:modelValue",n)),e.formValidate(this,"change")}))},onClear(){this.clearInput=!0,this.innerValue="",this.$nextTick((()=>{this.valueChange(""),this.$emit("clear")}))},clickHandler(){}}};if(!Array){e.resolveComponent("u-icon")()}Math;const t=e._export_sfc(n,[["render",function(n,t,i,o,r,s){return e.e({a:n.prefixIcon||n.$slots.prefix},n.prefixIcon||n.$slots.prefix?{b:e.p({name:n.prefixIcon,size:"18",customStyle:n.prefixIconStyle})}:{},{c:e.s(s.inputStyle),d:n.type,e:n.focus,f:n.cursor,g:r.innerValue,h:n.autoBlur,i:n.disabled||n.readonly,j:n.maxlength,k:n.placeholder,l:n.placeholderStyle,m:n.placeholderClass,n:n.confirmType,o:n.confirmHold,p:n.holdKeyboard,q:n.cursorSpacing,r:n.adjustPosition,s:n.selectionEnd,t:n.selectionStart,v:n.password||"password"===n.type||!1,w:n.ignoreCompositionEvent,x:e.o(((...e)=>s.onInput&&s.onInput(...e))),y:e.o(((...e)=>s.onBlur&&s.onBlur(...e))),z:e.o(((...e)=>s.onFocus&&s.onFocus(...e))),A:e.o(((...e)=>s.onConfirm&&s.onConfirm(...e))),B:e.o(((...e)=>s.onkeyboardheightchange&&s.onkeyboardheightchange(...e))),C:e.o(((...e)=>s.onnicknamereview&&s.onnicknamereview(...e))),D:e.o(((...e)=>s.clickHandler&&s.clickHandler(...e))),E:s.isShowClear},s.isShowClear?{F:e.p({name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}),G:e.o(((...e)=>s.onClear&&s.onClear(...e)))}:{},{H:n.suffixIcon||n.$slots.suffix},n.suffixIcon||n.$slots.suffix?{I:e.p({name:n.suffixIcon,size:"18",customStyle:n.suffixIconStyle})}:{},{J:e.n(s.inputClass),K:e.s(s.wrapperStyle)})}],["__scopeId","data-v-6a3de118"]]);wx.createComponent(t);
//# sourceMappingURL=u-input.js.map
