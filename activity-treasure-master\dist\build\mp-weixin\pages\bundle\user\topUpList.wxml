<view class="page"><view class="py30 df aic" style="background-color:#eeeeee"><u-text wx:if="{{a}}" u-i="9c4fcf88-0" bind:__l="__l" u-p="{{a}}"></u-text><u-text wx:if="{{b}}" u-i="9c4fcf88-1" bind:__l="__l" u-p="{{b}}"></u-text><u-text wx:if="{{c}}" u-i="9c4fcf88-2" bind:__l="__l" u-p="{{c}}"></u-text></view><mescroll-uni wx:if="{{i}}" u-s="{{['d']}}" class="list" bindinit="{{e}}" binddown="{{f}}" bindup="{{g}}" bindtopclick="{{h}}" u-i="9c4fcf88-3" bind:__l="__l" u-p="{{i}}"><view wx:for="{{d}}" wx:for-item="val" wx:key="g" class="py30 df aic borderBottom"><u-text wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></u-text><u-text wx:if="{{val.d}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{val.d}}"></u-text><u-text wx:if="{{val.f}}" u-i="{{val.e}}" bind:__l="__l" u-p="{{val.f}}"></u-text></view></mescroll-uni></view>