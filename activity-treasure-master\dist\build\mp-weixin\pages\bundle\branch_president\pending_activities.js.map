{"version": 3, "file": "pending_activities.js", "sources": ["../../../../../../src/pages/bundle/branch_president/pending_activities.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGJyYW5jaF9wcmVzaWRlbnRccGVuZGluZ19hY3Rpdml0aWVzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page\">\n    <myTitle\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      title=\"待审核活动\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <!-- 活动列表 -->\n      <view class=\"activity-list\" v-if=\"activityList.length > 0\">\n        <view\n          class=\"activity-item\"\n          v-for=\"activity in activityList\"\n          :key=\"activity.id\"\n          @click=\"viewActivityDetail(activity)\"\n        >\n          <view class=\"activity-header\">\n            <image \n              class=\"activity-image\"\n              :src=\"activity.img_url || '/static/default-activity.png'\"\n              mode=\"aspectFill\"\n            />\n            <view class=\"activity-info\">\n              <view class=\"activity-name\">{{ activity.name }}</view>\n              <view class=\"activity-title\">{{ activity.title }}</view>\n              <view class=\"activity-meta\">\n                <text class=\"organizer\">组织者：{{ activity.organizer_name }}</text>\n                <text class=\"create-time\">{{ formatTime(activity.create_time) }}</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"activity-actions\" @click.stop>\n            <u-button\n              type=\"success\"\n              size=\"small\"\n              :loading=\"activity.reviewing\"\n              @click.stop=\"reviewActivity(activity, 1)\"\n              customStyle=\"background: #6AC086; border: none; border-radius: 30rpx; margin-right: 20rpx;\"\n            >\n              通过\n            </u-button>\n            <u-button\n              type=\"error\"\n              size=\"small\"\n              :loading=\"activity.reviewing\"\n              @click.stop=\"showRejectModal(activity)\"\n              customStyle=\"background: #ff4757; border: none; border-radius: 30rpx;\"\n            >\n              拒绝\n            </u-button>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-else-if=\"!loading\">\n        <u-empty\n          mode=\"list\"\n          text=\"暂无待审核活动\"\n          textColor=\"#999999\"\n          textSize=\"28\"\n        ></u-empty>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMore && !loading\">\n        <u-button\n          type=\"primary\"\n          :loading=\"loadingMore\"\n          @click=\"loadMore\"\n          customStyle=\"background: #6AC086; border: none; border-radius: 50rpx;\"\n        >\n          {{ loadingMore ? '加载中...' : '加载更多' }}\n        </u-button>\n      </view>\n    </view>\n    \n    <!-- 拒绝理由弹窗 -->\n    <u-popup\n      :show=\"showRejectPopup\"\n      mode=\"center\"\n      :round=\"20\"\n      :closeable=\"true\"\n      @close=\"closeRejectModal\"\n    >\n      <view class=\"reject-modal\">\n        <view class=\"modal-title\">拒绝理由</view>\n        <u-textarea\n          v-model=\"rejectReason\"\n          placeholder=\"请输入拒绝理由（可选）\"\n          maxlength=\"200\"\n          count\n          height=\"120rpx\"\n        />\n        <view class=\"modal-actions\">\n          <u-button\n            type=\"info\"\n            @click=\"closeRejectModal\"\n            customStyle=\"margin-right: 20rpx; border-radius: 30rpx;\"\n          >\n            取消\n          </u-button>\n          <u-button\n            type=\"error\"\n            :loading=\"rejecting\"\n            @click=\"confirmReject\"\n            customStyle=\"background: #ff4757; border: none; border-radius: 30rpx;\"\n          >\n            确认拒绝\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 加载状态 -->\n    <u-loading-page \n      :loading=\"loading\" \n      loading-text=\"加载中...\"\n      bg-color=\"#f8f9fa\"\n    ></u-loading-page>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive } from \"vue\";\nimport { onLoad, onShow } from \"@dcloudio/uni-app\";\nimport { requireLogin } from \"@/utils/auth\";\nimport { branch_presidentpending_activities, branch_presidentreview_activity } from \"@/api\";\nimport { store } from \"@/store\";\n// 🔧 P1-3修复：使用统一的权限检查工具函数\nimport { hasActivityReviewPermission, PERMISSION_TYPES } from \"@/utils/permissions\";\n\n// 数据状态\nconst activityList = ref([]);\nconst loading = ref(true);\nconst loadingMore = ref(false);\nconst hasMore = ref(true);\nconst currentPage = ref(1);\nconst pageSize = 20;\n\n// 拒绝弹窗状态\nconst showRejectPopup = ref(false);\nconst rejectReason = ref('');\nconst rejecting = ref(false);\nconst currentRejectActivity = ref(null);\n\n// 页面加载\nonLoad(() => {\n  if (!requireLogin()) {\n    return;\n  }\n\n  // 🔧 P1-3修复：使用统一的权限检查工具函数\n  if (!hasActivityReviewPermission()) {\n    uni.showModal({\n      title: '权限不足',\n      content: '您没有活动审核权限，无法访问此页面',\n      showCancel: false,\n      success: () => {\n        uni.navigateBack();\n      }\n    });\n    return;\n  }\n\n  loadActivities();\n});\n\nonShow(() => {\n  // 每次显示页面时刷新数据\n  refreshData();\n});\n\n// 加载活动列表\nconst loadActivities = async (isRefresh = false) => {\n  try {\n    if (isRefresh) {\n      currentPage.value = 1;\n      hasMore.value = true;\n      loading.value = true;\n    } else if (!hasMore.value) {\n      return;\n    } else {\n      // 第一次加载时也要设置loading状态\n      loading.value = true;\n    }\n    \n    const userInfo = store().$state.userInfo;\n\n    // 验证用户登录状态\n    if (!userInfo || !userInfo.uid || !userInfo.token) {\n      uni.showToast({\n        title: '请先登录',\n        icon: 'none'\n      });\n      return;\n    }\n\n    console.log('加载待审核活动，页码:', currentPage.value);\n\n    const res = await branch_presidentpending_activities({\n      uid: userInfo.uid,\n      token: userInfo.token,\n      page: currentPage.value,\n      page_size: pageSize\n    });\n\n    console.log('待审核活动API响应:', res);\n    \n    if (res.status === 'ok') {\n      const newActivities = res.data || [];\n      \n      // 为每个活动添加审核状态\n      newActivities.forEach(activity => {\n        activity.reviewing = false;\n      });\n      \n      if (isRefresh) {\n        activityList.value = newActivities;\n      } else {\n        activityList.value.push(...newActivities);\n      }\n      \n      // 判断是否还有更多数据\n      hasMore.value = newActivities.length === pageSize;\n      \n    } else if (res.status === 'empty') {\n      if (isRefresh) {\n        activityList.value = [];\n      }\n      hasMore.value = false;\n    } else if (res.status === 'relogin') {\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n    } else {\n      uni.showToast({\n        title: res.msg || '加载失败',\n        icon: 'none'\n      });\n    }\n  } catch (error) {\n    console.error('加载活动列表失败:', error);\n    uni.showToast({\n      title: '网络错误，请稍后重试',\n      icon: 'none'\n    });\n  } finally {\n    loading.value = false;\n    loadingMore.value = false;\n  }\n};\n\n// 刷新数据\nconst refreshData = () => {\n  loadActivities(true);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (hasMore.value && !loadingMore.value) {\n    loadingMore.value = true;\n    currentPage.value++;\n    loadActivities();\n  }\n};\n\n// 审核活动\nconst reviewActivity = async (activity, status) => {\n  if (!activity || !activity.id) {\n    uni.showToast({\n      title: '活动信息错误',\n      icon: 'none'\n    });\n    return;\n  }\n\n  try {\n    activity.reviewing = true;\n\n    const userInfo = store().$state.userInfo;\n    if (!userInfo || !userInfo.uid || !userInfo.token) {\n      uni.showToast({\n        title: '用户信息错误，请重新登录',\n        icon: 'none'\n      });\n      return;\n    }\n\n    const res = await branch_presidentreview_activity({\n      uid: userInfo.uid,\n      token: userInfo.token,\n      huodong_id: activity.id,\n      status: status,\n      comment: status === 2 ? rejectReason.value : ''\n    });\n\n    console.log('审核活动API响应:', res);\n\n    if (res.status === 'ok') {\n      uni.showToast({\n        title: status === 1 ? '审核通过' : '已拒绝',\n        icon: 'success'\n      });\n\n      // 从列表中移除已审核的活动\n      const index = activityList.value.findIndex(item => item.id === activity.id);\n      if (index !== -1) {\n        activityList.value.splice(index, 1);\n      }\n\n    } else if (res.status === 'relogin') {\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n    } else {\n      uni.showToast({\n        title: res.msg || '审核失败',\n        icon: 'none'\n      });\n    }\n  } catch (error) {\n    console.error('审核活动失败:', error);\n    uni.showToast({\n      title: '网络错误，请稍后重试',\n      icon: 'none'\n    });\n  } finally {\n    activity.reviewing = false;\n  }\n};\n\n// 显示拒绝弹窗\nconst showRejectModal = (activity) => {\n  currentRejectActivity.value = activity;\n  rejectReason.value = '';\n  showRejectPopup.value = true;\n};\n\n// 关闭拒绝弹窗\nconst closeRejectModal = () => {\n  showRejectPopup.value = false;\n  currentRejectActivity.value = null;\n  rejectReason.value = '';\n};\n\n// 确认拒绝\nconst confirmReject = () => {\n  if (currentRejectActivity.value) {\n    rejecting.value = true;\n    reviewActivity(currentRejectActivity.value, 2).finally(() => {\n      rejecting.value = false;\n      closeRejectModal();\n    });\n  }\n};\n\n// 查看活动详情\nconst viewActivityDetail = (activity) => {\n  uni.navigateTo({\n    url: `/pages/bundle/index/activeInfo?id=${activity.id}`\n  });\n};\n\n// 格式化时间（修复iOS兼容性问题）\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n\n  // 修复iOS日期格式兼容性：将 \"yyyy-MM-dd HH:mm:ss\" 转换为 \"yyyy/MM/dd HH:mm:ss\"\n  const iosCompatibleTimeStr = timeStr.replace(/-/g, '/');\n  const date = new Date(iosCompatibleTimeStr);\n\n  // 检查日期是否有效\n  if (isNaN(date.getTime())) {\n    return timeStr; // 如果日期无效，返回原始字符串\n  }\n\n  const now = new Date();\n  const diff = now - date;\n\n  if (diff < 60000) { // 1分钟内\n    return '刚刚';\n  } else if (diff < 3600000) { // 1小时内\n    return Math.floor(diff / 60000) + '分钟前';\n  } else if (diff < 86400000) { // 1天内\n    return Math.floor(diff / 3600000) + '小时前';\n  } else {\n    return date.toLocaleDateString();\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.content-container {\n  padding: 30rpx;\n}\n\n.activity-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.activity-item {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.activity-item:active {\n  transform: scale(0.98);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.activity-header {\n  display: flex;\n  margin-bottom: 20rpx;\n}\n\n.activity-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 12rpx;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.activity-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.activity-name {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.activity-title {\n  font-size: 26rpx;\n  color: #666666;\n  margin-bottom: 12rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.activity-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 6rpx;\n}\n\n.organizer,\n.create-time {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.activity-actions {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.empty-state {\n  margin-top: 100rpx;\n}\n\n.load-more {\n  margin-top: 40rpx;\n  text-align: center;\n}\n\n.reject-modal {\n  padding: 40rpx;\n  width: 600rpx;\n}\n\n.modal-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n  text-align: center;\n  margin-bottom: 30rpx;\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: center;\n  margin-top: 30rpx;\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/branch_president/pending_activities.vue'\nwx.createPage(MiniProgramPage)"], "names": ["activityList", "ref", "loading", "loadingMore", "hasMore", "currentPage", "showRejectPopup", "rejectReason", "rejecting", "currentRejectActivity", "onLoad", "requireLogin", "hasActivityReviewPermission", "uni", "showModal", "title", "content", "showCancel", "success", "index", "navigateBack", "onShow", "loadActivities", "async", "isRefresh", "value", "userInfo", "store", "$state", "uid", "token", "showToast", "icon", "console", "log", "res", "branch_presidentpending_activities", "page", "page_size", "status", "newActivities", "data", "for<PERSON>ach", "activity", "reviewing", "push", "length", "msg", "error", "refreshData", "loadMore", "reviewActivity", "id", "branch_presidentreview_activity", "huodong_id", "comment", "findIndex", "item", "splice", "closeRejectModal", "confirmReject", "finally", "formatTime", "timeStr", "iosCompatibleTimeStr", "replace", "date", "Date", "isNaN", "getTime", "diff", "Math", "floor", "toLocaleDateString", "navigateTo", "url", "wx", "createPage", "MiniProgramPage"], "mappings": "2lCAyIA,MAAMA,EAAeC,EAAAA,IAAI,IACnBC,EAAUD,EAAAA,KAAI,GACdE,EAAcF,EAAAA,KAAI,GAClBG,EAAUH,EAAAA,KAAI,GACdI,EAAcJ,EAAAA,IAAI,GAIlBK,EAAkBL,EAAAA,KAAI,GACtBM,EAAeN,EAAAA,IAAI,IACnBO,EAAYP,EAAAA,KAAI,GAChBQ,EAAwBR,EAAAA,IAAI,MAGlCS,EAAAA,QAAO,KACAC,EAAYA,iBAKZC,EAA2BA,kCAC9BC,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,QAAS,oBACTC,YAAY,EACZC,QAAS,KACPL,EAAGM,MAACC,cAAY,QASxBC,EAAAA,QAAO,YAMD,MAAAC,EAAiBC,MAAOC,GAAY,KACpC,IACF,GAAIA,EACFnB,EAAYoB,MAAQ,EACpBrB,EAAQqB,OAAQ,EAChBvB,EAAQuB,OAAQ,MACtB,KAAgBrB,EAAQqB,MAClB,OAGAvB,EAAQuB,OAAQ,CAClB,CAEA,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAGhC,IAAKA,IAAaA,EAASG,MAAQH,EAASI,MAK1C,YAJAjB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,OACPiB,KAAM,SAKFC,QAAAC,IAAI,cAAe7B,EAAYoB,OAEjC,MAAAU,QAAYC,qCAAmC,CACnDP,IAAKH,EAASG,IACdC,MAAOJ,EAASI,MAChBO,KAAMhC,EAAYoB,MAClBa,UAlEW,KAuET,GAFIL,QAAAC,IAAI,cAAeC,GAER,OAAfA,EAAII,OAAiB,CACjB,MAAAC,EAAgBL,EAAIM,MAAQ,GAGlCD,EAAcE,SAAoBC,IAChCA,EAASC,WAAY,CAAA,IAGnBpB,EACFxB,EAAayB,MAAQe,EAERxC,EAAAyB,MAAMoB,QAAQL,GAIrBpC,EAAAqB,MAtFG,KAsFKe,EAAcM,MAEpC,KAA8B,UAAfX,EAAII,QACTf,IACFxB,EAAayB,MAAQ,IAEvBrB,EAAQqB,OAAQ,GACQ,YAAfU,EAAII,OACb1B,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,cACPiB,KAAM,SAGRnB,EAAAA,MAAIkB,UAAU,CACZhB,MAAOoB,EAAIY,KAAO,OAClBf,KAAM,QAGX,OAAQgB,GACCf,QAAAe,MAAM,YAAaA,GAC3BnC,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,aACPiB,KAAM,QAEZ,CAAY,QACR9B,EAAQuB,OAAQ,EAChBtB,EAAYsB,OAAQ,CACtB,GAIIwB,EAAc,KAClB3B,GAAe,EAAI,EAIf4B,EAAW,KACX9C,EAAQqB,QAAUtB,EAAYsB,QAChCtB,EAAYsB,OAAQ,EACRpB,EAAAoB,YAEd,EAII0B,EAAiB5B,MAAOoB,EAAUJ,KACtC,GAAKI,GAAaA,EAASS,GAQvB,IACFT,EAASC,WAAY,EAErB,MAAMlB,EAAWC,EAAAA,QAAQC,OAAOF,SAChC,IAAKA,IAAaA,EAASG,MAAQH,EAASI,MAK1C,YAJAjB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,eACPiB,KAAM,SAKJ,MAAAG,QAAYkB,kCAAgC,CAChDxB,IAAKH,EAASG,IACdC,MAAOJ,EAASI,MAChBwB,WAAYX,EAASS,GACrBb,SACAgB,QAAoB,IAAXhB,EAAehC,EAAakB,MAAQ,KAK3C,GAFIQ,QAAAC,IAAI,aAAcC,GAEP,OAAfA,EAAII,OAAiB,CACvB1B,EAAAA,MAAIkB,UAAU,CACZhB,MAAkB,IAAXwB,EAAe,OAAS,MAC/BP,KAAM,YAIF,MAAAb,EAAQnB,EAAayB,MAAM+B,cAAkBC,EAAKL,KAAOT,EAASS,MACtD,IAAdjC,GACWnB,EAAAyB,MAAMiC,OAAOvC,EAAO,EAGzC,KAA8B,YAAfgB,EAAII,OACb1B,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,cACPiB,KAAM,SAGRnB,EAAAA,MAAIkB,UAAU,CACZhB,MAAOoB,EAAIY,KAAO,OAClBf,KAAM,QAGX,OAAQgB,GACCf,QAAAe,MAAM,UAAWA,GACzBnC,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,aACPiB,KAAM,QAEZ,CAAY,QACRW,EAASC,WAAY,CACvB,MA5DE/B,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,SACPiB,KAAM,QA0DV,EAWI2B,EAAmB,KACvBrD,EAAgBmB,OAAQ,EACxBhB,EAAsBgB,MAAQ,KAC9BlB,EAAakB,MAAQ,EAAA,EAIjBmC,EAAgB,KAChBnD,EAAsBgB,QACxBjB,EAAUiB,OAAQ,EAClB0B,EAAe1C,EAAsBgB,MAAO,GAAGoC,SAAQ,KACrDrD,EAAUiB,OAAQ,SAGtB,EAWIqC,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAGrB,MAAMC,EAAuBD,EAAQE,QAAQ,KAAM,KAC7CC,EAAO,IAAIC,KAAKH,GAGtB,GAAII,MAAMF,EAAKG,WACN,OAAAN,EAGH,MACAO,EADM,IAAIH,KACGD,EAEnB,OAAII,EAAO,IACF,KACEA,EAAO,KACTC,KAAKC,MAAMF,EAAO,KAAS,MACzBA,EAAO,MACTC,KAAKC,MAAMF,EAAO,MAAW,MAE7BJ,EAAKO,oBACd,ihBAvDsB,CAAC9B,IACvBlC,EAAsBgB,MAAQkB,EAC9BpC,EAAakB,MAAQ,GACrBnB,EAAgBmB,OAAQ,CAAA,mMAsBC,CAACkB,IAC1B9B,EAAAA,MAAI6D,WAAW,CACbC,IAAK,qCAAqChC,EAASS,MACpD,yzBC9WHwB,GAAGC,WAAWC"}