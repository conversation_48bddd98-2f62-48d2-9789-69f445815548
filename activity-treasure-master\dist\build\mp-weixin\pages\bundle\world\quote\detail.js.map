{"version": 3, "file": "detail.js", "sources": ["../../../../../../../src/pages/bundle/world/quote/detail.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXHF1b3RlXGRldGFpbC52dWU"], "sourcesContent": ["<script setup>\nimport { ref, onMounted } from 'vue';\nimport { getQuoteDetail, getQuoteComments, postQuoteComment, likeQuote, favoriteQuote } from '@/api/index.js';\nimport { store } from '@/store';\nimport { navto } from '@/utils';\nimport { requireLogin } from '@/utils/auth';\nimport { onLoad } from '@dcloudio/uni-app';\n\n// 状态管理\nconst quote = ref(null);\nconst loading = ref(true);\nconst showComments = ref(false);\nconst comments = ref([]);\nconst commentsLoading = ref(false);\nconst commentText = ref('');\nconst quoteId = ref(0);\n\n// 加载摘录详情\nconst loadQuoteDetail = async () => {\n  try {\n    loading.value = true;\n    \n    const res = await getQuoteDetail({\n      quote_id: quoteId.value,\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || ''\n    });\n\n    if (res.status === 'ok') {\n      quote.value = res.data;\n    } else {\n      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n    }\n  } catch (error) {\n    console.error('加载摘录详情失败:', error);\n    uni.showToast({ title: '加载失败，请重试', icon: 'none' });\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 加载评论列表\nconst loadComments = async () => {\n  try {\n    commentsLoading.value = true;\n    \n    const res = await getQuoteComments({\n      quote_id: quoteId.value,\n      page: 1,\n      page_size: 50,\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || ''\n    });\n\n    if (res.status === 'ok') {\n      comments.value = res.data.list || [];\n    } else if (res.status === 'empty') {\n      comments.value = [];\n    } else {\n      uni.showToast({ title: res.msg || '加载评论失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('加载评论失败:', error);\n    uni.showToast({ title: '加载评论失败', icon: 'none' });\n  } finally {\n    commentsLoading.value = false;\n  }\n};\n\n// 点赞摘录\nconst handleLike = async () => {\n  if (!requireLogin('', '请先登录后再点赞')) {\n    return;\n  }\n\n  if (!quote.value) return;\n\n  try {\n    const res = await likeQuote({\n      id: quote.value.id,\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token\n    });\n\n    if (res.status === 'ok') {\n      quote.value.is_liked = !quote.value.is_liked;\n      quote.value.like_count = quote.value.is_liked ? \n        (quote.value.like_count || 0) + 1 : \n        (quote.value.like_count || 1) - 1;\n      \n      uni.showToast({\n        title: quote.value.is_liked ? '点赞成功' : '取消点赞',\n        icon: 'success'\n      });\n    } else {\n      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('点赞失败:', error);\n    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n  }\n};\n\n// 收藏摘录\nconst handleFavorite = async () => {\n  if (!requireLogin('', '请先登录后再收藏')) {\n    return;\n  }\n\n  if (!quote.value) return;\n\n  try {\n    const res = await favoriteQuote({\n      id: quote.value.id,\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token\n    });\n\n    if (res.status === 'ok') {\n      quote.value.is_favorited = !quote.value.is_favorited;\n      quote.value.favorite_count = quote.value.is_favorited ? \n        (quote.value.favorite_count || 0) + 1 : \n        (quote.value.favorite_count || 1) - 1;\n      \n      uni.showToast({\n        title: quote.value.is_favorited ? '收藏成功' : '取消收藏',\n        icon: 'success'\n      });\n    } else {\n      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('收藏失败:', error);\n    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n  }\n};\n\n// 发布评论\nconst submitComment = async () => {\n  if (!requireLogin('', '请先登录后再评论')) {\n    return;\n  }\n\n  if (!commentText.value.trim()) {\n    uni.showToast({ title: '请输入评论内容', icon: 'none' });\n    return;\n  }\n\n  try {\n    const res = await postQuoteComment({\n      quote_id: quoteId.value,\n      content: commentText.value.trim(),\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token\n    });\n\n    if (res.status === 'ok') {\n      commentText.value = '';\n      uni.showToast({ title: '评论成功', icon: 'success' });\n      // 重新加载评论列表\n      loadComments();\n    } else {\n      uni.showToast({ title: res.msg || '评论失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('评论失败:', error);\n    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n  }\n};\n\n// 切换评论区显示\nconst toggleComments = () => {\n  showComments.value = !showComments.value;\n  if (showComments.value && comments.value.length === 0) {\n    loadComments();\n  }\n};\n\n// 页面加载\nonLoad((options) => {\n  quoteId.value = parseInt(options.id);\n  if (!quoteId.value) {\n    uni.showToast({ title: '摘录ID无效', icon: 'none' });\n    uni.navigateBack();\n    return;\n  }\n\n  loadQuoteDetail();\n  \n  // 如果URL参数指定显示评论区\n  if (options.showComments === 'true') {\n    showComments.value = true;\n    loadComments();\n  }\n});\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n  const date = new Date(timeStr.replace(/-/g, '/'));\n  const now = new Date();\n  const diff = now - date;\n  \n  if (diff < 60000) return '刚刚';\n  if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';\n  if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';\n  if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';\n\n  return timeStr.split(' ')[0];\n};\n\n// 关闭页面\nconst closePage = () => {\n  uni.navigateBack();\n};\n</script>\n\n<template>\n  <view class=\"quote-detail-page\">\n    <!-- 顶部导航栏 -->\n    <view class=\"quote-header\">\n      <view class=\"back-button\" @click=\"closePage\">\n        <u-icon name=\"arrow-left\" color=\"#333\" size=\"20\"></u-icon>\n      </view>\n      <text class=\"header-title\">摘录详情</text>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <u-loading-icon mode=\"circle\" size=\"30\" color=\"#6AC086\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 摘录详情 -->\n    <view v-else-if=\"quote\" class=\"quote-detail\">\n      <!-- 摘录内容 -->\n      <view class=\"quote-content\">\n        <text class=\"quote-text\">{{ quote.content }}</text>\n        <view class=\"quote-meta\">\n          <text class=\"author\">—— {{ quote.author }}</text>\n          <text class=\"source\" v-if=\"quote.source\">《{{ quote.source }}》</text>\n        </view>\n      </view>\n\n      <!-- 摘录图片 -->\n      <view v-if=\"quote.images && quote.images.length > 0\" class=\"quote-images\">\n        <image \n          v-for=\"(img, index) in quote.images\" \n          :key=\"index\"\n          :src=\"img\"\n          class=\"quote-image\"\n          mode=\"aspectFill\"\n          @click=\"previewImage(img)\"\n        />\n      </view>\n\n      <!-- 用户信息 -->\n      <view class=\"user-info\">\n        <image :src=\"quote.user?.avatar_url || '/static/default-avatar.png'\" class=\"user-avatar\" />\n        <text class=\"user-name\">{{ quote.user?.nickname || '匿名用户' }}</text>\n        <text class=\"publish-time\">{{ formatTime(quote.created_at) }}</text>\n      </view>\n\n      <!-- 操作按钮 -->\n      <view class=\"action-buttons\">\n        <view class=\"action-btn\" @click=\"handleLike\">\n          <u-icon \n            :name=\"quote.is_liked ? 'heart-fill' : 'heart'\" \n            :color=\"quote.is_liked ? '#ff4757' : '#999'\"\n            size=\"20\"\n          />\n          <text class=\"action-text\">{{ quote.like_count || 0 }}</text>\n        </view>\n        \n        <view class=\"action-btn\" @click=\"handleFavorite\">\n          <u-icon \n            :name=\"quote.is_favorited ? 'star-fill' : 'star'\" \n            :color=\"quote.is_favorited ? '#ffa502' : '#999'\"\n            size=\"20\"\n          />\n          <text class=\"action-text\">{{ quote.favorite_count || 0 }}</text>\n        </view>\n        \n        <view class=\"action-btn\" @click=\"toggleComments\">\n          <u-icon name=\"chat\" color=\"#999\" size=\"20\" />\n          <text class=\"action-text\">{{ comments.length }}</text>\n        </view>\n      </view>\n\n      <!-- 评论区 -->\n      <view v-if=\"showComments\" class=\"comments-section\">\n        <view class=\"comments-header\">\n          <text class=\"comments-title\">评论 ({{ comments.length }})</text>\n        </view>\n\n        <!-- 评论列表 -->\n        <scroll-view\n          class=\"comments-list\"\n          scroll-y\n        >\n          <view v-if=\"comments.length === 0 && !commentsLoading\" class=\"empty-comments\">\n            <text>暂无评论，快来发表第一条评论吧</text>\n          </view>\n\n          <view v-for=\"(comment, index) in comments\" :key=\"comment.id\" class=\"comment-item\">\n            <image class=\"comment-avatar\" :src=\"comment.user?.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\n            <view class=\"comment-content\">\n              <view class=\"comment-header\">\n                <text class=\"comment-nickname\">{{ comment.user?.nickname || '用户' }}</text>\n                <text class=\"comment-time\">{{ formatTime(comment.created_at) }}</text>\n              </view>\n              <view class=\"comment-text\">\n                <text>{{ comment.content }}</text>\n              </view>\n            </view>\n          </view>\n\n          <view v-if=\"commentsLoading\" class=\"loading-more\">\n            <u-loading-icon mode=\"circle\" size=\"24\" color=\"#999\"></u-loading-icon>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n\n    <!-- 评论输入框 -->\n    <view v-if=\"showComments\" class=\"comment-input-area\">\n      <view class=\"input-wrapper\">\n        <input\n          class=\"comment-input\"\n          v-model=\"commentText\"\n          placeholder=\"写下你的评论...\"\n          confirm-type=\"send\"\n          @confirm=\"submitComment\"\n        />\n        <button class=\"send-btn\" @click=\"submitComment\">发送</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style scoped>\n.quote-detail-page {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-top: calc(88rpx + var(--status-bar-height) + 20rpx); /* 为导航栏留出空间 */\n  padding-left: 20rpx;\n  padding-right: 20rpx;\n  padding-bottom: 20rpx;\n}\n\n/* 导航栏样式 */\n.quote-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 30rpx;\n  padding-top: calc(var(--status-bar-height) + 20rpx);\n  padding-bottom: 20rpx;\n  background-color: #fff;\n  border-bottom: 1px solid #f0f0f0;\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(88rpx + var(--status-bar-height));\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 1000;\n}\n\n.back-button {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.header-title {\n  position: absolute;\n  left: 50%;\n  transform: translateX(-50%);\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 60vh;\n  gap: 20rpx;\n}\n\n.loading-text {\n  color: #999;\n  font-size: 28rpx;\n}\n\n.quote-detail {\n  background: white;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 20rpx;\n}\n\n.quote-content {\n  margin-bottom: 30rpx;\n}\n\n.quote-text {\n  font-size: 32rpx;\n  line-height: 1.6;\n  color: #333;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.quote-meta {\n  text-align: right;\n}\n\n.author {\n  font-size: 28rpx;\n  color: #666;\n  font-style: italic;\n}\n\n.source {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 10rpx;\n}\n\n.quote-images {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10rpx;\n  margin-bottom: 30rpx;\n}\n\n.quote-image {\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: 10rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx solid #eee;\n}\n\n.user-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n}\n\n.user-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.publish-time {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: auto;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 40rpx;\n  margin-bottom: 30rpx;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 10rpx;\n  padding: 10rpx 20rpx;\n  border-radius: 20rpx;\n  background: #f5f5f5;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.comments-section {\n  padding: 30rpx;\n  background-color: #fff;\n}\n\n.comments-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n  padding-bottom: 15rpx;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.comments-title {\n  font-weight: 600;\n  font-size: 32rpx;\n  color: #333;\n}\n\n.comments-list {\n  max-height: 600rpx;\n  overflow-y: auto;\n}\n\n.empty-comments {\n  padding: 60rpx 0;\n  text-align: center;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.comment-item {\n  display: flex;\n  padding: 24rpx 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.comment-avatar {\n  width: 70rpx;\n  height: 70rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n  border: 1px solid #f0f0f0;\n}\n\n.comment-content {\n  flex: 1;\n}\n\n.comment-header {\n  display: flex;\n  align-items: center;\n}\n\n.comment-nickname {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-right: 12rpx;\n}\n\n.comment-time {\n  font-size: 22rpx;\n  color: #999;\n  flex: 1;\n}\n\n.comment-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.6;\n  margin-bottom: 10rpx;\n  word-break: break-all;\n}\n\n.loading-more {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20rpx;\n}\n\n.comment-input-area {\n  padding: 20rpx 30rpx;\n  background-color: #fff;\n  border-top: 1px solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.input-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.comment-input {\n  flex: 1;\n  height: 80rpx;\n  background-color: #f5f5f5;\n  border-radius: 40rpx;\n  padding: 0 30rpx;\n  font-size: 28rpx;\n}\n\n.send-btn {\n  margin-left: 20rpx;\n  background-color: #576b95; /* 微信蓝色 */\n  color: #fff;\n  border: none;\n  border-radius: 40rpx;\n  padding: 0 30rpx;\n  height: 80rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/quote/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["quote", "ref", "loading", "showComments", "comments", "commentsLoading", "commentText", "quoteId", "loadComments", "async", "value", "res", "getQuoteComments", "quote_id", "page", "page_size", "uid", "store", "$state", "userInfo", "token", "status", "data", "list", "showToast", "title", "msg", "icon", "error", "console", "uni", "index", "handleLike", "requireLogin", "likeQuote", "id", "is_liked", "like_count", "handleFavorite", "favoriteQuote", "is_favorited", "favorite_count", "submitComment", "trim", "postQuoteComment", "content", "toggleComments", "length", "common_vendor", "onLoad", "options", "parseInt", "navigateBack", "getQuoteDetail", "setTimeout", "formatTime", "timeStr", "date", "Date", "replace", "diff", "Math", "floor", "split", "closePage", "wx", "createPage", "MiniProgramPage"], "mappings": "qsBASM,MAAAA,EAAQC,EAAAA,IAAI,MACZC,EAAUD,EAAAA,KAAI,GACdE,EAAeF,EAAAA,KAAI,GACnBG,EAAWH,EAAAA,IAAI,IACfI,EAAkBJ,EAAAA,KAAI,GACtBK,EAAcL,EAAAA,IAAI,IAClBM,EAAUN,EAAAA,IAAI,GA8BdO,EAAeC,kBACf,IACFJ,EAAgBK,OAAQ,EAElB,MAAAC,QAAYC,mBAAiB,CACjCC,SAAUN,EAAQG,MAClBI,KAAM,EACNC,UAAW,GACXC,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,KAGxB,OAAfT,EAAIU,OACNjB,EAASM,MAAQC,EAAIW,KAAKC,MAAQ,GACV,UAAfZ,EAAIU,OACbjB,EAASM,MAAQ,WAEbc,UAAU,CAAEC,MAAOd,EAAIe,KAAO,SAAUC,KAAM,QAErD,OAAQC,GACCC,QAAAD,MAAM,UAAWA,GACzBE,EAAGC,MAACP,UAAU,CAAEC,MAAO,SAAUE,KAAM,QAC3C,CAAY,QACRtB,EAAgBK,OAAQ,CAC1B,GAIIsB,EAAavB,UACjB,GAAKwB,EAAYA,aAAC,GAAI,aAIjBjC,EAAMU,MAEP,IACI,MAAAC,QAAYuB,YAAU,CAC1BC,GAAInC,EAAMU,MAAMyB,GAChBnB,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,QAGd,OAAfT,EAAIU,QACNrB,EAAMU,MAAM0B,UAAYpC,EAAMU,MAAM0B,SACpCpC,EAAMU,MAAM2B,WAAarC,EAAMU,MAAM0B,UAClCpC,EAAMU,MAAM2B,YAAc,GAAK,GAC/BrC,EAAMU,MAAM2B,YAAc,GAAK,EAElCP,EAAAA,MAAIN,UAAU,CACZC,MAAOzB,EAAMU,MAAM0B,SAAW,OAAS,OACvCT,KAAM,qBAGJH,UAAU,CAAEC,MAAOd,EAAIe,KAAO,OAAQC,KAAM,QAEnD,OAAQC,GACCC,QAAAD,MAAM,QAASA,GACvBE,EAAGC,MAACP,UAAU,CAAEC,MAAO,aAAcE,KAAM,QAC7C,GAIIW,EAAiB7B,UACrB,GAAKwB,EAAYA,aAAC,GAAI,aAIjBjC,EAAMU,MAEP,IACI,MAAAC,QAAY4B,gBAAc,CAC9BJ,GAAInC,EAAMU,MAAMyB,GAChBnB,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,QAGd,OAAfT,EAAIU,QACNrB,EAAMU,MAAM8B,cAAgBxC,EAAMU,MAAM8B,aACxCxC,EAAMU,MAAM+B,eAAiBzC,EAAMU,MAAM8B,cACtCxC,EAAMU,MAAM+B,gBAAkB,GAAK,GACnCzC,EAAMU,MAAM+B,gBAAkB,GAAK,EAEtCX,EAAAA,MAAIN,UAAU,CACZC,MAAOzB,EAAMU,MAAM8B,aAAe,OAAS,OAC3Cb,KAAM,qBAGJH,UAAU,CAAEC,MAAOd,EAAIe,KAAO,OAAQC,KAAM,QAEnD,OAAQC,GACCC,QAAAD,MAAM,QAASA,GACvBE,EAAGC,MAACP,UAAU,CAAEC,MAAO,aAAcE,KAAM,QAC7C,GAIIe,EAAgBjC,UACpB,GAAKwB,EAAYA,aAAC,GAAI,YAItB,GAAK3B,EAAYI,MAAMiC,OAKnB,IACI,MAAAhC,QAAYiC,mBAAiB,CACjC/B,SAAUN,EAAQG,MAClBmC,QAASvC,EAAYI,MAAMiC,OAC3B3B,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,QAGd,OAAfT,EAAIU,QACNf,EAAYI,MAAQ,GACpBoB,EAAGC,MAACP,UAAU,CAAEC,MAAO,OAAQE,KAAM,yBAIjCH,UAAU,CAAEC,MAAOd,EAAIe,KAAO,OAAQC,KAAM,QAEnD,OAAQC,GACCC,QAAAD,MAAM,QAASA,GACvBE,EAAGC,MAACP,UAAU,CAAEC,MAAO,aAAcE,KAAM,QAC7C,MAvBEG,EAAGC,MAACP,UAAU,CAAEC,MAAO,UAAWE,KAAM,QAuB1C,EAIImB,EAAiB,KACR3C,EAAAO,OAASP,EAAaO,MAC/BP,EAAaO,OAAmC,IAA1BN,EAASM,MAAMqC,WAEzC,EAIIC,EAAAC,QAAEC,IAEF,GADI3C,EAAAG,MAAQyC,SAASD,EAAQf,KAC5B5B,EAAQG,MAGX,OAFAoB,EAAGC,MAACP,UAAU,CAAEC,MAAO,SAAUE,KAAM,cACvCG,EAAGC,MAACqB,eAxKgB3C,mBAClB,IACFP,EAAQQ,OAAQ,EAEV,MAAAC,QAAY0C,iBAAe,CAC/BxC,SAAUN,EAAQG,MAClBM,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,KAGxB,OAAfT,EAAIU,OACNrB,EAAMU,MAAQC,EAAIW,cAEdE,UAAU,CAAEC,MAAOd,EAAIe,KAAO,OAAQC,KAAM,SAChD2B,YAAW,KACTxB,EAAGC,MAACqB,cAAY,GACf,MAEN,OAAQxB,GACCC,QAAAD,MAAM,YAAaA,GAC3BE,EAAGC,MAACP,UAAU,CAAEC,MAAO,WAAYE,KAAM,QAC7C,CAAY,QACRzB,EAAQQ,OAAQ,CAClB,MAwJ6B,SAAzBwC,EAAQ/C,eACVA,EAAaO,OAAQ,MAEvB,IAII,MAAA6C,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GACrB,MAAMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAM,MAEtCC,EADM,IAAIF,KACGD,EAEnB,OAAIG,EAAO,IAAc,KACrBA,EAAO,KAAgBC,KAAKC,MAAMF,EAAO,KAAS,MAClDA,EAAO,MAAiBC,KAAKC,MAAMF,EAAO,MAAW,MACrDA,EAAO,OAAmBC,KAAKC,MAAMF,EAAO,OAAY,KAErDJ,EAAQO,MAAM,KAAK,EAAC,EAIvBC,EAAY,KAChBlC,EAAGC,MAACqB,cAAY,4/CCvNlBa,GAAGC,WAAWC"}