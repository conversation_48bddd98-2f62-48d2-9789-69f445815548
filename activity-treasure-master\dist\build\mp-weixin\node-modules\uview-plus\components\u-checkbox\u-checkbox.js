"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-checkbox",mixins:[e.mpMixin,e.mixin,e.props$24],data:()=>({isChecked:!1,parentData:{iconSize:12,labelDisabled:null,disabled:null,shape:"square",activeColor:null,inactiveColor:null,size:18,modelValue:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}),computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize(){return e.addUnit(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor(){const e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.isChecked?this.elInactiveColor:"transparent":this.isChecked?e:"transparent"},iconClasses(){let e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),this.elDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.isChecked&&this.elDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e},iconWrapStyle(){const t={};return t.backgroundColor=this.isChecked&&!this.elDisabled?this.elActiveColor:"#ffffff",t.borderColor=this.isChecked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,t.width=e.addUnit(this.elSize),t.height=e.addUnit(this.elSize),this.usedAlone||"right"===this.parentData.iconPlacement&&(t.marginRight=0),t},checkboxStyle(){const t={};return this.usedAlone||(this.parentData.borderBottom&&this.parentData.placement,this.parentData.borderBottom&&"column"===this.parentData.placement&&(t.paddingBottom="8px")),e.deepMerge(t,e.addStyle(this.customStyle))}},mounted(){this.init()},emits:["change","update:checked"],methods:{init(){if(this.usedAlone)this.checked&&(this.isChecked=!0);else{this.updateParentData(),this.parent;const t=this.parentData.modelValue;this.checked?this.isChecked=!0:!this.usedAlone&&e.test.array(t)&&(this.isChecked=t.some((e=>e===this.name)))}},updateParentData(){this.getParentData("u-checkbox-group")},wrapperClickHandler(e){(this.usedAlone||"right"===this.parentData.iconPlacement)&&this.iconClickHandler(e)},iconClickHandler(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},labelClickHandler(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.$emit("change",this.isChecked),this.usedAlone&&this.$emit("update:checked",this.isChecked),this.$nextTick((()=>{e.formValidate(this,"change")}))},setRadioCheckedStatus(){this.isChecked=!this.isChecked,this.emitEvent(),this.usedAlone||"function"==typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}},watch:{checked(e,t){e!==this.isChecked&&(this.isChecked=e)}}};if(!Array){e.resolveComponent("u-icon")()}Math;const i=e._export_sfc(t,[["render",function(t,i,a,l,s,n){return{a:e.p({name:"checkbox-mark",size:n.elIconSize,color:n.elIconColor}),b:e.o(((...e)=>n.iconClickHandler&&n.iconClickHandler(...e))),c:e.n(n.iconClasses),d:e.s(n.iconWrapStyle),e:e.t(t.label),f:n.elDisabled?n.elInactiveColor:n.elLabelColor,g:n.elLabelSize,h:n.elLabelSize,i:e.r("label",{label:t.label,elDisabled:n.elDisabled}),j:e.o(((...e)=>n.labelClickHandler&&n.labelClickHandler(...e))),k:e.s(n.checkboxStyle),l:e.o(((...e)=>n.wrapperClickHandler&&n.wrapperClickHandler(...e))),m:e.n(`u-checkbox-label--${s.parentData.iconPlacement}`),n:e.n(s.parentData.borderBottom&&"column"===s.parentData.placement&&"u-border-bottom")}}],["__scopeId","data-v-5cc4f73b"]]);wx.createComponent(i);
//# sourceMappingURL=u-checkbox.js.map
