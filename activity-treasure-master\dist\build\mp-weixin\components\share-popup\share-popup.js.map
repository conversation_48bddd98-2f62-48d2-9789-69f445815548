{"version": 3, "file": "share-popup.js", "sources": ["../../../../../src/components/share-popup/share-popup.vue", "../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvc2hhcmUtcG9wdXAvc2hhcmUtcG9wdXAudnVl"], "sourcesContent": ["<script setup>\nimport { ref, computed, defineProps, defineEmits, watch, nextTick } from 'vue';\nimport { saveImageToAlbum } from '@/utils/painterShare';\nimport { showShareMenu } from '@/utils/uniShareConfig';\nimport { createCardShareConfig, createFeedShareConfig, createDefaultShareConfig } from '@/utils/painterConfig';\n\nconst props = defineProps({\n  show: {\n    type: Boolean,\n    default: false\n  },\n  title: {\n    type: String,\n    default: '分享'\n  },\n  shareData: {\n    type: Object,\n    default: () => ({})\n  },\n  showMemberInvite: {\n    type: Boolean,\n    default: false\n  }\n});\n\nconst emit = defineEmits(['close', 'select', 'share-success', 'share-error']);\n\n// Painter 相关状态\nconst isGeneratingImage = ref(false);\nconst generatedImagePath = ref('');\nconst painterConfig = ref(null);\nconst generationTimer = ref(null);\nconst GENERATION_TIMEOUT = 30000; // 30秒超时\n\n// 配置保护机制 - 防止Vue响应式系统意外清理\nconst configLocked = ref(false);\nconst isRenderingInProgress = ref(false);\nconst configProtectionTimer = ref(null);\nconst lastValidConfig = ref(null);\n\n// 基础分享选项\nconst baseShareOptions = [\n  { id: 'wechat', name: '微信', icon: '/static/weixin.svg' },\n  { id: 'moments', name: '朋友圈', icon: '/static/pengyouquan.svg' },\n  { id: 'image', name: '分享图片', icon: '/static/kapian.svg' },\n  { id: 'save', name: '保存图片', icon: '/static/baocun.svg' }\n];\n\n// 动态分享选项（包含体验会员邀请）\nconst shareOptions = computed(() => {\n  const options = [...baseShareOptions];\n\n  // 如果显示体验会员邀请\n  if (props.showMemberInvite) {\n    options.push({\n      id: 'member-invite',\n      name: '邀请体验',\n      icon: '/static/gift.svg'\n    });\n  }\n\n  return options;\n});\n\n// 处理选项点击\nconst handleSelect = async (option) => {\n  try {\n    switch (option.id) {\n      case 'wechat':\n        await shareToWeChat();\n        break;\n      case 'moments':\n        await shareToMoments();\n        break;\n      case 'image':\n        await generateAndShowShareImage();\n        break;\n      case 'save':\n        await generateAndSaveImage();\n        break;\n      case 'member-invite':\n        await shareMemberInvite();\n        break;\n      case 'uni-share':\n        await shareWithUniShare();\n        break;\n      default:\n        emit('select', option);\n    }\n  } catch (error) {\n    console.error('分享操作失败:', error);\n    emit('share-error', error);\n\n    // 提供更友好的错误提示\n    const errorMessage = error.message || '分享失败';\n    uni.showModal({\n      title: '分享失败',\n      content: errorMessage,\n      showCancel: true,\n      cancelText: '取消',\n      confirmText: '重试',\n      success: (res) => {\n        if (res.confirm) {\n          // 用户选择重试，重新调用相同的操作\n          handleSelect(option);\n        }\n      }\n    });\n  }\n};\n\n// 微信好友分享\nconst shareToWeChat = async () => {\n  // #ifdef MP-WEIXIN\n  try {\n    // 根据官方文档，直接提示用户点击右上角分享\n    uni.showToast({\n      title: '请点击右上角分享给好友',\n      icon: 'none',\n      duration: 2000\n    });\n\n    emit('share-success', { type: 'wechat' });\n    handleClose();\n  } catch (error) {\n    console.error('微信分享失败:', error);\n    throw error;\n  }\n  // #endif\n\n  // #ifndef MP-WEIXIN\n  uni.showToast({\n    title: '仅支持微信小程序',\n    icon: 'none'\n  });\n  // #endif\n};\n\n// 朋友圈分享\nconst shareToMoments = async () => {\n  // #ifdef MP-WEIXIN\n  try {\n    // 朋友圈分享需要跳转到详情页，因为需要onShareTimeline配置\n    uni.showToast({\n      title: '请点击右上角分享到朋友圈',\n      icon: 'none',\n      duration: 2000\n    });\n\n    emit('share-success', { type: 'moments' });\n    handleClose();\n  } catch (error) {\n    console.error('朋友圈分享失败:', error);\n    throw error;\n  }\n  // #endif\n\n  // #ifndef MP-WEIXIN\n  uni.showToast({\n    title: '仅支持微信小程序',\n    icon: 'none'\n  });\n  // #endif\n};\n\n// 生成并显示分享图片\nconst generateAndShowShareImage = async () => {\n  try {\n    if (isGeneratingImage.value) {\n      console.warn('图片正在生成中，请勿重复操作');\n      return;\n    }\n\n    console.log('=== 开始分享图片生成流程 ===');\n    isGeneratingImage.value = true;\n    generatedImagePath.value = ''; // 清空保存模式标记\n    uni.showLoading({ title: '生成图片中...' });\n\n    const config = painterData.value;\n    if (!config) {\n      throw new Error('无法生成分享配置');\n    }\n\n    console.log('✅ Painter配置生成成功:', JSON.stringify(config, null, 2));\n\n    // 验证配置的关键字段\n    if (!config.width || !config.height) {\n      throw new Error(`Painter配置缺少尺寸信息: width=${config.width}, height=${config.height}`);\n    }\n\n    if (!config.views || !Array.isArray(config.views)) {\n      throw new Error('Painter配置缺少views数组');\n    }\n\n    console.log('✅ Painter配置验证通过，views数量:', config.views.length);\n\n    // 启动超时计时\n    startGenerationTimer();\n    console.log('✅ 超时计时器已启动，超时时间:', GENERATION_TIMEOUT, 'ms');\n\n    // 启用配置保护机制\n    console.log('🛡️ 启用配置保护机制...');\n    configLocked.value = true;\n    isRenderingInProgress.value = true;\n\n    // 设置保护超时 - 5秒后自动解除保护\n    configProtectionTimer.value = setTimeout(() => {\n      console.log('🛡️ 配置保护超时，自动解除保护');\n      configLocked.value = false;\n      isRenderingInProgress.value = false;\n    }, 5000);\n\n    // 暂时注释内存清理，避免可能的冲突\n    // performMemoryCleanup();\n\n    // 设置Painter配置，触发图片生成\n    console.log('🎨 设置Painter配置，触发图片生成...');\n    console.log('🎨 配置中的背景图片URL:', config.views?.[0]?.url);\n\n    // 确保不会被意外清理\n    const configToSet = { ...config };\n    lastValidConfig.value = configToSet; // 保存最后有效配置\n    painterConfig.value = configToSet;\n\n    console.log('🎨 Painter配置已设置，等待组件渲染...');\n    console.log('🛡️ 配置保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);\n\n    // 添加延迟确保组件正确渲染\n    setTimeout(() => {\n      console.log('🎨 Painter组件渲染检查...');\n      console.log('🎨 当前painterConfig状态:', !!painterConfig.value);\n      console.log('🎨 当前isGeneratingImage状态:', isGeneratingImage.value);\n      console.log('🛡️ 当前保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);\n\n      if (!painterConfig.value) {\n        console.error('❌ Painter配置被意外清理！');\n        console.error('❌ 尝试恢复配置...');\n\n        // 重新启用保护机制\n        configLocked.value = true;\n        isRenderingInProgress.value = true;\n\n        // 恢复配置\n        painterConfig.value = configToSet;\n\n        console.log('✅ 配置已恢复，重新启动保护机制');\n        console.log('🛡️ 恢复后保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);\n\n        // 重新设置保护超时\n        if (configProtectionTimer.value) {\n          clearTimeout(configProtectionTimer.value);\n        }\n        configProtectionTimer.value = setTimeout(() => {\n          console.log('🛡️ 恢复后配置保护超时，自动解除保护');\n          configLocked.value = false;\n          isRenderingInProgress.value = false;\n        }, 5000);\n      }\n    }, 100);\n\n    // 添加 Painter 组件状态检查（小程序兼容版本）\n    setTimeout(() => {\n      console.log('🔍 深度检查 Painter 组件状态...');\n\n      // 小程序环境检查 - 简化版本，避免DOM API调用\n      console.log('🔍 小程序环境，跳过DOM检查');\n\n      // 直接使用 uni-app API 检查组件\n      try {\n        const query = uni.createSelectorQuery();\n        query.selectAll('painter').boundingClientRect((rects) => {\n          console.log('🔍 小程序中找到的 Painter 组件数量:', rects ? rects.length : 0);\n\n          if (!rects || rects.length === 0) {\n            console.warn('⚠️ 小程序中未找到 Painter 组件，可能渲染失败');\n            triggerPainterRerender();\n          } else {\n            console.log('✅ Painter 组件在小程序中存在');\n            rects.forEach((rect, index) => {\n              console.log(`🔍 Painter 组件 ${index + 1} 位置:`, rect);\n            });\n          }\n        }).exec();\n      } catch (error) {\n        console.error('🔍 组件检查失败:', error);\n        // 如果检查失败，直接尝试重新渲染\n        triggerPainterRerender();\n      }\n    }, 500);\n\n// 触发 Painter 重新渲染的函数\nconst triggerPainterRerender = () => {\n  console.log('🔄 尝试强制重新渲染 Painter 组件...');\n  const tempConfig = painterConfig.value;\n  painterConfig.value = null;\n\n  nextTick(() => {\n    painterConfig.value = tempConfig;\n    console.log('🔄 Painter 组件已重新渲染');\n  });\n};\n\n  } catch (error) {\n    console.error('❌ 生成分享图片失败:', error);\n    cleanupResources(true, 'generate-error');\n\n    const errorMessage = error.message || '生成分享图片失败';\n    uni.showToast({\n      title: errorMessage.length > 20 ? '生成图片失败' : errorMessage,\n      icon: 'none',\n      duration: 3000\n    });\n    throw error;\n  }\n};\n\n// 保存生成的图片到相册\nconst saveGeneratedImageToAlbum = async (imagePath) => {\n  try {\n    await saveImageToAlbum(imagePath);\n\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n\n    emit('share-success', { type: 'save', path: imagePath });\n    handleClose();\n  } catch (error) {\n    console.error('保存图片失败:', error);\n\n    const errorMessage = error.message || '保存图片失败';\n    uni.showToast({\n      title: errorMessage.length > 20 ? '保存失败' : errorMessage,\n      icon: 'none',\n      duration: 3000\n    });\n    throw error;\n  }\n};\n\n// 生成并保存图片\nconst generateAndSaveImage = async () => {\n  try {\n    if (isGeneratingImage.value) {\n      console.warn('图片正在生成中，请勿重复操作');\n      return;\n    }\n\n    // 设置保存模式标志\n    isGeneratingImage.value = true;\n    generatedImagePath.value = 'save-mode'; // 标记为保存模式\n    uni.showLoading({ title: '生成图片中...' });\n\n    const config = painterData.value;\n    if (!config) {\n      throw new Error('无法生成分享配置');\n    }\n\n    console.log('开始生成保存图片:', config);\n\n    // 启动超时计时\n    startGenerationTimer();\n\n    // 暂时注释内存清理，避免可能的冲突\n    // performMemoryCleanup();\n\n    // 设置Painter配置，触发图片生成\n    console.log('🎨 保存模式 - 配置中的背景图片URL:', config.views?.[0]?.url);\n    painterConfig.value = { ...config };\n\n  } catch (error) {\n    console.error('生成保存图片失败:', error);\n    cleanupResources(true, 'save-error');\n\n    const errorMessage = error.message || '生成保存图片失败';\n    uni.showToast({\n      title: errorMessage.length > 20 ? '生成图片失败' : errorMessage,\n      icon: 'none',\n      duration: 3000\n    });\n    throw error;\n  }\n};\n\n// 体验会员分享邀请\nconst shareMemberInvite = async () => {\n  try {\n    // #ifdef MP-WEIXIN\n    // 根据官方文档，直接提示用户点击右上角分享\n    uni.showToast({\n      title: '请点击右上角分享体验券',\n      icon: 'none',\n      duration: 2000\n    });\n    // #endif\n\n    emit('share-success', { type: 'member-invite' });\n    handleClose();\n  } catch (error) {\n    console.error('体验会员分享失败:', error);\n    throw error;\n  }\n};\n\n// 使用uni-share进行分享\nconst shareWithUniShare = async () => {\n  try {\n    console.log('开始uni-share分享，数据:', props.shareData);\n\n    // 根据模板类型创建分享配置\n    let shareConfig;\n    if (props.shareData.template === 'card') {\n      // 使用 uniShareConfig 中的函数创建 uni-share 配置\n      const { createCardShareConfig: createUniCardConfig } = await import('@/utils/uniShareConfig');\n      shareConfig = createUniCardConfig(props.shareData);\n    } else if (props.shareData.template === 'feed' || props.shareData.template === 'dynamic') {\n      // 使用 uniShareConfig 中的函数创建 uni-share 配置\n      const { createFeedShareConfig: createUniFeedConfig } = await import('@/utils/uniShareConfig');\n      shareConfig = createUniFeedConfig(props.shareData);\n    } else {\n      // 自定义分享配置\n      shareConfig = {\n        content: {\n          type: 0,\n          href: props.shareData.href || '',\n          title: props.shareData.content || '分享内容',\n          summary: `${props.shareData.author || '匿名用户'}分享了内容`,\n          imageUrl: props.shareData.image || ''\n        },\n        menus: [\n          {\n            \"img\": \"/static/app-plus/sharemenu/wechatfriend.png\",\n            \"text\": \"微信好友\",\n            \"share\": {\n              \"provider\": \"weixin\",\n              \"scene\": \"WXSceneSession\"\n            }\n          },\n          {\n            \"img\": \"/static/app-plus/sharemenu/wechatmoments.png\",\n            \"text\": \"微信朋友圈\",\n            \"share\": {\n              \"provider\": \"weixin\",\n              \"scene\": \"WXSceneTimeline\"\n            }\n          },\n          {\n            \"img\": \"/static/app-plus/sharemenu/copyurl.png\",\n            \"text\": \"复制链接\",\n            \"share\": \"copyurl\"\n          }\n        ],\n        cancelText: \"取消分享\"\n      };\n    }\n\n    // 显示uni-share分享菜单\n    const result = await showShareMenu(shareConfig);\n\n    if (result.success) {\n      console.log('uni-share分享成功:', result);\n      uni.showToast({ title: '分享成功', icon: 'success' });\n      emit('share-success', { type: 'uni-share', result });\n      handleClose();\n    } else {\n      console.log('用户取消分享');\n    }\n  } catch (error) {\n    console.error('uni-share分享失败:', error);\n    uni.showToast({ title: '分享失败', icon: 'none' });\n    emit('share-error', error);\n  }\n};\n\n// 内存清理函数\nconst performMemoryCleanup = () => {\n  console.log('🧹 开始内存清理...');\n\n  try {\n    // 强制垃圾回收（如果支持）\n    if (typeof wx !== 'undefined' && wx.triggerGC) {\n      console.log('🧹 触发微信小程序垃圾回收');\n      wx.triggerGC();\n    }\n\n    // 清理可能的内存泄漏\n    if (typeof global !== 'undefined' && global.gc) {\n      console.log('🧹 触发全局垃圾回收');\n      global.gc();\n    }\n\n    console.log('🧹 内存清理完成');\n  } catch (error) {\n    console.error('🧹 内存清理失败:', error);\n  }\n};\n\n// 清理资源函数 - 添加保护机制\nconst cleanupResources = (forceCleanup = false, reason = 'unknown') => {\n  console.log('🧹 清理资源请求 - 原因:', reason, '强制清理:', forceCleanup);\n  console.log('🧹 当前状态 - configLocked:', configLocked.value, 'isRenderingInProgress:', isRenderingInProgress.value);\n  console.log('🧹 调用栈:', new Error().stack);\n\n  // 保护机制：如果配置被锁定且不是强制清理，则拒绝清理\n  if (configLocked.value && !forceCleanup) {\n    console.log('🛡️ 配置被保护，拒绝清理 - 原因:', reason);\n    return;\n  }\n\n  // 保护机制：如果正在渲染且不是强制清理，则延迟清理\n  if (isRenderingInProgress.value && !forceCleanup) {\n    console.log('🛡️ 正在渲染中，延迟清理 - 原因:', reason);\n    setTimeout(() => {\n      cleanupResources(false, `delayed-${reason}`);\n    }, 1000);\n    return;\n  }\n\n  console.log('🧹 开始执行资源清理...');\n\n  if (generationTimer.value) {\n    clearTimeout(generationTimer.value);\n    generationTimer.value = null;\n  }\n\n  if (configProtectionTimer.value) {\n    clearTimeout(configProtectionTimer.value);\n    configProtectionTimer.value = null;\n  }\n\n  isGeneratingImage.value = false;\n  configLocked.value = false;\n  isRenderingInProgress.value = false;\n\n  // 清理 Painter 配置，触发组件销毁\n  if (painterConfig.value) {\n    console.log('🧹 清理 Painter 配置');\n    painterConfig.value = null;\n  }\n\n  // 清理生成的图片路径\n  if (generatedImagePath.value && generatedImagePath.value !== 'save-mode') {\n    generatedImagePath.value = '';\n  }\n\n  uni.hideLoading();\n\n  // 执行内存清理\n  performMemoryCleanup();\n\n  console.log('🧹 资源清理完成');\n};\n\n// 超时处理函数\nconst handleGenerationTimeout = () => {\n  console.error('⏰ 图片生成超时，超时时间:', GENERATION_TIMEOUT, 'ms');\n  console.error('⏰ 当前状态 - isGeneratingImage:', isGeneratingImage.value);\n  console.error('⏰ 当前状态 - painterConfig:', !!painterConfig.value);\n  console.error('⏰ 当前状态 - generatedImagePath:', generatedImagePath.value);\n  console.error('⏰ 当前保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);\n\n  // 超时时强制清理\n  cleanupResources(true, 'timeout');\n\n  uni.showToast({\n    title: '图片生成超时，请重试',\n    icon: 'none',\n    duration: 3000\n  });\n};\n\n\n\n// 开始超时计时\nconst startGenerationTimer = () => {\n  if (generationTimer.value) {\n    console.log('⏰ 清理之前的超时计时器');\n    clearTimeout(generationTimer.value);\n  }\n\n  console.log('⏰ 启动新的超时计时器，超时时间:', GENERATION_TIMEOUT, 'ms');\n  generationTimer.value = setTimeout(() => {\n    handleGenerationTimeout();\n  }, GENERATION_TIMEOUT);\n};\n\n// 计算 Painter 配置\nconst painterData = computed(() => {\n  console.log('🔧 计算 Painter 配置，shareData:', props.shareData);\n\n  if (!props.shareData) {\n    console.warn('⚠️ shareData 为空，无法生成 Painter 配置');\n    return null;\n  }\n\n  try {\n    console.log('🔧 根据模板类型生成配置，template:', props.shareData.template);\n\n    let config;\n    switch (props.shareData.template) {\n      case 'card':\n        console.log('🔧 使用日卡模板生成配置');\n        config = createCardShareConfig(props.shareData);\n        break;\n      case 'feed':\n      case 'dynamic':\n        console.log('🔧 使用动态模板生成配置');\n        config = createFeedShareConfig(props.shareData);\n        break;\n      default:\n        console.log('🔧 使用默认模板生成配置');\n        config = createDefaultShareConfig(props.shareData);\n    }\n\n    console.log('✅ Painter 配置生成成功，配置预览:', {\n      width: config?.width,\n      height: config?.height,\n      viewsCount: config?.views?.length,\n      background: config?.background\n    });\n\n    return config;\n  } catch (error) {\n    console.error('❌ 生成Painter配置失败:', error);\n    console.error('❌ 错误详情:', error.stack);\n    return null;\n  }\n});\n\n// Painter 成功回调\nconst onPainterSuccess = (event) => {\n  console.log('🎉 Painter success event 触发:', event);\n  console.log('🎉 Event detail:', event.detail);\n  console.log('🎉 Event 完整结构:', JSON.stringify(event, null, 2));\n\n  // 解除配置保护\n  console.log('🛡️ 解除配置保护 - 成功回调');\n  configLocked.value = false;\n  isRenderingInProgress.value = false;\n\n  if (configProtectionTimer.value) {\n    clearTimeout(configProtectionTimer.value);\n    configProtectionTimer.value = null;\n  }\n\n  // 清理超时计时器\n  if (generationTimer.value) {\n    console.log('✅ 清理超时计时器');\n    clearTimeout(generationTimer.value);\n    generationTimer.value = null;\n  }\n\n  isGeneratingImage.value = false;\n  uni.hideLoading();\n\n  const tempFilePath = event.detail?.path || event.detail?.tempFilePath || event.path;\n  console.log('🎉 提取的图片路径:', tempFilePath);\n\n  if (tempFilePath) {\n    console.log('✅ 图片生成成功，路径:', tempFilePath);\n\n    // 验证图片文件是否有效\n    console.log('🔍 开始验证图片文件...');\n    uni.getImageInfo({\n      src: tempFilePath,\n      success: (res) => {\n        console.log('✅ 图片验证成功:', res);\n        console.log('✅ 图片尺寸:', res.width, 'x', res.height);\n\n        // 根据模式决定是分享还是保存\n        if (generatedImagePath.value === 'save-mode') {\n          console.log('💾 进入保存模式');\n          saveGeneratedImageToAlbum(tempFilePath);\n        } else {\n          console.log('📤 进入分享模式');\n          shareGeneratedImage(tempFilePath);\n        }\n\n        generatedImagePath.value = tempFilePath;\n      },\n      fail: (err) => {\n        console.error('❌ 图片验证失败:', err);\n        cleanupResources(true, 'image-validation-failed');\n        uni.showToast({\n          title: '生成的图片无效',\n          icon: 'none'\n        });\n      }\n    });\n  } else {\n    console.error('❌ 图片生成成功但未获取到路径');\n    console.error('❌ Event 详情:', event);\n    cleanupResources(true, 'no-image-path');\n    uni.showToast({\n      title: '图片生成失败',\n      icon: 'none'\n    });\n  }\n};\n\n// Painter 失败回调\nconst onPainterFail = (event) => {\n  console.error('❌ Painter组件生成失败 event 触发:', event);\n  console.error('❌ Event detail:', event.detail);\n  console.error('❌ Event 完整结构:', JSON.stringify(event, null, 2));\n\n  // 解除配置保护\n  console.log('🛡️ 解除配置保护 - 失败回调');\n  configLocked.value = false;\n  isRenderingInProgress.value = false;\n\n  if (configProtectionTimer.value) {\n    clearTimeout(configProtectionTimer.value);\n    configProtectionTimer.value = null;\n  }\n\n  // 清理超时计时器\n  if (generationTimer.value) {\n    console.log('✅ 清理超时计时器');\n    clearTimeout(generationTimer.value);\n    generationTimer.value = null;\n  }\n\n  const errorMsg = event.detail?.error?.message ||\n                   event.detail?.message ||\n                   event.detail?.errMsg ||\n                   event.errMsg ||\n                   '图片生成失败';\n\n  console.error('❌ 分享图片生成失败，错误信息:', errorMsg);\n\n  // 尝试降级到传统 Canvas API\n  if (painterConfig.value && !painterConfig.value._fallbackAttempted) {\n    console.log('🔄 尝试降级到传统 Canvas API...');\n    painterConfig.value._fallbackAttempted = true;\n\n    // 重新设置配置，触发重试\n    setTimeout(() => {\n      console.log('🔄 开始降级重试...');\n      const config = { ...painterConfig.value };\n      delete config._fallbackAttempted;\n      painterConfig.value = null;\n\n      // 使用传统 Canvas API 重试\n      setTimeout(() => {\n        console.log('🔄 设置传统 Canvas API 配置...');\n        painterConfig.value = config;\n      }, 100);\n    }, 500);\n\n    return;\n  }\n\n  console.error('❌ 所有重试方案都已失败，清理资源');\n  cleanupResources(true, 'all-retries-failed');\n\n  uni.showToast({\n    title: '图片生成失败',\n    icon: 'none'\n  });\n};\n\n// 分享生成的图片\nconst shareGeneratedImage = (imagePath) => {\n  try {\n    // #ifdef MP-WEIXIN\n    // 微信小程序使用官方分享API\n    uni.showShareImageMenu({\n      path: imagePath,\n      success: () => {\n        console.log('分享图片成功');\n        emit('share-success', { type: 'image', path: imagePath });\n        handleClose();\n      },\n      fail: (error) => {\n        console.error('分享图片失败:', error);\n        uni.showToast({\n          title: '分享失败',\n          icon: 'none'\n        });\n      }\n    });\n    // #endif\n\n    // #ifndef MP-WEIXIN\n    // 其他平台显示图片预览\n    uni.previewImage({\n      urls: [imagePath],\n      current: imagePath\n    });\n    emit('share-success', { type: 'image', path: imagePath });\n    handleClose();\n    // #endif\n  } catch (error) {\n    console.error('分享图片失败:', error);\n    uni.showToast({\n      title: '分享失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 关闭弹窗\nconst handleClose = () => {\n  console.log('🚪 弹窗关闭请求');\n  console.log('🛡️ 当前保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);\n  console.log('🎨 当前生成状态 - isGeneratingImage:', isGeneratingImage.value);\n\n  // 如果正在生成图片，询问用户是否确认关闭\n  if (isGeneratingImage.value && (configLocked.value || isRenderingInProgress.value)) {\n    console.log('⚠️ 正在生成图片，延迟关闭');\n    uni.showModal({\n      title: '提示',\n      content: '图片正在生成中，确定要关闭吗？',\n      success: (res) => {\n        if (res.confirm) {\n          console.log('👤 用户确认关闭，强制清理资源');\n          cleanupResources(true, 'user-confirmed-close');\n          generatedImagePath.value = '';\n          emit('close');\n        } else {\n          console.log('👤 用户取消关闭');\n        }\n      }\n    });\n    return;\n  }\n\n  // 正常关闭流程\n  console.log('🚪 执行正常关闭流程');\n  cleanupResources(true, 'popup-close');\n  generatedImagePath.value = '';\n  emit('close');\n};\n\n// 监听 Painter 配置变化，防止意外清理\nwatch(painterConfig, (newVal, oldVal) => {\n  // 如果配置被清理但应该受保护\n  if (!newVal && oldVal && (configLocked.value || isRenderingInProgress.value)) {\n    console.warn('🛡️ 检测到配置被意外清理，尝试恢复');\n    console.log('🛡️ 保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);\n\n    if (lastValidConfig.value) {\n      console.log('🔄 使用最后有效配置恢复');\n      // 延迟恢复，避免立即被再次清理\n      setTimeout(() => {\n        if (!painterConfig.value && (configLocked.value || isRenderingInProgress.value)) {\n          painterConfig.value = { ...lastValidConfig.value };\n          console.log('✅ 配置已自动恢复');\n        }\n      }, 50);\n    }\n  }\n}, { deep: true });\n\n// Painter 组件事件处理\nconst onPainterDidShow = () => {\n  console.log('🎨 Painter didShow 事件 - 组件已显示');\n  console.log('🎨 Painter 组件状态检查');\n  console.log('🎨 - painterConfig:', !!painterConfig.value);\n  console.log('🎨 - isGeneratingImage:', isGeneratingImage.value);\n  console.log('🎨 - configLocked:', configLocked.value);\n};\n\nconst onPainterViewUpdate = (e) => {\n  console.log('🎨 Painter viewUpdate 事件:', e);\n  console.log('🎨 ViewUpdate 详情:', JSON.stringify(e, null, 2));\n};\n\nconst onPainterTouchStart = (e) => {\n  console.log('🎨 Painter touchStart 事件:', e);\n};\n\nconst onPainterTouchMove = (e) => {\n  console.log('🎨 Painter touchMove 事件:', e);\n};\n\nconst onPainterTouchEnd = (e) => {\n  console.log('🎨 Painter touchEnd 事件:', e);\n};\n</script>\n\n<template>\n  <view class=\"share-popup-wrapper\" :class=\"{ 'show': show }\" @click.self=\"handleClose\">\n    <view class=\"share-popup\" :class=\"{ 'show': show }\">\n      <view class=\"share-popup-header\">\n        <text class=\"share-popup-title\">{{ title }}</text>\n        <view class=\"share-popup-close\" @click=\"handleClose\">\n          <u-icon name=\"close\" size=\"24\" color=\"#999\"></u-icon>\n        </view>\n      </view>\n\n      <view class=\"share-options\">\n        <view\n          v-for=\"option in shareOptions\"\n          :key=\"option.id\"\n          class=\"share-option-item\"\n          @click=\"handleSelect(option)\"\n        >\n          <image class=\"share-option-icon\" :src=\"option.icon\" mode=\"aspectFit\"></image>\n          <text class=\"share-option-name\">{{ option.name }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- Painter组件用于生成分享图片 -->\n    <painter\n      v-if=\"painterConfig\"\n      :palette=\"painterConfig\"\n      :use2D=\"true\"\n      :LRU=\"false\"\n      :dirty=\"false\"\n      :disableAction=\"true\"\n      :widthPixels=\"750\"\n      @imgOK=\"onPainterSuccess\"\n      @imgErr=\"onPainterFail\"\n      @didShow=\"onPainterDidShow\"\n      @viewUpdate=\"onPainterViewUpdate\"\n      @touchStart=\"onPainterTouchStart\"\n      @touchMove=\"onPainterTouchMove\"\n      @touchEnd=\"onPainterTouchEnd\"\n      :customStyle=\"'position: fixed; top: -9999px; left: -9999px; width: 750rpx; height: 1334rpx; z-index: -1;'\"\n    />\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.share-popup-wrapper {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-end;\n  opacity: 0;\n  visibility: hidden;\n  transition: opacity 0.3s ease, visibility 0.3s ease;\n\n  &.show {\n    opacity: 1;\n    visibility: visible;\n  }\n}\n\n.share-popup {\n  background-color: #fff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 30rpx;\n  transform: translateY(100%);\n  transition: transform 0.3s ease;\n\n  &.show {\n    transform: translateY(0);\n  }\n}\n\n.share-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.share-popup-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.share-popup-close {\n  padding: 10rpx;\n}\n\n.share-options {\n  display: flex;\n  flex-direction: row; // 改为横排\n  justify-content: space-around; // 均匀分布\n  padding: 30rpx 0;\n  border-top: 1rpx solid #f0f0f0; // 添加顶部边框\n}\n\n.share-option-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 0 10rpx;\n  transition: all 0.3s ease;\n}\n\n.share-option-item:active {\n  transform: scale(1.15);\n}\n\n.share-option-icon {\n  width: 80rpx; // 固定大小\n  height: 80rpx; // 固定大小\n  margin-bottom: 16rpx;\n  transition: all 0.2s ease;\n}\n\n.share-option-item:active .share-option-icon {\n  filter: brightness(1.1) contrast(1.1);\n}\n\n.share-option-name {\n  font-size: 24rpx;\n  color: #333;\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/share-popup/share-popup.vue'\nwx.createComponent(Component)"], "names": ["isGeneratingImage", "ref", "generatedImagePath", "painter<PERSON><PERSON><PERSON><PERSON>", "generationTimer", "GENERATION_TIMEOUT", "configLocked", "isRenderingInProgress", "configProtectionTimer", "lastValidConfig", "baseShareOptions", "id", "name", "icon", "shareOptions", "computed", "options", "props", "showMemberInvite", "push", "handleSelect", "async", "option", "shareToWeChat", "shareToMoments", "generateAndShowShareImage", "generateAndSaveImage", "shareMemberInvite", "shareWithUniShare", "emit", "error", "console", "errorMessage", "message", "uni", "showModal", "title", "content", "showCancel", "cancelText", "confirmText", "success", "res", "confirm", "showToast", "duration", "type", "value", "warn", "log", "showLoading", "config", "<PERSON><PERSON><PERSON>", "Error", "JSON", "stringify", "width", "height", "views", "Array", "isArray", "length", "setTimeout", "_b", "_a", "url", "configToSet", "clearTimeout", "createSelectorQuery", "selectAll", "boundingClientRect", "rects", "for<PERSON>ach", "rect", "index", "exec", "trigger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tempConfig", "nextTick", "cleanupResources", "shareConfig", "shareData", "template", "createCardShareConfig", "createUniCardConfig", "createFeedShareConfig", "createUniFeedConfig", "href", "summary", "author", "imageUrl", "image", "menus", "img", "text", "share", "provider", "scene", "result", "showShareMenu", "forceCleanup", "reason", "stack", "hideLoading", "wx", "wx$1", "triggerGC", "global", "gc", "startGenerationTimer", "createDefaultShareConfig", "viewsCount", "background", "onPainterSuccess", "event", "detail", "tempFile<PERSON>ath", "path", "getImageInfo", "src", "imagePath", "saveImageToAlbum", "saveGeneratedImageToAlbum", "shareGeneratedImage", "fail", "err", "onPainterFail", "errorMsg", "_c", "_d", "errMsg", "_fallbackAttempted", "showShareImageMenu", "handleClose", "watch", "newVal", "oldVal", "deep", "onPainterDidShow", "onPainterViewUpdate", "e", "onPainterTouchStart", "onPainterTouchMove", "onPainterTouchEnd", "createComponent", "Component"], "mappings": "0kBA4BMA,EAAoBC,EAAAA,KAAI,GACxBC,EAAqBD,EAAAA,IAAI,IACzBE,EAAgBF,EAAAA,IAAI,MACpBG,EAAkBH,EAAAA,IAAI,MACtBI,EAAqB,IAGrBC,EAAeL,EAAAA,KAAI,GACnBM,EAAwBN,EAAAA,KAAI,GAC5BO,EAAwBP,EAAAA,IAAI,MAC5BQ,EAAkBR,EAAAA,IAAI,MAGtBS,EAAmB,CACvB,CAAEC,GAAI,SAAUC,KAAM,KAAMC,KAAM,sBAClC,CAAEF,GAAI,UAAWC,KAAM,MAAOC,KAAM,2BACpC,CAAEF,GAAI,QAASC,KAAM,OAAQC,KAAM,sBACnC,CAAEF,GAAI,OAAQC,KAAM,OAAQC,KAAM,uBAI9BC,EAAeC,EAAQA,UAAC,KACtB,MAAAC,EAAU,IAAIN,GAWb,OARHO,EAAMC,kBACRF,EAAQG,KAAK,CACXR,GAAI,gBACJC,KAAM,OACNC,KAAM,qBAIHG,CAAA,IAIHI,EAAeC,MAAOC,IACtB,IACF,OAAQA,EAAOX,IACb,IAAK,eACGY,IACN,MACF,IAAK,gBACGC,IACN,MACF,IAAK,cACGC,IACN,MACF,IAAK,aACGC,IACN,MACF,IAAK,sBACGC,IACN,MACF,IAAK,kBACGC,IACN,MACF,QACEC,EAAK,SAAUP,GAEpB,OAAQQ,GACCC,QAAAD,MAAM,UAAWA,GACzBD,EAAK,cAAeC,GAGd,MAAAE,EAAeF,EAAMG,SAAW,OACtCC,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,QAASL,EACTM,YAAY,EACZC,WAAY,KACZC,YAAa,KACbC,QAAUC,IACJA,EAAIC,SAENvB,EAAaE,EACf,GAGN,GAIIC,EAAgBF,UAEhB,IAEFa,EAAAA,MAAIU,UAAU,CACZR,MAAO,cACPvB,KAAM,OACNgC,SAAU,MAGZhB,EAAK,gBAAiB,CAAEiB,KAAM,cAE/B,OAAQhB,GAED,MADEC,QAAAD,MAAM,UAAWA,GACnBA,CACR,GAYIN,EAAiBH,UAEjB,IAEFa,EAAAA,MAAIU,UAAU,CACZR,MAAO,eACPvB,KAAM,OACNgC,SAAU,MAGZhB,EAAK,gBAAiB,CAAEiB,KAAM,eAE/B,OAAQhB,GAED,MADEC,QAAAD,MAAM,WAAYA,GACpBA,CACR,GAYIL,EAA4BJ,kBAC5B,IACF,GAAIrB,EAAkB+C,MAEpB,YADAhB,QAAQiB,KAAK,kBAIfjB,QAAQkB,IAAI,sBACZjD,EAAkB+C,OAAQ,EAC1B7C,EAAmB6C,MAAQ,GAC3Bb,EAAAA,MAAIgB,YAAY,CAAEd,MAAO,aAEzB,MAAMe,EAASC,EAAYL,MAC3B,IAAKI,EACG,MAAA,IAAIE,MAAM,YAMlB,GAHAtB,QAAQkB,IAAI,mBAAoBK,KAAKC,UAAUJ,EAAQ,KAAM,KAGxDA,EAAOK,QAAUL,EAAOM,OAC3B,MAAM,IAAIJ,MAAM,0BAA0BF,EAAOK,iBAAiBL,EAAOM,UAGvE,IAACN,EAAOO,QAAUC,MAAMC,QAAQT,EAAOO,OACnC,MAAA,IAAIL,MAAM,sBAGlBtB,QAAQkB,IAAI,2BAA4BE,EAAOO,MAAMG,YAI7C9B,QAAAkB,IAAI,mBAAoB5C,EAAoB,MAGpD0B,QAAQkB,IAAI,mBACZ3C,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,EAGRvC,EAAAuC,MAAQe,YAAW,KACvC/B,QAAQkB,IAAI,qBACZ3C,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,CAAA,GAC7B,KAMHhB,QAAQkB,IAAI,4BACZlB,QAAQkB,IAAI,kBAAmB,OAAAc,EAAA,OAAAC,EAAAb,EAAOO,YAAQ,EAAAM,EAAA,aAAIC,KAG5C,MAAAC,EAAc,IAAKf,GACzB1C,EAAgBsC,MAAQmB,EACxB/D,EAAc4C,MAAQmB,EAEtBnC,QAAQkB,IAAI,6BACZlB,QAAQkB,IAAI,uBAAwB3C,EAAayC,MAAO,aAAcxC,EAAsBwC,OAG5Fe,YAAW,KACT/B,QAAQkB,IAAI,uBACZlB,QAAQkB,IAAI,0BAA2B9C,EAAc4C,OAC7ChB,QAAAkB,IAAI,4BAA6BjD,EAAkB+C,OAC3DhB,QAAQkB,IAAI,uBAAwB3C,EAAayC,MAAO,aAAcxC,EAAsBwC,OAEvF5C,EAAc4C,QACjBhB,QAAQD,MAAM,qBACdC,QAAQD,MAAM,eAGdxB,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,EAG9B5C,EAAc4C,MAAQmB,EAEtBnC,QAAQkB,IAAI,oBACZlB,QAAQkB,IAAI,wBAAyB3C,EAAayC,MAAO,aAAcxC,EAAsBwC,OAGzFvC,EAAsBuC,OACxBoB,aAAa3D,EAAsBuC,OAEfvC,EAAAuC,MAAQe,YAAW,KACvC/B,QAAQkB,IAAI,wBACZ3C,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,CAAA,GAC7B,KACL,GACC,KAGHe,YAAW,KACT/B,QAAQkB,IAAI,2BAGZlB,QAAQkB,IAAI,oBAGR,IACYf,QAAIkC,sBACZC,UAAU,WAAWC,oBAAoBC,IAC7CxC,QAAQkB,IAAI,2BAA4BsB,EAAQA,EAAMV,OAAS,GAE1DU,GAA0B,IAAjBA,EAAMV,QAIlB9B,QAAQkB,IAAI,uBACNsB,EAAAC,SAAQ,CAACC,EAAMC,KACnB3C,QAAQkB,IAAI,iBAAiByB,EAAQ,QAASD,EAAI,MALpD1C,QAAQiB,KAAK,oCAOf,IACC2B,MACJ,OAAQ7C,GACCC,QAAAD,MAAM,aAAcA,MAG9B,IACC,KAGP,MAAM8C,EAAyB,KAC7B7C,QAAQkB,IAAI,6BACZ,MAAM4B,EAAa1E,EAAc4C,MACjC5C,EAAc4C,MAAQ,KAEtB+B,EAAAA,YAAS,KACP3E,EAAc4C,MAAQ8B,EACtB9C,QAAQkB,IAAI,qBAAoB,GACjC,CAGA,OAAQnB,GACCC,QAAAD,MAAM,cAAeA,GAC7BiD,GAAiB,EAAM,kBAEjB,MAAA/C,EAAeF,EAAMG,SAAW,WAMhC,MALNC,EAAAA,MAAIU,UAAU,CACZR,MAAOJ,EAAa6B,OAAS,GAAK,SAAW7B,EAC7CnB,KAAM,OACNgC,SAAU,MAENf,CACR,GA6BIJ,EAAuBL,kBACvB,IACF,GAAIrB,EAAkB+C,MAEpB,YADAhB,QAAQiB,KAAK,kBAKfhD,EAAkB+C,OAAQ,EAC1B7C,EAAmB6C,MAAQ,YAC3Bb,EAAAA,MAAIgB,YAAY,CAAEd,MAAO,aAEzB,MAAMe,EAASC,EAAYL,MAC3B,IAAKI,EACG,MAAA,IAAIE,MAAM,YAGVtB,QAAAkB,IAAI,YAAaE,OASzBpB,QAAQkB,IAAI,yBAA0B,OAAAc,EAAA,OAAAC,EAAAb,EAAOO,YAAQ,EAAAM,EAAA,aAAIC,KAC3C9D,EAAA4C,MAAQ,IAAKI,EAE5B,OAAQrB,GACCC,QAAAD,MAAM,YAAaA,GAC3BiD,GAAiB,EAAM,cAEjB,MAAA/C,EAAeF,EAAMG,SAAW,WAMhC,MALNC,EAAAA,MAAIU,UAAU,CACZR,MAAOJ,EAAa6B,OAAS,GAAK,SAAW7B,EAC7CnB,KAAM,OACNgC,SAAU,MAENf,CACR,GAIIH,EAAoBN,UACpB,IAGFa,EAAAA,MAAIU,UAAU,CACZR,MAAO,cACPvB,KAAM,OACNgC,SAAU,MAIZhB,EAAK,gBAAiB,CAAEiB,KAAM,qBAE/B,OAAQhB,GAED,MADEC,QAAAD,MAAM,YAAaA,GACrBA,CACR,GAIIF,EAAoBP,UACpB,IAIE,IAAA2D,EACA,GAJIjD,QAAAkB,IAAI,oBAAqBhC,EAAMgE,WAIN,SAA7BhE,EAAMgE,UAAUC,SAAqB,CAEvC,MAAQC,sBAAuBC,QAAqC,gCACtDJ,EAAAI,EAAoBnE,EAAMgE,UAC9C,MAAA,GAA4C,SAA7BhE,EAAMgE,UAAUC,UAAoD,YAA7BjE,EAAMgE,UAAUC,SAAwB,CAExF,MAAQG,sBAAuBC,QAAqC,gCACtDN,EAAAM,EAAoBrE,EAAMgE,UAC9C,MAEoBD,EAAA,CACZ3C,QAAS,CACPS,KAAM,EACNyC,KAAMtE,EAAMgE,UAAUM,MAAQ,GAC9BnD,MAAOnB,EAAMgE,UAAU5C,SAAW,OAClCmD,QAAS,GAAGvE,EAAMgE,UAAUQ,QAAU,cACtCC,SAAUzE,EAAMgE,UAAUU,OAAS,IAErCC,MAAO,CACL,CACEC,IAAO,8CACPC,KAAQ,OACRC,MAAS,CACPC,SAAY,SACZC,MAAS,mBAGb,CACEJ,IAAO,+CACPC,KAAQ,QACRC,MAAS,CACPC,SAAY,SACZC,MAAS,oBAGb,CACEJ,IAAO,yCACPC,KAAQ,OACRC,MAAS,YAGbxD,WAAY,QAKhB,MAAM2D,QAAeC,gBAAcnB,GAE/BkB,EAAOzD,SACDV,QAAAkB,IAAI,iBAAkBiD,GAC9BhE,EAAGwC,MAAC9B,UAAU,CAAER,MAAO,OAAQvB,KAAM,YACrCgB,EAAK,gBAAiB,CAAEiB,KAAM,YAAaoD,gBAG3CnE,QAAQkB,IAAI,SAEf,OAAQnB,GACCC,QAAAD,MAAM,iBAAkBA,GAChCI,EAAGwC,MAAC9B,UAAU,CAAER,MAAO,OAAQvB,KAAM,SACrCgB,EAAK,cAAeC,EACtB,GA2BIiD,EAAmB,CAACqB,GAAe,EAAOC,EAAS,aAMnD,GALJtE,QAAQkB,IAAI,kBAAmBoD,EAAQ,QAASD,GAChDrE,QAAQkB,IAAI,0BAA2B3C,EAAayC,MAAO,yBAA0BxC,EAAsBwC,OAC3GhB,QAAQkB,IAAI,WAAW,IAAII,OAAQiD,QAG/BhG,EAAayC,OAAUqD,EAAvB,CAMA,GAAA7F,EAAsBwC,QAAUqD,EAKlC,OAJQrE,QAAAkB,IAAI,uBAAwBoD,QACpCvC,YAAW,KACQiB,GAAA,EAAO,WAAWsB,IAAQ,GAC1C,KAILtE,QAAQkB,IAAI,kBAER7C,EAAgB2C,QAClBoB,aAAa/D,EAAgB2C,OAC7B3C,EAAgB2C,MAAQ,MAGtBvC,EAAsBuC,QACxBoB,aAAa3D,EAAsBuC,OACnCvC,EAAsBuC,MAAQ,MAGhC/C,EAAkB+C,OAAQ,EAC1BzC,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,EAG1B5C,EAAc4C,QAChBhB,QAAQkB,IAAI,oBACZ9C,EAAc4C,MAAQ,MAIpB7C,EAAmB6C,OAAsC,cAA7B7C,EAAmB6C,QACjD7C,EAAmB6C,MAAQ,IAG7Bb,EAAGwC,MAAC6B,cAtEuB,MAC3BxE,QAAQkB,IAAI,gBAER,SAEgB,IAAPuD,EAAEC,MAAoBD,EAAAA,KAAGE,YAClC3E,QAAQkB,IAAI,kBACZuD,EAAEC,KAACC,aAIiB,oBAAXC,QAA0BA,OAAOC,KAC1C7E,QAAQkB,IAAI,eACZ0D,OAAOC,MAGT7E,QAAQkB,IAAI,YACb,OAAQnB,GACCC,QAAAD,MAAM,aAAcA,EAC9B,MAwDAC,QAAQkB,IAAI,YA3CZ,MAFUlB,QAAAkB,IAAI,uBAAwBoD,EA6Cf,EAwBnBQ,EAAuB,KACvBzG,EAAgB2C,QAClBhB,QAAQkB,IAAI,gBACZkB,aAAa/D,EAAgB2C,QAGvBhB,QAAAkB,IAAI,oBAAqB5C,EAAoB,MACrCD,EAAA2C,MAAQe,YAAW,KA1B3B/B,QAAAD,MAAM,iBAAkBzB,EAAoB,MAC5C0B,QAAAD,MAAM,8BAA+B9B,EAAkB+C,OAC/DhB,QAAQD,MAAM,4BAA6B3B,EAAc4C,OACjDhB,QAAAD,MAAM,+BAAgC5B,EAAmB6C,OACjEhB,QAAQD,MAAM,qBAAsBxB,EAAayC,MAAO,aAAcxC,EAAsBwC,OAG5FgC,GAAiB,EAAM,WAEvB7C,EAAAA,MAAIU,UAAU,CACZR,MAAO,aACPvB,KAAM,OACNgC,SAAU,QAgBTxC,EAAkB,EAIjB+C,EAAcrC,EAAQA,UAAC,WAGvB,GAFIgB,QAAAkB,IAAI,8BAA+BhC,EAAMgE,YAE5ChE,EAAMgE,UAEF,OADPlD,QAAQiB,KAAK,mCACN,KAGL,IAGE,IAAAG,EACI,OAHRpB,QAAQkB,IAAI,0BAA2BhC,EAAMgE,UAAUC,UAG/CjE,EAAMgE,UAAUC,UACtB,IAAK,OACHnD,QAAQkB,IAAI,iBACHkC,EAAAA,EAAqBA,sBAAClE,EAAMgE,WACrC,MACF,IAAK,OACL,IAAK,UACHlD,QAAQkB,IAAI,iBACHoC,EAAAA,EAAqBA,sBAACpE,EAAMgE,WACrC,MACF,QACElD,QAAQkB,IAAI,iBACH6D,EAAAA,EAAwBA,yBAAC7F,EAAMgE,WAUrC,OAPPlD,QAAQkB,IAAI,yBAA0B,CACpCO,MAAe,MAARL,OAAQ,EAAAA,EAAAK,MACfC,OAAgB,MAARN,OAAQ,EAAAA,EAAAM,OAChBsD,WAAY,OAAA/C,EAAQ,MAAAb,OAAA,EAAAA,EAAAO,YAAO,EAAAM,EAAAH,OAC3BmD,WAAoB,MAAR7D,OAAQ,EAAAA,EAAA6D,aAGf7D,CACR,OAAQrB,GAGA,OAFCC,QAAAD,MAAM,mBAAoBA,GAC1BC,QAAAD,MAAM,UAAWA,EAAMwE,OACxB,IACT,KAIIW,EAAoBC,YAChBnF,QAAAkB,IAAI,+BAAgCiE,GACpCnF,QAAAkB,IAAI,mBAAoBiE,EAAMC,QACtCpF,QAAQkB,IAAI,iBAAkBK,KAAKC,UAAU2D,EAAO,KAAM,IAG1DnF,QAAQkB,IAAI,qBACZ3C,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,EAE1BvC,EAAsBuC,QACxBoB,aAAa3D,EAAsBuC,OACnCvC,EAAsBuC,MAAQ,MAI5B3C,EAAgB2C,QAClBhB,QAAQkB,IAAI,aACZkB,aAAa/D,EAAgB2C,OAC7B3C,EAAgB2C,MAAQ,MAG1B/C,EAAkB+C,OAAQ,EAC1Bb,EAAGwC,MAAC6B,cAEE,MAAAa,GAAe,OAAApD,IAAMmD,aAAN,EAAAnD,EAAcqD,QAAQ,OAAAtD,EAAMmD,EAAAC,aAAQ,EAAApD,EAAAqD,eAAgBF,EAAMG,KACvEtF,QAAAkB,IAAI,cAAemE,GAEvBA,GACMrF,QAAAkB,IAAI,eAAgBmE,GAG5BrF,QAAQkB,IAAI,kBACZf,EAAAA,MAAIoF,aAAa,CACfC,IAAKH,EACL3E,QAAUC,IACAX,QAAAkB,IAAI,YAAaP,GACzBX,QAAQkB,IAAI,UAAWP,EAAIc,MAAO,IAAKd,EAAIe,QAGV,cAA7BvD,EAAmB6C,OACrBhB,QAAQkB,IAAI,aAnWY5B,OAAOmG,IACnC,UACIC,EAAAA,iBAAiBD,GAEvBtF,EAAAA,MAAIU,UAAU,CACZR,MAAO,OACPvB,KAAM,YAGRgB,EAAK,gBAAiB,CAAEiB,KAAM,OAAQuE,KAAMG,OAE7C,OAAQ1F,GACCC,QAAAD,MAAM,UAAWA,GAEnB,MAAAE,EAAeF,EAAMG,SAAW,SAMhC,MALNC,EAAAA,MAAIU,UAAU,CACZR,MAAOJ,EAAa6B,OAAS,GAAK,OAAS7B,EAC3CnB,KAAM,OACNgC,SAAU,MAENf,CACR,GA+UQ4F,CAA0BN,KAE1BrF,QAAQkB,IAAI,aACZ0E,EAAoBP,IAGtBlH,EAAmB6C,MAAQqE,CAAA,EAE7BQ,KAAOC,IACG9F,QAAAD,MAAM,YAAa+F,GAC3B9C,GAAiB,EAAM,2BACvB7C,EAAAA,MAAIU,UAAU,CACZR,MAAO,UACPvB,KAAM,QACP,MAILkB,QAAQD,MAAM,mBACNC,QAAAD,MAAM,cAAeoF,GAC7BnC,GAAiB,EAAM,iBACvB7C,EAAAA,MAAIU,UAAU,CACZR,MAAO,SACPvB,KAAM,SAEV,EAIIiH,EAAiBZ,gBACbnF,QAAAD,MAAM,4BAA6BoF,GACnCnF,QAAAD,MAAM,kBAAmBoF,EAAMC,QACvCpF,QAAQD,MAAM,gBAAiBwB,KAAKC,UAAU2D,EAAO,KAAM,IAG3DnF,QAAQkB,IAAI,qBACZ3C,EAAayC,OAAQ,EACrBxC,EAAsBwC,OAAQ,EAE1BvC,EAAsBuC,QACxBoB,aAAa3D,EAAsBuC,OACnCvC,EAAsBuC,MAAQ,MAI5B3C,EAAgB2C,QAClBhB,QAAQkB,IAAI,aACZkB,aAAa/D,EAAgB2C,OAC7B3C,EAAgB2C,MAAQ,MAG1B,MAAMgF,GAAW,OAAAhE,EAAA,OAAMC,EAAAkD,EAAAC,aAAQ,EAAAnD,EAAAlC,gBAAOG,WACrB,OAAA+F,EAAAd,EAAMC,aAAN,EAAAa,EAAc/F,WACd,OAAAgG,EAAMf,EAAAC,aAAQ,EAAAc,EAAAC,SACdhB,EAAMgB,QACN,SAKjB,GAHQnG,QAAAD,MAAM,mBAAoBiG,GAG9B5H,EAAc4C,QAAU5C,EAAc4C,MAAMoF,mBAkB9C,OAjBApG,QAAQkB,IAAI,4BACZ9C,EAAc4C,MAAMoF,oBAAqB,OAGzCrE,YAAW,KACT/B,QAAQkB,IAAI,gBACZ,MAAME,EAAS,IAAKhD,EAAc4C,cAC3BI,EAAOgF,mBACdhI,EAAc4C,MAAQ,KAGtBe,YAAW,KACT/B,QAAQkB,IAAI,4BACZ9C,EAAc4C,MAAQI,CAAA,GACrB,IAAG,GACL,KAKLpB,QAAQD,MAAM,qBACdiD,GAAiB,EAAM,sBAEvB7C,EAAAA,MAAIU,UAAU,CACZR,MAAO,SACPvB,KAAM,QACP,EAIG8G,EAAuBH,IACvB,IAGFtF,EAAAA,MAAIkG,mBAAmB,CACrBf,KAAMG,EACN/E,QAAS,KACPV,QAAQkB,IAAI,UACZpB,EAAK,gBAAiB,CAAEiB,KAAM,QAASuE,KAAMG,SAG/CI,KAAO9F,IACGC,QAAAD,MAAM,UAAWA,GACzBI,EAAAA,MAAIU,UAAU,CACZR,MAAO,OACPvB,KAAM,QACP,GAcN,OAAQiB,GACCC,QAAAD,MAAM,UAAWA,GACzBI,EAAAA,MAAIU,UAAU,CACZR,MAAO,OACPvB,KAAM,QAEV,GAIIwH,EAAc,KAMlB,GALAtG,QAAQkB,IAAI,aACZlB,QAAQkB,IAAI,uBAAwB3C,EAAayC,MAAO,aAAcxC,EAAsBwC,OACpFhB,QAAAkB,IAAI,iCAAkCjD,EAAkB+C,OAG5D/C,EAAkB+C,QAAUzC,EAAayC,OAASxC,EAAsBwC,OAgB1E,OAfAhB,QAAQkB,IAAI,uBACZf,EAAAA,MAAIC,UAAU,CACZC,MAAO,KACPC,QAAS,kBACTI,QAAUC,IACJA,EAAIC,SACNZ,QAAQkB,IAAI,oBACZ8B,GAAiB,EAAM,wBACvB7E,EAAmB6C,MAAQ,GAC3BlB,EAAK,UAELE,QAAQkB,IAAI,YACd,IAONlB,QAAQkB,IAAI,eACZ8B,GAAiB,EAAM,eACvB7E,EAAmB6C,MAAQ,GAC3BlB,EAAK,QAAO,EAIdyG,EAAAA,MAAMnI,GAAe,CAACoI,EAAQC,MAEvBD,GAAUC,IAAWlI,EAAayC,OAASxC,EAAsBwC,SACpEhB,QAAQiB,KAAK,uBACbjB,QAAQkB,IAAI,qBAAsB3C,EAAayC,MAAO,aAAcxC,EAAsBwC,OAEtFtC,EAAgBsC,QAClBhB,QAAQkB,IAAI,iBAEZa,YAAW,KACJ3D,EAAc4C,QAAUzC,EAAayC,QAASxC,EAAsBwC,QACvE5C,EAAc4C,MAAQ,IAAKtC,EAAgBsC,OAC3ChB,QAAQkB,IAAI,aACd,GACC,KAEP,GACC,CAAEwF,MAAM,IAGX,MAAMC,EAAmB,KACvB3G,QAAQkB,IAAI,iCACZlB,QAAQkB,IAAI,qBACZlB,QAAQkB,IAAI,wBAAyB9C,EAAc4C,OAC3ChB,QAAAkB,IAAI,0BAA2BjD,EAAkB+C,OACjDhB,QAAAkB,IAAI,qBAAsB3C,EAAayC,MAAK,EAGhD4F,EAAuBC,IACnB7G,QAAAkB,IAAI,4BAA6B2F,GACzC7G,QAAQkB,IAAI,oBAAqBK,KAAKC,UAAUqF,EAAG,KAAM,GAAE,EAGvDC,EAAuBD,IACnB7G,QAAAkB,IAAI,4BAA6B2F,EAAC,EAGtCE,EAAsBF,IAClB7G,QAAAkB,IAAI,2BAA4B2F,EAAC,EAGrCG,EAAqBH,IACjB7G,QAAAkB,IAAI,0BAA2B2F,EAAC,giBC92B1CpC,GAAGwC,gBAAgBC"}