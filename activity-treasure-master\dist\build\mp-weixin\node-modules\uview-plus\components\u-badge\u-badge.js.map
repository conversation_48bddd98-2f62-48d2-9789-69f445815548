{"version": 3, "file": "u-badge.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-badge/u-badge.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWJhZGdlL3UtYmFkZ2UudnVl"], "sourcesContent": ["<template>\n\t<text\n\t\tv-if=\"show && ((Number(value) === 0 ? showZero : true) || isDot)\"\n\t\t:class=\"[isDot ? 'u-badge--dot' : 'u-badge--not-dot', inverted && 'u-badge--inverted', shape === 'horn' && 'u-badge--horn', `u-badge--${type}${inverted ? '--inverted' : ''}`]\"\n\t\t:style=\"[addStyle(customStyle), badgeStyle]\"\n\t\tclass=\"u-badge\"\n\t>{{ isDot ? '' :showValue }}</text>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, addUnit } from '../../libs/function/index';\n\t/**\n\t * badge 徽标数\n\t * @description 该组件一般用于图标右上角显示未读的消息数量，提示用户点击，有圆点和圆包含文字两种形式。\n\t * @tutorial https://uview-plus.jiangruyi.com/components/badge.html\n\t * \n\t * @property {Boolean} \t\t\tisDot \t\t是否显示圆点 （默认 false ）\n\t * @property {String | Number} \tvalue \t\t显示的内容\n\t * @property {Boolean} \t\t\tshow \t\t是否显示 （默认 true ）\n\t * @property {String | Number} \tmax \t\t最大值，超过最大值会显示 '{max}+'  （默认999）\n\t * @property {String} \t\t\ttype \t\t主题类型，error|warning|success|primary （默认 'error' ）\n\t * @property {Boolean} \t\t\tshowZero\t当数值为 0 时，是否展示 Badge （默认 false ）\n\t * @property {String} \t\t\tbgColor \t背景颜色，优先级比type高，如设置，type参数会失效\n\t * @property {String} \t\t\tcolor \t\t字体颜色 （默认 '#ffffff' ）\n\t * @property {String} \t\t\tshape \t\t徽标形状，circle-四角均为圆角，horn-左下角为直角 （默认 'circle' ）\n\t * @property {String} \t\t\tnumberType\t设置数字的显示方式，overflow|ellipsis|limit  （默认 'overflow' ）\n\t * @property {Array}} \t\t\toffset\t\t设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，absolute为true时有效\n\t * @property {Boolean} \t\t\tinverted\t是否反转背景和字体颜色（默认 false ）\n\t * @property {Boolean} \t\t\tabsolute\t是否绝对定位（默认 false ）\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * @example <u-badge :type=\"type\" :count=\"count\"></u-badge>\n\t */\n\texport default {\n\t\tname: 'u-badge',\n\t\tmixins: [mpMixin, props, mixin],\n\t\tcomputed: {\n\t\t\t// 是否将badge中心与父组件右上角重合\n\t\t\tboxStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t// 整个组件的样式\n\t\t\tbadgeStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tif(this.color) {\n\t\t\t\t\tstyle.color = this.color\n\t\t\t\t}\n\t\t\t\tif (this.bgColor && !this.inverted) {\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\t}\n\t\t\t\tif (this.absolute) {\n\t\t\t\t\tstyle.position = 'absolute'\n\t\t\t\t\t// 如果有设置offset参数\n\t\t\t\t\tif(this.offset.length) {\n\t\t\t\t\t\t// top和right分为为offset的第一个和第二个值，如果没有第二个值，则right等于top\n\t\t\t\t\t\tconst top = this.offset[0]\n\t\t\t\t\t\tconst right = this.offset[1] || top\n\t\t\t\t\t\tstyle.top = addUnit(top)\n\t\t\t\t\t\tstyle.right = addUnit(right)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tshowValue() {\n\t\t\t\tswitch (this.numberType) {\n\t\t\t\t\tcase \"overflow\":\n\t\t\t\t\t\treturn Number(this.value) > Number(this.max) ? this.max + \"+\" : this.value\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"ellipsis\":\n\t\t\t\t\t\treturn Number(this.value) > Number(this.max) ? \"...\" : this.value\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"limit\":\n\t\t\t\t\t\treturn Number(this.value) > 999 ? Number(this.value) >= 9999 ?\n\t\t\t\t\t\t\tMath.floor(this.value / 1e4 * 100) / 100 + \"w\" : Math.floor(this.value /\n\t\t\t\t\t\t\t\t1e3 * 100) / 100 + \"k\" : this.value\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn Number(this.value)\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\taddStyle\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t$u-badge-primary: $u-primary !default;\n\t$u-badge-error: $u-error !default;\n\t$u-badge-success: $u-success !default;\n\t$u-badge-info: $u-info !default;\n\t$u-badge-warning: $u-warning !default;\n\t$u-badge-dot-radius: 100px !default;\n\t$u-badge-dot-size: 8px !default;\n\t$u-badge-dot-right: 4px !default;\n\t$u-badge-dot-top: 0 !default;\n\t$u-badge-text-font-size: 11px !default;\n\t$u-badge-text-right: 10px !default;\n\t$u-badge-text-padding: 2px 5px !default;\n\t$u-badge-text-align: center !default;\n\t$u-badge-text-color: #FFFFFF !default;\n\n\t.u-badge {\n\t\tborder-top-right-radius: $u-badge-dot-radius;\n\t\tborder-top-left-radius: $u-badge-dot-radius;\n\t\tborder-bottom-left-radius: $u-badge-dot-radius;\n\t\tborder-bottom-right-radius: $u-badge-dot-radius;\n\t\t@include flex;\n\t\tline-height: $u-badge-text-font-size;\n\t\ttext-align: $u-badge-text-align;\n\t\tfont-size: $u-badge-text-font-size;\n\t\tcolor: $u-badge-text-color;\n\n\t\t&--dot {\n\t\t\theight: $u-badge-dot-size;\n\t\t\twidth: $u-badge-dot-size;\n\t\t}\n\t\t\n\t\t&--inverted {\n\t\t\tfont-size: 13px;\n\t\t}\n\t\t\n\t\t&--not-dot {\n\t\t\tpadding: $u-badge-text-padding;\n\t\t}\n\n\t\t&--horn {\n\t\t\tborder-bottom-left-radius: 0;\n\t\t}\n\n\t\t&--primary {\n\t\t\tbackground-color: $u-badge-primary;\n\t\t}\n\t\t\n\t\t&--primary--inverted {\n\t\t\tcolor: $u-badge-primary;\n\t\t}\n\n\t\t&--error {\n\t\t\tbackground-color: $u-badge-error;\n\t\t}\n\t\t\n\t\t&--error--inverted {\n\t\t\tcolor: $u-badge-error;\n\t\t}\n\n\t\t&--success {\n\t\t\tbackground-color: $u-badge-success;\n\t\t}\n\t\t\n\t\t&--success--inverted {\n\t\t\tcolor: $u-badge-success;\n\t\t}\n\n\t\t&--info {\n\t\t\tbackground-color: $u-badge-info;\n\t\t}\n\t\t\n\t\t&--info--inverted {\n\t\t\tcolor: $u-badge-info;\n\t\t}\n\n\t\t&--warning {\n\t\t\tbackground-color: $u-badge-warning;\n\t\t}\n\t\t\n\t\t&--warning--inverted {\n\t\t\tcolor: $u-badge-warning;\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-badge/u-badge.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "props", "props$44", "mixin", "computed", "boxStyle", "badgeStyle", "style", "this", "color", "bgColor", "inverted", "backgroundColor", "absolute", "position", "offset", "length", "top", "right", "addUnit", "showValue", "numberType", "Number", "value", "max", "Math", "floor", "methods", "addStyle", "wx", "createComponent", "Component"], "mappings": "6DAmCMA,EAAU,CACdC,KAAM,UACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKC,SAAEC,SACzBC,SAAU,CAETC,SAAW,KACE,CAAA,GAIb,UAAAC,GACC,MAAMC,EAAQ,CAAC,EAOf,GANGC,KAAKC,QACPF,EAAME,MAAQD,KAAKC,OAEhBD,KAAKE,UAAYF,KAAKG,WACzBJ,EAAMK,gBAAkBJ,KAAKE,SAE1BF,KAAKK,WACRN,EAAMO,SAAW,WAEdN,KAAKO,OAAOC,QAAQ,CAEhB,MAAAC,EAAMT,KAAKO,OAAO,GAClBG,EAAQV,KAAKO,OAAO,IAAME,EAC1BV,EAAAU,IAAME,EAAOA,QAACF,GACdV,EAAAW,MAAQC,EAAOA,QAACD,EACvB,CAEM,OAAAX,CACP,EACD,SAAAa,GACC,OAAQZ,KAAKa,YACZ,IAAK,WACG,OAAAC,OAAOd,KAAKe,OAASD,OAAOd,KAAKgB,KAAOhB,KAAKgB,IAAM,IAAMhB,KAAKe,MAEtE,IAAK,WACG,OAAAD,OAAOd,KAAKe,OAASD,OAAOd,KAAKgB,KAAO,MAAQhB,KAAKe,MAE7D,IAAK,QACJ,OAAOD,OAAOd,KAAKe,OAAS,IAAMD,OAAOd,KAAKe,QAAU,KACvDE,KAAKC,MAAMlB,KAAKe,MAAQ,IAAM,KAAO,IAAM,IAAME,KAAKC,MAAMlB,KAAKe,MAChE,IAAM,KAAO,IAAM,IAAMf,KAAKe,MAEjC,QACQ,OAAAD,OAAOd,KAAKe,OAErB,GAEFI,QAAS,CACRC,SAAAA,EAAOA,gfCpFVC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}