{"version": 3, "file": "u-sticky.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-sticky/u-sticky.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN0aWNreS91LXN0aWNreS52dWU"], "sourcesContent": ["<template>\n\t<view\n\t\tclass=\"u-sticky\"\n\t\t:id=\"elId\"\n\t\t:style=\"[style]\"\n\t>\n\t\t<view\n\t\t\t:style=\"[stickyContent]\"\n\t\t\tclass=\"u-sticky__content\"\n\t\t>\n\t\t\t<slot />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge, getPx, guid, sys, os } from '../../libs/function/index';\n\timport zIndex from '../../libs/config/zIndex';\n\t/**\n\t * sticky 吸顶\n\t * @description 该组件与CSS中position: sticky属性实现的效果一致，当组件达到预设的到顶部距离时， 就会固定在指定位置，组件位置大于预设的顶部距离时，会重新按照正常的布局排列。\n\t * @tutorial https://ijry.github.io/uview-plus/components/sticky.html\n\t * @property {String ｜ Number}\toffsetTop\t\t吸顶时与顶部的距离，单位px（默认 0 ）\n\t * @property {String ｜ Number}\tcustomNavHeight\t自定义导航栏的高度 （h5 默认44  其他默认 0 ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否开启吸顶功能 （默认 false ）\n\t * @property {String}\t\t\tbgColor\t\t\t组件背景颜色（默认 '#ffffff' ）\n\t * @property {String ｜ Number}\tzIndex\t\t\t吸顶时的z-index值\n\t * @property {String ｜ Number}\tindex\t\t\t自定义标识，用于区分是哪一个组件\n\t * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\n\t * @event {Function} fixed\t\t组件吸顶时触发\n\t * @event {Function} unfixed\t组件取消吸顶时触发\n\t * @example <u-sticky offsetTop=\"200\"><view>塞下秋来风景异，衡阳雁去无留意</view></u-sticky>\n\t */\n\texport default {\n\t\tname: 'u-sticky',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcssSticky: false, // 是否使用css的sticky实现\n\t\t\t\tstickyTop: 0, // 吸顶的top值，因为可能受自定义导航栏影响，最终的吸顶值非offsetTop值\n\t\t\t\telId: guid(),\n\t\t\t\tleft: 0, // js模式时，吸顶的内容因为处于postition: fixed模式，为了和原来保持一致的样式，需要记录并重新设置它的left，height，width属性\n\t\t\t\twidth: 'auto',\n\t\t\t\theight: 'auto',\n\t\t\t\tfixed: false, // js模式时，是否处于吸顶模式\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tstyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tif(!this.disabled) {\n\t\t\t\t\tif (this.cssSticky) {\n\t\t\t\t\t\tstyle.position = 'sticky'\n\t\t\t\t\t\tstyle.zIndex = this.uZindex\n\t\t\t\t\t\tstyle.top = addUnit(this.stickyTop)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstyle.height = this.fixed ? this.height + 'px' : 'auto'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 无需吸顶时，设置会默认的relative(nvue)和非nvue的static静态模式即可\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tstyle.position = 'relative'\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tstyle.position = 'static'\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\treturn deepMerge(addStyle(this.customStyle), style)\n\t\t\t},\n\t\t\t// 吸顶内容的样式\n\t\t\tstickyContent() {\n\t\t\t\tconst style = {}\n\t\t\t\tif (!this.cssSticky) {\n\t\t\t\t\tstyle.position = this.fixed ? 'fixed' : 'static'\n\t\t\t\t\tstyle.top = this.stickyTop + 'px'\n\t\t\t\t\tstyle.left = this.left + 'px'\n\t\t\t\t\tstyle.width = this.width == 'auto' ? 'auto' : this.width + 'px'\n\t\t\t\t\tstyle.zIndex = this.uZindex\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tuZindex() {\n\t\t\t\treturn this.zIndex ? this.zIndex : zIndex.sticky\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tthis.getStickyTop()\n\t\t\t\t// 判断使用的模式\n\t\t\t\tthis.checkSupportCssSticky()\n\t\t\t\t// 如果不支持css sticky，则使用js方案，此方案性能比不上css方案\n\t\t\t\tif (!this.cssSticky) {\n\t\t\t\t\t!this.disabled && this.initObserveContent()\n\t\t\t\t}\n\t\t\t},\n\t\t\tinitObserveContent() {\n\t\t\t\t// 获取吸顶内容的高度，用于在js吸顶模式时，给父元素一个填充高度，防止\"塌陷\"\n\t\t\t\tthis.$uGetRect('#' + this.elId).then((res) => {\n\t\t\t\t\tthis.height = res.height\n\t\t\t\t\tthis.left = res.left\n\t\t\t\t\tthis.width = res.width\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.observeContent()\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\tobserveContent() {\n\t\t\t\t// 先断掉之前的观察\n\t\t\t\tthis.disconnectObserver('contentObserver')\n\t\t\t\tconst contentObserver = uni.createIntersectionObserver({\n\t\t\t\t\t// 检测的区间范围\n\t\t\t\t\tthresholds: [0.95, 0.98, 1]\n\t\t\t\t})\n\t\t\t\t// 到屏幕顶部的高度时触发\n\t\t\t\tcontentObserver.relativeToViewport({\n\t\t\t\t\ttop: -this.stickyTop\n\t\t\t\t})\n\t\t\t\t// 绑定观察的元素\n\t\t\t\tcontentObserver.observe(`#${this.elId}`, res => {\n\t\t\t\t\tthis.setFixed(res.boundingClientRect.top)\n\t\t\t\t})\n\t\t\t\tthis.contentObserver = contentObserver\n\t\t\t},\n\t\t\tsetFixed(top) {\n\t\t\t\t// 判断是否出于吸顶条件范围\n\t\t\t\tconst fixed = top <= this.stickyTop\n\t\t\t\tthis.fixed = fixed\n\t\t\t},\n\t\t\tdisconnectObserver(observerName) {\n\t\t\t\t// 断掉观察，释放资源\n\t\t\t\tconst observer = this[observerName]\n\t\t\t\tobserver && observer.disconnect()\n\t\t\t},\n\t\t\tgetStickyTop() {\n\t\t\t\tthis.stickyTop = getPx(this.offsetTop) + getPx(this.customNavHeight)\n\t\t\t},\n\t\t\tasync checkSupportCssSticky() {\n\t\t\t\t// #ifdef H5\n\t\t\t\t// H5，一般都是现代浏览器，是支持css sticky的，这里使用创建元素嗅探的形式判断\n\t\t\t\tif (this.checkCssStickyForH5()) {\n\t\t\t\t\tthis.cssSticky = true\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\t// 如果安卓版本高于8.0，依然认为是支持css sticky的(因为安卓7在某些机型，可能不支持sticky)\n\t\t\t\tif (os() === 'android' && Number(sys().system) > 8) {\n\t\t\t\t\tthis.cssSticky = true\n\t\t\t\t}\n\n\t\t\t\t// APP-Vue和微信平台，通过computedStyle判断是否支持css sticky\n\t\t\t\t// #ifdef APP-VUE || MP-WEIXIN || MP-TOUTIAO\n\t\t\t\tthis.cssSticky = await this.checkComputedStyle()\n\t\t\t\t// #endif\n\n\t\t\t\t// ios上，从ios6开始，都是支持css sticky的\n\t\t\t\tif (os() === 'ios') {\n\t\t\t\t\tthis.cssSticky = true\n\t\t\t\t}\n\n\t\t\t\t// nvue，是支持css sticky的\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.cssSticky = true\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 在APP和微信小程序上，通过uni.createSelectorQuery可以判断是否支持css sticky\n\t\t\tcheckComputedStyle() {\n\t\t\t\t// 方法内进行判断，避免在其他平台生成无用代码\n\t\t\t\t// #ifdef APP-VUE || MP-WEIXIN || MP-TOUTIAO\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tuni.createSelectorQuery().in(this).select('.u-sticky').fields({\n\t\t\t\t\t\tcomputedStyle: [\"position\"]\n\t\t\t\t\t}).exec(e => {\n\t\t\t\t\t\tresolve('sticky' === e[0].position)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// H5通过创建元素的形式嗅探是否支持css sticky\n\t\t\t// 判断浏览器是否支持sticky属性\n\t\t\tcheckCssStickyForH5() {\n\t\t\t\t// 方法内进行判断，避免在其他平台生成无用代码\n\t\t\t\t// #ifdef H5\n\t\t\t\tconst vendorList = ['', '-webkit-', '-ms-', '-moz-', '-o-'],\n\t\t\t\t\tvendorListLength = vendorList.length,\n\t\t\t\t\tstickyElement = document.createElement('div')\n\t\t\t\tfor (let i = 0; i < vendorListLength; i++) {\n\t\t\t\t\tstickyElement.style.position = vendorList[i] + 'sticky'\n\t\t\t\t\tif (stickyElement.style.position !== '') {\n\t\t\t\t\t\treturn true\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tbeforeUnmount() {\n\t\t\tthis.disconnectObserver('contentObserver')\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.u-sticky {\n\t\t/* #ifdef APP-VUE || MP-WEIXIN || MP-TOUTIAO */\n\t\t// 此处默认写sticky属性，是为了给微信和APP通过uni.createSelectorQuery查询是否支持css sticky使用\n\t\tposition: sticky;\n\t\t/* #endif */\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-sticky/u-sticky.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "cssSticky", "stickyTop", "elId", "guid", "left", "width", "height", "fixed", "computed", "style", "this", "disabled", "position", "zIndex", "uZindex", "top", "addUnit", "backgroundColor", "bgColor", "deepMerge", "addStyle", "customStyle", "sticky<PERSON>ontent", "sticky", "mounted", "init", "methods", "getStickyTop", "checkSupportCssSticky", "initObserveContent", "$uGetRect", "then", "res", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "disconnectObserver", "contentObserver", "uni", "index", "createIntersectionObserver", "thresholds", "relativeToViewport", "observe", "setFixed", "boundingClientRect", "observerName", "observer", "disconnect", "getPx", "offsetTop", "customNavHeight", "os", "Number", "sys", "system", "checkComputedStyle", "Promise", "resolve", "createSelectorQuery", "in", "select", "fields", "computedStyle", "exec", "e", "checkCssStickyForH5", "beforeUnmount", "wx", "createComponent", "Component"], "mappings": "6DAoCMA,EAAU,CACdC,KAAM,WACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,WACzBC,KAAO,KACC,CACNC,WAAW,EACXC,UAAW,EACXC,KAAMC,EAAAA,OACNC,KAAM,EACNC,MAAO,OACPC,OAAQ,OACRC,OAAO,IAGTC,SAAU,CACT,KAAAC,GACC,MAAMA,EAAQ,CAAC,EAmBf,OAlBIC,KAAKC,SAcRF,EAAMG,SAAW,SAbbF,KAAKV,WACRS,EAAMG,SAAW,SACjBH,EAAMI,OAASH,KAAKI,QACpBL,EAAMM,IAAMC,UAAQN,KAAKT,YAEzBQ,EAAMH,OAASI,KAAKH,MAAQG,KAAKJ,OAAS,KAAO,OAWnDG,EAAMQ,gBAAkBP,KAAKQ,QACtBC,EAASA,UAACC,EAAQA,SAACV,KAAKW,aAAcZ,EAC7C,EAED,aAAAa,GACC,MAAMb,EAAQ,CAAC,EAQR,OAPFC,KAAKV,YACHS,EAAAG,SAAWF,KAAKH,MAAQ,QAAU,SAClCE,EAAAM,IAAML,KAAKT,UAAY,KACvBQ,EAAAL,KAAOM,KAAKN,KAAO,KACzBK,EAAMJ,MAAsB,QAAdK,KAAKL,MAAkB,OAASK,KAAKL,MAAQ,KAC3DI,EAAMI,OAASH,KAAKI,SAEdL,CACP,EACD,OAAAK,GACC,OAAOJ,KAAKG,OAASH,KAAKG,OAASA,EAAMA,OAACU,MAC3C,GAED,OAAAC,GACCd,KAAKe,MACL,EACDC,QAAS,CACR,IAAAD,GACCf,KAAKiB,eAELjB,KAAKkB,wBAEAlB,KAAKV,YACRU,KAAKC,UAAYD,KAAKmB,oBAExB,EACD,kBAAAA,GAECnB,KAAKoB,UAAU,IAAMpB,KAAKR,MAAM6B,MAAMC,IACrCtB,KAAKJ,OAAS0B,EAAI1B,OAClBI,KAAKN,KAAO4B,EAAI5B,KAChBM,KAAKL,MAAQ2B,EAAI3B,MACjBK,KAAKuB,WAAU,KACdvB,KAAKwB,gBAAe,GACpB,GAEF,EACD,cAAAA,GAECxB,KAAKyB,mBAAmB,mBAClB,MAAAC,EAAkBC,EAAGC,MAACC,2BAA2B,CAEtDC,WAAY,CAAC,IAAM,IAAM,KAG1BJ,EAAgBK,mBAAmB,CAClC1B,KAAML,KAAKT,YAGZmC,EAAgBM,QAAQ,IAAIhC,KAAKR,QAAe8B,IAC1CtB,KAAAiC,SAASX,EAAIY,mBAAmB7B,IAAG,IAEzCL,KAAK0B,gBAAkBA,CACvB,EACD,QAAAO,CAAS5B,GAEF,MAAAR,EAAQQ,GAAOL,KAAKT,UAC1BS,KAAKH,MAAQA,CACb,EACD,kBAAA4B,CAAmBU,GAEZ,MAAAC,EAAWpC,KAAKmC,GACtBC,GAAYA,EAASC,YACrB,EACD,YAAApB,GACMjB,KAAAT,UAAY+C,EAAAA,MAAMtC,KAAKuC,WAAaD,EAAKA,MAACtC,KAAKwC,gBACpD,EACD,2BAAMtB,GASQ,YAATuB,EAAAA,MAAsBC,OAAOC,EAAAA,MAAMC,QAAU,IAChD5C,KAAKV,WAAY,GAKbU,KAAAV,gBAAkBU,KAAK6C,qBAIf,QAATJ,EAAAA,OACHzC,KAAKV,WAAY,EAOlB,EAED,kBAAAuD,GAGQ,OAAA,IAAIC,SAAmBC,YACzBC,sBAAsBC,GAAGjD,MAAMkD,OAAO,aAAaC,OAAO,CAC7DC,cAAe,CAAC,cACdC,MAAUC,IACZP,EAAQ,WAAaO,EAAE,GAAGpD,SAAQ,GAClC,GAGF,EAGD,mBAAAqD,GAcA,GAED,aAAAC,GACCxD,KAAKyB,mBAAmB,kBACzB,uJC3MFgC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}