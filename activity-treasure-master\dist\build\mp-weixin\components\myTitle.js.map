{"version": 3, "file": "myTitle.js", "sources": ["../../../../src/components/myTitle.vue", "../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvbXlUaXRsZS52dWU"], "sourcesContent": ["<template>\n  <view class=\"title\">\n    <view\n      class=\"pr\"\n      :style=\"{\n        background: bgColor,\n        width: width,\n        height: height,\n      }\"\n    >\n      <!-- 背景图 -->\n      <image\n        v-if=\"img\"\n        :style=\"{\n          width: width,\n          height: height,\n          borderRadius: radius\n        }\"\n        :src=\"img.indexOf('http') === -1 ? store().$state.url + img : img\"\n        :mode=\"mode\"\n        lazy-load\n      ></image>\n      <u-status-bar v-if=\"topBar && !img\" :bgColor=\"bgColor\"></u-status-bar>\n      <!-- 标题 -->\n      <view class=\"pa w\" style=\"top: 0\">\n        <u-status-bar :bgColor=\"bgColor\"></u-status-bar>\n        <view\n          class=\"df aic w pr\"\n          :class=\"[title ? 'jcc' : '']\"\n          :style=\"{ height: 88 + 'rpx', minHeight: 88 + 'rpx' }\"\n        >\n          <!-- 返回按钮 -->\n          <view\n            class=\"pa z20 df aic jcc\"\n            style=\"top: 22rpx; left: 30rpx; width: 88rpx; height: 88rpx\"\n            v-if=\"backShow\"\n          >\n            <u-icon :color=\"backColor\" name=\"arrow-left\" :size=\"size\" @click=\"back\" />\n          </view>\n          <!-- 标题 -->\n          <view\n            class=\"x32\"\n            :style=\"{ color, fontWeight: blod ? '700' : '400' }\"\n            v-if=\"title\"\n          >\n            {{ title }}\n          </view>\n          <slot name=\"right\"></slot>\n          <slot></slot>\n        </view>\n        <!-- 搜索框 -->\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { defineComponent, watch, ref, reactive, nextTick } from \"vue\";\nimport {\n  onLoad,\n  onShow,\n  onReady,\n  onPageScroll,\n  onPullDownRefresh,\n  onReachBottom,\n} from \"@dcloudio/uni-app\";\nimport { store } from \"@/store\";\n\nconst props = defineProps({\n  title: {\n    type: String,\n    default: \"\",\n  },\n  img: {\n    type: String,\n  },\n  width: {\n    type: String,\n    default: \"750rpx\",\n  },\n  height: {\n    type: String,\n    default: \"128rpx\",\n  },\n  bgColor: {\n    type: String,\n    default: \"transparent\",\n  },\n  color: {\n    type: String,\n    default: \"#000\",\n  },\n  blod: {\n    type: Boolean,\n    default: true,\n  },\n  backShow: {\n    type: Boolean,\n    default: true,\n  },\n  mode: {\n    type: String,\n    default: \"aspectFill\",\n  },\n  backColor: {\n    type: String,\n    default: \"#000\",\n  },\n  radius: {\n    type: String,\n  },\n  back: {\n    type: String,\n    default: \"\",\n  },\n  topBar: {\n    type: Boolean,\n    default: true,\n  },\n  size: {\n    type: String,\n    default: \"44rpx\",\n  },\n});\n\nconst rightHeight = ref();\nnextTick(() => {\n  rightHeight.value = pxToRpx(uni.getMenuButtonBoundingClientRect().height);\n});\nconst back = () => {\n  if (props.back == \"switch\") uni.reLaunch({ url: \"/pages/index\" });\n  else\n    uni.navigateBack({\n      delta: 1,\n      fail: (err) => uni.reLaunch({ url: \"/pages/index\" }),\n    });\n};\nconst pxToRpx = (px) => {\n  // 使用新的 API 替代已废弃的 getSystemInfoSync\n  const screenWidth = uni.getWindowInfo().windowWidth;\n  return (750 * Number.parseInt(px)) / screenWidth;\n};\n</script>\n<style scoped lang=\"less\">\n.title {\n  position: relative;\n  width: 100%;\n  display: block;\n  overflow: hidden;\n  /* 确保导航栏有足够的最小高度 */\n  min-height: 200rpx;\n}\n\n.title .pr {\n  position: relative;\n  display: block;\n  /* 确保内容区域有足够的最小高度 */\n  min-height: 200rpx;\n}\n\n.title .pa {\n  position: absolute;\n}\n\n.title .w {\n  width: 100%;\n}\n\n.title .df {\n  display: flex;\n}\n\n.title .aic {\n  align-items: center;\n}\n\n.title .jcc {\n  justify-content: center;\n}\n\n.title .x32 {\n  font-size: 32rpx;\n}\n\n.title .z20 {\n  z-index: 20;\n}\n\n.title .jcc {\n  justify-content: center;\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/myTitle.vue'\nwx.createComponent(Component)"], "names": ["rightHeight", "ref", "nextTick", "value", "pxToRpx", "uni", "index", "getMenuButtonBoundingClientRect", "height", "back", "props", "reLaunch", "url", "navigateBack", "delta", "fail", "err", "px", "screenWidth", "getWindowInfo", "windowWidth", "Number", "parseInt", "wx", "createComponent", "Component"], "mappings": "qjCA6HMA,EAAcC,EAAGA,MACvBC,EAAAA,YAAS,KACPF,EAAYG,MAAQC,EAAQC,EAAGC,MAACC,kCAAkCC,OAAM,IAE1E,MAAMC,EAAO,KACO,UAAdC,EAAMD,KAAkBJ,EAAAA,MAAIM,SAAS,CAAEC,IAAK,iBAE9CP,EAAAA,MAAIQ,aAAa,CACfC,MAAO,EACPC,KAAOC,GAAQX,EAAGC,MAACK,SAAS,CAAEC,IAAK,kBACpC,EAECR,EAAWa,IAEf,MAAMC,EAAcb,EAAAA,MAAIc,gBAAgBC,YACxC,OAAQ,IAAMC,OAAOC,SAASL,GAAOC,CAAA,wiBC3IvCK,GAAGC,gBAAgBC"}