"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),a=require("../../../store/index.js"),r=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const i={__name:"notifications",setup(i){const l=e.ref([]),o=e.ref(!1),s=e.ref(!1),n=e.ref(1),u=e.ref(0),c=e.ref(0);e.onLoad((()=>{v()})),e.onShow((()=>{f()})),e.onReachBottom((()=>{s.value||o.value||d()})),e.onPullDownRefresh((()=>{p()}));const v=async(r=!1)=>{if(!o.value){r&&(n.value=1,s.value=!1),o.value=!0;try{const i=await t.userget_notifications({uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token,page:n.value,page_size:10});if("ok"===(null==i?void 0:i.status)){const e=i.data.list||[];l.value=r?e:[...l.value,...e],u.value=i.data.total||0,c.value=i.data.unread_count||0,e.length<10&&(s.value=!0)}else"empty"===(null==i?void 0:i.status)?(r&&(l.value=[]),s.value=!0):e.index.$u.toast((null==i?void 0:i.msg)||"获取通知失败")}catch(i){console.error("获取通知失败:",i),e.index.$u.toast("获取通知失败")}finally{o.value=!1,r&&e.index.stopPullDownRefresh()}}},d=()=>{n.value++,v()},p=()=>{v(!0)},f=async()=>{try{await t.usermark_notification_read({uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token,notification_id:0})}catch(e){console.error("标记已读失败:",e)}},g=e=>({share_success:"分享成功",receive_vip:"会员领取",activity_update:"活动更新",activity_cancelled:"活动取消",activity_registration:"活动报名",registration_cancelled:"取消报名",system:"系统通知"}[e]||"通知"),h=e=>1===e.is_global||"1"===e.is_global,_=e=>({share_success:"#6AC086",receive_vip:"#D19C69",activity_update:"#5DADE2",system:"#999"}[e]||"#999"),m=e=>{const t=e.replace(/-/g,"/"),a=new Date(t);if(isNaN(a.getTime()))return"时间格式错误";const r=new Date-a;if(r<6e4)return"刚刚";if(r<36e5)return Math.floor(r/6e4)+"分钟前";if(r<864e5)return Math.floor(r/36e5)+"小时前";if(r<6048e5)return Math.floor(r/864e5)+"天前";return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")} ${String(a.getHours()).padStart(2,"0")}:${String(a.getMinutes()).padStart(2,"0")}:${String(a.getSeconds()).padStart(2,"0")}`};return(t,a)=>e.e({a:e.p({title:"我的通知",bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",color:"#ffffff",blod:!0,backColor:"#ffffff",backShow:!0}),b:u.value>0},u.value>0?e.e({c:e.t(u.value),d:c.value>0},c.value>0?{e:e.t(c.value)}:{}):{},{f:l.value.length>0},l.value.length>0?{g:e.f(l.value,((t,a,i)=>{return e.e({a:"49c224d4-1-"+i,b:e.p({name:(l=t.type,{share_success:"share-square",receive_vip:"vip",activity_update:"calendar",system:"bell"}[l]||"bell"),color:_(t.type),size:"32rpx"}),c:_(t.type)+"20",d:e.t(t.title),e:e.t(m(t.created_at)),f:e.t(t.content),g:e.t(g(t.type)),h:h(t)},(h(t),{}),{i:0==t.is_read},(t.is_read,{}),{j:t.id,k:0==t.is_read?1:"",l:e.o((e=>{var a;"activity_update"===(a=t).type&&a.related_id?r.navto(`/pages/bundle/active/activeInfo?id=${a.related_id}`):"share_success"===a.type&&a.related_id&&r.navto("/pages/bundle/user/shareRecords")}),t.id)});var l}))}:o.value?{}:{i:e.p({name:"bell",color:"#ccc",size:"120rpx"})},{h:!o.value,j:o.value&&0===l.value.length},o.value&&0===l.value.length?{k:e.p({mode:"circle",color:"#6AC086",size:"60rpx"})}:{},{l:o.value&&l.value.length>0},o.value&&l.value.length>0?{m:e.p({mode:"circle",color:"#6AC086",size:"40rpx"})}:{},{n:s.value&&l.value.length>0},(s.value&&l.value.length,{}))}};wx.createPage(i);
//# sourceMappingURL=notifications.js.map
