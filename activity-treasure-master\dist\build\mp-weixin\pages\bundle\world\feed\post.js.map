{"version": 3, "file": "post.js", "sources": ["../../../../../../../src/pages/bundle/world/feed/post.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGZlZWRccG9zdC52dWU"], "sourcesContent": ["<script setup>\nimport { ref, reactive, computed } from 'vue';\nimport { publishFeed, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport { onLoad } from '@dcloudio/uni-app';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// --- State Refs ---\nconst content = ref(''); // 动态内容\nconst images = ref([]); // u-upload fileList\nconst location = ref(null); // 地点对象 { name: '', address: '', latitude: 0, longitude: 0 }\nconst tags = ref(''); // 标签字符串 (逗号分隔)\nconst privacy = ref('public'); // 隐私设置: 'public' or 'private'\nconst isPrivate = ref(false); // 是否设为私密\nconst isSubmitting = ref(false); // 是否正在提交中\nconst feedIdToEdit = ref(null); // 用于编辑模式\n\n// --- Computed Properties ---\nconst locationDisplay = computed(() => {\n    return location.value ? (location.value.name || location.value.address) : '添加位置';\n});\n\n// 计算最大上传图片数量（会员4张，非会员1张）\nconst maxImageCount = computed(() => {\n    const userInfo = store().$state.userInfo;\n    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员\n    return isVip ? 4 : 1;\n});\n\n// --- Lifecycle Hooks ---\nonLoad((options) => {\n    if (options && options.id) {\n        feedIdToEdit.value = options.id;\n        // TODO: Fetch feed data for editing\n        console.log(\"Editing feed with ID:\", feedIdToEdit.value);\n        // fetchFeedDataForEdit(feedIdToEdit.value);\n    }\n});\n\n// --- Event Handlers ---\nconst handleClose = () => {\n    uni.navigateBack();\n};\n\n/**\n * u-upload 组件读取文件后的处理函数\n */\nconst handleAfterRead = async (event) => {\n    // 将新选择的文件添加到列表，并标记为上传中\n    let lists = [].concat(event.file);\n    let fileListLen = images.value.length;\n\n    lists.map((item) => {\n        images.value.push({\n            ...item,\n            status: 'uploading',\n            message: '上传中'\n        });\n    });\n\n    // 依次上传新选择的文件\n    for (let i = 0; i < lists.length; i++) {\n        const currentFileIndex = fileListLen + i; // 记录当前处理文件在 images 数组中的索引\n        try {\n            // 调用上传 API - 注意这里不再传递第二个参数\n            const res = await upload_img(lists[i].url);\n            console.log(`Upload result for index ${currentFileIndex}:`, JSON.stringify(res)); // 记录上传结果\n\n            // 检查上传结果\n            if (res.status === 'ok' && res.data) {\n                // 更新文件状态为成功，并保存返回的 URL\n                let item = images.value[currentFileIndex];\n                if (item) { // 确保项目存在\n                    images.value.splice(currentFileIndex, 1, {\n                        ...item,\n                        status: 'success',\n                        message: '',\n                        url: res.data // 使用返回的data字段作为URL\n                    });\n                }\n            } else {\n                // 处理上传成功但返回错误状态的情况\n                if (images.value[currentFileIndex]) {\n                    images.value[currentFileIndex].status = 'failed';\n                    images.value[currentFileIndex].message = res.msg || '上传失败';\n                }\n                console.error(\"Upload API error:\", res);\n                uni.showToast({ title: res.msg || '图片上传失败', icon: 'none' });\n            }\n        } catch (error) {\n            // 处理上传过程中的异常\n            if (images.value[currentFileIndex]) {\n                images.value[currentFileIndex].status = 'failed';\n                images.value[currentFileIndex].message = '上传失败';\n            }\n            console.error(\"Upload exception:\", error);\n            uni.showToast({ title: '图片上传失败，请重试', icon: 'none' });\n        }\n    }\n};\n\n/**\n * u-upload 组件删除图片时的处理函数\n */\nconst handleDeletePic = (event) => {\n    images.value.splice(event.index, 1);\n};\n\n/**\n * 处理添加位置按钮点击事件\n */\nconst handleAddLocation = () => {\n    uni.chooseLocation({\n        success: (res) => {\n            location.value = { // Store location object\n                name: res.name,\n                address: res.address,\n                latitude: res.latitude,\n                longitude: res.longitude\n            };\n        },\n        fail: (err) => {\n            console.error('Choose location failed:', err);\n        }\n    });\n};\n\n/**\n * 处理发布按钮点击事件\n */\nconst handleSubmit = async () => {\n  console.log('handleSubmit triggered!');\n  // 检查用户是否已登录\n  if (!store().$state.userInfo?.uid || !store().$state.userInfo?.token) {\n    uni.showToast({ title: '请先登录', icon: 'none' });\n    return;\n  }\n\n  if (!content.value.trim() && images.value.filter(img => img.status === 'success').length === 0) {\n    uni.showToast({ title: '内容或图片至少要有一个哦', icon: 'none' });\n    return;\n  }\n  if (isSubmitting.value) return;\n\n  isSubmitting.value = true;\n\n  try {\n    // 确保所有图片都已上传成功\n    const pendingUploads = images.value.filter(img => img.status === 'uploading');\n    if (pendingUploads.length > 0) {\n      uni.showToast({ title: '图片上传中，请稍候再试', icon: 'none' });\n      isSubmitting.value = false;\n      return;\n    }\n\n    // 获取所有上传成功的图片URL\n    const uploadedImageUrls = images.value\n      .filter(img => img.status === 'success' && img.url) // 确保URL存在\n      .map(img => img.url);\n\n    console.log('Submitting images:', uploadedImageUrls);\n    console.log('Images type:', Array.isArray(uploadedImageUrls) ? 'Array' : typeof uploadedImageUrls);\n\n    // 准备参数 - 确保与后端API一致\n    const params = {\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      content: content.value.trim(),\n      images: uploadedImageUrls, // 直接传递数组，后端会处理\n      location: location.value ? JSON.stringify({\n        name: location.value.name,\n        address: location.value.address,\n        latitude: location.value.latitude,\n        longitude: location.value.longitude\n      }) : '',\n      tags: tags.value.trim(),\n      privacy: isPrivate.value ? 'private' : 'public', // 使用isPrivate开关\n      type: 'feed' // 标识为动态类型\n    };\n\n    // 详细记录参数信息\n    console.log('Submitting params:', JSON.parse(JSON.stringify(params))); // 记录所有参数\n    console.log('Images param type:', Array.isArray(params.images) ? 'Array' : typeof params.images);\n    console.log('Images param value:', params.images);\n\n    let res;\n    if (feedIdToEdit.value) {\n      // 调用updateFeed API（需要创建/导入）\n      // res = await updateFeed({ ...params, id: feedIdToEdit.value });\n      uni.showToast({ title: '编辑功能待实现', icon: 'none' });\n      isSubmitting.value = false; // 重置提交状态\n      return; // 暂时在这里停止\n    } else {\n      // 调用publishFeed API\n      res = await publishFeed(params);\n    }\n\n    if (res.status === 'ok') {\n      uni.showToast({ title: feedIdToEdit.value ? '修改成功' : '发布成功', icon: 'success' });\n\n      // {{ AURA-X: Add - 发布成功后触发列表页面刷新. Confirmed via 寸止 }}\n      // 触发动态列表刷新\n      uni.$emit('refreshFeedList');\n\n      // 考虑仅在新帖子成功时清除表单\n      // if (!feedIdToEdit.value) { clearForm(); }\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1000);\n    } else if (res.status === 'relogin') {\n      uni.showToast({ title: '请先登录', icon: 'none' });\n    } else {\n      uni.showToast({ title: res.msg || (feedIdToEdit.value ? '修改失败' : '发布失败'), icon: 'none' });\n    }\n  } catch (error) {\n    console.error('Submit feed error:', error);\n    uni.showToast({ title: (feedIdToEdit.value ? '修改失败' : '发布失败') + '，请重试', icon: 'none' });\n  } finally {\n    isSubmitting.value = false;\n  }\n};\n\n</script>\n\n<template>\n  <view class=\"post-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar\n      title=\"动态\"\n      backIcon=\"close\"\n      @back=\"handleClose\"\n    />\n\n    <!-- Main Content Area -->\n    <scroll-view scroll-y class=\"main-content\">\n\n        <!-- 橙色提示条 -->\n        <view class=\"warning-notice\">\n            <u-icon name=\"info-circle-fill\" size=\"14\" color=\"#E6A23C\"></u-icon>\n            <text class=\"notice-text\">请勿发布营销推广内容，违者删封号。</text>\n        </view>\n\n\n        <!-- Content Textarea -->\n        <view class=\"textarea-wrapper\">\n            <u--textarea\n                v-model=\"content\"\n                placeholder=\"发表原创文字，沉淀灵感，留住思考\"\n                height=\"300\"\n                maxlength=\"-1\"\n                border=\"none\"\n                :customStyle=\"{ padding: '32rpx', lineHeight: '1.6', fontSize: '32rpx', color: '#333333', backgroundColor: 'transparent' }\"\n            ></u--textarea>\n        </view>\n\n        <!-- Image Upload -->\n        <view class=\"upload-wrapper\">\n             <u-upload\n                :fileList=\"images\"\n                @afterRead=\"handleAfterRead\"\n                @delete=\"handleDeletePic\"\n                name=\"file\"\n                multiple\n                :maxCount=\"maxImageCount\"\n                :previewImage=\"true\"\n                width=\"200rpx\"\n                height=\"200rpx\"\n                uploadIconColor=\"#ccc\"\n            ></u-upload>\n            <view v-if=\"maxImageCount === 1\" class=\"upload-tip\">\n                <text class=\"tip-text\">非会员最多上传1张图片，升级会员可上传4张</text>\n            </view>\n        </view>\n\n        <!-- {{ AURA-X: Modify - 去除标签选项. Confirmed via 寸止 }} -->\n        <!-- {{ AURA-X: Modify - 统一位置组件样式，与日记页面保持一致. Confirmed via 寸止 }} -->\n        <!-- Options Section -->\n        <view class=\"options-section\">\n            <view class=\"option-item\" @click=\"handleAddLocation\">\n                <u-icon name=\"map\" size=\"16\" color=\"#6AC086\"></u-icon>\n                <text class=\"option-text\" :class=\"{ 'selected': location }\">{{ locationDisplay }}</text>\n                <u-icon name=\"arrow-right\" size=\"14\" color=\"#999\" customStyle=\"margin-left: auto;\"></u-icon>\n            </view>\n             <view class=\"option-item\">\n                 <text class=\"option-text\">设为私密</text>\n                 <view class=\"privacy-switch\">\n                     <u-switch v-model=\"isPrivate\" activeColor=\"#6AC086\" size=\"24\"></u-switch>\n                 </view>\n             </view>\n        </view>\n    </scroll-view>\n\n    <!-- 发布按钮 - 固定在页面底部右侧 -->\n    <view class=\"publish-button-container\">\n      <view\n        class=\"publish-btn\"\n        :class=\"{ 'disabled': isSubmitting || (!content.trim() && images.filter(img => img.status === 'success').length === 0) }\"\n        @click=\"handleSubmit\"\n      >\n        <u-icon name=\"checkmark\" size=\"44rpx\" color=\"#ffffff\" v-if=\"!isSubmitting\"></u-icon>\n        <u-loading-icon v-if=\"isSubmitting\" color=\"#ffffff\" size=\"40rpx\"></u-loading-icon>\n        <text class=\"publish-text\" v-if=\"!isSubmitting\">发布</text>\n      </view>\n    </view>\n\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n/* 统一设计变量 */\n:root {\n  --spacing-md: 24rpx;\n  --spacing-lg: 32rpx;\n  --radius-card: 20rpx;\n  --radius-button: 50rpx;\n  --color-bg-page: #f8f9fa;\n  --color-bg-card: #ffffff;\n  --color-text-title: #333333;\n  --color-text-body: #666666;\n  --color-text-caption: #999999;\n  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);\n}\n\n.post-page {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: var(--color-bg-page);\n}\n\n/* {{ AURA-X: Modify - 缩小提示条高度和字体. Confirmed via 寸止 }} */\n/* 橙色提示条样式 */\n.warning-notice {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 32rpx;\n  margin: 24rpx;\n  background-color: #FFF3CD;\n  border: 1rpx solid #FFEAA7;\n  border-radius: 16rpx;\n\n  .notice-text {\n    margin-left: 12rpx;\n    font-size: 22rpx;\n    color: #856404;\n    line-height: 1.3;\n  }\n}\n\n/* 发布按钮容器 */\n.publish-button-container {\n  position: fixed;\n  bottom: 40rpx;\n  right: 40rpx;\n  z-index: 1000;\n}\n\n/* 发布按钮样式 */\n.publish-btn {\n  width: 120rpx;\n  height: 120rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: var(--radius-button);\n  box-shadow: var(--shadow-card);\n  transition: all 0.3s ease;\n\n  .publish-text {\n    font-size: 24rpx;\n    color: #ffffff;\n    margin-top: 8rpx;\n    font-weight: 500;\n  }\n\n  &.disabled {\n    opacity: 0.6;\n    pointer-events: none;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n/* {{ AURA-X: Modify - 去除卡片样式，扩大输入区域. Confirmed via 寸止 }} */\n.main-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n  padding-bottom: 200rpx; /* 为底部发布按钮留出空间 */\n  background-color: #ffffff;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.textarea-wrapper {\n  margin-bottom: 0;\n  background-color: transparent;\n  border-radius: 0;\n  padding: 0;\n  box-shadow: none;\n  min-height: 40vh;\n\n  :deep(.u-textarea) {\n    font-size: 28rpx;\n    color: var(--color-text-body);\n    line-height: 1.6;\n\n    .u-textarea__field::placeholder {\n      color: var(--color-text-caption);\n      font-size: 28rpx;\n    }\n  }\n}\n\n/* {{ AURA-X: Modify - 去除图片上传卡片样式. Confirmed via 寸止 }} */\n.upload-wrapper {\n  margin-bottom: var(--spacing-lg);\n  background-color: transparent;\n  border-radius: 0;\n  padding: var(--spacing-lg);\n  box-shadow: none;\n\n  :deep(.u-upload__wrap) {\n    gap: var(--spacing-md);\n  }\n\n  :deep(.u-upload__button) {\n    background-color: var(--color-bg-page);\n    border: 2rpx dashed #e0e0e0;\n    border-radius: var(--radius-card);\n    transition: all 0.3s ease;\n  }\n\n  :deep(.u-upload__item) {\n    border-radius: var(--radius-card);\n    overflow: hidden;\n    box-shadow: var(--shadow-card);\n  }\n\n  .upload-tip {\n    margin-top: var(--spacing-md);\n    padding: var(--spacing-md);\n    background-color: #fff3cd;\n    border: 2rpx solid #ffeaa7;\n    border-radius: var(--radius-card);\n\n    .tip-text {\n      font-size: 24rpx;\n      color: #856404;\n      line-height: 1.4;\n    }\n  }\n}\n\n/* {{ AURA-X: Modify - 去除选项区域卡片样式. Confirmed via 寸止 }} */\n.options-section {\n  background-color: transparent;\n  border-radius: 0;\n  box-shadow: none;\n  overflow: hidden;\n  padding: var(--spacing-lg);\n\n  /* {{ AURA-X: Modify - 修改位置组件样式与日记页面保持一致. Confirmed via 寸止 }} */\n  .option-item {\n    display: flex;\n    align-items: center;\n    padding: var(--spacing-lg);\n    border-bottom: 1rpx solid #f0f0f0;\n    transition: background-color 0.3s ease;\n    background-color: #ffffff;\n    border-radius: 12rpx;\n    margin-bottom: 16rpx;\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    .option-text {\n      margin-left: var(--spacing-md);\n      font-size: 30rpx;\n      color: var(--color-text-body);\n\n      &.selected {\n        color: #6AC086;\n        font-weight: 500;\n      }\n    }\n\n    .tag-input {\n      flex: 1;\n      margin-left: var(--spacing-md);\n      font-size: 28rpx;\n      color: var(--color-text-body);\n      text-align: right;\n      background: transparent;\n      border: none;\n      outline: none;\n      width: 100%;\n      max-width: 300rpx;\n\n      &::placeholder {\n        color: var(--color-text-caption);\n      }\n    }\n\n    .privacy-switch {\n      margin-left: auto;\n\n      :deep(.u-radio) {\n        font-size: 28rpx;\n\n        .u-radio__label {\n          color: var(--color-text-body);\n        }\n      }\n\n      :deep(.u-radio--checked) {\n        .u-radio__label {\n          color: #6AC086;\n          font-weight: 500;\n        }\n      }\n\n      :deep(.u-radio__icon-wrap--checked) {\n        background-color: #6AC086 !important;\n        border-color: #6AC086 !important;\n      }\n    }\n  }\n}\n\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/feed/post.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "content", "ref", "images", "location", "tags", "isPrivate", "isSubmitting", "feedIdToEdit", "locationDisplay", "computed", "value", "name", "address", "maxImageCount", "userInfo", "store", "$state", "role_type", "common_vendor", "onLoad", "options", "id", "console", "log", "handleClose", "uni", "index", "navigateBack", "handleAfterRead", "async", "event", "lists", "concat", "file", "fileListLen", "length", "map", "item", "push", "status", "message", "i", "currentFileIndex", "res", "upload_img", "url", "JSON", "stringify", "data", "splice", "msg", "error", "showToast", "title", "icon", "handleDeletePic", "handleAddLocation", "chooseLocation", "success", "latitude", "longitude", "fail", "err", "handleSubmit", "uid", "token", "trim", "filter", "img", "uploadedImageUrls", "Array", "isArray", "params", "privacy", "type", "parse", "publishFeed", "$emit", "setTimeout", "wx", "createPage", "MiniProgramPage"], "mappings": "2+BAKA,MAAMA,EAAe,IAAW,mEAG1B,MAAAC,EAAUC,EAAAA,IAAI,IACdC,EAASD,EAAAA,IAAI,IACbE,EAAWF,EAAAA,IAAI,MACfG,EAAOH,EAAAA,IAAI,IACDA,EAAGA,IAAC,UACd,MAAAI,EAAYJ,EAAAA,KAAI,GAChBK,EAAeL,EAAAA,KAAI,GACnBM,EAAeN,EAAAA,IAAI,MAGnBO,EAAkBC,EAAQA,UAAC,IACtBN,EAASO,MAASP,EAASO,MAAMC,MAAQR,EAASO,MAAME,QAAW,SAIxEC,EAAgBJ,EAAQA,UAAC,KAC3B,MAAMK,EAAWC,EAAAA,QAAQC,OAAOF,SAEhC,OADsC,KAAxB,MAAAA,OAAA,EAAAA,EAAUG,YAA2C,WAAxBH,WAAUG,WACtC,EAAI,CAAA,IAIjBC,EAAAC,QAAEC,IACAA,GAAWA,EAAQC,KACnBd,EAAaG,MAAQU,EAAQC,GAErBC,QAAAC,IAAI,wBAAyBhB,EAAaG,OAEtD,IAIJ,MAAMc,EAAc,KAChBC,EAAGC,MAACC,cAAY,EAMdC,EAAkBC,MAAOC,IAE3B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MACxBC,EAAchC,EAAOQ,MAAMyB,OAEzBJ,EAAAK,KAAKC,IACPnC,EAAOQ,MAAM4B,KAAK,IACXD,EACHE,OAAQ,YACRC,QAAS,OACZ,IAIL,IAAA,IAASC,EAAI,EAAGA,EAAIV,EAAMI,OAAQM,IAAK,CACnC,MAAMC,EAAmBR,EAAcO,EACnC,IAEA,MAAME,QAAYC,EAAUA,WAACb,EAAMU,GAAGI,KAItC,GAHAvB,QAAQC,IAAI,2BAA2BmB,KAAqBI,KAAKC,UAAUJ,IAGxD,OAAfA,EAAIJ,QAAmBI,EAAIK,KAAM,CAE7B,IAAAX,EAAOnC,EAAOQ,MAAMgC,GACpBL,GACOnC,EAAAQ,MAAMuC,OAAOP,EAAkB,EAAG,IAClCL,EACHE,OAAQ,UACRC,QAAS,GACTK,IAAKF,EAAIK,MAGjC,MAEoB9C,EAAOQ,MAAMgC,KACNxC,EAAAQ,MAAMgC,GAAkBH,OAAS,SACxCrC,EAAOQ,MAAMgC,GAAkBF,QAAUG,EAAIO,KAAO,QAEhD5B,QAAA6B,MAAM,oBAAqBR,WAC/BS,UAAU,CAAEC,MAAOV,EAAIO,KAAO,SAAUI,KAAM,QAEzD,OAAQH,GAEDjD,EAAOQ,MAAMgC,KACNxC,EAAAQ,MAAMgC,GAAkBH,OAAS,SACjCrC,EAAAQ,MAAMgC,GAAkBF,QAAU,QAErClB,QAAA6B,MAAM,oBAAqBA,GACnC1B,EAAGC,MAAC0B,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC/C,CACJ,GAMEC,EAAmBzB,IACrB5B,EAAOQ,MAAMuC,OAAOnB,EAAMJ,MAAO,EAAC,EAMhC8B,EAAoB,KACtB/B,EAAAA,MAAIgC,eAAe,CACfC,QAAUf,IACNxC,EAASO,MAAQ,CACbC,KAAMgC,EAAIhC,KACVC,QAAS+B,EAAI/B,QACb+C,SAAUhB,EAAIgB,SACdC,UAAWjB,EAAIiB,UAC/B,EAEQC,KAAOC,IACKxC,QAAA6B,MAAM,0BAA2BW,EAAG,GAEnD,EAMCC,EAAelC,kBAGnB,GAFAP,QAAQC,IAAI,4BAEPR,OAAAA,EAAAA,EAAKA,QAAGC,OAAOF,eAAfC,EAAAA,EAAyBiD,OAAQjD,OAAAA,IAAAA,QAAQC,OAAOF,mBAAUmD,OAK/D,GAAKjE,EAAQU,MAAMwD,QAA0E,IAAhEhE,EAAOQ,MAAMyD,QAAOC,GAAsB,YAAfA,EAAI7B,SAAsBJ,QAIlF,IAAI7B,EAAaI,MAAjB,CAEAJ,EAAaI,OAAQ,EAEjB,IAGE,GADmBR,EAAOQ,MAAMyD,QAAcC,GAAe,cAAfA,EAAI7B,SACnCJ,OAAS,EAG1B,OAFAV,EAAGC,MAAC0B,UAAU,CAAEC,MAAO,cAAeC,KAAM,cAC5ChD,EAAaI,OAAQ,GAKvB,MAAM2D,EAAoBnE,EAAOQ,MAC9ByD,WAA6B,YAAfC,EAAI7B,QAAwB6B,EAAIvB,MAC9CT,KAAIgC,GAAOA,EAAIvB,MAEVvB,QAAAC,IAAI,qBAAsB8C,GAC1B/C,QAAAC,IAAI,eAAgB+C,MAAMC,QAAQF,GAAqB,eAAiBA,GAGhF,MAAMG,EAAS,CACbR,IAAKjD,EAAKA,QAAGC,OAAOF,SAASkD,IAC7BC,MAAOlD,EAAKA,QAAGC,OAAOF,SAASmD,MAC/BjE,QAASA,EAAQU,MAAMwD,OACvBhE,OAAQmE,EACRlE,SAAUA,EAASO,MAAQoC,KAAKC,UAAU,CACxCpC,KAAMR,EAASO,MAAMC,KACrBC,QAAST,EAASO,MAAME,QACxB+C,SAAUxD,EAASO,MAAMiD,SACzBC,UAAWzD,EAASO,MAAMkD,YACvB,GACLxD,KAAMA,EAAKM,MAAMwD,OACjBO,QAASpE,EAAUK,MAAQ,UAAY,SACvCgE,KAAM,QAQJ,IAAA/B,EACJ,GALQrB,QAAAC,IAAI,qBAAsBuB,KAAK6B,MAAM7B,KAAKC,UAAUyB,KACpDlD,QAAAC,IAAI,qBAAsB+C,MAAMC,QAAQC,EAAOtE,QAAU,eAAiBsE,EAAOtE,QACjFoB,QAAAC,IAAI,sBAAuBiD,EAAOtE,QAGtCK,EAAaG,MAKf,OAFAe,EAAGC,MAAC0B,UAAU,CAAEC,MAAO,UAAWC,KAAM,cACxChD,EAAaI,OAAQ,GAIfiC,QAAMiC,cAAYJ,GAGP,OAAf7B,EAAIJ,QACNd,EAAAA,MAAI2B,UAAU,CAAEC,MAAO9C,EAAaG,MAAQ,OAAS,OAAQ4C,KAAM,oBAI/DuB,MAAM,mBAIVC,YAAW,KACTrD,EAAGC,MAACC,cAAY,GACf,MACqB,YAAfgB,EAAIJ,OACbd,EAAGC,MAAC0B,UAAU,CAAEC,MAAO,OAAQC,KAAM,SAErC7B,EAAAA,MAAI2B,UAAU,CAAEC,MAAOV,EAAIO,MAAQ3C,EAAaG,MAAQ,OAAS,QAAS4C,KAAM,QAEnF,OAAQH,GACC7B,QAAA6B,MAAM,qBAAsBA,GACpC1B,EAAAA,MAAI2B,UAAU,CAAEC,OAAQ9C,EAAaG,MAAQ,OAAS,QAAU,OAAQ4C,KAAM,QAClF,CAAY,QACRhD,EAAaI,OAAQ,CACvB,CA7EwB,OAHtBe,EAAGC,MAAC0B,UAAU,CAAEC,MAAO,eAAgBC,KAAM,cAL7C7B,EAAGC,MAAC0B,UAAU,CAAEC,MAAO,OAAQC,KAAM,QAqFvC,umCC1NFyB,GAAGC,WAAWC"}