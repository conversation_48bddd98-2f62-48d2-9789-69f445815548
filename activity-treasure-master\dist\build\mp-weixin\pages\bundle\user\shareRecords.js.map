{"version": 3, "file": "shareRecords.js", "sources": ["../../../../../../src/pages/bundle/user/shareRecords.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHVzZXJcc2hhcmVSZWNvcmRzLnZ1ZQ"], "sourcesContent": ["<script setup>\nimport { ref, reactive, onMounted } from \"vue\";\nimport { onLoad, onShow, onPullDownRefresh, onReachBottom } from \"@dcloudio/uni-app\";\nimport { userget_share_records } from \"@/api\";\nimport { store } from \"@/store\";\nimport { navto } from \"@/utils\";\nimport { requireLogin } from \"@/utils/auth\";\nimport myTitle from \"@/components/myTitle.vue\";\n\nconst shareRecords = ref([]);\nconst loading = ref(false);\nconst finished = ref(false);\nconst page = ref(1);\nconst pageSize = 20;\nconst total = ref(0);\n\nonLoad(() => {\n  getShareRecords();\n});\n\nonShow(() => {\n  // 页面显示时刷新数据\n  refreshData();\n});\n\nonPullDownRefresh(() => {\n  refreshData();\n});\n\nonReachBottom(() => {\n  if (!finished.value && !loading.value) {\n    loadMore();\n  }\n});\n\n// 获取分享记录\nconst getShareRecords = async (isRefresh = false) => {\n  if (loading.value) return;\n\n  // 使用统一的登录校验\n  if (!requireLogin('', '请先登录后查看分享记录')) {\n    return;\n  }\n\n  loading.value = true;\n\n  try {\n    const res = await userget_share_records({\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      page: isRefresh ? 1 : page.value,\n      page_size: pageSize\n    });\n\n    if (res?.status === 'ok') {\n      const newRecords = res.data?.list || [];\n\n      if (isRefresh) {\n        shareRecords.value = newRecords;\n        page.value = 1;\n        finished.value = false;\n      } else {\n        shareRecords.value = [...shareRecords.value, ...newRecords];\n      }\n\n      total.value = res.data?.total || 0;\n\n      // 检查是否还有更多数据\n      if (newRecords.length < pageSize) {\n        finished.value = true;\n      } else {\n        page.value++;\n      }\n    } else {\n      uni.$u.toast(res?.msg || '获取分享记录失败');\n    }\n  } catch (error) {\n    console.error('获取分享记录失败:', error);\n    uni.$u.toast('获取分享记录失败');\n  } finally {\n    loading.value = false;\n    uni.stopPullDownRefresh();\n  }\n};\n\n// 刷新数据\nconst refreshData = () => {\n  page.value = 1;\n  finished.value = false;\n  getShareRecords(true);\n};\n\n// 加载更多\nconst loadMore = () => {\n  getShareRecords(false);\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n  // 修复iOS日期格式问题\n  const formattedTimeStr = timeStr.replace(/-/g, '/');\n  const time = new Date(formattedTimeStr);\n\n  if (isNaN(time.getTime())) {\n    return '时间格式错误';\n  }\n\n  return time.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  const statusMap = {\n    0: '未领取',\n    1: '已领取',\n    2: '已过期'\n  };\n  return statusMap[status] || '未知';\n};\n\n// 获取状态颜色\nconst getStatusColor = (status) => {\n  const colorMap = {\n    0: '#FF9500',\n    1: '#6AC086',\n    2: '#999'\n  };\n  return colorMap[status] || '#999';\n};\n\n// 复制分享链接\nconst copyShareLink = (record) => {\n  if (!record.share_code) {\n    uni.$u.toast('分享码不存在');\n    return;\n  }\n\n  // 获取当前域名或使用默认值\n  const domain = window?.location?.host || 'your-domain.com';\n  const protocol = window?.location?.protocol || 'https:';\n  const shareUrl = `${protocol}//${domain}/pages/bundle/user/trialClaim?code=${record.share_code}`;\n\n  uni.setClipboardData({\n    data: shareUrl,\n    success: () => {\n      uni.$u.toast('分享链接已复制到剪贴板');\n    },\n    fail: () => {\n      uni.$u.toast('复制失败');\n    }\n  });\n};\n</script>\n\n<template>\n  <view class=\"page\">\n    <myTitle\n      title=\"分享记录\"\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n\n    <view class=\"records-container\">\n      <!-- 统计信息 -->\n      <view v-if=\"total > 0\" class=\"stats-card\">\n        <text class=\"stats-text\">共 {{ total }} 条分享记录</text>\n      </view>\n\n      <!-- 分享记录列表 -->\n      <view v-if=\"shareRecords.length > 0\" class=\"records-list\">\n        <view\n          v-for=\"(item, index) in shareRecords\"\n          :key=\"item.id\"\n          class=\"record-item\"\n        >\n          <!-- 记录头部 -->\n          <view class=\"record-header\">\n            <view class=\"record-info\">\n              <text class=\"record-title\">体验会员分享</text>\n              <text class=\"record-time\">{{ formatTime(item.share_time) }}</text>\n            </view>\n            <view class=\"record-status\" :style=\"{ color: getStatusColor(item.status) }\">\n              {{ getStatusText(item.status) }}\n            </view>\n          </view>\n\n          <!-- 记录内容 -->\n          <view class=\"record-content\">\n            <view class=\"record-detail\">\n              <text class=\"detail-label\">体验天数：</text>\n              <text class=\"detail-value\">{{ item.trial_days }}天</text>\n            </view>\n            <view class=\"record-detail\">\n              <text class=\"detail-label\">分享码：</text>\n              <text class=\"detail-value\">{{ item.share_code }}</text>\n            </view>\n            <view v-if=\"item.expire_time\" class=\"record-detail\">\n              <text class=\"detail-label\">过期时间：</text>\n              <text class=\"detail-value\">{{ formatTime(item.expire_time) }}</text>\n            </view>\n            <view v-if=\"item.receive_time\" class=\"record-detail\">\n              <text class=\"detail-label\">领取时间：</text>\n              <text class=\"detail-value\">{{ formatTime(item.receive_time) }}</text>\n            </view>\n          </view>\n\n          <!-- 操作按钮 -->\n          <view v-if=\"item.status === 0\" class=\"record-actions\">\n            <button class=\"action-btn copy-btn\" @click=\"copyShareLink(item)\">\n              复制分享链接\n            </button>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-else-if=\"!loading\" class=\"empty-state\">\n        <u-icon name=\"share-square\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">暂无分享记录</text>\n        <text class=\"empty-desc\">您的分享记录将在这里显示</text>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading && shareRecords.length === 0\" class=\"loading-state\">\n        <u-loading-icon mode=\"circle\" color=\"#6AC086\" size=\"60rpx\"></u-loading-icon>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"loading && shareRecords.length > 0\" class=\"load-more\">\n        <u-loading-icon mode=\"circle\" color=\"#6AC086\" size=\"40rpx\"></u-loading-icon>\n        <text class=\"load-more-text\">加载中...</text>\n      </view>\n\n      <!-- 没有更多 -->\n      <view v-if=\"finished && shareRecords.length > 0\" class=\"no-more\">\n        <text class=\"no-more-text\">没有更多记录了</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"less\">\n.page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);\n  padding-bottom: 40rpx;\n}\n\n.records-container {\n  padding: 20rpx 30rpx;\n  margin-top: 20rpx;\n}\n\n.stats-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);\n  border: 1rpx solid rgba(106, 192, 134, 0.08);\n}\n\n.stats-text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.records-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.record-item {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24rpx;\n  padding: 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);\n  border: 1rpx solid rgba(106, 192, 134, 0.08);\n}\n\n.record-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n}\n\n.record-info {\n  flex: 1;\n}\n\n.record-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 600;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.record-time {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.record-status {\n  font-size: 24rpx;\n  font-weight: 500;\n  background: rgba(106, 192, 134, 0.1);\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n}\n\n.record-content {\n  margin-bottom: 16rpx;\n}\n\n.record-detail {\n  display: flex;\n  margin-bottom: 8rpx;\n}\n\n.detail-label {\n  font-size: 24rpx;\n  color: #666;\n  width: 140rpx;\n  flex-shrink: 0;\n}\n\n.detail-value {\n  font-size: 24rpx;\n  color: #333;\n  flex: 1;\n}\n\n.record-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  font-size: 24rpx;\n  padding: 12rpx 24rpx;\n  border-radius: 20rpx;\n  border: none;\n\n  &.copy-btn {\n    background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n    color: #fff;\n  }\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n  text-align: center;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  font-weight: 500;\n  margin: 24rpx 0 12rpx;\n}\n\n.empty-desc {\n  font-size: 26rpx;\n  color: #ccc;\n  line-height: 1.5;\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 40rpx;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-top: 16rpx;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-more-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-left: 16rpx;\n}\n\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.no-more-text {\n  font-size: 24rpx;\n  color: #999;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/user/shareRecords.vue'\nwx.createPage(MiniProgramPage)"], "names": ["myTitle", "shareRecords", "ref", "loading", "finished", "page", "total", "onLoad", "onShow", "onPullDownRefresh", "onReachBottom", "value", "getShareRecords", "async", "isRefresh", "requireLogin", "res", "userget_share_records", "uid", "store", "$state", "userInfo", "token", "page_size", "status", "newRecords", "_a", "data", "list", "_b", "length", "uni", "index", "$u", "toast", "msg", "error", "console", "stopPullDownRefresh", "refreshData", "loadMore", "formatTime", "timeStr", "formattedTimeStr", "replace", "time", "Date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getStatusColor", "record", "share_code", "domain", "window", "location", "host", "shareUrl", "protocol", "setClipboardData", "success", "fail", "wx", "createPage", "MiniProgramPage"], "mappings": "ioBAOA,MAAMA,EAAU,IAAW,mEAE3B,MAAMC,EAAeC,EAAAA,IAAI,IACnBC,EAAUD,EAAAA,KAAI,GACdE,EAAWF,EAAAA,KAAI,GACfG,EAAOH,EAAAA,IAAI,GAEXI,EAAQJ,EAAAA,IAAI,GAElBK,EAAAA,QAAO,YAIPC,EAAAA,QAAO,YAKPC,EAAAA,mBAAkB,YAIlBC,EAAAA,eAAc,KACPN,EAASO,OAAUR,EAAQQ,UAEhC,IAII,MAAAC,EAAkBC,MAAOC,GAAY,aACzC,IAAIX,EAAQQ,OAGPI,EAAYA,aAAC,GAAI,eAAtB,CAIAZ,EAAQQ,OAAQ,EAEZ,IACI,MAAAK,QAAYC,wBAAsB,CACtCC,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/BjB,KAAMS,EAAY,EAAIT,EAAKM,MAC3BY,UAtCW,KAyCT,GAAgB,QAAhB,MAAAP,OAAA,EAAAA,EAAKQ,QAAiB,CACxB,MAAMC,GAAa,OAAAC,EAAAV,EAAIW,WAAJ,EAAAD,EAAUE,OAAQ,GAEjCd,GACFb,EAAaU,MAAQc,EACrBpB,EAAKM,MAAQ,EACbP,EAASO,OAAQ,GAEjBV,EAAaU,MAAQ,IAAIV,EAAaU,SAAUc,GAGlDnB,EAAMK,OAAQ,OAAAkB,EAAAb,EAAIW,WAAJ,EAAAE,EAAUvB,QAAS,EAG7BmB,EAAWK,OAvDJ,GAwDT1B,EAASO,OAAQ,EAEZN,EAAAM,OAEb,MACMoB,EAAGC,MAACC,GAAGC,OAAM,MAAAlB,OAAA,EAAAA,EAAKmB,MAAO,WAE5B,OAAQC,GACCC,QAAAD,MAAM,YAAaA,GAC3BL,EAAAA,MAAIE,GAAGC,MAAM,WACjB,CAAY,QACR/B,EAAQQ,OAAQ,EAChBoB,EAAGC,MAACM,qBACN,CAxCA,CAwCA,EAIIC,EAAc,KAClBlC,EAAKM,MAAQ,EACbP,EAASO,OAAQ,EACjBC,GAAgB,EAAI,EAIhB4B,EAAW,KACf5B,GAAgB,EAAK,EAIjB6B,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAErB,MAAMC,EAAmBD,EAAQE,QAAQ,KAAM,KACzCC,EAAO,IAAIC,KAAKH,GAEtB,OAAII,MAAMF,EAAKG,WACN,SAGFH,EAAKI,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WACT,EAcGC,EAAkB/B,IACL,CACf,EAAG,UACH,EAAG,UACH,EAAG,QAEWA,IAAW,yRAhBNA,WACH,CAChB,EAAG,MACH,EAAG,MACH,EAAG,OAEYA,IAAW,kOAcR,CAACgC,YACjB,IAACA,EAAOC,WAEV,YADA1B,EAAAA,MAAIE,GAAGC,MAAM,UAKf,MAAMwB,GAAS,OAAAhC,EAAA,MAAAiC,YAAA,EAAAA,OAAQC,eAAR,EAAAlC,EAAkBmC,OAAQ,kBAEnCC,EAAW,IADA,OAAAjC,EAAA,MAAA8B,YAAA,EAAAA,OAAQC,eAAR,EAAA/B,EAAkBkC,WAAY,aACdL,uCAA4CF,EAAOC,aAEpF1B,EAAAA,MAAIiC,iBAAiB,CACnBrC,KAAMmC,EACNG,QAAS,KACPlC,EAAAA,MAAIE,GAAGC,MAAM,cAAa,EAE5BgC,KAAM,KACJnC,EAAAA,MAAIE,GAAGC,MAAM,OAAM,GAEtB,2BAvCmB,IAACV,0XCrHvB2C,GAAGC,WAAWC"}