"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),s=require("../../../store/index.js"),t=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-gap")+e.resolveComponent("u-rate")+e.resolveComponent("u-textarea")+e.resolveComponent("u-icon")+e.resolveComponent("u-upload")+e.resolveComponent("u-button")+e.resolveComponent("u-safe-bottom"))()}Math||(a+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-rate/u-rate.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-safe-bottom/u-safe-bottom.js"))();const a=()=>"../../../components/myLine.js",u={__name:"evaluate",setup(a){const u=e.ref({}),n=e.ref(0);e.onLoad((e=>{u.value=s.store().$state.goodInfo,u.value.goods_info.forEach((e=>{e.fileList=[],e.imgs_url=[],e.star_num=1,e.contents=""}))})),e.onReady((async()=>{const e=await t.getListHeight("bottomBox");n.value=e.height}));const i=e=>{u.value.goods_info[e.name].fileList.splice(e.index,1),u.value.goods_info[e.name].imgs_url.splice(e.index,1)},l=async e=>{let s=u.value.goods_info[e.name].fileList,t=[].concat(e.file),a=s.length;t.map((e=>{s.push({...e,status:"uploading",message:"上传中"})}));for(let n=0;n<t.length;n++){const i=await o.upload_img(t[n].url);let l=s[a];s.splice(a,1,{...l,status:"success",message:"",url:i.data}),a++,u.value.goods_info[e.name].imgs_url.push(i.data)}},r=async()=>{let s=[];u.value.goods_info.forEach((e=>{s.push({goods_id:e.goods_id,guige_id:e.guige_id,star_num:e.star_num,contents:e.contents,imgs_url:e.imgs_url})}));const a=await o.goodsadd_pingjia({order_id:u.value.order_id,pingjia_data:JSON.stringify(s)});"ok"===a.status?t.back({tip:"提交成功，即将返回",time:2e3}):e.index.$u.toast(a.msg)};return(o,s)=>({a:e.f(u.value.goods_info,((o,s,t)=>({a:"6776ca4a-0-"+t,b:e.p({w:"8",h:"30",bg:"#EF6227",c:"#EF6227",title:o.goods_name,size:"32"}),c:"6776ca4a-1-"+t,d:"6776ca4a-2-"+t,e:e.o((e=>o.star_num=e),s),f:e.p({count:"5",activeColor:"#EF6227",modelValue:o.star_num}),g:"6776ca4a-3-"+t,h:"6776ca4a-4-"+t,i:e.o((e=>o.contents=e),s),j:e.p({placeholder:"请输入评价内容~（选填）",count:!0,maxlength:"500",modelValue:o.contents}),k:"6776ca4a-5-"+t,l:"6776ca4a-7-"+t+",6776ca4a-6-"+t,m:e.o(l,s),n:e.o(i,s),o:"6776ca4a-6-"+t,p:e.p({fileList:o.fileList,name:s+"",multiple:!0,maxCount:9,width:"215rpx",height:"215rpx"}),q:"6776ca4a-8-"+t,r:s}))),b:e.p({height:"20rpx"}),c:e.p({height:"20rpx"}),d:e.p({height:"20rpx"}),e:e.p({name:"plus",size:"32",color:"#aaa"}),f:e.p({height:"20rpx"}),g:e.p({height:n.value+10}),h:e.o(r),i:e.p({shape:"circle",color:"linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)",text:"提交",customStyle:{color:"#333"}})})},__runtimeHooks:1};wx.createPage(u);
//# sourceMappingURL=evaluate.js.map
