"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),a=require("../../../../store/index.js"),t=require("../../../../utils/uniShareConfig.js"),n=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/cacheManager.js"),require("../../../../store/counter.js"),require("../../../../components/share-popup/uni-share.js"),require("../../../../components/share-popup/uni-image-menu.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+u)();const u=()=>"../../../../components/share-popup/share-popup.js",l={__name:"detail",setup(u){console.log("日卡详情页面加载");const l=e.ref(null),i=e.ref(!0),r=e.ref(!1),s=e.ref(""),d=e.ref([]),c=e.ref(!1),v=e.ref(1),g=e.ref(!0),m=e.ref(!1),f=e.ref(null),h=e.ref("latest"),p=e.ref(!1),k=e.ref(""),_=e.ref(!1),w=e.ref(null),x=e.ref([]),T=e.ref(0),$=e.ref(!1),y=e.ref(!1),b=e.ref(""),I=e.ref(0),S=e.ref(!1),q=e.ref([]),j=e.ref(0),C=e.ref(!1),L=e.ref(!0),M=e.ref(null);e.computed((()=>{var e;return(null==(e=l.value)?void 0:e.background_image_url)?{backgroundImage:`url(${l.value.background_image_url})`}:{}}));const D=e.computed((()=>f.value?`回复 ${f.value.nickname}`:"写下你的评论...")),F=e.computed((()=>x.value[T.value]||l.value)),z=e.computed((()=>{var e;return(null==(e=F.value)?void 0:e._type)||"card"})),H=e.computed((()=>F.value?F.value.images&&F.value.images.length>0?{backgroundImage:`url(${F.value.images[0]})`,backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"}:F.value.background_image_url?{backgroundImage:`url(${F.value.background_image_url})`}:{}:{}));e.onLoad((async o=>{const a=null==o?void 0:o.cardId;if(!a)return e.index.showToast({title:"无法加载日卡详情",icon:"none"}),void e.index.navigateBack();try{i.value=!0,await U(a),"true"===(null==o?void 0:o.showComments)&&(r.value=!0,J(!0)),"true"===(null==o?void 0:o.share)&&setTimeout((()=>{handleShareFromUtils()}),500),M.value=setTimeout((()=>{L.value=!1}),3e3)}catch(t){console.error("加载日卡详情失败:",t),e.index.showToast({title:"加载失败，请重试",icon:"none"})}finally{i.value=!1}})),e.onMounted((()=>{var e;(null==(e=l.value)?void 0:e.id)&&J(!0)})),e.onShareAppMessage((()=>{var e,o,t,n,u,i,r,s,d;try{return l.value?{title:l.value.description?l.value.description.length>30?l.value.description.substring(0,30)+"...":l.value.description:"分享一张精美日卡",path:`/pages/bundle/world/card/detail?cardId=${l.value.id}`,imageUrl:l.value.background_image_url||(null==(i=null==(u=null==(n=a.store().$state.config)?void 0:n.img_config)?void 0:u.app_logo)?void 0:i.val)||""}:(console.warn("日卡信息未加载完成，使用默认分享信息"),{title:"分享一张精美日卡",path:"/pages/bundle/world/card/index",imageUrl:(null==(t=null==(o=null==(e=a.store().$state.config)?void 0:e.img_config)?void 0:o.app_logo)?void 0:t.val)||""})}catch(c){return console.error("日卡分享配置失败:",c),{title:"分享一张精美日卡",path:"/pages/bundle/world/card/index",imageUrl:(null==(d=null==(s=null==(r=a.store().$state.config)?void 0:r.img_config)?void 0:s.app_logo)?void 0:d.val)||""}}})),e.onShareTimeline((()=>l.value?{title:l.value.description.substring(0,30)+"...",query:`cardId=${l.value.id}`,imageUrl:l.value.background_image_url}:{})),e.onBackPress((({from:e})=>(console.log("返回按键触发，来源:",e),!("backbutton"!==e||!t.isShareMenuShow())&&(t.hideShareMenu(),!0))));const U=async e=>{var t,n;try{const u=await o.getCardDetail({id:e,uid:(null==(t=a.store().$state.userInfo)?void 0:t.uid)||0,token:(null==(n=a.store().$state.userInfo)?void 0:n.token)||""});if("ok"!==u.status)throw new Error(u.msg||"获取日卡详情失败");l.value=u.data,x.value=[{...u.data,_type:"card"}],T.value=0,q.value=[],j.value=0,C.value=!1,y.value=!1,b.value="",S.value=(()=>{const e=a.store().$state.userInfo;return!!e&&1==e.is_huiyuan&&(!e.huiyuan_end_time||new Date(e.huiyuan_end_time.replace(/-/g,"/"))>new Date)})(),console.log("日卡详情加载完成，初始化内容历史栈:",x.value.length)}catch(u){throw console.error("Error loading card detail:",u),u}},Y=async()=>{var t;if(!$.value&&!y.value)try{$.value=!0;const n=a.store().$state.userInfo,u=await o.getRandomQuotesForCard({uid:n.uid,token:n.token});if("ok"===u.status){const e={content:(t=u.data).content,images:t.images||[],user:{nickname:t.author||"佚名",avatar_url:"/static/quote-avatar.png"},created_at:(new Date).toISOString(),location:t.source?`来源：${t.source}`:"",tags:"quote,摘录",_type:"quote",_originalQuote:t};q.value.push({...u.data,adaptedQuote:e,viewTime:Date.now()}),x.value.push(e),T.value=x.value.length-1,j.value=Math.max(j.value,T.value),I.value=u.data.remaining_count,S.value=u.data.is_member,u.data.remaining_count<=0&&(y.value=!0,b.value=u.data.is_member?"您已经刷了二十条了，给自己的眼睛放放假请明天再来吧":"您今天已经刷了五条了，开通会员可以畅享二十条摘录"),console.log("获取新摘录成功，当前历史栈长度:",x.value.length,"缓存数量:",q.value.length)}else"limit"===u.status?(y.value=!0,b.value=u.msg,e.index.showToast({title:u.msg,icon:"none",duration:3e3})):"error"===u.status&&"用户未登录"===u.msg?(e.index.showToast({title:"登录状态已过期，请重新登录",icon:"none"}),setTimeout((()=>{e.index.reLaunch({url:"/pages/login/index"})}),1e3)):e.index.showToast({title:u.msg||"获取摘录失败",icon:"none"})}catch(n){console.error("获取随机摘录失败:",n),e.index.showToast({title:"获取摘录失败",icon:"none"})}finally{$.value=!1}};let B=0,W=0;const A=e=>{B=e.touches[0].clientY,W=Date.now()},E=e=>{},O=e=>{const o=e.changedTouches[0].clientY,a=Date.now(),t=o-B,n=a-W;console.log("触摸结束:",{deltaY:t,deltaTime:n,touchStartY:B,touchEndY:o}),Math.abs(t)>30&&n<800?t<0?(console.log("检测到上滑手势"),P()):(console.log("检测到下滑手势"),Q()):console.log("滑动不满足条件:",{"距离":Math.abs(t),"时间":n,"距离要求":">30px","时间要求":"<800ms"})},P=async()=>{if(console.log("触发上滑事件，当前索引:",T.value,"历史栈长度:",x.value.length),T.value<x.value.length-1)return C.value=!0,$.value=!0,T.value++,console.log("重复查看历史摘录，索引:",T.value),void setTimeout((()=>{$.value=!1}),300);y.value?e.index.showToast({title:b.value,icon:"none",duration:3e3}):(console.log("开始获取新的随机摘录"),C.value=!1,await Y())},Q=()=>{console.log("触发下拉事件，当前索引:",T.value,"会员状态:",S.value),T.value<=0?S.value?e.index.showToast({title:"已经是最初的日卡了",icon:"none",duration:2e3}):e.index.showToast({title:"开通会员可查看浏览过的摘录历史",icon:"none",duration:3e3}):S.value?(C.value=!0,$.value=!0,T.value--,console.log("回看历史摘录，索引:",T.value),setTimeout((()=>{$.value=!1}),300)):e.index.showToast({title:"开通会员可查看浏览过的摘录历史",icon:"none",duration:3e3})},J=async(t=!1)=>{var n,u,i;if(!c.value&&(g.value||t)&&(null==(n=l.value)?void 0:n.id))try{c.value=!0,t&&(v.value=1,d.value=[]);const e=await o.getCardComments({card_id:l.value.id,page:v.value,page_size:10,uid:(null==(u=a.store().$state.userInfo)?void 0:u.uid)||0,token:(null==(i=a.store().$state.userInfo)?void 0:i.token)||"",sort_type:h.value});"ok"===e.status?(d.value=t?e.data.list||[]:[...d.value,...e.data.list||[]],g.value=10===(e.data.list||[]).length,v.value++):"empty"===e.status&&(g.value=!1)}catch(r){console.error("加载评论失败:",r),e.index.showToast({title:"加载评论失败",icon:"none"})}finally{c.value=!1,m.value=!1}},N=()=>{m.value=!0,J(!0)},R=async()=>{if(!n.requireLogin("","请先登录后再点赞"))return;if(!l.value)return;const t=l.value.isLiked,u=l.value.likeCount;l.value.isLiked=!l.value.isLiked,l.value.likeCount+=l.value.isLiked?1:-1;try{console.log("发送点赞请求:",{id:l.value.id,uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token});const n=await o.likeCard({id:l.value.id,uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token});console.log("点赞响应:",n),"ok"!==n.status?(l.value.isLiked=t,l.value.likeCount=u,e.index.showToast({title:n.msg||"操作失败",icon:"none"})):e.index.showToast({title:n.msg||"操作成功",icon:"success"})}catch(i){l.value.isLiked=t,l.value.likeCount=u,console.error("点赞失败:",i),e.index.showToast({title:"操作失败，请重试",icon:"none"})}},G=async()=>{if(!n.requireLogin("","请先登录后再收藏"))return;if(!l.value)return;const t=l.value.isFavorited||!1;l.value.isFavorited=!l.value.isFavorited;try{const n=await o.favoriteCard({id:l.value.id,uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token});"ok"!==n.status?(l.value.isFavorited=t,e.index.showToast({title:n.msg||"操作失败",icon:"none"})):e.index.showToast({title:n.msg||"操作成功",icon:"success"})}catch(u){l.value.isFavorited=t,console.error("收藏失败:",u),e.index.showToast({title:"操作失败，请重试",icon:"none"})}},K=()=>{r.value=!r.value,r.value&&J(!0)},V=o=>{f.value=o,o&&setTimeout((()=>{e.index.pageScrollTo({scrollTop:9999,duration:300})}),100)},X=async()=>{var t;if(s.value.trim()){if(n.requireLogin("","请先登录后再评论"))try{const n=(null==(t=f.value)?void 0:t.id)?parseInt(f.value.id):null;console.log("发送评论请求:",{uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token,card_id:l.value.id,content:s.value,parent_id:n});const u=await o.commentCard({uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token,card_id:l.value.id,content:s.value,parent_id:n});console.log("评论响应:",u),"ok"===u.status?(e.index.showToast({title:"评论成功",icon:"success"}),s.value="",f.value=null,J(!0),l.value.comment_count=(l.value.comment_count||0)+1):e.index.showToast({title:u.msg||"评论失败",icon:"none"})}catch(u){console.error("提交评论失败:",u),e.index.showToast({title:"评论失败，请重试",icon:"none"})}}else e.index.showToast({title:"评论内容不能为空",icon:"none"})},Z=e=>{h.value!==e&&(h.value=e,console.log("切换日卡评论排序方式为:",e),J(!0))},ee=()=>{e.index.navigateBack()},oe=e=>{if(!e)return"";const o=e.replace(/-/g,"/"),a=new Date(o),t=new Date,n=`${String(a.getHours()).padStart(2,"0")}:${String(a.getMinutes()).padStart(2,"0")}:${String(a.getSeconds()).padStart(2,"0")}`;if(a.setHours(0,0,0,0)===t.setHours(0,0,0,0))return`今天 ${n}`;const u=new Date(t);u.setDate(u.getDate()-1);if(a.setHours(0,0,0,0)===u.setHours(0,0,0,0))return`昨天 ${n}`;return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")} ${n}`},ae=o=>{console.log("分享成功:",o),e.index.showToast({title:"分享成功",icon:"success"})},te=o=>{console.error("分享失败:",o),e.index.showToast({title:"分享失败",icon:"none"})},ne=e=>{var o;return!(!e||!(null==(o=a.store().$state.userInfo)?void 0:o.uid))&&e.user_id===a.store().$state.userInfo.uid};return(t,n)=>{var u,i,v,g,x,T,b,I,S,q,j,C,M,U,Y;return e.e({a:e.unref(F)},e.unref(F)?{b:e.unref(F).background_image_url||(null==(u=e.unref(F).images)?void 0:u[0])||"/static/default-card-bg.jpg"}:{},{c:e.unref(F)},e.unref(F)?e.e({d:e.unref(F).background_image_url||(null==(i=e.unref(F).images)?void 0:i[0])||"/static/default-card-bg.jpg",e:e.t(e.unref(F).description||e.unref(F).content),f:e.unref(F).author||(null==(v=e.unref(F).user)?void 0:v.nickname)},e.unref(F).author||(null==(g=e.unref(F).user)?void 0:g.nickname)?e.e({g:e.t(e.unref(F).author||(null==(x=e.unref(F).user)?void 0:x.nickname)),h:e.unref(F).source},e.unref(F).source?{i:e.t(e.unref(F).source)}:{},{j:e.unref(F).location},e.unref(F).location?{k:e.t(e.unref(F).location)}:{}):{},{l:$.value?1:"",m:L.value&&!y.value},(L.value&&y.value,{}),{n:e.o((()=>{}))}):{},{o:"card"===e.unref(z)},"card"===e.unref(z)?{p:e.p({name:"chat",size:"26",color:"rgba(255, 255, 255, 0.9)"}),q:e.t((null==(T=l.value)?void 0:T.comment_count)||0),r:e.o(K),s:e.p({name:(null==(b=l.value)?void 0:b.isLiked)?"heart-fill":"heart",size:"26",color:(null==(I=l.value)?void 0:I.isLiked)?"#ff6b81":"rgba(255, 255, 255, 0.9)"}),t:e.t((null==(S=l.value)?void 0:S.likeCount)||0),v:(null==(q=l.value)?void 0:q.isLiked)?1:"",w:e.o(R),x:e.p({name:(null==(j=l.value)?void 0:j.isFavorited)?"star-fill":"star",size:"26",color:(null==(C=l.value)?void 0:C.isFavorited)?"#FFD700":"rgba(255, 255, 255, 0.9)"}),y:e.o(G)}:{},{z:p.value},p.value?{A:k.value,B:e.o(((...e)=>t.shareToWechatFriend&&t.shareToWechatFriend(...e))),C:e.o(((...e)=>t.shareToWechatMoments&&t.shareToWechatMoments(...e))),D:e.o((e=>t.saveShareImage(k.value))),E:e.o((e=>p.value=!1))}:{},{F:r.value},r.value?e.e({G:e.t((null==(M=l.value)?void 0:M.comment_count)||0),H:"latest"===h.value?1:"",I:e.o((e=>Z("latest"))),J:"hot"===h.value?1:"",K:e.o((e=>Z("hot"))),L:e.o((e=>r.value=!1)),M:e.p({name:"close",size:"20",color:"#999"}),N:0===d.value.length&&!c.value},(0!==d.value.length||c.value,{}),{O:e.f(d.value,((t,n,u)=>{var i,r,s;return e.e({a:(null==(i=t.user)?void 0:i.avatar)||"/static/default-avatar.png",b:e.t((null==(r=t.user)?void 0:r.nickname)||"用户"),c:e.t(oe(t.created_at)),d:t.parent_id},t.parent_id?{e:e.t((null==(s=t.reply_to)?void 0:s.nickname)||"用户")}:{},{f:e.t(t.content),g:e.o((e=>{var o;return V({id:t.id,nickname:(null==(o=t.user)?void 0:o.nickname)||"用户"})}),t.id),h:ne(t)},ne(t)?{i:e.o((n=>(t=>{ne(t)?e.index.showModal({title:"确认删除",content:"删除后无法恢复，确定要删除这条评论吗？",confirmColor:"#6AC086",success:async n=>{if(n.confirm)try{e.index.showLoading({title:"删除中..."});const n={uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token,comment_id:t.id,comment_type:"card"};console.log("删除评论请求参数:",JSON.stringify(n));const u=await o.deleteComment(n);if(console.log("删除评论返回结果:",JSON.stringify(u)),e.index.hideLoading(),"ok"===u.status){e.index.showToast({title:"删除成功",icon:"success"});const o=d.value.findIndex((e=>e.id===t.id));-1!==o&&d.value.splice(o,1),l.value&&(l.value.comment_count=Math.max(0,(l.value.comment_count||0)-1))}else e.index.showToast({title:u.msg||"删除失败",icon:"none"})}catch(u){e.index.hideLoading(),console.error("删除评论出错:",u),e.index.showToast({title:"删除失败，请稍后重试",icon:"none"})}}}):e.index.showToast({title:"您无权删除此评论",icon:"none"})})(t)),t.id)}:{},{j:t.id})})),P:c.value},c.value?{Q:e.p({mode:"circle",size:"24",color:"#999"})}:{},{R:e.o(J),S:m.value,T:e.o(N),U:f.value},f.value?{V:e.t(f.value.nickname),W:e.o((e=>V(null))),X:e.p({name:"close",size:"12",color:"#999"})}:{},{Y:e.unref(D),Z:e.o(X),aa:s.value,ab:e.o((e=>s.value=e.detail.value)),ac:e.o(X)}):{},{ad:e.p({name:"close",color:"rgba(255, 255, 255, 0.9)",size:"20"}),ae:e.o(ee),af:e.o((e=>_.value=!1)),ag:e.o(ae),ah:e.o(te),ai:e.p({show:_.value,title:"分享日卡","share-data":w.value,"show-member-invite":0===(null==(U=e.unref(a.store)().$state.userInfo)?void 0:U.role_type)||1===(null==(Y=e.unref(a.store)().$state.userInfo)?void 0:Y.role_type)}),aj:e.s(e.unref(H)),ak:e.o(ee),al:e.o(A),am:e.o(E),an:e.o(O)})}}},i=e._export_sfc(l,[["__scopeId","data-v-9fb9602d"]]);l.__runtimeHooks=6,wx.createPage(i);
//# sourceMappingURL=detail.js.map
