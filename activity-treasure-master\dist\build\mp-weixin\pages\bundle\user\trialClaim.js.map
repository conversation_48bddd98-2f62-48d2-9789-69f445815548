{"version": 3, "file": "trialClaim.js", "sources": ["../../../../../../src/pages/bundle/user/trialClaim.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHVzZXJcdHJpYWxDbGFpbS52dWU"], "sourcesContent": ["<script setup>\nimport { ref, reactive, onMounted } from \"vue\";\nimport { onLoad } from \"@dcloudio/uni-app\";\nimport { userget_trial_info, userclaim_trial_member, sendNotification } from \"@/api\";\nimport { store } from \"@/store\";\nimport { navto, login, getUserInfo } from \"@/utils\";\nimport { requireLogin } from \"@/utils/auth\";\n\nconst shareCode = ref('');\nconst trialInfo = ref(null);\nconst loading = ref(false);\nconst claiming = ref(false);\nconst claimed = ref(false);\n\nonLoad((options) => {\n  if (options.code) {\n    shareCode.value = options.code;\n    getTrialInfo();\n  } else {\n    uni.$u.toast('分享链接无效');\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 2000);\n  }\n});\n\n// 获取分享信息\nconst getTrialInfo = async () => {\n  loading.value = true;\n\n  try {\n    console.log('开始获取分享信息，分享码:', shareCode.value);\n\n    const res = await userget_trial_info({\n      share_code: shareCode.value\n    });\n\n    console.log('获取分享信息响应:', res);\n\n    if (res?.status === 'ok') {\n      trialInfo.value = res.data;\n      // {{ AURA-X: Add - 对昵称进行HTML解码处理，解决字母乱码问题. Confirmed via 寸止. }}\n      if (trialInfo.value.sharer_nickname) {\n        trialInfo.value.sharer_nickname = decodeHtmlEntities(trialInfo.value.sharer_nickname);\n      }\n      console.log('分享信息获取成功:', trialInfo.value);\n    } else {\n      console.error('获取分享信息失败:', res);\n      uni.$u.toast(res?.msg || '获取分享信息失败');\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 2000);\n    }\n  } catch (error) {\n    console.error('获取分享信息异常:', error);\n    uni.$u.toast('获取分享信息失败');\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 2000);\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 领取体验会员\nconst claimTrialMember = async () => {\n  // 使用统一的登录校验，但保持特殊的重试逻辑\n  if (!requireLogin('', '请先登录后再领取体验会员')) {\n    return;\n  }\n\n  if (claiming.value) return;\n\n  claiming.value = true;\n\n  try {\n    console.log('开始领取体验会员，参数:', {\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token?.substring(0, 8) + '...',\n      share_code: shareCode.value\n    });\n\n    const res = await userclaim_trial_member({\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      share_code: shareCode.value\n    });\n\n    console.log('领取体验会员响应:', res);\n\n    if (res?.status === 'ok') {\n      claimed.value = true;\n      uni.$u.toast('领取成功！');\n\n      // 通知逻辑已在后端处理，无需前端重复发送\n\n      // 更新用户信息\n      getUserInfo();\n\n      // {{ AURA-X: Modify - 修复switchTab错误，使用正确的首页路径. Confirmed via 寸止. }}\n      // 3秒后跳转到首页\n      setTimeout(() => {\n        uni.navigateTo({\n          url: '/pages/index'\n        });\n      }, 3000);\n    } else if (res?.status === 'relogin') {\n      console.error('登录信息验证失败，需要重新登录');\n      uni.$u.toast('登录信息已过期，请重新登录');\n      // 清除登录信息并跳转到登录页面\n      store().$patch({\n        userInfo: {}\n      });\n      setTimeout(() => {\n        login({\n          fun: () => {\n            getUserInfo();\n            setTimeout(() => {\n              claimTrialMember();\n            }, 1000);\n          }\n        });\n      }, 1500);\n    } else {\n      console.error('领取失败:', res);\n      uni.$u.toast(res?.msg || '领取失败');\n    }\n  } catch (error) {\n    console.error('领取异常:', error);\n    uni.$u.toast('领取失败，请重试');\n  } finally {\n    claiming.value = false;\n  }\n};\n\n// {{ AURA-X: Add - HTML解码函数，解决昵称字母乱码问题，适配微信小程序环境. Confirmed via 寸止. }}\nconst decodeHtmlEntities = (str) => {\n  if (!str) return str;\n\n  // 微信小程序环境下的HTML实体解码\n  return str\n    .replace(/&amp;/g, '&')\n    .replace(/&lt;/g, '<')\n    .replace(/&gt;/g, '>')\n    .replace(/&quot;/g, '\"')\n    .replace(/&#39;/g, \"'\")\n    .replace(/&nbsp;/g, ' ');\n};\n\n// {{ AURA-X: Modify - 修复iOS日期格式兼容性问题. Confirmed via 寸止. }}\n// 格式化时间\nconst formatDate = (dateStr) => {\n  if (!dateStr) return '';\n\n  // 将 \"yyyy-MM-dd HH:mm:ss\" 格式转换为 iOS 兼容的格式\n  const isoDateStr = dateStr.replace(/\\s/, 'T');\n  const date = new Date(isoDateStr);\n\n  // 检查日期是否有效\n  if (isNaN(date.getTime())) {\n    console.warn('无效的日期格式:', dateStr);\n    return dateStr; // 返回原始字符串\n  }\n\n  return date.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit'\n  });\n};\n\n// 跳转到登录页面\nconst goToLogin = () => {\n  navto('/pages/bundle/common/login');\n};\n\n// {{ AURA-X: Modify - 修复switchTab错误，使用正确的首页路径. Confirmed via 寸止. }}\n// 跳转到首页\nconst goToHome = () => {\n  uni.navigateTo({\n    url: '/pages/index'\n  });\n};\n</script>\n\n<template>\n  <view class=\"page\">\n    <!-- 简约标题栏 -->\n    <view class=\"header\">\n      <view class=\"back-btn\" @click=\"goToHome\">\n        <u-icon name=\"arrow-left\" color=\"#333\" size=\"20\"></u-icon>\n      </view>\n      <text class=\"header-title\">会员体验券</text>\n      <view class=\"placeholder\"></view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <view class=\"loading-content\">\n        <view class=\"loading-icon\">\n          <u-loading-icon mode=\"circle\" color=\"#333\" size=\"40\"></u-loading-icon>\n        </view>\n        <text class=\"loading-text\">获取信息中...</text>\n      </view>\n    </view>\n\n    <!-- 成功领取状态 -->\n    <view v-else-if=\"claimed\" class=\"success-container\">\n      <view class=\"success-content\">\n        <view class=\"success-icon\">\n          <u-icon name=\"checkmark-circle\" color=\"#4CAF50\" size=\"60\"></u-icon>\n        </view>\n        <text class=\"success-title\">领取成功</text>\n        <text class=\"success-desc\">您已获得 {{ trialInfo?.trial_days }} 天会员体验</text>\n        <view class=\"success-btn\" @click=\"goToHome\">\n          <text class=\"btn-text\">立即体验</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 主要内容 -->\n    <view v-else-if=\"trialInfo\" class=\"main-content\">\n      <!-- 体验券卡片 -->\n      <view class=\"voucher-card\">\n        <view class=\"voucher-header\">\n          <!-- {{ AURA-X: Delete - 移除30天体验会员券前的present图标. Confirmed via 寸止. }} -->\n          <text class=\"voucher-title\">30天会员体验券</text>\n        </view>\n\n        <view class=\"voucher-content\">\n          <text class=\"voucher-desc\">{{ trialInfo.sharer_nickname || '好友' }} 赠送给您</text>\n          <view class=\"voucher-value\">\n            <text class=\"value-text\">{{ trialInfo.trial_days }}</text>\n            <text class=\"value-unit\">天</text>\n          </view>\n        </view>\n\n        <view class=\"voucher-footer\">\n          <text class=\"expire-text\">有效期至 {{ formatDate(trialInfo.expire_time) }}</text>\n        </view>\n      </view>\n\n      <!-- 权益说明 -->\n      <view class=\"benefits-card\">\n        <text class=\"benefits-title\">体验权益</text>\n        <view class=\"benefits-grid\">\n          <view class=\"benefit-item\">\n            <u-icon name=\"checkmark\" color=\"#333\" size=\"16\"></u-icon>\n            <text class=\"benefit-text\">无限制发布活动</text>\n          </view>\n          <view class=\"benefit-item\">\n            <u-icon name=\"checkmark\" color=\"#333\" size=\"16\"></u-icon>\n            <text class=\"benefit-text\">专属客服支持</text>\n          </view>\n          <view class=\"benefit-item\">\n            <u-icon name=\"checkmark\" color=\"#333\" size=\"16\"></u-icon>\n            <text class=\"benefit-text\">高级数据分析</text>\n          </view>\n          <view class=\"benefit-item\">\n            <u-icon name=\"checkmark\" color=\"#333\" size=\"16\"></u-icon>\n            <text class=\"benefit-text\">优先推荐位置</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部操作区 -->\n    <view v-if=\"trialInfo\" class=\"bottom-actions\">\n      <view class=\"action-btn primary\" @click=\"store().$state.userInfo?.uid ? claimTrialMember() : goToLogin()\">\n        <u-loading-icon v-if=\"claiming\" mode=\"circle\" color=\"#fff\" size=\"16\"></u-loading-icon>\n        <text class=\"btn-text\">{{ claiming ? '领取中...' : (store().$state.userInfo?.uid ? '立即领取' : '登录后领取') }}</text>\n      </view>\n      <view class=\"action-btn secondary\" @click=\"goToHome\">\n        <text class=\"btn-text\">稍后再说</text>\n      </view>\n    </view>\n\n    <!-- 使用说明 -->\n    <view v-if=\"trialInfo\" class=\"usage-tips\">\n      <text class=\"tips-text\">• 每个体验券仅限一人领取</text>\n      <text class=\"tips-text\">• 仅限未成为过会员的用户领取</text>\n      <text class=\"tips-text\">• 体验期结束后可选择续费正式会员</text>\n    </view>\n  </view>\n</template>\n\n<style lang=\"less\">\n// {{ AURA-X: Modify - 整个页面向下移动100rpx. Confirmed via 寸止. }}\n.page {\n  min-height: 100vh;\n  background: #FAFAFA;\n  display: flex;\n  flex-direction: column;\n  margin-top: 100rpx;\n}\n\n// 标题栏\n.header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  background: #FFFFFF;\n  border-bottom: 1rpx solid #F0F0F0;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.back-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  background: #F8F8F8;\n  transition: all 0.3s ease;\n\n  &:active {\n    background: #E8E8E8;\n    transform: scale(0.95);\n  }\n}\n\n.header-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.placeholder {\n  width: 60rpx;\n}\n\n// 加载状态\n.loading-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 40rpx;\n}\n\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.loading-icon {\n  margin-bottom: 20rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #666;\n}\n\n// 成功状态\n.success-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 40rpx;\n}\n\n.success-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.success-icon {\n  margin-bottom: 30rpx;\n}\n\n.success-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16rpx;\n}\n\n.success-desc {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 60rpx;\n  line-height: 1.5;\n}\n\n.success-btn {\n  background: #333;\n  border-radius: 50rpx;\n  padding: 24rpx 60rpx;\n  transition: all 0.3s ease;\n\n  &:active {\n    background: #555;\n    transform: scale(0.98);\n  }\n}\n\n.btn-text {\n  font-size: 28rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n// 主要内容\n.main-content {\n  flex: 1;\n  padding: 40rpx 30rpx 0;\n}\n\n// 体验券卡片\n.voucher-card {\n  background: #FFFFFF;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);\n  border: 1rpx solid #F0F0F0;\n}\n\n// {{ AURA-X: Modify - 移除gift-icon样式，简化voucher-header布局. Confirmed via 寸止. }}\n.voucher-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.voucher-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.voucher-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 30rpx;\n}\n\n.voucher-desc {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.voucher-value {\n  display: flex;\n  align-items: baseline;\n}\n\n.value-text {\n  font-size: 48rpx;\n  font-weight: 700;\n  color: #333;\n  line-height: 1;\n}\n\n.value-unit {\n  font-size: 24rpx;\n  color: #666;\n  margin-left: 8rpx;\n}\n\n.voucher-footer {\n  padding-top: 20rpx;\n  border-top: 1rpx solid #F0F0F0;\n}\n\n.expire-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n// 权益卡片\n.benefits-card {\n  background: #FFFFFF;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);\n  border: 1rpx solid #F0F0F0;\n}\n\n.benefits-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 24rpx;\n  display: block;\n}\n\n.benefits-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20rpx;\n}\n\n.benefit-item {\n  display: flex;\n  align-items: center;\n}\n\n.benefit-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-left: 12rpx;\n}\n\n// 底部操作区\n.bottom-actions {\n  padding: 30rpx;\n  background: #FFFFFF;\n  border-top: 1rpx solid #F0F0F0;\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 88rpx;\n  border-radius: 50rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &.primary {\n    background: #333;\n    color: #FFFFFF;\n\n    &:active {\n      background: #555;\n      transform: scale(0.98);\n    }\n  }\n\n  &.secondary {\n    background: #F8F8F8;\n    color: #666;\n\n    &:active {\n      background: #E8E8E8;\n      transform: scale(0.98);\n    }\n  }\n}\n\n.btn-text {\n  margin-left: 8rpx;\n}\n\n// 使用说明\n.usage-tips {\n  padding: 20rpx 30rpx 40rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #999;\n  line-height: 1.5;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/user/trialClaim.vue'\nwx.createPage(MiniProgramPage)"], "names": ["shareCode", "ref", "trialInfo", "loading", "claiming", "claimed", "common_vendor", "onLoad", "options", "code", "value", "uni", "$u", "toast", "setTimeout", "index", "navigateBack", "getTrialInfo", "async", "console", "log", "res", "userget_trial_info", "share_code", "status", "data", "sharer_nickname", "decodeHtmlEntities", "error", "msg", "claimTrialMember", "requireLogin", "uid", "store", "$state", "userInfo", "token", "substring", "userclaim_trial_member", "getUserInfo", "navigateTo", "url", "store_index", "$patch", "login", "fun", "str", "replace", "formatDate", "dateStr", "isoDateStr", "date", "Date", "isNaN", "getTime", "warn", "toLocaleDateString", "year", "month", "day", "goToHome", "navto", "wx", "createPage", "MiniProgramPage"], "mappings": "uqBAQM,MAAAA,EAAYC,EAAAA,IAAI,IAChBC,EAAYD,EAAAA,IAAI,MAChBE,EAAUF,EAAAA,KAAI,GACdG,EAAWH,EAAAA,KAAI,GACfI,EAAUJ,EAAAA,KAAI,GAEdK,EAAAC,QAAEC,IACFA,EAAQC,MACVT,EAAUU,MAAQF,EAAQC,WAG1BE,EAAAA,MAAIC,GAAGC,MAAM,UACbC,YAAW,KACTH,EAAGI,MAACC,cAAY,GACf,KACL,IAIF,MAAMC,EAAeC,UACnBf,EAAQO,OAAQ,EAEZ,IACMS,QAAAC,IAAI,gBAAiBpB,EAAUU,OAEjC,MAAAW,QAAYC,qBAAmB,CACnCC,WAAYvB,EAAUU,QAGhBS,QAAAC,IAAI,YAAaC,GAEL,QAAhB,MAAAA,OAAA,EAAAA,EAAKG,SACPtB,EAAUQ,MAAQW,EAAII,KAElBvB,EAAUQ,MAAMgB,kBAClBxB,EAAUQ,MAAMgB,gBAAkBC,EAAmBzB,EAAUQ,MAAMgB,kBAE/DP,QAAAC,IAAI,YAAalB,EAAUQ,SAE3BS,QAAAS,MAAM,YAAaP,GAC3BV,EAAGI,MAACH,GAAGC,OAAM,MAAAQ,OAAA,EAAAA,EAAKQ,MAAO,YACzBf,YAAW,KACTH,EAAGI,MAACC,cAAY,GACf,KAEN,OAAQY,GACCT,QAAAS,MAAM,YAAaA,GAC3BjB,EAAAA,MAAIC,GAAGC,MAAM,YACbC,YAAW,KACTH,EAAGI,MAACC,cAAY,GACf,IACP,CAAY,QACRb,EAAQO,OAAQ,CAClB,GAIIoB,EAAmBZ,gBAEvB,GAAKa,EAAYA,aAAC,GAAI,kBAIlB3B,EAASM,MAAb,CAEAN,EAASM,OAAQ,EAEb,IACFS,QAAQC,IAAI,eAAgB,CAC1BY,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,SAASC,YAAxBH,EAAAA,EAA+BI,UAAU,EAAG,IAAK,MACxDd,WAAYvB,EAAUU,QAGlB,MAAAW,QAAYiB,yBAAuB,CACvCN,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/Bb,WAAYvB,EAAUU,QAGhBS,QAAAC,IAAI,YAAaC,GAEL,QAAhB,MAAAA,OAAA,EAAAA,EAAKG,SACPnB,EAAQK,OAAQ,EAChBC,EAAAA,MAAIC,GAAGC,MAAM,SAKb0B,EAAAA,cAIAzB,YAAW,KACTH,EAAAA,MAAI6B,WAAW,CACbC,IAAK,gBACN,GACA,MACsB,aAAX,MAALpB,OAAK,EAAAA,EAAAG,SACdL,QAAQS,MAAM,mBACdjB,EAAAA,MAAIC,GAAGC,MAAM,iBAER6B,EAAAT,QAAGU,OAAO,CACbR,SAAU,CAAC,IAEbrB,YAAW,KACT8B,QAAM,CACJC,IAAK,KACHN,EAAAA,cACAzB,YAAW,WAER,IAAI,GAEV,GACA,QAEKK,QAAAS,MAAM,QAASP,GACvBV,EAAGI,MAACH,GAAGC,OAAM,MAAAQ,OAAA,EAAAA,EAAKQ,MAAO,QAE5B,OAAQD,GACCT,QAAAS,MAAM,QAASA,GACvBjB,EAAAA,MAAIC,GAAGC,MAAM,WACjB,CAAY,QACRT,EAASM,OAAQ,CACnB,CA7DoB,CA6DpB,EAIIiB,EAAsBmB,GACrBA,EAGEA,EACJC,QAAQ,SAAU,KAClBA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KACjBA,QAAQ,UAAW,KACnBA,QAAQ,SAAU,KAClBA,QAAQ,UAAW,KATLD,EAcbE,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAGrB,MAAMC,EAAaD,EAAQF,QAAQ,KAAM,KACnCI,EAAO,IAAIC,KAAKF,GAGtB,OAAIG,MAAMF,EAAKG,YACLnC,QAAAoC,KAAK,WAAYN,GAClBA,GAGFE,EAAKK,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,UACPC,IAAK,WACN,EAUGC,EAAW,KACfjD,EAAAA,MAAI6B,WAAW,CACbC,IAAK,gBACN,83BARDoB,EAAKA,MAAC,4EC5KRC,GAAGC,WAAWC"}