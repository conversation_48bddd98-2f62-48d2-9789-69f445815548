{"version": 3, "file": "world.js", "sources": ["../../../../src/pages/world.vue", "../../../../uniPage:/cGFnZXMvd29ybGQudnVl"], "sourcesContent": ["<script setup>\nimport { ref } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app'; // 引入 onLoad\nimport CardIndex from '@/pages/bundle/world/card/index.vue';\nimport FeedIndex from '@/pages/bundle/world/feed/index.vue';\nimport DiaryIndex from '@/pages/bundle/world/diary/index.vue';\nimport QuoteIndex from '@/pages/bundle/world/quote/index.vue';\nimport { getDailyCards } from '@/api/index.js'; // 引入 API\nimport { store } from '@/store'; // 引入 store\nimport { navto } from '@/utils';\nimport { requireLogin } from '@/utils/auth'; // 引入 navto\nimport CustomTabBar from '../components/CustomTabBar.vue';\n\n// 显式注册组件以避免微信小程序的组件加载问题\nconst components = {\n  CardIndex,\n  FeedIndex,\n  DiaryIndex,\n  QuoteIndex,\n  CustomTabBar\n};\n\n// {{ AURA-X: Add - 添加子组件ref引用，用于按需调用数据加载方法. Confirmed via 寸止. }}\n// 子组件引用\nconst feedIndexRef = ref(null);\nconst diaryIndexRef = ref(null);\nconst quoteIndexRef = ref(null);\n\n// 调试：检查组件是否正确导入\nconsole.log('world.vue: 组件导入检查', {\n  CardIndex: !!CardIndex,\n  FeedIndex: !!FeedIndex,\n  DiaryIndex: !!DiaryIndex,\n  QuoteIndex: !!QuoteIndex\n});\n\n// 当前选中的 Tab 索引 (0: 日卡, 1: 动态)\nconst currentTab = ref(0);\n// Tab 列表数据\nconst tabs = ref([\n  { name: '日卡' },\n  { name: '动态' },\n  { name: '日记' },\n  { name: '摘录' }\n]);\n\n// --- Card Data State (Moved from CardIndex) ---\nconst dailyCardsData = ref([]);\nconst isCardLoading = ref(true);\nconst cardError = ref('');\n\n// FAB State\nconst showFabOptions = ref(false);\nconst isFabRotated = ref(false);\n\n// --- Utility Functions (Moved from CardIndex - formatYYYYMMDD needed here) ---\n/**\n * 格式化日期为 YYYY-MM-DD\n * @param {Date} date - Date 对象\n * @returns {string} 格式化后的日期字符串\n */\nconst formatYYYYMMDD = (date) => {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n}\n\n/**\n * 加载日卡数据\n */\nconst loadCardData = async () => {\n    // Optional: Prevent re-fetch if data exists and not explicitly refreshing\n    // if (dailyCardsData.value.length > 0 && !forceRefresh) return;\n    console.log(\"world.vue: loadCardData triggered\"); // 添加日志\n    isCardLoading.value = true;\n    cardError.value = '';\n    try {\n        // --- 修改日期范围 ---\n        const endDate = new Date(); // 结束日期为今天\n        const startDate = new Date();\n        startDate.setDate(endDate.getDate() - 15); // 开始日期为今天往前15天\n        // --- 结束修改 ---\n\n        const params = {\n            startDate: formatYYYYMMDD(startDate),\n            endDate: formatYYYYMMDD(endDate),\n            uid: store().$state.userInfo?.uid || 0,\n            token: store().$state.token || ''\n        };\n\n        console.log(\"world.vue: Calling getDailyCards with params:\", params); // 添加日志\n\n        // 添加重试逻辑\n        let retryCount = 0;\n        const maxRetries = 3;\n        let res;\n\n        while (retryCount < maxRetries) {\n            try {\n                res = await getDailyCards(params);\n                console.log(\"world.vue: API response:\", res); // 添加日志\n                break; // 如果成功获取数据，跳出循环\n            } catch (retryError) {\n                retryCount++;\n                console.error(`world.vue: Retry ${retryCount}/${maxRetries} failed:`, retryError);\n                if (retryCount >= maxRetries) {\n                    throw retryError; // 重试次数用完，抛出错误\n                }\n                // 等待一段时间再重试\n                await new Promise(resolve => setTimeout(resolve, 1000));\n            }\n        }\n\n        if (res.status === 'ok' && res.data?.cards) {\n          console.log(\"world.vue: Successfully received card data:\", res.data.cards);\n          console.log(\"world.vue: Card data length:\", res.data.cards.length);\n          if (res.data.cards.length > 0) {\n            console.log(\"world.vue: First card sample:\", res.data.cards[0]);\n          }\n          // 按日期排序\n          dailyCardsData.value = res.data.cards.sort((a, b) => new Date(a.date) - new Date(b.date));\n        } else if (res.status === 'empty') {\n            console.log(\"world.vue: API returned empty status\");\n            dailyCardsData.value = [];\n            cardError.value = '暂无日卡数据';\n        } else {\n          console.warn(\"world.vue: API returned error status:\", res.status, res.msg);\n          dailyCardsData.value = [];\n          cardError.value = res.msg || '获取日卡失败';\n        }\n    } catch (error) {\n        console.error('world.vue: Error loading card data:', error);\n        dailyCardsData.value = [];\n        // 检查错误是否是因为 API 函数未定义\n        if (error.message.includes('getDailyCards is not a function') || error.message.includes('undefined')) {\n            cardError.value = 'API函数缺失，请手动修复api/index.js';\n        } else {\n            cardError.value = '加载失败，请稍后重试';\n        }\n\n        // 不使用模拟数据，保持空状态\n        dailyCardsData.value = [];\n    } finally {\n        isCardLoading.value = false;\n    }\n}\n\n\n\n/**\n * 处理 Tab 切换事件\n * @param {number | object} indexOrEvent - u-tabs 切换事件返回的索引或事件对象\n */\nconst handleTabChange = (indexOrEvent) => {\n  // u-tabs 返回的可能是索引值，也可能是包含 index 的对象\n  const index = typeof indexOrEvent === 'number' ? indexOrEvent : indexOrEvent.index;\n\n  // 防止重复切换到同一个tab\n  if (currentTab.value === index) {\n    console.log(`Tab ${index} 已经是当前选中状态，跳过切换`);\n    return;\n  }\n\n  console.log(`Tab切换: ${currentTab.value} -> ${index}`);\n  currentTab.value = index;\n\n  // 根据tab索引加载对应数据\n  switch(index) {\n    case 0: // 日卡\n      console.log('切换到日卡tab，加载日卡数据');\n      loadCardData();\n      break;\n    case 1: // 动态\n      console.log('切换到动态tab，加载动态数据');\n      loadFeedData();\n      break;\n    case 2: // 日记\n      console.log('切换到日记tab，加载日记数据');\n      loadDiaryData();\n      break;\n    case 3: // 摘录\n      console.log('切换到摘录tab，加载摘录数据');\n      loadQuoteData();\n      break;\n    default:\n      console.warn(`未知的tab索引: ${index}`);\n      break;\n  }\n};\n\n// {{ AURA-X: Modify - 修改为调用子组件的数据加载方法. Confirmed via 寸止. }}\n// 加载动态数据（修复实现）\nconst loadFeedData = async () => {\n  try {\n    console.log('开始加载动态数据');\n\n    // 检查用户登录状态\n    const userInfo = store().$state.userInfo;\n    if (!userInfo || !userInfo.uid) {\n      console.warn('用户未登录，但仍可查看动态数据');\n      // 允许未登录用户查看动态，但某些功能会受限\n    }\n\n    // 调用子组件的数据加载方法\n    if (feedIndexRef.value && feedIndexRef.value.loadFeedData) {\n      console.log('调用FeedIndex组件的loadFeedData方法');\n      feedIndexRef.value.loadFeedData();\n    } else {\n      console.warn('FeedIndex组件ref未准备好');\n    }\n\n  } catch (error) {\n    console.error('加载动态数据失败:', error);\n    uni.showToast({\n      title: '加载失败',\n      icon: 'none'\n    });\n  }\n};\n\n// {{ AURA-X: Modify - 修改为调用子组件的数据加载方法. Confirmed via 寸止. }}\n// 加载日记数据（在当前页面内切换）\nconst loadDiaryData = async () => {\n  try {\n    console.log('world.vue: 切换到日记tab，加载日记数据');\n\n    // 检查用户登录状态\n    const userInfo = store().$state.userInfo;\n    if (!userInfo || !userInfo.uid) {\n      console.warn('world.vue: 用户未登录，但仍可查看日记数据');\n      // 允许未登录用户查看日记，但某些功能会受限\n    }\n\n    // 调用子组件的数据加载方法\n    if (diaryIndexRef.value && diaryIndexRef.value.loadDiaryData) {\n      console.log('调用DiaryIndex组件的loadDiaryData方法');\n      diaryIndexRef.value.loadDiaryData();\n    } else {\n      console.warn('DiaryIndex组件ref未准备好');\n    }\n\n  } catch (error) {\n    console.error('world.vue: 加载日记数据失败:', error);\n    uni.showToast({\n      title: '加载失败',\n      icon: 'none'\n    });\n  }\n};\n\n// {{ AURA-X: Modify - 修改为调用子组件的数据加载方法. Confirmed via 寸止. }}\n// 加载摘录数据（在当前页面内切换）\nconst loadQuoteData = async () => {\n  try {\n    console.log('world.vue: 切换到摘录tab，加载摘录数据');\n\n    // 检查用户登录状态\n    const userInfo = store().$state.userInfo;\n    if (!userInfo || !userInfo.uid) {\n      console.warn('world.vue: 用户未登录，但仍可查看摘录数据');\n      // 允许未登录用户查看摘录，但某些功能会受限\n    }\n\n    // 调用子组件的数据加载方法\n    if (quoteIndexRef.value && quoteIndexRef.value.loadQuoteData) {\n      console.log('调用QuoteIndex组件的loadQuoteData方法');\n      quoteIndexRef.value.loadQuoteData();\n    } else {\n      console.warn('QuoteIndex组件ref未准备好');\n    }\n\n  } catch (error) {\n    console.error('world.vue: 加载摘录数据失败:', error);\n    uni.showToast({\n      title: '加载失败',\n      icon: 'none'\n    });\n  }\n};\n\n// FAB Actions\nconst toggleFabMenu = () => {\n    console.log('FAB按钮被点击，当前状态:', { isFabRotated: isFabRotated.value, showFabOptions: showFabOptions.value });\n    isFabRotated.value = !isFabRotated.value;\n    showFabOptions.value = !showFabOptions.value;\n    console.log('FAB按钮状态更新后:', { isFabRotated: isFabRotated.value, showFabOptions: showFabOptions.value });\n};\n\nconst closeFabMenu = () => {\n    isFabRotated.value = false;\n    showFabOptions.value = false;\n};\n\nconst handleFabOptionClick = (option) => {\n    console.log('FAB Option Clicked:', option);\n    closeFabMenu(); // Close menu after selection\n\n    // 使用统一的登录校验\n    if (!requireLogin('', '请先登录后再发布内容')) {\n        return;\n    }\n\n    switch (option) {\n        case 'feed':\n            try {\n                navto('/pages/bundle/world/feed/post');\n            } catch (navError) {\n                console.error('导航到动态发布页面失败:', navError);\n                uni.showToast({\n                    title: '页面跳转失败',\n                    icon: 'none'\n                });\n            }\n            break;\n        case 'diary':\n            try {\n                console.log('跳转到日记发布页面');\n                navto('/pages/bundle/world/diary/post');\n            } catch (navError) {\n                console.error('导航到日记发布页面失败:', navError);\n                uni.showToast({\n                    title: '页面跳转失败',\n                    icon: 'none'\n                });\n            }\n            break;\n        case 'quote':\n            try {\n                console.log('跳转到摘录发布页面');\n                navto('/pages/bundle/world/quote/post');\n            } catch (navError) {\n                console.error('导航到摘录发布页面失败:', navError);\n                uni.showToast({\n                    title: '页面跳转失败',\n                    icon: 'none'\n                });\n            }\n            break;\n        default:\n            console.warn('未知的FAB选项:', option);\n    }\n};\n\n// --- Lifecycle Hooks ---\nonLoad(() => {\n    console.log(\"world.vue: onLoad triggered. Current tab:\", currentTab.value);\n    console.log(\"world.vue: Store state:\", {\n        userInfo: store().$state.userInfo,\n        token: store().$state.token,\n        uid: store().$state.userInfo?.uid\n    });\n\n    // {{ AURA-X: Modify - 默认只加载日卡数据，按需加载其他数据. Confirmed via 寸止. }}\n    // 默认只加载日卡数据，其他数据在切换tab时按需加载\n    console.log(\"world.vue: Loading card data on onLoad (default)\");\n    loadCardData();\n});\n\n</script>\n\n<template>\n  <view class=\"world-page-container\" @click.self=\"closeFabMenu\">\n    <!-- 页面内层包裹，用于整体添加 padding-top -->\n    <view class=\"world-inner-wrapper\">\n      <!-- 顶部粘性区域，包含 Tab 导航 -->\n      <!-- Removed :customStyle=\"{ 'margin-top': '40rpx' }\" -->\n      <u-sticky offset-top=\"0\">\n        <!-- Tab 导航栏 -->\n        <!-- Removed style=\"padding-top: 10rpx;\" -->\n        <view class=\"world-navbar\">\n          <!-- uview-plus 的 Tab 组件 -->\n          <u-tabs\n            :list=\"tabs\"\n            :current=\"currentTab\"\n            @change=\"handleTabChange\"\n            lineColor=\"#FFD700\"\n            :activeStyle=\"{\n              color: '#000000',\n              fontWeight: 'bold'\n            }\"\n            :inactiveStyle=\"{\n              color: '#666666'\n            }\"\n            itemStyle=\"padding-left: 15px; padding-right: 15px; height: 48px;\"\n          ></u-tabs>\n        </view>\n      </u-sticky>\n\n      <!-- 内容区域，根据选中的 Tab 显示不同组件 -->\n      <view class=\"world-content\">\n        <!-- 使用 v-show 保持组件实例，避免v-if导致的渲染问题 -->\n        <view v-show=\"currentTab === 0\" class=\"tab-content\">\n          <!-- 日卡页面组件 - 传递 Props -->\n          <CardIndex\n            :cards=\"dailyCardsData\"\n            :loading=\"isCardLoading\"\n            :error=\"cardError\"\n          />\n        </view>\n        <view v-show=\"currentTab === 1\" class=\"tab-content\">\n          <!-- 动态页面组件 -->\n          <FeedIndex ref=\"feedIndexRef\" />\n        </view>\n        <view v-show=\"currentTab === 2\" class=\"tab-content\">\n          <!-- 日记页面组件 -->\n          <DiaryIndex ref=\"diaryIndexRef\" />\n        </view>\n        <view v-show=\"currentTab === 3\" class=\"tab-content\">\n          <!-- 摘录页面组件 -->\n          <QuoteIndex ref=\"quoteIndexRef\" />\n        </view>\n      </view>\n\n      <!-- New FAB Container -->\n      <view class=\"fab-container\">\n          <!-- Options (conditionally rendered) -->\n          <view v-if=\"showFabOptions\" class=\"fab-options\">\n              <view class=\"fab-option option-feed\" :class=\"{ visible: showFabOptions }\" @click=\"handleFabOptionClick('feed')\">\n                  <u-icon name=\"chat\" color=\"#6AC086\" size=\"20\"></u-icon>\n                  <text class=\"fab-option-text\">动态</text>\n              </view>\n              <view class=\"fab-option option-diary\" :class=\"{ visible: showFabOptions }\" @click=\"handleFabOptionClick('diary')\">\n                  <u-icon name=\"edit-pen\" color=\"#6AC086\" size=\"20\"></u-icon>\n                  <text class=\"fab-option-text\">日记</text>\n              </view>\n              <view class=\"fab-option option-quote\" :class=\"{ visible: showFabOptions }\" @click=\"handleFabOptionClick('quote')\">\n                  <u-icon name=\"bookmark\" color=\"#6AC086\" size=\"20\"></u-icon>\n                  <text class=\"fab-option-text\">摘录</text>\n              </view>\n          </view>\n\n          <!-- Main FAB Button -->\n          <view class=\"fab-main\" :class=\"{ rotated: isFabRotated }\" @click.stop=\"toggleFabMenu\">\n              <u-icon name=\"edit-pen\" color=\"#ffffff\" size=\"28\"></u-icon>\n          </view>\n      </view>\n\n    </view>\n\n    <!-- 自定义底部导航栏 -->\n    <CustomTabBar :current=\"1\" />\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.world-page-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh; // Ensure full height\n  background-color: #F8F9FA; // Use light gray background from spec\n  // 为自定义底部导航栏预留空间 - 优化：考虑发布按钮额外高度\n  padding-bottom: calc(140rpx + constant(safe-area-inset-bottom));\n  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));\n  // 确保页面可以完整滚动\n  box-sizing: border-box;\n}\n\n.world-inner-wrapper {\n    padding-top: 50rpx; // 整体内容下移 50rpx\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    overflow: hidden; // Prevent potential double scrollbars\n}\n\n.world-navbar {\n  background-color: #FFFFFF; // White background for navbar\n  // Add border bottom if needed, u-tabs might have its own\n   border-bottom: 1px solid #f0f0f0;\n}\n\n.world-content {\n  flex: 1; // Takes remaining height\n  overflow-y: auto; // Allows scrolling within content area if needed\n}\n\n.tab-content {\n  height: 100%;\n  width: 100%;\n}\n\n.fab-container {\n  position: fixed;\n  right: 40rpx;\n  // 修复：上移50rpx并提高z-index确保在底部导航栏之上\n  bottom: calc(170rpx + env(safe-area-inset-bottom)); // 原120rpx + 50rpx上移\n  z-index: 1100; // 提高层级，确保在底部导航栏(z-index: 1000)之上\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.fab-main {\n  width: 112rpx;\n  height: 112rpx;\n  background: linear-gradient(135deg, #84fab0, #8fd3f4); // Green/Blue gradient\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);\n  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); // Smooth rotation with overshoot\n  z-index: 1101; // 提高层级，确保按钮可点击\n  // 添加点击区域保护\n  pointer-events: auto;\n\n  &.rotated {\n    transform: rotate(135deg);\n  }\n}\n\n.fab-options {\n    position: absolute;\n    bottom: 130rpx; // Position above the main FAB\n    left: 50%;\n    transform: translateX(-50%);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 25rpx; // Space between option buttons\n    z-index: 1100; // 确保选项菜单也在底部导航栏之上\n}\n\n.fab-option {\n    width: 88rpx;\n    height: 88rpx;\n    background-color: rgba(255, 255, 255, 0.95);\n    border-radius: 50%;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n    opacity: 0;\n    transform: scale(0.5) translateY(20px);\n    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);\n    // 确保选项按钮可点击\n    pointer-events: auto;\n    z-index: 1101;\n\n    // Staggered animation delay\n    &.option-feed { transition-delay: 0.05s; }\n    &.option-diary { transition-delay: 0.1s; }\n    &.option-quote { transition-delay: 0.15s; }\n\n    &.visible {\n        opacity: 1;\n        transform: scale(1) translateY(0);\n    }\n}\n\n.fab-option-text {\n    font-size: 20rpx;\n    color: #6AC086;\n    margin-top: 4rpx;\n    font-weight: 500;\n}\n\n/* Remove or comment out the old .fab-post style if it exists */\n// .fab-post { ... }\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/world.vue'\nwx.createPage(MiniProgramPage)"], "names": ["CardIndex", "FeedIndex", "DiaryIndex", "QuoteIndex", "CustomTabBar", "feedIndexRef", "ref", "diaryIndexRef", "quoteIndexRef", "console", "log", "currentTab", "tabs", "name", "dailyCardsData", "isCardLoading", "cardError", "showFabOptions", "isFabRotated", "formatYYYYMMDD", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "loadCardData", "async", "value", "endDate", "Date", "startDate", "setDate", "params", "uid", "store", "$state", "userInfo", "token", "retryCount", "maxRetries", "res", "getDailyCards", "retryError", "error", "Promise", "resolve", "setTimeout", "status", "_b", "data", "cards", "length", "sort", "a", "b", "warn", "msg", "message", "includes", "handleTabChange", "indexOrEvent", "index", "loadFeedData", "uni", "showToast", "title", "icon", "loadDiaryData", "loadQuoteData", "toggleFabMenu", "closeFabMenu", "handleFabOptionClick", "option", "requireLogin", "navto", "navError", "onLoad", "store_index", "_a", "wx", "createPage", "MiniProgramPage"], "mappings": "6oBAGA,MAAMA,EAAY,IAAW,+BACvBC,EAAY,IAAW,+BACvBC,EAAa,IAAW,gCACxBC,EAAa,IAAW,gCAKxBC,EAAe,IAAW,2DAa1B,MAAAC,EAAeC,EAAAA,IAAI,MACnBC,EAAgBD,EAAAA,IAAI,MACpBE,EAAgBF,EAAAA,IAAI,MAG1BG,QAAQC,IAAI,oBAAqB,CAC/BV,YAAaA,EACbC,YAAaA,EACbC,aAAcA,EACdC,aAAcA,IAIV,MAAAQ,EAAaL,EAAAA,IAAI,GAEjBM,EAAON,EAAAA,IAAI,CACf,CAAEO,KAAM,MACR,CAAEA,KAAM,MACR,CAAEA,KAAM,MACR,CAAEA,KAAM,QAIJC,EAAiBR,EAAAA,IAAI,IACrBS,EAAgBT,EAAAA,KAAI,GACpBU,EAAYV,EAAAA,IAAI,IAGhBW,EAAiBX,EAAAA,KAAI,GACrBY,EAAeZ,EAAAA,KAAI,GAQnBa,EAAkBC,GAIb,GAHMA,EAAKC,iBACJC,OAAOF,EAAKG,WAAa,GAAGC,SAAS,EAAG,QAC1CF,OAAOF,EAAKK,WAAWD,SAAS,EAAG,OAO7CE,EAAeC,kBAGjBlB,QAAQC,IAAI,qCACZK,EAAca,OAAQ,EACtBZ,EAAUY,MAAQ,GACd,IAEM,MAAAC,EAAU,IAAIC,KACdC,EAAY,IAAID,KACtBC,EAAUC,QAAQH,EAAQJ,UAAY,IAGtC,MAAMQ,EAAS,CACXF,UAAWZ,EAAeY,GAC1BF,QAASV,EAAeU,GACxBK,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,MAAOH,EAAKA,QAAGC,OAAOE,OAAS,IAG3B7B,QAAAC,IAAI,gDAAiDuB,GAG7D,IAAIM,EAAa,EACjB,MAAMC,EAAa,EACf,IAAAC,EAEJ,KAAOF,EAAaC,GACZ,IACMC,QAAMC,gBAAcT,GAClBxB,QAAAC,IAAI,2BAA4B+B,GACxC,KACH,OAAQE,GAGL,GAFAJ,IACA9B,QAAQmC,MAAM,oBAAoBL,KAAcC,YAAsBG,GAClEJ,GAAcC,EACR,MAAAG,QAGJ,IAAIE,SAAQC,GAAWC,WAAWD,EAAS,MACrD,CAGe,OAAfL,EAAIO,SAAmB,OAAAC,EAAIR,EAAAS,eAAMC,QACnC1C,QAAQC,IAAI,8CAA+C+B,EAAIS,KAAKC,OACpE1C,QAAQC,IAAI,+BAAgC+B,EAAIS,KAAKC,MAAMC,QACvDX,EAAIS,KAAKC,MAAMC,OAAS,GAC1B3C,QAAQC,IAAI,gCAAiC+B,EAAIS,KAAKC,MAAM,IAG9DrC,EAAec,MAAQa,EAAIS,KAAKC,MAAME,MAAK,CAACC,EAAGC,IAAM,IAAIzB,KAAKwB,EAAElC,MAAQ,IAAIU,KAAKyB,EAAEnC,SAC3D,UAAfqB,EAAIO,QACXvC,QAAQC,IAAI,wCACZI,EAAec,MAAQ,GACvBZ,EAAUY,MAAQ,WAEpBnB,QAAQ+C,KAAK,wCAAyCf,EAAIO,OAAQP,EAAIgB,KACtE3C,EAAec,MAAQ,GACbZ,EAAAY,MAAQa,EAAIgB,KAAO,SAElC,OAAQb,GACGnC,QAAAmC,MAAM,sCAAuCA,GACrD9B,EAAec,MAAQ,GAEnBgB,EAAMc,QAAQC,SAAS,oCAAsCf,EAAMc,QAAQC,SAAS,aACpF3C,EAAUY,MAAQ,4BAElBZ,EAAUY,MAAQ,aAItBd,EAAec,MAAQ,EAC/B,CAAc,QACNb,EAAca,OAAQ,CAC1B,GASEgC,EAAmBC,IAEvB,MAAMC,EAAgC,iBAAjBD,EAA4BA,EAAeA,EAAaC,MAGzE,GAAAnD,EAAWiB,QAAUkC,EASzB,OAJArD,QAAQC,IAAI,UAAUC,EAAWiB,YAAYkC,KAC7CnD,EAAWiB,MAAQkC,EAGZA,GACL,KAAK,EACHrD,QAAQC,IAAI,uBAEZ,MACF,KAAK,EACHD,QAAQC,IAAI,uBAEZ,MACF,KAAK,EACHD,QAAQC,IAAI,uBAEZ,MACF,KAAK,EACHD,QAAQC,IAAI,uBAEZ,MACF,QACUD,QAAA+C,KAAK,aAAaM,UA1BpBrD,QAAAC,IAAI,OAAOoD,mBA4BrB,EAKIC,EAAepC,UACf,IACFlB,QAAQC,IAAI,YAGZ,MAAM2B,EAAWF,EAAAA,QAAQC,OAAOC,SAC3BA,GAAaA,EAASH,KACzBzB,QAAQ+C,KAAK,mBAKXnD,EAAauB,OAASvB,EAAauB,MAAMmC,cAC3CtD,QAAQC,IAAI,gCACZL,EAAauB,MAAMmC,gBAEnBtD,QAAQ+C,KAAK,qBAGhB,OAAQZ,GACCnC,QAAAmC,MAAM,YAAaA,GAC3BoB,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,QAEV,GAKIC,EAAgBzC,UAChB,IACFlB,QAAQC,IAAI,8BAGZ,MAAM2B,EAAWF,EAAAA,QAAQC,OAAOC,SAC3BA,GAAaA,EAASH,KACzBzB,QAAQ+C,KAAK,8BAKXjD,EAAcqB,OAASrB,EAAcqB,MAAMwC,eAC7C3D,QAAQC,IAAI,kCACZH,EAAcqB,MAAMwC,iBAEpB3D,QAAQ+C,KAAK,sBAGhB,OAAQZ,GACCnC,QAAAmC,MAAM,uBAAwBA,GACtCoB,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,QAEV,GAKIE,EAAgB1C,UAChB,IACFlB,QAAQC,IAAI,8BAGZ,MAAM2B,EAAWF,EAAAA,QAAQC,OAAOC,SAC3BA,GAAaA,EAASH,KACzBzB,QAAQ+C,KAAK,8BAKXhD,EAAcoB,OAASpB,EAAcoB,MAAMyC,eAC7C5D,QAAQC,IAAI,kCACZF,EAAcoB,MAAMyC,iBAEpB5D,QAAQ+C,KAAK,sBAGhB,OAAQZ,GACCnC,QAAAmC,MAAM,uBAAwBA,GACtCoB,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,QAEV,GAIIG,EAAgB,KACV7D,QAAAC,IAAI,iBAAkB,CAAEQ,aAAcA,EAAaU,MAAOX,eAAgBA,EAAeW,QACpFV,EAAAU,OAASV,EAAaU,MACpBX,EAAAW,OAASX,EAAeW,MAC/BnB,QAAAC,IAAI,cAAe,CAAEQ,aAAcA,EAAaU,MAAOX,eAAgBA,EAAeW,OAAO,EAGnG2C,EAAe,KACjBrD,EAAaU,OAAQ,EACrBX,EAAeW,OAAQ,CAAA,EAGrB4C,EAAwBC,IAK1B,GAJQhE,QAAAC,IAAI,sBAAuB+D,OAI9BC,EAAYA,aAAC,GAAI,cAItB,OAAQD,GACJ,IAAK,OACG,IACAE,EAAKA,MAAC,gCACT,OAAQC,GACGnE,QAAAmC,MAAM,eAAgBgC,GAC9BZ,EAAAA,MAAIC,UAAU,CACVC,MAAO,SACPC,KAAM,QAEd,CACA,MACJ,IAAK,QACG,IACA1D,QAAQC,IAAI,aACZiE,EAAKA,MAAC,iCACT,OAAQC,GACGnE,QAAAmC,MAAM,eAAgBgC,GAC9BZ,EAAAA,MAAIC,UAAU,CACVC,MAAO,SACPC,KAAM,QAEd,CACA,MACJ,IAAK,QACG,IACA1D,QAAQC,IAAI,aACZiE,EAAKA,MAAC,iCACT,OAAQC,GACGnE,QAAAmC,MAAM,eAAgBgC,GAC9BZ,EAAAA,MAAIC,UAAU,CACVC,MAAO,SACPC,KAAM,QAEd,CACA,MACJ,QACY1D,QAAA+C,KAAK,YAAaiB,GAClC,SAIJI,EAAAA,QAAO,WACKpE,QAAAC,IAAI,4CAA6CC,EAAWiB,OACpEnB,QAAQC,IAAI,0BAA2B,CACnC2B,SAAUF,EAAAA,QAAQC,OAAOC,SACzBC,MAAOH,EAAAA,QAAQC,OAAOE,MACtBJ,IAAKC,OAAAA,EAAK2C,EAAA3C,QAAGC,OAAOC,eAAU,EAAA0C,EAAA7C,MAKlCzB,QAAQC,IAAI,m+BClWhBsE,GAAGC,WAAWC"}