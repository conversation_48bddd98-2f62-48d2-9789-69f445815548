{"version": 3, "file": "pointsLog.js", "sources": ["../../../../../../src/pages/bundle/user/pointsLog.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHVzZXJccG9pbnRzTG9nLnZ1ZQ"], "sourcesContent": ["<script setup>\nimport { ref, reactive, onMounted } from \"vue\";\nimport { onLoad, onShow, onPullDownRefresh, onReachBottom } from \"@dcloudio/uni-app\";\nimport { userget_points_log } from \"@/api\";\nimport { store } from \"@/store\";\nimport { navto } from \"@/utils\";\nimport { requireLogin } from \"@/utils/auth\";\nimport myTitle from \"@/components/myTitle.vue\";\n\nconst pointsLog = ref([]);\nconst loading = ref(false);\nconst finished = ref(false);\nconst page = ref(1);\nconst pageSize = 20;\nconst total = ref(0);\nconst currentPoints = ref(0);\n\nonLoad(() => {\n  getPointsLog();\n});\n\nonShow(() => {\n  // 页面显示时刷新数据\n  refreshData();\n});\n\nonPullDownRefresh(() => {\n  refreshData();\n});\n\nonReachBottom(() => {\n  if (!finished.value && !loading.value) {\n    loadMore();\n  }\n});\n\n// 获取积分记录\nconst getPointsLog = async (isRefresh = false) => {\n  if (loading.value) return;\n  \n  // 使用统一的登录校验\n  if (!requireLogin('', '请先登录后查看积分记录')) {\n    return;\n  }\n\n  loading.value = true;\n  \n  try {\n    console.log('开始获取积分记录，参数:', {\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      page: isRefresh ? 1 : page.value,\n      page_size: pageSize\n    });\n\n    const res = await userget_points_log({\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      page: isRefresh ? 1 : page.value,\n      page_size: pageSize\n    });\n\n    console.log('积分记录API响应:', res);\n\n    if (res?.status === 'ok') {\n      const newRecords = res.data?.list || [];\n      \n      if (isRefresh) {\n        pointsLog.value = newRecords;\n        page.value = 1;\n        finished.value = false;\n      } else {\n        pointsLog.value = [...pointsLog.value, ...newRecords];\n      }\n      \n      total.value = res.data?.total || 0;\n      currentPoints.value = res.data?.current_points || 0;\n      \n      // 检查是否还有更多数据\n      if (newRecords.length < pageSize) {\n        finished.value = true;\n      } else {\n        page.value++;\n      }\n    } else if (res?.status === 'empty') {\n      if (isRefresh) {\n        pointsLog.value = [];\n      }\n      finished.value = true;\n      console.log('没有积分记录数据');\n    } else {\n      console.error('获取积分记录失败:', res?.msg || '未知错误');\n      uni.$u.toast(res?.msg || '获取积分记录失败');\n    }\n  } catch (error) {\n    console.error('获取积分记录异常:', error);\n    uni.$u.toast('获取积分记录失败，请稍后重试');\n  } finally {\n    loading.value = false;\n    uni.stopPullDownRefresh();\n  }\n};\n\n// 刷新数据\nconst refreshData = () => {\n  page.value = 1;\n  finished.value = false;\n  getPointsLog(true);\n};\n\n// 加载更多\nconst loadMore = () => {\n  getPointsLog(false);\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n  // 修复iOS日期格式问题\n  const formattedTimeStr = timeStr.replace(/-/g, '/');\n  const time = new Date(formattedTimeStr);\n  \n  if (isNaN(time.getTime())) {\n    return '时间格式错误';\n  }\n  \n  return time.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\n// 获取积分变动类型文本\nconst getSourceTypeText = (sourceType) => {\n  const typeMap = {\n    'member_init': '开通会员',\n    'activity_checkin': '活动签到',\n    'activity_complete': '活动完成',\n    'daily_signin': '每日签到',\n    'invite_friend': '邀请好友',\n    'consume_points': '积分消费',\n    'admin_adjust': '管理员调整'\n  };\n  return typeMap[sourceType] || '其他';\n};\n\n// 获取积分变动颜色\nconst getPointsColor = (pointsChange) => {\n  return pointsChange > 0 ? '#6AC086' : '#FF6B35';\n};\n\n// 获取积分变动图标\nconst getPointsIcon = (pointsChange) => {\n  return pointsChange > 0 ? 'plus-circle-fill' : 'minus-circle-fill';\n};\n</script>\n\n<template>\n  <view class=\"page\">\n    <myTitle\n      title=\"积分记录\"\n      bg-color=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      :titleStyle=\"{\n        color: '#ffffff',\n        fontWeight: '600',\n        fontSize: '36rpx'\n      }\"\n    ></myTitle>\n\n    <view class=\"points-container\">\n      <!-- 当前积分显示 -->\n      <view class=\"current-points-card\">\n        <view class=\"points-header\">\n          <u-icon name=\"star-fill\" color=\"#ffffff\" size=\"44rpx\"></u-icon>\n          <text class=\"points-title\">当前积分</text>\n        </view>\n        <text class=\"points-value\">{{ currentPoints }}</text>\n        <text class=\"points-desc\">积分可用于兑换奖品或参与活动</text>\n      </view>\n      \n      <!-- 积分记录列表 -->\n      <view v-if=\"pointsLog.length > 0\" class=\"points-list\">\n        <view \n          v-for=\"(item, index) in pointsLog\" \n          :key=\"item.id\"\n          class=\"points-item\"\n        >\n          <!-- 记录头部 -->\n          <view class=\"points-header\">\n            <view class=\"points-info\">\n              <view class=\"points-type\">\n                <u-icon \n                  :name=\"getPointsIcon(item.points_change)\" \n                  :color=\"getPointsColor(item.points_change)\" \n                  size=\"32rpx\"\n                ></u-icon>\n                <text class=\"type-text\">{{ getSourceTypeText(item.source_type) }}</text>\n              </view>\n              <text class=\"points-time\">{{ formatTime(item.created_at) }}</text>\n            </view>\n            <view class=\"points-change\" :style=\"{ color: getPointsColor(item.points_change) }\">\n              {{ item.points_change > 0 ? '+' : '' }}{{ item.points_change }}\n            </view>\n          </view>\n          \n          <!-- 记录内容 -->\n          <view class=\"points-content\">\n            <text class=\"points-desc\">{{ item.description }}</text>\n            <text class=\"points-balance\">余额：{{ item.points_balance }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view v-else-if=\"!loading\" class=\"empty-state\">\n        <u-icon name=\"star\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">暂无积分记录</text>\n        <text class=\"empty-desc\">您的积分变动记录将在这里显示</text>\n      </view>\n      \n      <!-- 加载状态 -->\n      <view v-if=\"loading && pointsLog.length === 0\" class=\"loading-state\">\n        <u-loading-icon mode=\"circle\" color=\"#6AC086\" size=\"60rpx\"></u-loading-icon>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view v-if=\"loading && pointsLog.length > 0\" class=\"load-more\">\n        <u-loading-icon mode=\"circle\" color=\"#6AC086\" size=\"40rpx\"></u-loading-icon>\n        <text class=\"load-more-text\">加载中...</text>\n      </view>\n      \n      <!-- 没有更多 -->\n      <view v-if=\"finished && pointsLog.length > 0\" class=\"no-more\">\n        <text class=\"no-more-text\">没有更多记录了</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"less\">\n.page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);\n  padding-bottom: 40rpx;\n}\n\n.points-container {\n  padding: 30rpx;\n  margin-top: 220rpx; /* 调整顶部边距，确保不被标题遮挡 */\n}\n\n.current-points-card {\n  background: linear-gradient(135deg, #6AC086 0%, #88D7A0 100%);\n  border-radius: 24rpx;\n  padding: 40rpx 32rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.25);\n  color: #fff;\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.current-points-card::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  right: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);\n  transform: rotate(45deg);\n}\n\n.points-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.points-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  margin-left: 12rpx;\n}\n\n.points-value {\n  font-size: 56rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 12rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.points-desc {\n  font-size: 26rpx;\n  opacity: 0.9;\n  position: relative;\n  z-index: 1;\n}\n\n.points-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.points-item {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24rpx;\n  padding: 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);\n  border: 1rpx solid rgba(106, 192, 134, 0.08);\n}\n\n.points-item .points-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12rpx;\n}\n\n.points-info {\n  flex: 1;\n}\n\n.points-type {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.type-text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 600;\n  margin-left: 8rpx;\n}\n\n.points-time {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.points-change {\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.points-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.points-item .points-desc {\n  font-size: 24rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n.points-balance {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n  text-align: center;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  font-weight: 500;\n  margin: 24rpx 0 12rpx;\n}\n\n.empty-desc {\n  font-size: 26rpx;\n  color: #ccc;\n  line-height: 1.5;\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 40rpx;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-top: 16rpx;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-more-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-left: 16rpx;\n}\n\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.no-more-text {\n  font-size: 24rpx;\n  color: #999;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/user/pointsLog.vue'\nwx.createPage(MiniProgramPage)"], "names": ["myTitle", "pointsLog", "ref", "loading", "finished", "page", "total", "currentPoints", "onLoad", "onShow", "onPullDownRefresh", "onReachBottom", "value", "getPointsLog", "async", "isRefresh", "requireLogin", "console", "log", "uid", "store", "$state", "userInfo", "token", "page_size", "res", "userget_points_log", "status", "newRecords", "_a", "data", "list", "_b", "_c", "current_points", "length", "error", "msg", "uni", "index", "$u", "toast", "stopPullDownRefresh", "refreshData", "loadMore", "formatTime", "timeStr", "formattedTimeStr", "replace", "time", "Date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getPointsColor", "pointsChange", "sourceType", "member_init", "activity_checkin", "activity_complete", "daily_signin", "invite_friend", "consume_points", "admin_adjust", "wx", "createPage", "MiniProgramPage"], "mappings": "ioBAOA,MAAMA,EAAU,IAAW,gEAE3B,MAAMC,EAAYC,EAAAA,IAAI,IAChBC,EAAUD,EAAAA,KAAI,GACdE,EAAWF,EAAAA,KAAI,GACfG,EAAOH,EAAAA,IAAI,GAEXI,EAAQJ,EAAAA,IAAI,GACZK,EAAgBL,EAAAA,IAAI,GAE1BM,EAAAA,QAAO,YAIPC,EAAAA,QAAO,YAKPC,EAAAA,mBAAkB,YAIlBC,EAAAA,eAAc,KACPP,EAASQ,OAAUT,EAAQS,UAEhC,IAII,MAAAC,EAAeC,MAAOC,GAAY,eACtC,IAAIZ,EAAQS,OAGPI,EAAYA,aAAC,GAAI,eAAtB,CAIAb,EAAQS,OAAQ,EAEZ,IACFK,QAAQC,IAAI,eAAgB,CAC1BC,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/BlB,KAAMU,EAAY,EAAIV,EAAKO,MAC3BY,UAvCW,KA0CP,MAAAC,QAAYC,qBAAmB,CACnCP,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/BlB,KAAMU,EAAY,EAAIV,EAAKO,MAC3BY,UA9CW,KAmDT,GAFIP,QAAAC,IAAI,aAAcO,GAEN,QAAhB,MAAAA,OAAA,EAAAA,EAAKE,QAAiB,CACxB,MAAMC,GAAa,OAAAC,EAAAJ,EAAIK,WAAJ,EAAAD,EAAUE,OAAQ,GAEjChB,GACFd,EAAUW,MAAQgB,EAClBvB,EAAKO,MAAQ,EACbR,EAASQ,OAAQ,GAEjBX,EAAUW,MAAQ,IAAIX,EAAUW,SAAUgB,GAG5CtB,EAAMM,OAAQ,OAAAoB,EAAAP,EAAIK,WAAJ,EAAAE,EAAU1B,QAAS,EACjCC,EAAcK,OAAQ,OAAAqB,EAAAR,EAAIK,WAAJ,EAAAG,EAAUC,iBAAkB,EAG9CN,EAAWO,OAlEJ,GAmET/B,EAASQ,OAAQ,EAEZP,EAAAO,OAEb,KAA+B,WAAX,MAALa,OAAK,EAAAA,EAAAE,SACVZ,IACFd,EAAUW,MAAQ,IAEpBR,EAASQ,OAAQ,EACjBK,QAAQC,IAAI,cAEZD,QAAQmB,MAAM,aAAkB,MAALX,OAAK,EAAAA,EAAAY,MAAO,QACvCC,EAAGC,MAACC,GAAGC,OAAM,MAAAhB,OAAA,EAAAA,EAAKY,MAAO,YAE5B,OAAQD,GACCnB,QAAAmB,MAAM,YAAaA,GAC3BE,EAAAA,MAAIE,GAAGC,MAAM,iBACjB,CAAY,QACRtC,EAAQS,OAAQ,EAChB0B,EAAGC,MAACG,qBACN,CAzDA,CAyDA,EAIIC,EAAc,KAClBtC,EAAKO,MAAQ,EACbR,EAASQ,OAAQ,EACjBC,GAAa,EAAI,EAIb+B,EAAW,KACf/B,GAAa,EAAK,EAIdgC,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAErB,MAAMC,EAAmBD,EAAQE,QAAQ,KAAM,KACzCC,EAAO,IAAIC,KAAKH,GAEtB,OAAII,MAAMF,EAAKG,WACN,SAGFH,EAAKI,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WACT,EAkBGC,EAAkBC,GACfA,EAAe,EAAI,UAAY,2WAIjBA,kBACdA,EAAe,EAAI,mBAAqB,oEApBtBC,gBACT,CACdC,YAAe,OACfC,iBAAoB,OACpBC,kBAAqB,OACrBC,aAAgB,OAChBC,cAAiB,OACjBC,eAAkB,OAClBC,aAAgB,SAEHP,IAAe,+JAVN,IAACA,EAmBJD,kXC1JvBS,GAAGC,WAAWC"}