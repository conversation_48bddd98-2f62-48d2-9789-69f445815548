{"version": 3, "file": "u-notify.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-notify/u-notify.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LW5vdGlmeS91LW5vdGlmeS52dWU"], "sourcesContent": ["<template>\n\t<u-transition\n\t\tmode=\"slide-down\"\n\t\t:customStyle=\"containerStyle\"\n\t\t:show=\"open\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-notify\"\n\t\t\t:class=\"[`u-notify--${tmpConfig.type}`]\"\n\t\t\t:style=\"[backgroundColor, addStyle(customStyle)]\"\n\t\t>\n\t\t\t<u-status-bar v-if=\"tmpConfig.safeAreaInsetTop\"></u-status-bar>\n\t\t\t<view class=\"u-notify__warpper\">\n\t\t\t\t<slot name=\"icon\">\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\tv-if=\"['success', 'warning', 'error'].includes(tmpConfig.type)\"\n\t\t\t\t\t\t:name=\"tmpConfig.icon\"\n\t\t\t\t\t\t:color=\"tmpConfig.color\"\n\t\t\t\t\t\t:size=\"1.3 * tmpConfig.fontSize\"\n\t\t\t\t\t\t:customStyle=\"{marginRight: '4px'}\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t</slot>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-notify__warpper__text\"\n\t\t\t\t\t:style=\"{\n\t\t\t\t\t\tfontSize: addUnit(tmpConfig.fontSize),\n\t\t\t\t\t\tcolor: tmpConfig.color\n\t\t\t\t\t}\"\n\t\t\t\t>{{ tmpConfig.message }}</text>\n\t\t\t</view>\n\t\t</view>\n\t</u-transition>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport defProps from '../../libs/config/props.js'\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * notify 顶部提示\n\t * @description 该组件一般用于页面顶部向下滑出一个提示，尔后自动收起的场景\n\t * @tutorial\n\t * @property {String | Number}\ttop\t\t\t\t\t到顶部的距离 ( 默认 0 )\n\t * @property {String}\t\t\ttype\t\t\t\t主题，primary，success，warning，error ( 默认 'primary' )\n\t * @property {String}\t\t\tcolor\t\t\t\t字体颜色 ( 默认 '#ffffff' )\n\t * @property {String}\t\t\tbgColor\t\t\t\t背景颜色\n\t * @property {String}\t\t\tmessage\t\t\t\t展示的文字内容\n\t * @property {String | Number}\tduration\t\t\t展示时长，为0时不消失，单位ms ( 默认 3000 )\n\t * @property {String | Number}\tfontSize\t\t\t字体大小 ( 默认 15 )\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否留出顶部安全距离（状态栏高度） ( 默认 false )\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\n\t * @event {Function}\topen\t开启组件时调用的函数\n\t * @event {Function}\tclose\t关闭组件式调用的函数\n\t * @example <u-notify message=\"Hi uView\"></u-notify>\n\t */\n\texport default {\n\t\tname: 'u-notify',\n\t\tmixins: [mpMixin, mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 是否展示组件\n\t\t\t\topen: false,\n\t\t\t\ttimer: null,\n\t\t\t\tconfig: {\n\t\t\t\t\t// 到顶部的距离\n\t\t\t\t\ttop: defProps.notify.top,\n\t\t\t\t\t// type主题，primary，success，warning，error\n\t\t\t\t\ttype: defProps.notify.type,\n\t\t\t\t\t// 字体颜色\n\t\t\t\t\tcolor: defProps.notify.color,\n\t\t\t\t\t// 背景颜色\n\t\t\t\t\tbgColor: defProps.notify.bgColor,\n\t\t\t\t\t// 展示的文字内容\n\t\t\t\t\tmessage: defProps.notify.message,\n\t\t\t\t\t// 展示时长，为0时不消失，单位ms\n\t\t\t\t\tduration: defProps.notify.duration,\n\t\t\t\t\t// 字体大小\n\t\t\t\t\tfontSize: defProps.notify.fontSize,\n\t\t\t\t\t// 是否留出顶部安全距离（状态栏高度）\n\t\t\t\t\tsafeAreaInsetTop: defProps.notify.safeAreaInsetTop\n\t\t\t\t},\n\t\t\t\t// 合并后的配置，避免多次调用组件后，可能会复用之前使用的配置参数\n\t\t\t\ttmpConfig: {}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tcontainerStyle() {\n\t\t\t\tlet top = 0\n\t\t\t\tif (this.tmpConfig.top === 0) {\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t// H5端，导航栏为普通元素，需要将组件移动到导航栏的下边沿\n\t\t\t\t\t// H5的导航栏高度为44px\n\t\t\t\t\ttop = 44\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t\tconst style = {\n\t\t\t\t\ttop: addUnit(this.tmpConfig.top === 0 ? top : this.tmpConfig.top),\n\t\t\t\t\t// 因为组件底层为u-transition组件，必须将其设置为fixed定位\n\t\t\t\t\t// 让其出现在导航栏底部\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tzIndex: 10076\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 组件背景颜色\n\t\t\tbackgroundColor() {\n\t\t\t\tconst style = {}\n\t\t\t\tif (this.tmpConfig.bgColor) {\n\t\t\t\t\tstyle.backgroundColor = this.tmpConfig.bgColor\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 默认主题下的图标\n\t\t\ticon() {\n\t\t\t\tlet icon\n\t\t\t\tif (this.tmpConfig.type === 'success') {\n\t\t\t\t\ticon = 'checkmark-circle'\n\t\t\t\t} else if (this.tmpConfig.type === 'error') {\n\t\t\t\t\ticon = 'close-circle'\n\t\t\t\t} else if (this.tmpConfig.type === 'warning') {\n\t\t\t\t\ticon = 'error-circle'\n\t\t\t\t}\n\t\t\t\treturn icon\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 通过主题的形式调用toast，批量生成方法函数\n\t\t\t['primary', 'success', 'error', 'warning'].map(item => {\n\t\t\t\tthis[item] = message => this.show({\n\t\t\t\t\ttype: item,\n\t\t\t\t\tmessage\n\t\t\t\t})\n\t\t\t})\n\t\t},\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tshow(options) {\n\t\t\t\t// 不将结果合并到this.config变量，避免多次调用u-toast，前后的配置造成混乱\n\t\t\t\tthis.tmpConfig = deepMerge(this.config, options)\n\t\t\t\t// 任何定时器初始化之前，都要执行清除操作，否则可能会造成混乱\n\t\t\t\tthis.clearTimer()\n\t\t\t\tthis.open = true\n\t\t\t\tif (this.tmpConfig.duration > 0) {\n\t\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\t\tthis.open = false\n\t\t\t\t\t\t// 倒计时结束，清除定时器，隐藏toast组件\n\t\t\t\t\t\tthis.clearTimer()\n\t\t\t\t\t\t// 判断是否存在callback方法，如果存在就执行\n\t\t\t\t\t\ttypeof(this.tmpConfig.complete) === 'function' && this.tmpConfig.complete()\n\t\t\t\t\t}, this.tmpConfig.duration)\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 关闭notify\n\t\t\tclose() {\n\t\t\t\tthis.clearTimer()\n\t\t\t},\n\t\t\tclearTimer() {\n\t\t\t\tthis.open = false\n\t\t\t\t// 清除定时器\n\t\t\t\tclearTimeout(this.timer)\n\t\t\t\tthis.timer = null\n\t\t\t}\n\t\t},\n\t\tbeforeUnmount() {\n\t\t\tthis.clearTimer()\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t$u-notify-padding: 8px 10px !default;\n\t$u-notify-text-font-size: 15px !default;\n\t$u-notify-primary-bgColor: $u-primary !default;\n\t$u-notify-success-bgColor: $u-success !default;\n\t$u-notify-error-bgColor: $u-error !default;\n\t$u-notify-warning-bgColor: $u-warning !default;\n\n\n\t.u-notify {\n\t\tpadding: $u-notify-padding;\n\n\t\t&__warpper {\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t\ttext-align: center;\n\t\t\tjustify-content: center;\n\n\t\t\t&__text {\n\t\t\t\tfont-size: $u-notify-text-font-size;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t\t&--primary {\n\t\t\tbackground-color: $u-notify-primary-bgColor;\n\t\t}\n\n\t\t&--success {\n\t\t\tbackground-color: $u-notify-success-bgColor;\n\t\t}\n\n\t\t&--error {\n\t\t\tbackground-color: $u-notify-error-bgColor;\n\t\t}\n\n\t\t&--warning {\n\t\t\tbackground-color: $u-notify-warning-bgColor;\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-notify/u-notify.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "open", "timer", "config", "top", "defProps", "notify", "type", "color", "bgColor", "message", "duration", "fontSize", "safeAreaInsetTop", "tmpConfig", "computed", "containerStyle", "this", "addUnit", "position", "left", "right", "zIndex", "backgroundColor", "style", "icon", "created", "map", "item", "show", "methods", "addStyle", "options", "deepMerge", "clearTimer", "setTimeout", "complete", "close", "clearTimeout", "beforeUnmount", "wx", "createComponent", "Component"], "mappings": "6DAyDMA,EAAU,CACdC,KAAM,WACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAACC,YACxBC,KAAO,KACC,CAENC,MAAM,EACNC,MAAO,KACPC,OAAQ,CAEPC,IAAKC,EAAAA,SAASC,OAAOF,IAErBG,KAAMF,EAAAA,SAASC,OAAOC,KAEtBC,MAAOH,EAAAA,SAASC,OAAOE,MAEvBC,QAASJ,EAAAA,SAASC,OAAOG,QAEzBC,QAASL,EAAAA,SAASC,OAAOI,QAEzBC,SAAUN,EAAAA,SAASC,OAAOK,SAE1BC,SAAUP,EAAAA,SAASC,OAAOM,SAE1BC,iBAAkBR,EAAAA,SAASC,OAAOO,kBAGnCC,UAAW,CAAC,IAGdC,SAAU,CACT,cAAAC,GAEKC,KAAKH,UAAUV,IAgBZ,MATO,CACbA,IAAKc,EAAAA,QAA+B,IAAvBD,KAAKH,UAAUV,IATnB,EASqCa,KAAKH,UAAUV,KAG7De,SAAU,QACVC,KAAM,EACNC,MAAO,EACPC,OAAQ,MAGT,EAED,eAAAC,GACC,MAAMC,EAAQ,CAAC,EAIR,OAHHP,KAAKH,UAAUL,UACZe,EAAAD,gBAAkBN,KAAKH,UAAUL,SAEjCe,CACP,EAED,IAAAC,GACK,IAAAA,EAQG,MAPqB,YAAxBR,KAAKH,UAAUP,KACXkB,EAAA,mBAC2B,UAAxBR,KAAKH,UAAUP,KAClBkB,EAAA,eAC2B,YAAxBR,KAAKH,UAAUP,OAClBkB,EAAA,gBAEDA,CACR,GAED,OAAAC,GAEC,CAAC,UAAW,UAAW,QAAS,WAAWC,KAAYC,IACtDX,KAAKW,GAAmBlB,GAAAO,KAAKY,KAAK,CACjCtB,KAAMqB,EACNlB,WACA,GAEF,EACDoB,QAAS,CACRC,SAAAA,EAAQA,SACRb,QAAAA,EAAOA,QACP,IAAAW,CAAKG,GAEJf,KAAKH,UAAYmB,EAAAA,UAAUhB,KAAKd,OAAQ6B,GAExCf,KAAKiB,aACLjB,KAAKhB,MAAO,EACRgB,KAAKH,UAAUH,SAAW,IACxBM,KAAAf,MAAQiC,YAAW,KACvBlB,KAAKhB,MAAO,EAEZgB,KAAKiB,aAE+B,mBAA7BjB,KAAKH,UAAUsB,UAA4BnB,KAAKH,UAAUsB,UAAS,GACxEnB,KAAKH,UAAUH,UAEnB,EAED,KAAA0B,GACCpB,KAAKiB,YACL,EACD,UAAAA,GACCjB,KAAKhB,MAAO,EAEZqC,aAAarB,KAAKf,OAClBe,KAAKf,MAAQ,IACd,GAED,aAAAqC,GACCtB,KAAKiB,YACN,+3BCzKFM,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}