"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),a=require("../../../store/index.js"),t=require("../../../utils/index.js"),l=require("../../../utils/BaseUrl.js");if(require("../../../utils/request.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-gap")+e.resolveComponent("u-text")+e.resolveComponent("u-icon")+e.resolveComponent("u-button")+e.resolveComponent("u-checkbox")+e.resolveComponent("u-checkbox-group")+e.resolveComponent("u-picker"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-checkbox/u-checkbox.js")+(()=>"../../../node-modules/uview-plus/components/u-checkbox-group/u-checkbox-group.js")+(()=>"../../../node-modules/uview-plus/components/u-picker/u-picker.js"))();const n={__name:"login",setup(n){const s=e.ref(!1),i=e.ref([]),u=e=>{},r=e.ref(""),c=e.ref("/static/myd.png"),d=e.ref(""),v=e.ref("绑定手机号"),g=e.ref({}),p=e.ref(""),x=e.ref(""),m=e.ref(),f=e.ref(!1),h=e.ref(!1),w=e.ref(!1),y=e.ref(""),k=e.ref(""),T=e.ref(""),b=["/pages/index","/pages/addActive","/pages/world","/pages/my"],j=o=>new Promise(((a,t)=>{if(console.log("=== 智能跳转开始 ==="),console.log("目标URL:",o),console.log("URL类型检查:",o),!o||"string"!=typeof o){const e=new Error("无效的URL参数");return console.error("URL验证失败:",e),void t(e)}const l=o.split("?")[0],n=b.includes(l);console.log("URL去除参数后:",l),console.log("是否为主要页面:",n),console.log("主要页面列表:",b),n?(console.log("使用 uni.reLaunch 跳转到主要页面（项目使用自定义tabBar）"),e.index.reLaunch({url:o,success:e=>{console.log("✅ 主要页面跳转成功:",o),console.log("跳转结果:",e),a(e)},fail:e=>{console.error("❌ 主要页面跳转失败:",e),console.error("错误详情:",JSON.stringify(e)),t(e)}})):(console.log("使用 uni.navigateTo 跳转到普通页面"),e.index.navigateTo({url:o,success:e=>{console.log("✅ navigateTo 跳转成功:",o),console.log("跳转结果:",e),a(e)},fail:l=>{console.warn("⚠️ navigateTo 失败，尝试 redirectTo:",l),console.warn("navigateTo 错误详情:",JSON.stringify(l)),e.index.redirectTo({url:o,success:e=>{console.log("✅ redirectTo 跳转成功:",o),console.log("跳转结果:",e),a(e)},fail:e=>{console.error("❌ 所有跳转方式都失败"),console.error("redirectTo 错误详情:",JSON.stringify(e)),t(e)}})}})),console.log("=== 智能跳转请求已发送 ===")}));e.onLoad((e=>{C(),(null==e?void 0:e.id)&&a.store().changePid(e.pid),(null==e?void 0:e.returnUrl)&&(T.value=decodeURIComponent(e.returnUrl),console.log("登录页面接收到返回URL:",T.value))}));const $=()=>{e.index.login({provider:"weixin",success:async t=>{var l,n,i;let u="";try{const o=await new Promise(((o,a)=>{e.index.getLocation({type:"gcj02",success:o,fail:a})}));if(o&&o.latitude&&o.longitude){const{getAddr:a}=await"../../../api/index.js",t=await a({latitude:o.latitude,longitude:o.longitude});if(t&&1==t.status){const o=t.regeocode.addressComponent;u=e.index.$u.test.isEmpty(o.city)?o.province:o.city,o.adcode&&(k.value=o.adcode)}}}catch(p){console.log("获取城市信息失败，将使用空值登录:",p)}const c=await o.userlogin({code:t.code,pid:a.store().$state.pid,city:u,city_id:k.value});if("ok"===c.status){if(r.value=c.data.avatar,d.value=c.data.nickname,m.value=c.data.sex,(null==(l=c.data)?void 0:l.mobile)&&(v.value="已绑定手机号"),c.data.avatar&&c.data.nickname&&(null==(n=c.data)?void 0:n.sex)&&0!=(null==(i=c.data)?void 0:i.sex))return a.store().changeUserInfo(c.data),console.log("登录成功，用户信息完整，保存用户信息:",c.data),e.index.$u.toast("登录成功"),void setTimeout((async()=>{if(T.value)try{await j(T.value),console.log("成功跳转到指定页面:",T.value)}catch(p){console.error("智能跳转失败:",p);try{e.index.navigateBack(),console.log("已返回上一页")}catch(o){console.error("返回上一页也失败，跳转到首页"),e.index.switchTab({url:"/pages/index"})}}else console.log("返回上一页"),e.index.navigateBack()}),1500);s.value=!0,g.value=c.data,e.index.$u.toast("请上传头像、昵称以及绑定手机号码")}else e.index.$u.toast(c.msg)}})},_=async()=>{if(x.value&&e.index.$u.test.mobile(x.value))try{const{uid:o,token:a}=g.value,t=await U("user/update_mobile_manual","POST",{mobile:x.value,uid:o,token:a});"ok"==t.status?(p.value=x.value,y.value="已绑定手机号",w.value=!1,e.index.showToast({title:"手机号绑定成功",icon:"success"}),setTimeout((async()=>{await B()}),1e3)):e.index.showToast({title:t.msg||"绑定失败",icon:"none"})}catch(o){console.error("手动绑定手机号异常:",o),e.index.showToast({title:"绑定失败，请重试",icon:"none"})}else e.index.showToast({title:"请输入正确的手机号",icon:"none"})},U=(o,a,t)=>new Promise(((n,s)=>{e.index.request({url:l.BaseUrl+o,method:a,data:t,header:{"content-type":"application/x-www-form-urlencoded"},success:e=>n(e.data)})})),C=()=>{e.wx$1.getPrivacySetting({success:o=>{o.needAuthorization&&(h.value=!0,e.wx$1.requirePrivacyAuthorize({success:()=>{},fail:()=>e.index.$u.toast("您已拒绝授权"),complete:()=>{}}))},fail:()=>{},complete:()=>{}})},q=e=>d.value=e.detail.value,L=e=>d.value=e.detail.value,S=async e=>{m.value=e.indexs[0]+1,f.value=!1},P=async e=>{var o;if(console.log("微信手机号授权结果:",e.detail),A())try{if(e.detail.code){console.log("微信授权成功，获取手机号code:",e.detail.code);const{uid:a,token:t}=g.value,l=await U("user/update_mobile","POST",{code:e.detail.code,uid:a,token:t});"ok"===l.status?(console.log("手机号绑定成功，完成注册流程"),p.value=(null==(o=l.data)?void 0:o.mobile)||"已绑定",y.value="已绑定手机号",await B()):(console.log("手机号绑定失败，降级到手动输入:",l.msg),z())}else console.log("用户拒绝微信授权，降级到手动输入"),z()}catch(a){console.error("微信授权处理异常:",a),z()}},A=()=>0===i.value.length?(e.index.$u.toast("请先同意用户注册协议"),!1):r.value&&d.value?!!m.value||(e.index.$u.toast("请选择性别"),!1):(e.index.$u.toast("请填写完整信息"),!1),z=()=>{w.value=!0,y.value="请输入手机号完成注册",e.index.showToast({title:"请输入手机号完成注册",icon:"none"})},B=async()=>{const{uid:o,token:t}=g.value,l=await U("user/update","POST",{avatar:r.value,nickname:d.value,sex:m.value,uid:o,token:t});if("ok"===l.status){const o={...g.value,avatar:r.value,nickname:d.value,sex:m.value};return a.store().changeUserInfo(o),console.log("登录成功，保存用户信息:",o),e.index.$u.toast("登录成功"),void setTimeout((async()=>{if(T.value)try{await j(T.value),console.log("成功跳转到指定页面:",T.value)}catch(o){console.error("智能跳转失败:",o);try{e.index.navigateBack(),console.log("已返回上一页")}catch(a){console.error("返回上一页也失败，跳转到首页"),e.index.switchTab({url:"/pages/index"})}}else console.log("返回上一页"),e.index.navigateBack()}),1500)}e.index.$u.toast(l.msg)},F=async a=>{try{if(console.log("微信头像选择结果:",a.detail),a.detail.avatarUrl){e.index.showLoading({title:"上传头像中..."}),c.value=a.detail.avatarUrl;const t=await o.upload_img(a.detail.avatarUrl);"ok"===t.status?(r.value=null==t?void 0:t.data,console.log("微信头像上传成功:",t.data),e.index.showToast({title:"头像设置成功",icon:"success"})):(console.error("微信头像上传失败:",t.msg),e.index.$u.toast(t.msg||"头像上传失败"))}else e.index.showToast({title:"未获取到头像",icon:"none"})}catch(t){console.error("头像处理失败:",t),e.index.showToast({title:"头像处理失败",icon:"none"})}finally{e.index.hideLoading()}};return(o,l)=>{var n,r,v,g,p,h;return e.e({a:e.p({height:"150rpx"}),b:null==(v=null==(r=null==(n=e.unref(a.store)().$state.config)?void 0:n.img_config)?void 0:r.app_logo)?void 0:v.img_url,c:e.p({height:"50rpx"}),d:e.p({margin:"20rpx 0",align:"center",size:"48rpx",bold:!0,color:"#ff5700",text:`${null==(h=null==(p=null==(g=e.unref(a.store)().$state.config)?void 0:g.config)?void 0:p.app_name)?void 0:h.val}`}),e:e.p({height:"10rpx"}),f:s.value},s.value?e.e({g:c.value,h:e.o(F),i:e.p({height:"30rpx"}),j:d.value,k:e.o(C),l:e.o(q),m:e.o(L),n:y.value},y.value?{o:e.p({name:"checkmark-circle",size:"32rpx",color:"#6AC086"}),p:e.t(y.value)}:{},{q:w.value},w.value?{r:x.value,s:e.o((e=>x.value=e.detail.value)),t:e.o(_),v:e.p({size:"small",type:"primary",text:"绑定",customStyle:{width:"120rpx",height:"60rpx",fontSize:"24rpx",backgroundColor:"#6AC086",borderColor:"#6AC086"}})}:{},{w:e.t(m.value?1==m.value?"男":"女":"请选择性别"),x:e.n(m.value?"":"c6a"),y:e.o((e=>f.value=!0)),z:e.p({height:"170rpx"})}):{},{A:s.value},s.value?{B:e.p({name:"a"}),C:e.o((o=>e.unref(t.navto)("/pages/bundle/common/xieyi?type=1"))),D:e.o(u),E:e.o((e=>i.value=e)),F:e.p({placement:"column",shape:"circle","active-color":"#ff8110",modelValue:i.value})}:{},{G:s.value},s.value?{H:e.o(P)}:{I:e.o($),J:e.p({color:"linear-gradient(103deg, #836FFF 0%, #836FFF 100%)",shape:"circle",customStyle:{width:"580rpx",height:"66rpx",color:"#f5f5f5",fontSize:"22rpx"},text:"登录"})},{K:e.o((e=>f.value=!1)),L:e.o((e=>f.value=!1)),M:e.o(S),N:e.p({show:f.value,columns:[["男","女"]],"close-on-click-overlay":!0})})}}},s=e._export_sfc(n,[["__scopeId","data-v-ddbfa397"]]);n.__runtimeHooks=3,wx.createPage(s);
//# sourceMappingURL=login.js.map
