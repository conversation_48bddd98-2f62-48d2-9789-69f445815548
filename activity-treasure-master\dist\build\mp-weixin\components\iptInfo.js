"use strict";const e=require("../common/vendor.js");require("../utils/request.js");const o=require("../store/index.js");if(require("../utils/BaseUrl.js"),require("../utils/index.js"),require("../api/index.js"),require("../utils/systemInfo.js"),require("../utils/auth.js"),require("../store/counter.js"),require("../utils/cacheManager.js"),!Array){(e.resolveComponent("u-input")+e.resolveComponent("u-gap")+e.resolveComponent("u-button")+e.resolveComponent("u-image")+e.resolveComponent("u-text")+e.resolveComponent("u-popup"))()}Math||((()=>"../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../node-modules/uview-plus/components/u-image/u-image.js")+(()=>"../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../node-modules/uview-plus/components/u-popup/u-popup.js"))();const t={__name:"iptInfo",props:{popupShow:{type:Boolean,default:!1},src:{type:String},ipt:{type:Boolean,default:!0}},emits:["update:popupShow","submit"],setup(t,{emit:u}){const s=e.ref({lianxi_name:o.store().$state.userInfo.nickname,lianxi_mobile:o.store().$state.userInfo.mobile});return(o,n)=>e.e({a:t.ipt},t.ipt?{b:e.o((e=>s.value.lianxi_name=e)),c:e.p({type:"text","adjust-position":!1,border:"surround",shape:"circle",placeholder:"请输入姓名",modelValue:s.value.lianxi_name}),d:e.p({height:"50rpx","bg-color":""}),e:e.o((e=>s.value.lianxi_mobile=e)),f:e.p({type:"number","adjust-position":!1,border:"surround",shape:"circle",placeholder:"请输入联系方式",modelValue:s.value.lianxi_mobile}),g:e.p({height:"50rpx"}),h:e.o((e=>u("submit",s.value))),i:e.p({shape:"circle",color:"linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)",customStyle:{color:"#000"},text:"提交"})}:{j:e.p({src:t.src,width:"200rpx",height:"200rpx"}),k:e.p({text:"长按识别二维码，以便联系活动发起人",align:"center",bold:!0,color:"#333",size:"32rpx"})},{l:e.o((e=>u("update:popupShow",!1))),m:e.p({show:t.popupShow,"close-on-click-overlay":!0,round:"30rpx",mode:"center","safe-area-inset-bottom":!1})})}};wx.createComponent(t);
//# sourceMappingURL=iptInfo.js.map
