"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),t=require("../../../../store/index.js"),n=require("../../../../utils/painterShare.js"),l=require("../../../../utils/uniShareConfig.js"),i=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/cacheManager.js"),require("../../../../store/counter.js"),require("../../../../components/share-popup/uni-share.js"),require("../../../../components/share-popup/uni-image-menu.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+a)();const a=()=>"../../../../components/share-popup/share-popup.js",s={__name:"detail",setup(a){console.log("日记详情页面加载");const s=e.ref(null),u=e.ref(!0),r=e.ref(!0),d=e.ref(""),c=e.ref([]),v=e.ref(!1),g=e.ref(1),f=e.ref(!0),m=e.ref(!1),h=e.ref(null),p=e.ref([]),k=e.ref(!1),w=e.ref(""),x=e.ref("latest"),y=e.ref(!1),L=e.ref(null),$=e.computed((()=>h.value?`回复 ${h.value.nickname}`:"写下你的评论...")),_=e.computed((()=>{var e;return!(!s.value||!(null==(e=t.store().$state.userInfo)?void 0:e.uid))&&s.value.user_id===t.store().$state.userInfo.uid})),I=e=>{var o;return!(!e||!(null==(o=t.store().$state.userInfo)?void 0:o.uid))&&e.user_id===t.store().$state.userInfo.uid};e.onLoad((async o=>{console.log("=== 动态详情页面 onLoad 开始 ==="),console.log("接收到的 options 参数:",JSON.stringify(o));const t=null==o?void 0:o.feedId;if(console.log("解析的 feedId:",t,"类型:",typeof t),!t)return console.error("feedId 为空或未定义"),e.index.showToast({title:"无法加载动态详情",icon:"none"}),void e.index.navigateBack();try{u.value=!0,console.log("开始加载动态详情，feedId:",t),await T(t),console.log("动态详情加载完成"),"true"===(null==o?void 0:o.showComments)&&(console.log("需要显示评论区，开始加载评论"),r.value=!0,C(!0),console.log("评论加载开始"))}catch(n){console.error("加载动态详情失败:",n),e.index.showToast({title:"加载失败，请重试",icon:"none"})}finally{u.value=!1,console.log("=== 动态详情页面 onLoad 结束 ===")}})),e.index.$on("feed-updated",(e=>{e.id===feedId.value&&T(feedId.value)})),e.onUnmounted((()=>{e.index.$off("feed-updated")})),e.onMounted((()=>{var e;(null==(e=s.value)?void 0:e.id)&&C(!0)})),e.onShareAppMessage((()=>{var e,o,n,l,i,a,u,r,d;try{return s.value?{title:s.value.content?s.value.content.length>30?s.value.content.substring(0,30)+"...":s.value.content:"分享一条精彩动态",path:`/pages/bundle/world/feed/detail?feedId=${s.value.id}`,imageUrl:s.value.images&&s.value.images.length>0?s.value.images[0]:(null==(a=null==(i=null==(l=t.store().$state.config)?void 0:l.img_config)?void 0:i.app_logo)?void 0:a.val)||""}:(console.warn("动态信息未加载完成，使用默认分享信息"),{title:"分享一条精彩动态",path:"/pages/bundle/world/feed/index",imageUrl:(null==(n=null==(o=null==(e=t.store().$state.config)?void 0:e.img_config)?void 0:o.app_logo)?void 0:n.val)||""})}catch(c){return console.error("动态分享配置失败:",c),{title:"分享一条精彩动态",path:"/pages/bundle/world/feed/index",imageUrl:(null==(d=null==(r=null==(u=t.store().$state.config)?void 0:u.img_config)?void 0:r.app_logo)?void 0:d.val)||""}}})),e.onShareTimeline((()=>s.value?{title:s.value.content.substring(0,30)+"...",query:`feedId=${s.value.id}`,imageUrl:s.value.images&&s.value.images.length>0?s.value.images[0]:""}:{})),e.onBackPress((({from:e})=>(console.log("返回按键触发，来源:",e),!("backbutton"!==e||!l.isShareMenuShow())&&(l.hideShareMenu(),!0))));const T=async e=>{var n,l,i;console.log("=== loadFeedDetail 开始 ==="),console.log("传入的 feedId:",e,"类型:",typeof e);const a=t.store().$state.userInfo;console.log("当前用户信息:",{uid:null==a?void 0:a.uid,hasToken:!!(null==a?void 0:a.token),tokenLength:null==(n=null==a?void 0:a.token)?void 0:n.length});const u={id:e,uid:(null==a?void 0:a.uid)||0,token:(null==a?void 0:a.token)||""};console.log("API请求参数:",u);try{const e=await o.getFeedDetail(u);if(console.log("API响应:",e),"ok"!==e.status)throw new Error(e.msg||"获取动态详情失败");{console.log("API响应成功，动态数据:",e.data),s.value=e.data,console.log("设置后的 feedDetail.value:",s.value),console.log("动态ID:",null==(l=s.value)?void 0:l.id,"类型:",typeof(null==(i=s.value)?void 0:i.id));const o=t.store().getFeedLikeState(s.value.id),n=t.store().getFeedFavoriteState(s.value.id);console.log("全局点赞状态:",o),console.log("全局收藏状态:",n),o&&(s.value.isLiked=o.isLiked,s.value.likeCount=o.likeCount,console.log("同步点赞状态:",{isLiked:s.value.isLiked,likeCount:s.value.likeCount})),null!==n&&(s.value.isFavorited=n,console.log("同步收藏状态:",s.value.isFavorited)),console.log("原始动态数据:",s.value);let a=[];if(s.value.images_json)try{a=JSON.parse(s.value.images_json),console.log("从images_json解析图片:",a)}catch(r){console.error("解析images_json失败:",r)}else if(s.value.images)if("string"==typeof s.value.images)try{a=JSON.parse(s.value.images),console.log("从images字符串解析图片:",a)}catch(r){a=[s.value.images],console.log("将images作为单个URL:",a)}else Array.isArray(s.value.images)&&(a=s.value.images,console.log("直接使用images数组:",a));p.value=Array.isArray(a)?a.filter((e=>e&&e.trim())):[],console.log("最终图片列表:",p.value)}}catch(d){throw console.error("Error loading feed detail:",d),d}},C=async(n=!1)=>{var l,i,a,u,r,d,h,p;if(console.log("=== loadComments 开始 ==="),console.log("reset 参数:",n),console.log("commentsLoading.value:",v.value),console.log("hasMoreComments.value:",f.value),console.log("feedDetail.value:",s.value),console.log("feedDetail.value?.id:",null==(l=s.value)?void 0:l.id,"类型:",typeof(null==(i=s.value)?void 0:i.id)),v.value||!f.value&&!n)console.log("跳过加载评论：正在加载中或没有更多评论");else if(null==(a=s.value)?void 0:a.id)try{v.value=!0,n&&(g.value=1,c.value=[]),console.log("获取评论请求参数:",{feed_id:s.value.id,page:g.value,page_size:10,uid:(null==(u=t.store().$state.userInfo)?void 0:u.uid)||0,token:(null==(r=t.store().$state.userInfo)?void 0:r.token)||"",sort_type:x.value});const e=await o.getFeedComments({feed_id:s.value.id,page:g.value,page_size:10,uid:(null==(d=t.store().$state.userInfo)?void 0:d.uid)||0,token:(null==(h=t.store().$state.userInfo)?void 0:h.token)||"",sort_type:x.value});if(console.log("评论API响应:",e),"ok"===e.status){const o=e.data.list||[];o.length>0&&(null==(p=t.store().$state.userInfo)?void 0:p.uid)&&await F(o),c.value=n?o:[...c.value,...o],f.value=10===o.length,g.value++}else"empty"===e.status&&(f.value=!1)}catch(k){console.error("加载评论失败:",k),e.index.showToast({title:"加载评论失败",icon:"none"})}finally{v.value=!1,m.value=!1}else console.error("feedDetail.value?.id 为空，无法加载评论")},S=()=>{m.value=!0,C(!0)},F=async e=>{try{const n=e.map((e=>e.id)).join(","),l=await o.getCommentLikeStatus({uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,comment_ids:n,comment_type:"feed"});if("ok"===l.status){const o=l.data.liked_comments||[];e.forEach((e=>{const n=t.store().getCommentLikeState(e.id);n?(e.isLiked=n.isLiked,e.like_count=n.likeCount):(e.isLiked=o.includes(e.id),e.like_count=e.like_count||0)}))}}catch(n){console.error("加载评论点赞状态失败:",n)}},j=async()=>{var n,l;if(console.log("=== handleLike 开始 ==="),console.log("feedDetail.value:",s.value),console.log("当前点赞状态:",null==(n=s.value)?void 0:n.isLiked),console.log("当前点赞数:",null==(l=s.value)?void 0:l.likeCount),console.log("用户信息:",t.store().$state.userInfo),!i.requireLogin("","请先登录后再点赞"))return void console.log("用户未登录，跳过点赞");if(!s.value)return void console.error("feedDetail.value 为空，无法点赞");const a=s.value.isLiked,u=s.value.likeCount;s.value.isLiked=!s.value.isLiked,s.value.likeCount+=s.value.isLiked?1:-1;try{console.log("发送日记点赞请求:",{id:s.value.id,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});const n=await o.likeFeed({id:s.value.id,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});console.log("日记点赞响应:",n),"ok"!==n.status?(s.value.isLiked=a,s.value.likeCount=u,e.index.showToast({title:n.msg||"操作失败",icon:"none"})):(t.store().updateFeedLike(s.value.id,s.value.isLiked,s.value.likeCount),e.index.showToast({title:n.msg||"操作成功",icon:"success"}))}catch(r){s.value.isLiked=a,s.value.likeCount=u,console.error("点赞失败:",r),e.index.showToast({title:"操作失败，请重试",icon:"none"})}},q=async()=>{var n;if(console.log("=== handleFavorite 开始 ==="),console.log("feedDetail.value:",s.value),console.log("当前收藏状态:",null==(n=s.value)?void 0:n.isFavorited),console.log("用户信息:",t.store().$state.userInfo),!i.requireLogin("","请先登录后再收藏"))return void console.log("用户未登录，跳过收藏");if(!s.value)return void console.error("feedDetail.value 为空，无法收藏");const l=s.value.isFavorited||!1;s.value.isFavorited=!s.value.isFavorited;try{const n=await o.favoriteFeed({id:s.value.id,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});"ok"!==n.status?(s.value.isFavorited=l,e.index.showToast({title:n.msg||"操作失败",icon:"none"})):(t.store().updateFeedFavorite(s.value.id,s.value.isFavorited),e.index.showToast({title:n.msg||"操作成功",icon:"success"}))}catch(a){s.value.isFavorited=l,console.error("收藏失败:",a),e.index.showToast({title:"操作失败，请重试",icon:"none"})}},b=()=>{console.log("toggleComments 被调用，当前状态:",r.value),r.value=!r.value,console.log("toggleComments 切换后状态:",r.value),r.value&&(console.log("开始加载评论..."),C(!0))},M=o=>{h.value=o?{id:o.id,nickname:o.nickname}:null,o&&setTimeout((()=>{e.index.pageScrollTo({scrollTop:9999,duration:300})}),100)},A=async()=>{var n;if(console.log("=== submitComment 开始 ==="),console.log("commentText.value:",d.value),console.log("replyTo.value:",h.value),console.log("feedDetail.value:",s.value),console.log("用户信息:",t.store().$state.userInfo),!d.value.trim())return console.log("评论内容为空"),void e.index.showToast({title:"评论内容不能为空",icon:"none"});if(i.requireLogin("","请先登录后再评论"))try{const l={uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,feed_id:s.value.id,content:d.value};(null==(n=h.value)?void 0:n.id)&&(l.parent_id=Number(h.value.id)),console.log("发送日记评论请求:",l);const i=await o.commentFeed(l);console.log("日记评论响应:",i),"ok"===i.status?(e.index.showToast({title:"评论成功",icon:"success"}),d.value="",h.value=null,C(!0),s.value.commentCount=(s.value.commentCount||0)+1):e.index.showToast({title:i.msg||"评论失败",icon:"none"})}catch(l){console.error("提交评论失败:",l),e.index.showToast({title:"评论失败，请重试",icon:"none"})}else console.log("用户未登录，跳过评论")},z={canvasContexts:new Set,timers:new Set,eventListeners:new Set,registerCanvas(e){this.canvasContexts.add(e)},registerTimer(e){this.timers.add(e)},registerEventListener(e){this.eventListeners.add(e)},cleanup(){this.canvasContexts.forEach((o=>{try{e.index.createSelectorQuery().select(`#${o}`).fields({node:!0,size:!0}).exec((e=>{if(e[0]&&e[0].node){const o=e[0].node,t=o.getContext("2d");t&&(t.clearRect(0,0,o.width,o.height),o.width=0,o.height=0)}}))}catch(t){console.warn(`清理Canvas ${o} 失败:`,t)}})),this.timers.forEach((e=>{clearTimeout(e),clearInterval(e)})),this.eventListeners.forEach((e=>{"function"==typeof e&&e()})),this.canvasContexts.clear(),this.timers.clear(),this.eventListeners.clear()}},D=()=>{z.cleanup(),e.index.navigateBack()},N=o=>{console.log("分享成功:",o),e.index.showToast({title:"分享成功",icon:"success"})},E=o=>{console.error("分享失败:",o),e.index.showToast({title:"分享失败",icon:"none"})},J=()=>{try{e.wx$1.showShareMenu({withShareTicket:!0,menus:["shareAppMessage"]}),e.index.showToast({title:"请点击右上角分享",icon:"none"})}catch(o){console.error("显示分享菜单失败:",o),e.index.showToast({title:"分享功能暂不可用",icon:"none"})}},O=()=>{try{e.wx$1.showShareMenu({withShareTicket:!0,menus:["shareAppMessage","shareTimeline"]}),e.index.showToast({title:"请点击右上角分享到朋友圈",icon:"none"})}catch(o){console.error("显示分享菜单失败:",o),e.index.showToast({title:"分享功能暂不可用",icon:"none"})}},U=e=>{if(!e)return"";const o=e.replace(/-/g,"/"),t=new Date(o),n=new Date,l=n-t;if(l<36e5){const e=Math.floor(l/6e4);return e<=0?"刚刚":`${e}分钟前`}if(l<864e5){return`${Math.floor(l/36e5)}小时前`}const i=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),u=String(t.getHours()).padStart(2,"0"),r=String(t.getMinutes()).padStart(2,"0");return i===n.getFullYear()?`${a}-${s} ${u}:${r}`:`${i}-${a}-${s} ${u}:${r}`},P=e=>{x.value!==e&&(x.value=e,console.log("切换排序方式为:",e),C(!0))},B=()=>{_.value?e.index.navigateTo({url:`/pages/bundle/world/feed/edit?id=${feedId.value}`}):e.index.showToast({title:"您无权编辑此动态",icon:"none"})},W=()=>{_.value?e.index.showModal({title:"确认删除",content:"删除后无法恢复，确定要删除这条动态吗？",confirmColor:"#6AC086",success:async n=>{if(n.confirm)try{e.index.showLoading({title:"删除中..."});const n={uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,feed_id:s.value.id};console.log("删除动态请求参数:",JSON.stringify(n));const l=await o.deleteFeed(n);console.log("删除动态返回结果:",JSON.stringify(l)),e.index.hideLoading(),"ok"===l.status?(e.index.showToast({title:"删除成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)):e.index.showToast({title:l.msg||"删除失败",icon:"none"})}catch(l){e.index.hideLoading(),console.error("删除动态失败:",l),e.index.showToast({title:"删除失败，请稍后重试",icon:"none"})}}}):e.index.showToast({title:"您无权删除此动态",icon:"none"})};return(l,a)=>{var g,T,F,z;return e.e({a:e.p({name:"arrow-left",color:"#333",size:"20"}),b:e.o(D),c:k.value},k.value?{d:w.value,e:e.o(J),f:e.o(O),g:e.o((o=>(async o=>{try{if(!o)return console.error("图片路径为空:",o),void e.index.showToast({title:"图片路径无效，无法保存",icon:"none"});if(![".jpg",".jpeg",".png",".gif",".webp"].some((e=>o.toLowerCase().includes(e)))&&!o.startsWith("wxfile://")&&!o.startsWith("http"))return console.error("图片路径格式无效:",o),void e.index.showToast({title:"图片格式不支持",icon:"none"});o.startsWith("http:")&&(console.warn("检测到http协议图片，尝试转换为https"),o=o.replace("http:","https:")),console.log("准备保存图片，路径:",o),await n.saveImageToAlbum(o),e.index.showToast({title:"图片已保存到相册",icon:"success"})}catch(t){console.error("保存图片失败:",t),t.errMsg&&t.errMsg.includes("authorize")?e.index.showModal({title:"提示",content:"需要授权访问相册才能保存图片",confirmText:"去授权",success:o=>{o.confirm&&e.index.openSetting()}}):e.index.showToast({title:"保存图片失败",icon:"none"})}})(w.value))),h:e.o((e=>k.value=!1))}:{},{i:u.value},u.value?{j:e.p({mode:"circle",size:"30",color:"#333"})}:s.value?e.e({l:(null==(g=s.value.user)?void 0:g.avatar_url)||"/static/default-avatar.png",m:e.t((null==(T=s.value.user)?void 0:T.nickname)||"用户"),n:e.t(U(s.value.created_at)),o:e.unref(_)},e.unref(_)?{p:e.p({name:"edit",color:"#6AC086",size:"18"}),q:e.o(B),r:e.p({name:"trash",color:"#999",size:"18"}),s:e.o(W)}:{},{t:e.t(s.value.content),v:p.value.length>0},p.value.length>0?{w:e.f(p.value,((o,t,n)=>({a:o,b:t,c:e.o((o=>(o=>{p.value&&0!==p.value.length&&e.index.previewImage({current:p.value[o],urls:p.value})})(t)),t)}))),x:e.n(`grid-${Math.min(p.value.length,4)}`)}:{},{y:s.value.location},s.value.location?{z:e.p({name:"map",size:"16",color:"#999"}),A:e.t(s.value.location)}:{},{B:s.value.tags},s.value.tags?{C:e.f("string"==typeof s.value.tags?s.value.tags.split(","):s.value.tags,((o,t,n)=>({a:e.t(o),b:t})))}:{},{D:s.value.isLiked?"/static/dianzanqianhou.svg":"/static/dianzanqian.svg",E:s.value.isLiked?"none":"opacity(0.7)",F:e.t(s.value.likeCount||0),G:s.value.isLiked?1:"",H:e.o(j),I:e.p({name:"chat",color:"#999",size:"20"}),J:e.t(s.value.commentCount||0),K:e.o(b),L:s.value.isFavorited?"/static/shoucanghou.svg":"/static/shoucangqian.svg",M:s.value.isFavorited?"none":"opacity(0.7)",N:e.o(q),O:r.value},r.value?e.e({P:e.t(s.value.commentCount||0),Q:"latest"===x.value?1:"",R:e.o((e=>P("latest"))),S:"hot"===x.value?1:"",T:e.o((e=>P("hot"))),U:0===c.value.length&&!v.value},(0!==c.value.length||v.value,{}),{V:e.f(c.value,((n,l,a)=>{var u,r,d;return e.e({a:(null==(u=n.user)?void 0:u.avatar)||"/static/default-avatar.png",b:e.t((null==(r=n.user)?void 0:r.nickname)||"用户"),c:e.t(U(n.created_at)),d:I(n)},I(n)?{e:"d43d67ab-6-"+a,f:e.p({name:"trash",color:"#999",size:"14"}),g:e.o((l=>(n=>{I(n)?e.index.showModal({title:"确认删除",content:"删除后无法恢复，确定要删除这条评论吗？",confirmColor:"#6AC086",success:async l=>{if(l.confirm)try{e.index.showLoading({title:"删除中..."});const l={uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,comment_id:n.id,comment_type:"feed"};console.log("删除评论请求参数:",JSON.stringify(l));const i=await o.deleteComment(l);if(console.log("删除评论返回结果:",JSON.stringify(i)),e.index.hideLoading(),"ok"===i.status){e.index.showToast({title:"删除成功",icon:"success"});const o=c.value.findIndex((e=>e.id===n.id));-1!==o&&c.value.splice(o,1),s.value&&(s.value.comment_count=Math.max(0,(s.value.comment_count||0)-1))}else e.index.showToast({title:i.msg||"删除失败",icon:"none"})}catch(i){e.index.hideLoading(),console.error("删除评论出错:",i),e.index.showToast({title:"删除失败，请稍后重试",icon:"none"})}}}):e.index.showToast({title:"您无权删除此评论",icon:"none"})})(n)),n.id)}:{},{h:n.parent_id},n.parent_id?{i:e.t((null==(d=n.reply_to)?void 0:d.nickname)||"用户")}:{},{j:e.t(n.content),k:"d43d67ab-7-"+a,l:e.p({name:n.isLiked?"thumb-up-fill":"thumb-up",color:n.isLiked?"#6AC086":"#999",size:"14"}),m:e.t(n.like_count||0),n:n.isLiked?1:"",o:e.o((l=>(async n=>{if(!i.requireLogin("","请先登录后再点赞评论"))return;const l=n.isLiked,a=n.like_count||0;n.isLiked=!n.isLiked,n.like_count=n.isLiked?a+1:a-1;try{const i={id:n.id,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,comment_type:"feed"};console.log("发送评论点赞请求:",i);const s=await o.likeComment(i);console.log("评论点赞响应:",s),"ok"!==s.status?(n.isLiked=l,n.like_count=a,e.index.showToast({title:s.msg||"操作失败",icon:"none"})):(t.store().updateCommentLike(n.id,n.isLiked,n.like_count),e.index.showToast({title:n.isLiked?"点赞成功":"已取消点赞",icon:"success",duration:1500}))}catch(s){n.isLiked=l,n.like_count=a,console.error("评论点赞失败:",s),e.index.showToast({title:"操作失败，请重试",icon:"none"})}})(n)),n.id),p:e.o((e=>{var o;return M({id:n.id,nickname:(null==(o=n.user)?void 0:o.nickname)||"用户"})}),n.id),q:n.id})})),W:v.value},v.value?{X:e.p({mode:"circle",size:"24",color:"#999"})}:{},{Y:f.value&&!v.value},f.value&&!v.value?{Z:e.o(C)}:{},{aa:e.o(C),ab:m.value,ac:e.o(S)}):{}):{},{k:s.value,ad:r.value},r.value?e.e({ae:h.value},h.value?{af:e.t(h.value.nickname),ag:e.o((e=>M(null))),ah:e.p({name:"close",size:"12",color:"#999"})}:{},{ai:e.unref($),aj:e.o(A),ak:d.value,al:e.o((e=>d.value=e.detail.value)),am:e.o(A)}):{},{an:e.o((e=>y.value=!1)),ao:e.o(N),ap:e.o(E),aq:e.p({show:y.value,title:"分享动态","share-data":L.value,"show-member-invite":0===(null==(F=e.unref(t.store)().$state.userInfo)?void 0:F.role_type)||1===(null==(z=e.unref(t.store)().$state.userInfo)?void 0:z.role_type)})})}}},u=e._export_sfc(s,[["__scopeId","data-v-d43d67ab"]]);s.__runtimeHooks=6,wx.createPage(u);
//# sourceMappingURL=detail.js.map
