"use strict";const e=require("../../../../common/vendor.js"),n={name:"u-picker",mixins:[e.mpMixin,e.mixin,e.props$9],data:()=>({lastIndex:[],innerIndex:[],innerColumns:[],columnIndex:0,showByClickInput:!1}),watch:{defaultIndex:{immediate:!0,deep:!0,handler(e){this.setIndexs(e,!0)}},columns:{immediate:!0,deep:!0,handler(e){this.setColumns(e)}}},emits:["close","cancel","confirm","change","update:modelValue","update:show"],computed:{inputLabel(){let e=this.innerColumns.map(((e,n)=>e[this.innerIndex[n]])),n=[];return e.forEach((e=>{n.push(e[this.keyName])})),n},inputValue(){let e=this.innerColumns.map(((e,n)=>e[this.innerIndex[n]])),n=[];return e.forEach((e=>{n.push(e.id)})),n}},methods:{addUnit:e.addUnit,testArray:e.test.array,getItemText(n){return e.test.object(n)?n[this.keyName]:n},closeHandler(){this.closeOnClickOverlay&&(this.hasInput&&(this.showByClickInput=!1),this.$emit("update:show",!1),this.$emit("close"))},cancel(){this.hasInput&&(this.showByClickInput=!1),this.$emit("update:show",!1),this.$emit("cancel")},confirm(){this.$emit("update:modelValue",this.inputValue),this.hasInput&&(this.showByClickInput=!1),this.$emit("update:show",!1),this.$emit("confirm",{indexs:this.innerIndex,value:this.innerColumns.map(((e,n)=>e[this.innerIndex[n]])),values:this.innerColumns})},changeHandler(e){const{value:n}=e.detail;let t=0,i=0;for(let o=0;o<n.length;o++){let e=n[o];if(e!==(this.lastIndex[o]||0)){i=o,t=e;break}}this.columnIndex=i;const s=this.innerColumns;this.setLastIndex(n),this.setIndexs(n),this.$emit("update:modelValue",this.inputValue),this.$emit("change",{value:this.innerColumns.map(((e,t)=>e[n[t]])),index:t,indexs:n,values:s,columnIndex:i})},setIndexs(n,t){this.innerIndex=e.deepClone(n),t&&this.setLastIndex(n)},setLastIndex(n){this.lastIndex=e.deepClone(n)},setColumnValues(n,t){this.innerColumns.splice(n,1,t),this.setLastIndex(this.innerIndex.slice(0,n));let i=e.deepClone(this.innerIndex);for(let e=0;e<this.innerColumns.length;e++)e>this.columnIndex&&(i[e]=0);this.setIndexs(i)},getColumnValues(n){return(async()=>{await e.sleep()})(),this.innerColumns[n]},setColumns(n){this.innerColumns=e.deepClone(n),0===this.innerIndex.length&&(this.innerIndex=new Array(n.length).fill(0))},getIndexs(){return this.innerIndex},getValues(){return(async()=>{await e.sleep()})(),this.innerColumns.map(((e,n)=>e[this.innerIndex[n]]))}}};if(!Array){(e.resolveComponent("u-toolbar")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-popup"))()}Math||((()=>"../u-toolbar/u-toolbar.js")+(()=>"../u-loading-icon/u-loading-icon.js")+(()=>"../u-popup/u-popup.js"))();const t=e._export_sfc(n,[["render",function(n,t,i,s,o,l){return e.e({a:n.hasInput},n.hasInput?{b:e.t(l.inputLabel&&l.inputLabel.length?l.inputLabel.join("/"):n.placeholder),c:e.o((e=>o.showByClickInput=!o.showByClickInput))}:{},{d:n.showToolbar},n.showToolbar?{e:e.o(l.cancel),f:e.o(l.confirm),g:e.p({cancelColor:n.cancelColor,confirmColor:n.confirmColor,cancelText:n.cancelText,confirmText:n.confirmText,title:n.title,rightSlot:!!n.toolbarRightSlot})}:{},{h:e.f(o.innerColumns,((t,i,s)=>e.e({a:l.testArray(t)},l.testArray(t)?{b:e.f(t,((n,t,s)=>({a:e.t(l.getItemText(n)),b:t,c:t===o.innerIndex[i]?"bold":"normal"}))),c:l.addUnit(n.itemHeight),d:l.addUnit(n.itemHeight)}:{},{e:i}))),i:`height: ${l.addUnit(n.itemHeight)}`,j:o.innerIndex,k:n.immediateChange,l:`${l.addUnit(n.visibleItemCount*n.itemHeight)}`,m:e.o(((...e)=>l.changeHandler&&l.changeHandler(...e))),n:n.loading},n.loading?{o:e.p({mode:"circle"})}:{},{p:e.o(l.closeHandler),q:e.p({show:n.show||n.hasInput&&o.showByClickInput,mode:n.popupMode})})}],["__scopeId","data-v-40b415f9"]]);wx.createComponent(t);
//# sourceMappingURL=u-picker.js.map
