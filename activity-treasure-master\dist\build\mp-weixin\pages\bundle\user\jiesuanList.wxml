<view><mescroll-uni wx:if="{{g}}" u-s="{{['d']}}" class="list" bindinit="{{c}}" binddown="{{d}}" bindup="{{e}}" bindtopclick="{{f}}" u-i="ac7c05fe-0" bind:__l="__l" u-p="{{g}}"><view wx:for="{{a}}" wx:for-item="val" wx:key="i" class="p30 df borderBottom"><u-avatar wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></u-avatar><view class="ml20 df fdc f1"><u-text wx:if="{{val.d}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{val.d}}"></u-text><u-text wx:if="{{b}}" u-i="{{val.e}}" bind:__l="__l" u-p="{{val.f}}"></u-text></view><u-text wx:if="{{val.h}}" u-i="{{val.g}}" bind:__l="__l" u-p="{{val.h}}"></u-text></view></mescroll-uni></view>