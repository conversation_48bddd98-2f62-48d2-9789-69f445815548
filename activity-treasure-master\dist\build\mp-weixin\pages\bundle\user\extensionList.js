"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),r=require("../../../store/index.js"),n=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),s=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-avatar")+e.resolveComponent("u-icon")+e.resolveComponent("mescroll-uni"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js"))();const o={__name:"extensionList",setup(o){const u=e.ref([]),{mescrollInit:a,downCallback:i,getMescroll:l}=n.useMescroll(e.onPageScroll,e.onReachBottom),c=e.ref("");e.ref(0),e.onReady((async()=>{c.value=await s.setListHeight()+"px"}));const m=async e=>{t.userget_xiaji_user({page:e.num,page_size:e.size,to_uid:r.store().$state.userInfo.uid}).then((t=>{const r=t.data||[];1==e.num&&(u.value=[]),u.value=u.value.concat(r),e.endBySize(r.length,t.count)})).catch((()=>{e.endErr()}))},p=e=>{switch(e){case 1:return"gender-male";case 2:return"gender-female";default:return"gender-unknown"}},g=e=>{if(!e)return"";const t=new Date(e),r=new Date-t,n=Math.floor(r/864e5);if(0===n)return"今天注册";if(1===n)return"昨天注册";if(n<30)return`${n}天前注册`;return`${t.getMonth()+1}月${t.getDate()}日注册`};return(t,n)=>e.e({a:e.p({"bg-color":"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"176rpx",title:"我的推广",backShow:!0,titleStyle:{color:"#ffffff",fontWeight:"600",fontSize:"36rpx"}}),b:u.value.length>0},u.value.length>0?{c:e.t(u.value.length),d:e.t(u.value.filter((e=>e.is_huiyuan)).length)}:{},{e:e.f(u.value,((t,n,s)=>e.e({a:"2758284a-2-"+s+",2758284a-1",b:e.p({size:"120rpx",mode:"aspectFill",src:t.avatar,shape:"circle","bg-color":"#F5F5F5"}),c:t.is_huiyuan},t.is_huiyuan?{d:`${e.unref(r.store)().$state.url}vip.png`}:{},{e:e.t(t.nickname||"未设置昵称"),f:e.t(t.uid),g:"2758284a-3-"+s+",2758284a-1",h:e.p({color:"#fff",name:0==t.sex?"question-circle":1==t.sex?"man":"woman",size:"20rpx",margin:"0 6rpx 0 0"}),i:e.t(0==t.sex?"未知":1==t.sex?"男":"女"),j:e.n(p(t.sex)),k:t.create_time},t.create_time?{l:"2758284a-4-"+s+",2758284a-1",m:e.p({name:"time",size:"24rpx",color:"#999",margin:"0 8rpx 0 0"}),n:e.t(g(t.create_time))}:{},{o:t.is_huiyuan?1:"",p:e.t(t.is_huiyuan?"会员":"普通"),q:n}))),f:0===u.value.length},0===u.value.length?{g:`${e.unref(r.store)().$state.url}empty.png`}:{},{h:e.o(e.unref(a)),i:e.o(e.unref(i)),j:e.o(m),k:e.o((e=>e.scrollTo(0))),l:e.p({height:c.value,up:{page:{num:0,size:20,time:null}}})})}},u=e._export_sfc(o,[["__scopeId","data-v-2758284a"]]);o.__runtimeHooks=1,wx.createPage(u);
//# sourceMappingURL=extensionList.js.map
