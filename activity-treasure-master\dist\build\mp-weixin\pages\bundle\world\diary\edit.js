"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../api/index.js"),o=require("../../../../store/index.js"),t=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/cacheManager.js"),require("../../../../store/counter.js"),!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-textarea")+e.resolveComponent("u-upload")+e.resolveComponent("u-icon")+e.resolveComponent("u-switch")+e.resolveComponent("u-button"))()}Math||(s+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../../node-modules/uview-plus/components/u-button/u-button.js"))();const s=()=>"../../../../components/customNavbar.js",i={__name:"edit",setup(s){const i=e.ref(""),n=e.ref([]),u=e.ref(null),l=e.ref("public"),r=e.ref(!1),c=e.ref(!1),d=e.ref(!0),v=e.ref(null),m=e.computed((()=>{const e=o.store().$state.userInfo;return 1===(null==e?void 0:e.role_type)||2===(null==e?void 0:e.role_type)?4:1}));e.onLoad((async a=>{if(!a.id)return e.index.showToast({title:"参数错误",icon:"none"}),void e.index.navigateBack();v.value=a.id,await p()}));const p=async()=>{try{d.value=!0;const t=o.store().$state.userInfo,s=await a.getDiaryDetail({id:v.value,uid:t.uid,token:t.token});if("ok"===s.status&&s.data){const a=s.data;if(a.user_id!==t.uid)return e.index.showToast({title:"您无权编辑此日记",icon:"none"}),void e.index.navigateBack();i.value=a.content||"",l.value=a.privacy||"public",r.value="private"===a.privacy,a.location&&(u.value={name:a.location}),a.images&&a.images.length>0&&(n.value=a.images.map(((e,a)=>({url:e,status:"success",message:"",uid:Date.now()+a}))))}else e.index.showToast({title:s.msg||"加载失败",icon:"none"}),e.index.navigateBack()}catch(t){console.error("加载日记数据失败:",t),e.index.showToast({title:"加载失败",icon:"none"}),e.index.navigateBack()}finally{d.value=!1}},g=async e=>{let a=[].concat(e.file),o=n.value.length;a.map((e=>{n.value.push({...e,status:"uploading",message:"上传中"})}));for(let t=0;t<a.length;t++){const e=await h(a[t].file);let s=n.value[o];n.value.splice(o,1,Object.assign(s,{status:e.success?"success":"failed",message:e.success?"":e.message,url:e.success?e.data:""})),o++}},h=e=>new Promise(((o,t)=>{let s=new FormData;s.append("file",e),a.upload_img(s).then((e=>{"ok"===e.status?o({success:!0,data:e.data.url}):o({success:!1,message:e.msg||"上传失败"})})).catch((e=>{o({success:!1,message:"上传失败"})}))})),f=e=>{n.value.splice(e.index,1)},x=e=>{l.value=e?"private":"public"},w=()=>{e.index.chooseLocation({success:e=>{u.value={name:e.name,address:e.address,latitude:e.latitude,longitude:e.longitude}},fail:e=>{console.log("选择位置失败:",e)}})},y=async()=>{if(t.requireLogin())if(i.value.trim()){if(!c.value)try{c.value=!0,e.index.showLoading({title:"保存中..."});const t=o.store().$state.userInfo,s=n.value.filter((e=>"success"===e.status&&e.url)).map((e=>e.url)),r={uid:t.uid,token:t.token,diary_id:v.value,content:i.value.trim(),images:s,location:u.value?u.value.name:"",privacy:l.value},d=await a.editDiary(r);"ok"===d.status?(e.index.hideLoading(),e.index.showToast({title:"保存成功",icon:"success"}),e.index.$emit("diary-updated",{id:v.value}),setTimeout((()=>{e.index.navigateBack()}),1500)):(e.index.hideLoading(),e.index.showToast({title:d.msg||"保存失败",icon:"none"}))}catch(s){console.error("保存日记失败:",s),e.index.hideLoading(),e.index.showToast({title:"保存失败，请重试",icon:"none"})}finally{c.value=!1}}else e.index.showToast({title:"请输入日记内容",icon:"none"})},j=()=>{e.index.navigateBack()};return(a,o)=>e.e({a:e.o(j),b:e.p({title:"编辑日记",showBack:!0}),c:d.value},d.value?{d:e.p({mode:"spinner",color:"#6AC086",size:"40"})}:{e:e.o((e=>i.value=e)),f:e.p({placeholder:"记录今天的美好时光...",maxlength:5e3,showWordLimit:!0,autoHeight:!0,minHeight:200,modelValue:i.value}),g:e.o(g),h:e.o(f),i:e.o((()=>a.uni.showToast({title:"图片大小不能超过400KB",icon:"none"}))),j:e.p({fileList:n.value,maxCount:e.unref(m),multiple:!0,previewFullImage:!0,accept:"image",maxSize:409600}),k:e.t(e.unref(m)),l:e.p({name:"map",color:"#6AC086",size:"20"}),m:e.t(u.value?u.value.name:"添加位置"),n:e.p({name:"arrow-right",color:"#999",size:"16"}),o:e.o(w),p:e.o(x),q:e.o((e=>r.value=e)),r:e.p({activeColor:"#6AC086",modelValue:r.value})},{s:e.t(c.value?"保存中...":"保存"),t:e.o(y),v:e.p({type:"primary",loading:c.value,disabled:!i.value.trim()||c.value,color:"#6AC086"})})}},n=e._export_sfc(i,[["__scopeId","data-v-6771bc08"]]);wx.createPage(n);
//# sourceMappingURL=edit.js.map
