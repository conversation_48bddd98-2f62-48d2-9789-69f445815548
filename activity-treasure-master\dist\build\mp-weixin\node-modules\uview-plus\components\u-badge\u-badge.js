"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-badge",mixins:[e.mpMixin,e.props$44,e.mixin],computed:{boxStyle:()=>({}),badgeStyle(){const t={};if(this.color&&(t.color=this.color),this.bgColor&&!this.inverted&&(t.backgroundColor=this.bgColor),this.absolute&&(t.position="absolute",this.offset.length)){const o=this.offset[0],s=this.offset[1]||o;t.top=e.addUnit(o),t.right=e.addUnit(s)}return t},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}},methods:{addStyle:e.addStyle}};const o=e._export_sfc(t,[["render",function(t,o,s,i,r,a){return e.e({a:t.show&&(0!==Number(t.value)||t.showZero||t.isDot)},t.show&&(0!==Number(t.value)||t.showZero||t.isDot)?{b:e.t(t.isDot?"":a.showValue),c:e.n(t.isDot?"u-badge--dot":"u-badge--not-dot"),d:e.n(t.inverted&&"u-badge--inverted"),e:e.n("horn"===t.shape&&"u-badge--horn"),f:e.n(`u-badge--${t.type}${t.inverted?"--inverted":""}`),g:e.s(a.addStyle(t.customStyle)),h:e.s(a.badgeStyle)}:{})}],["__scopeId","data-v-11c87331"]]);wx.createComponent(o);
//# sourceMappingURL=u-badge.js.map
