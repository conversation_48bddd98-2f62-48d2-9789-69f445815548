"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-radio-group",mixins:[e.mpMixin,e.mixin,e.props$20],computed:{parentData(){return[this.modelValue,this.disabled,this.inactiveColor,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.borderBottom,this.placement]},bemClass(){return this.bem("radio-group",["placement"])},radioGroupStyle(){const t={gap:e.addUnit(this.gap)};return e.deepMerge(t,e.addStyle(this.customStyle))}},watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.init&&e.init()}))}},data:()=>({}),created(){this.children=[]},emits:["update:modelValue","change"],methods:{unCheckedOther(e){this.children.map((t=>{e!==t&&(t.checked=!1)}));const{name:t}=e;this.$emit("update:modelValue",t),this.$emit("change",t)}}};const i=e._export_sfc(t,[["render",function(t,i,a,s,n,r){return{a:e.n(r.bemClass),b:e.s(r.radioGroupStyle)}}],["__scopeId","data-v-7be6e2fb"]]);wx.createComponent(i);
//# sourceMappingURL=u-radio-group.js.map
