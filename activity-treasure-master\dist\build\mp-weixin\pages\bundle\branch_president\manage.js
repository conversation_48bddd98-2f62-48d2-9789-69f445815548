"use strict";const e=require("../../../common/vendor.js"),n=require("../../../utils/index.js"),t=require("../../../utils/auth.js"),a=require("../../../api/index.js"),i=require("../../../store/index.js"),o=require("../../../utils/permissions.js");if(require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-icon")+e.resolveComponent("u-loading-page"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-page/u-loading-page.js"))();const s={__name:"manage",setup(s){const r=e.reactive({branch_name:"",branch_location:"",branch_id:null}),c=e.reactive({pending_activities:0,total_members:0,normal_members:0,vip_members:0,month_commission:"0.00"}),m=e.ref(!0);e.onLoad((()=>{t.requireLogin()&&(o.hasBranchManagementPermission()?l():e.index.showModal({title:"权限不足",content:"您没有分会管理权限，无法访问此页面",showCancel:!1,success:()=>{e.index.navigateBack()}}))})),e.onShow((()=>{_()}));const l=async()=>{try{m.value=!0;const e=i.store().$state.userInfo;r.branch_name=e.branch_name||"未知分会",r.branch_location=e.branch_location||"",r.branch_id=e.branch_id,await u(),await d()}catch(n){console.error("加载数据失败:",n),e.index.showToast({title:"加载失败",icon:"none"})}finally{m.value=!1}},u=async()=>{try{const e=i.store().$state.userInfo,n=await a.branch_presidentpending_activities({uid:e.uid,token:e.token,page:1,page_size:1});"ok"===n.status&&(c.pending_activities=n.count||0)}catch(e){console.error("获取待审核活动数量失败:",e)}},d=async()=>{try{const e=i.store().$state.userInfo,n=await a.branch_presidentget_stats({uid:e.uid,token:e.token});"ok"===n.status&&(c.total_members=n.data.total_members||0,c.normal_members=n.data.normal_members||0,c.vip_members=n.data.vip_members||0,c.month_commission=n.data.month_commission||"0.00")}catch(e){console.error("获取分会统计数据失败:",e)}},_=()=>{l()},p=()=>{e.index.navigateTo({url:"/pages/bundle/branch_president/branch_members"})},b=()=>{e.index.navigateTo({url:"/pages/bundle/branch_president/branch_activities"})};return(t,a)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"分会管理",color:"#ffffff",blod:!0}),b:e.t(r.branch_name||"加载中..."),c:e.t(r.branch_location||""),d:e.t(c.pending_activities||0),e:e.t(c.total_members||0),f:e.t(c.vip_members||0),g:e.t(c.normal_members||0),h:e.t(c.month_commission||"0.00"),i:e.p({name:"list",size:"40",color:"#6AC086"}),j:c.pending_activities>0},c.pending_activities>0?{k:e.t(c.pending_activities)}:{},{l:e.o((t=>e.unref(n.navto)("/pages/bundle/branch_president/pending_activities"))),m:e.p({name:"rmb-circle",size:"40",color:"#6AC086"}),n:e.o((t=>e.unref(n.navto)("/pages/bundle/branch_president/commission"))),o:e.p({name:"account",size:"40",color:"#6AC086"}),p:e.o(p),q:e.p({name:"calendar",size:"40",color:"#6AC086"}),r:e.o(b),s:e.p({name:"reload",size:"32",color:"#6AC086"}),t:e.o(_),v:e.p({loading:m.value,"loading-text":"加载中...","bg-color":"#f8f9fa"})})}},r=e._export_sfc(s,[["__scopeId","data-v-aa317403"]]);wx.createPage(r);
//# sourceMappingURL=manage.js.map
