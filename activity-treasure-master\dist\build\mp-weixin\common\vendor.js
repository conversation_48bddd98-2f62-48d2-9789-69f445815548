"use strict";function e(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function t(e){if(S(e)){const n={};for(let o=0;o<e.length;o++){const r=e[o],a=A(r)?i(r):t(r);if(a)for(const e in a)n[e]=a[e]}return n}return A(e)||C(e)?e:void 0}const n=/;(?![^(]*\))/g,o=/:([^]+)/,r=/\/\*.*?\*\//gs;function i(e){const t={};return e.replace(r,"").split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function a(e){let t="";if(A(e))t=e;else if(S(e))for(let n=0;n<e.length;n++){const o=a(e[n]);o&&(t+=o+" ")}else if(C(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const l=(e,t)=>t&&t.__v_isRef?l(e,t.value):x(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:v(t)?{[`Set(${t.size})`]:[...t.values()]}:!C(t)||S(t)||P(t)?t:String(t),s={},c=[],u=()=>{},p=()=>!1,f=/^on[^a-z]/,d=e=>f.test(e),h=e=>e.startsWith("onUpdate:"),g=Object.assign,m=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},y=Object.prototype.hasOwnProperty,b=(e,t)=>y.call(e,t),S=Array.isArray,x=e=>"[object Map]"===I(e),v=e=>"[object Set]"===I(e),w=e=>"function"==typeof e,A=e=>"string"==typeof e,B=e=>"symbol"==typeof e,C=e=>null!==e&&"object"==typeof e,k=e=>C(e)&&w(e.then)&&w(e.catch),E=Object.prototype.toString,I=e=>E.call(e),P=e=>"[object Object]"===I(e),O=e=>A(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,N=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),T=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},j=/-(\w)/g,z=T((e=>e.replace(j,((e,t)=>t?t.toUpperCase():"")))),Q=/\B([A-Z])/g,F=T((e=>e.replace(Q,"-$1").toLowerCase())),M=T((e=>e.charAt(0).toUpperCase()+e.slice(1))),q=T((e=>e?`on${M(e)}`:"")),D=(e,t)=>!Object.is(e,t),L=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},H=e=>{const t=parseFloat(e);return isNaN(t)?e:t},U="zh-Hans",V="zh-Hant",R="en";function $(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return U;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?U:e.indexOf("-hant")>-1?V:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?V:U);var n;let o=[R,"fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}const W="onShow",G="onHide",J="onLaunch",X="onError",K="onThemeChange",Y="onPageNotFound",Z="onUnhandledRejection",_="onLoad",ee="onReady",te="onUnload",ne="onInit",oe="onSaveExitState",re="onResize",ie="onBackPress",ae="onPageScroll",le="onTabItemTap",se="onReachBottom",ce="onPullDownRefresh",ue="onShareTimeline",pe="onAddToFavorites",fe="onShareAppMessage",de="onNavigationBarButtonTap",he="onNavigationBarSearchInputClicked",ge="onNavigationBarSearchInputChanged",me="onNavigationBarSearchInputConfirmed",ye="onNavigationBarSearchInputFocusChanged",be=/:/g;function Se(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function xe(e,t){if(!A(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let o=n[0];return e||(e={}),1===n.length?e[o]:xe(e[o],n.slice(1).join("."))}function ve(e){let t={};return P(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}const we=encodeURIComponent;function Ae(e,t=we){const n=e?Object.keys(e).map((n=>{let o=e[n];return void 0===typeof o||null===o?o="":P(o)&&(o=JSON.stringify(o)),t(n)+"="+t(o)})).filter((e=>e.length>0)).join("&"):null;return n?`?${n}`:""}const Be=[ne,_,W,G,te,ie,ae,le,se,ce,ue,fe,pe,oe,de,he,ge,me,ye];const Ce=[W,G,J,X,K,Y,Z,ne,_,ee,te,re,ie,ae,le,se,ce,ue,pe,fe,oe,de,he,ge,me,ye],ke=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function Ee(e,t,n=!0){return!(n&&!w(t))&&(Ce.indexOf(e)>-1||0===e.indexOf("on"))}let Ie;const Pe=[];const Oe=Se(((e,t)=>{if(w(e._component.onError))return t(e)})),Ne=function(){};Ne.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,a=o.length;i<a;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var Te=Ne;function je(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let ze=1;const Qe={};function Fe(e,t,n){if("number"==typeof e){const o=Qe[e];if(o)return o.keepAlive||delete Qe[e],o.callback(t,n)}return t}const Me="success",qe="fail",De="complete";function Le(e,t={},{beforeAll:n,beforeSuccess:o}={}){P(t)||(t={});const{success:r,fail:i,complete:a}=function(e){const t={};for(const n in e){const o=e[n];w(o)&&(t[n]=je(o),delete e[n])}return t}(t),l=w(r),s=w(i),c=w(a),u=ze++;return function(e,t,n,o=!1){Qe[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),w(n)&&n(u),u.errMsg===e+":ok"?(w(o)&&o(u,t),l&&r(u)):s&&i(u),c&&a(u)})),u}const He="success",Ue="fail",Ve="complete",Re={},$e={};function We(e,t){return function(n){return e(n,t)||n}}function Ge(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(We(i,n));else{const e=i(t,n);if(k(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Je(e,t={}){return[He,Ue,Ve].forEach((n=>{const o=e[n];if(!S(o))return;const r=t[n];t[n]=function(e){Ge(o,e,t).then((e=>w(r)&&r(e)||e))}})),t}function Xe(e,t){const n=[];S(Re.returnValue)&&n.push(...Re.returnValue);const o=$e[e];return o&&S(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Ke(e){const t=Object.create(null);Object.keys(Re).forEach((e=>{"returnValue"!==e&&(t[e]=Re[e].slice())}));const n=$e[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Ye(e,t,n,o){const r=Ke(e);if(r&&Object.keys(r).length){if(S(r.invoke)){return Ge(r.invoke,n).then((n=>t(Je(Ke(e),n),...o)))}return t(Je(r,n),...o)}return t(n,...o)}function Ze(e,t){return(n={},...o)=>function(e){return!(!P(e)||![Me,qe,De].find((t=>w(e[t]))))}(n)?Xe(e,Ye(e,t,n,o)):Xe(e,new Promise(((r,i)=>{Ye(e,t,g(n,{success:r,fail:i}),o)})))}function _e(e,t,n,o){return Fe(e,g({errMsg:t+":fail"+(n?" "+n:"")},o))}function et(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(A(e))return e}const r=function(e,t){const n=e[0];if(!t||!P(t.formatArgs)&&P(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],a=o[t];if(w(a)){const o=a(e[0][t],n);if(A(o))return o}else b(n,t)||(n[t]=a)}}(t,o);if(r)return r}function tt(e,t,n,o){return n=>{const r=Le(e,n,o),i=et(0,[n],0,o);return i?_e(r,e,i):t(n,{resolve:t=>function(e,t,n){return Fe(e,g(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>_e(r,e,function(e){return!e||A(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function nt(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=et(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}let ot=!1,rt=0,it=0;function at(){const{platform:e,pixelRatio:t,windowWidth:n}=wx.getSystemInfoSync();rt=n,it=t,ot="ios"===e}const lt=nt(0,((e,t)=>{if(0===rt&&at(),0===(e=Number(e)))return 0;let n=e/750*(t||rt);return n<0&&(n=-n),n=Math.floor(n+1e-4),0===n&&(n=1!==it&&ot?.5:1),e<0?-n:n}));function st(e,t){Object.keys(t).forEach((n=>{w(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):S(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function ct(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];S(o)&&w(r)&&m(o,r)}))}const ut=nt(0,((e,t)=>{A(e)&&P(t)?st($e[e]||($e[e]={}),t):P(e)&&st(Re,e)})),pt=nt(0,((e,t)=>{A(e)?P(t)?ct($e[e],t):delete $e[e]:P(e)&&ct(Re,e)})),ft=new Te,dt=nt(0,((e,t)=>(ft.on(e,t),()=>ft.off(e,t)))),ht=nt(0,((e,t)=>(ft.once(e,t),()=>ft.off(e,t)))),gt=nt(0,((e,t)=>{e?(S(e)||(e=[e]),e.forEach((e=>ft.off(e,t)))):ft.e={}})),mt=nt(0,((e,...t)=>{ft.emit(e,...t)}));let yt,bt,St;function xt(e){try{return JSON.parse(e)}catch(t){}return e}const vt=[];function wt(e,t){vt.forEach((n=>{n(e,t)})),vt.length=0}const At=Ze(Bt="getPushClientId",function(e,t,n,o){return tt(e,t,0,o)}(Bt,((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===St&&(St=!1,yt="",bt="uniPush is not enabled"),vt.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==yt&&wt(yt,bt)}))}),0,Ct));var Bt,Ct;const kt=[],Et=/^\$|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,It=/^create|Manager$/,Pt=["createBLEConnection"],Ot=["createBLEConnection"],Nt=/^on|^off/;function Tt(e){return It.test(e)&&-1===Pt.indexOf(e)}function jt(e){return Et.test(e)&&-1===Ot.indexOf(e)}function zt(e){return!(Tt(e)||jt(e)||function(e){return Nt.test(e)&&"onPush"!==e}(e))}function Qt(e,t){return zt(e)&&w(t)?function(n={},...o){return w(n.success)||w(n.fail)||w(n.complete)?Xe(e,Ye(e,t,n,o)):Xe(e,new Promise(((r,i)=>{Ye(e,t,g({},n,{success:r,fail:i}),o)})))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e&&e()).then((()=>n))),(n=>t.resolve(e&&e()).then((()=>{throw n}))))});const Ft=["success","fail","cancel","complete"];const Mt=()=>{const e=w(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:$(wx.getSystemInfoSync().language)||R},qt=[];"undefined"!=typeof global&&(global.getLocale=Mt);const Dt="__DC_STAT_UUID";let Lt;function Ht(e=wx){return function(t,n){Lt=Lt||e.getStorageSync(Dt),Lt||(Lt=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:Dt,data:Lt})),n.deviceId=Lt}}function Ut(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function Vt(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(e),r=t.toLocaleLowerCase();for(let t=0;t<o.length;t++){const i=o[t];if(-1!==r.indexOf(i)){n=e[i];break}}}return n}function Rt(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function $t(e){return Mt?Mt():e}function Wt(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const Gt={returnValue:(e,t)=>{Ut(e,t),Ht()(e,t),function(e,t){const{brand:n="",model:o="",system:r="",language:i="",theme:a,version:l,platform:s,fontSizeSetting:c,SDKVersion:u,pixelRatio:p,deviceOrientation:f}=e;let d="",h="";d=r.split(" ")[0]||"",h=r.split(" ")[1]||"";let m=l,y=Vt(e,o),b=Rt(n),S=Wt(e),x=f,v=p,w=u;const A=i.replace(/_/g,"-"),B={appId:"__UNI__BFAFD02",appName:"",appVersion:"1.0.0",appVersionCode:"100",appLanguage:$t(A),uniCompileVersion:"3.8.7",uniRuntimeVersion:"3.8.7",uniPlatform:"mp-weixin",deviceBrand:b,deviceModel:o,deviceType:y,devicePixelRatio:v,deviceOrientation:x,osName:d.toLocaleLowerCase(),osVersion:h,hostTheme:a,hostVersion:m,hostLanguage:A,hostName:S,hostSDKVersion:w,hostFontSizeSetting:c,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0};g(t,B)}(e,t)}},Jt=Gt,Xt={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!S(o))return;const r=o.length;return r?(n<0?n=0:n>=r&&(n=r-1),n>0?(t.current=o[n],t.urls=o.filter(((e,t)=>!(t<n)||e!==o[n]))):t.current=o[0],{indicator:!1,loop:!1}):void 0}},Kt={args(e,t){t.alertText=e.title}},Yt={returnValue:(e,t)=>{const{brand:n,model:o}=e;let r=Vt(e,o),i=Rt(n);Ht()(e,t),t=ve(g(t,{deviceType:r,deviceBrand:i,deviceModel:o}))}},Zt={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:r,theme:i}=e;let a=Wt(e),l=o.replace(/_/g,"-");t=ve(g(t,{hostVersion:n,hostLanguage:l,hostName:a,hostSDKVersion:r,hostTheme:i,appId:"__UNI__BFAFD02",appName:"",appVersion:"1.0.0",appVersionCode:"100",appLanguage:$t(l)}))}},_t={returnValue:(e,t)=>{Ut(e,t),t=ve(g(t,{windowTop:0,windowBottom:0}))}},en={$on:dt,$off:gt,$once:ht,$emit:mt,upx2px:lt,interceptors:{},addInterceptor:ut,removeInterceptor:pt,onCreateVueApp:function(e){if(Ie)return e(Ie);Pe.push(e)},invokeCreateVueAppHook:function(e){Ie=e,Pe.forEach((t=>t(e)))},getLocale:Mt,setLocale:e=>{const t=w(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,qt.forEach((t=>t({locale:e}))),!0)},onLocaleChange:e=>{-1===qt.indexOf(e)&&qt.push(e)},getPushClientId:At,onPushMessage:e=>{-1===kt.indexOf(e)&&kt.push(e)},offPushMessage:e=>{if(e){const t=kt.indexOf(e);t>-1&&kt.splice(t,1)}else kt.length=0},invokePushCallback:function(e){if("enabled"===e.type)St=!0;else if("clientId"===e.type)yt=e.cid,bt=e.errMsg,wt(yt,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:xt(e.message)};for(let e=0;e<kt.length;e++){if((0,kt[e])(t),t.stopped)break}}else"click"===e.type&&kt.forEach((t=>{t({type:"click",data:xt(e.message)})}))}};const tn=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],nn=["lanDebug","router","worklet"],on=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function rn(e){return(!on||1154!==on.scene||!nn.includes(e))&&(tn.indexOf(e)>-1||"function"==typeof wx[e])}function an(){const e={};for(const t in wx)rn(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const ln=["__route__","__wxExparserNodeId__","__wxWebviewId__"],sn=(cn={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]},function({service:e,success:t,fail:n,complete:o}){let r;cn[e]?(r={errMsg:"getProvider:ok",service:e,provider:cn[e]},w(t)&&t(r)):(r={errMsg:"getProvider:fail:服务["+e+"]不存在"},w(n)&&n(r)),w(o)&&o(r)});var cn;const un=an();let pn=un.getAppBaseInfo&&un.getAppBaseInfo();pn||(pn=un.getSystemInfoSync());const fn=pn?pn.host:null,dn=fn&&"SAAASDK"===fn.env?un.miniapp.shareVideoMessage:un.shareVideoMessage;var hn=Object.freeze({__proto__:null,createSelectorQuery:function(){const e=un.createSelectorQuery(),t=e.in;return e.in=function(e){return t.call(this,function(e){const t=Object.create(null);return ln.forEach((n=>{t[n]=e[n]})),t}(e))},e},getProvider:sn,shareVideoMessage:dn});const gn={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var mn=Object.freeze({__proto__:null,compressImage:gn,getAppAuthorizeSetting:{returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},getAppBaseInfo:Zt,getDeviceInfo:Yt,getSystemInfo:Gt,getSystemInfoSync:Jt,getWindowInfo:_t,previewImage:Xt,redirectTo:{},showActionSheet:Kt});const yn=an();var bn=function(e,t,n=wx){const o=function(e){function t(e,t,n){return function(r){return t(o(e,r,n))}}function n(e,n,o={},r={},i=!1){if(P(n)){const a=!0===i?n:{};w(o)&&(o=o(n,a)||{});for(const l in n)if(b(o,l)){let t=o[l];w(t)&&(t=t(n[l],n,a)),t?A(t)?a[t]=n[l]:P(t)&&(a[t.name?t.name:l]=t.value):console.warn(`微信小程序 ${e} 暂不支持 ${l}`)}else if(-1!==Ft.indexOf(l)){const o=n[l];w(o)&&(a[l]=t(e,o,r))}else i||b(a,l)||(a[l]=n[l]);return a}return w(n)&&(n=t(e,n,r)),n}function o(t,o,r,i=!1){return w(e.returnValue)&&(o=e.returnValue(t,o)),n(t,o,r,{},i)}return function(t,r){if(!b(e,t))return r;const i=e[t];return i?function(e,r){let a=i;w(i)&&(a=i(e));const l=[e=n(t,e,a.args,a.returnValue)];void 0!==r&&l.push(r);const s=wx[a.name||t].apply(wx,l);return jt(t)?o(t,s,a.returnValue,Tt(t)):s}:function(){console.error(`微信小程序 暂不支持${t}`)}}}(t);return new Proxy({},{get:(t,r)=>b(t,r)?t[r]:b(e,r)?Qt(r,e[r]):b(en,r)?Qt(r,en[r]):Qt(r,o(r,n[r]))})}(hn,mn,yn);let Sn;class xn{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Sn,!e&&Sn&&(this.index=(Sn.scopes||(Sn.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Sn;try{return Sn=this,e()}finally{Sn=t}}}on(){Sn=this}off(){Sn=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function vn(e){return new xn(e)}function wn(){return Sn}const An=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Bn=e=>(e.w&In)>0,Cn=e=>(e.n&In)>0,kn=new WeakMap;let En=0,In=1;let Pn;const On=Symbol(""),Nn=Symbol("");class Tn{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=Sn){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=Pn,t=zn;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=Pn,Pn=this,zn=!0,In=1<<++En,En<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=In})(this):jn(this),this.fn()}finally{En<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];Bn(r)&&!Cn(r)?r.delete(e):t[n++]=r,r.w&=~In,r.n&=~In}t.length=n}})(this),In=1<<--En,Pn=this.parent,zn=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Pn===this?this.deferStop=!0:this.active&&(jn(this),this.onStop&&this.onStop(),this.active=!1)}}function jn(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let zn=!0;const Qn=[];function Fn(){Qn.push(zn),zn=!1}function Mn(){const e=Qn.pop();zn=void 0===e||e}function qn(e,t,n){if(zn&&Pn){let t=kn.get(e);t||kn.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=An()),Dn(o)}}function Dn(e,t){let n=!1;En<=30?Cn(e)||(e.n|=In,n=!Bn(e)):n=!e.has(Pn),n&&(e.add(Pn),Pn.deps.push(e))}function Ln(e,t,n,o,r,i){const a=kn.get(e);if(!a)return;let l=[];if("clear"===t)l=[...a.values()];else if("length"===n&&S(e)){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n>=e)&&l.push(t)}))}else switch(void 0!==n&&l.push(a.get(n)),t){case"add":S(e)?O(n)&&l.push(a.get("length")):(l.push(a.get(On)),x(e)&&l.push(a.get(Nn)));break;case"delete":S(e)||(l.push(a.get(On)),x(e)&&l.push(a.get(Nn)));break;case"set":x(e)&&l.push(a.get(On))}if(1===l.length)l[0]&&Hn(l[0]);else{const e=[];for(const t of l)t&&e.push(...t);Hn(An(e))}}function Hn(e,t){const n=S(e)?e:[...e];for(const o of n)o.computed&&Un(o);for(const o of n)o.computed||Un(o)}function Un(e,t){(e!==Pn||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const Vn=e("__proto__,__v_isRef,__isVue"),Rn=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(B)),$n=Yn(),Wn=Yn(!1,!0),Gn=Yn(!0),Jn=Xn();function Xn(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Qo(this);for(let t=0,r=this.length;t<r;t++)qn(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Qo)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Fn();const n=Qo(this)[t].apply(this,e);return Mn(),n}})),e}function Kn(e){const t=Qo(this);return qn(t,0,e),t.hasOwnProperty(e)}function Yn(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?Eo:ko:t?Co:Bo).get(n))return n;const i=S(n);if(!e){if(i&&b(Jn,o))return Reflect.get(Jn,o,r);if("hasOwnProperty"===o)return Kn}const a=Reflect.get(n,o,r);return(B(o)?Rn.has(o):Vn(o))?a:(e||qn(n,0,o),t?a:Ho(a)?i&&O(o)?a:a.value:C(a)?e?Oo(a):Po(a):a)}}function Zn(e=!1){return function(t,n,o,r){let i=t[n];if(jo(i)&&Ho(i)&&!Ho(o))return!1;if(!e&&(zo(o)||jo(o)||(i=Qo(i),o=Qo(o)),!S(t)&&Ho(i)&&!Ho(o)))return i.value=o,!0;const a=S(t)&&O(n)?Number(n)<t.length:b(t,n),l=Reflect.set(t,n,o,r);return t===Qo(r)&&(a?D(o,i)&&Ln(t,"set",n,o):Ln(t,"add",n,o)),l}}const _n={get:$n,set:Zn(),deleteProperty:function(e,t){const n=b(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Ln(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return B(t)&&Rn.has(t)||qn(e,0,t),n},ownKeys:function(e){return qn(e,0,S(e)?"length":On),Reflect.ownKeys(e)}},eo={get:Gn,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},to=g({},_n,{get:Wn,set:Zn(!0)}),no=e=>e,oo=e=>Reflect.getPrototypeOf(e);function ro(e,t,n=!1,o=!1){const r=Qo(e=e.__v_raw),i=Qo(t);n||(t!==i&&qn(r,0,t),qn(r,0,i));const{has:a}=oo(r),l=o?no:n?qo:Mo;return a.call(r,t)?l(e.get(t)):a.call(r,i)?l(e.get(i)):void(e!==r&&e.get(t))}function io(e,t=!1){const n=this.__v_raw,o=Qo(n),r=Qo(e);return t||(e!==r&&qn(o,0,e),qn(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function ao(e,t=!1){return e=e.__v_raw,!t&&qn(Qo(e),0,On),Reflect.get(e,"size",e)}function lo(e){e=Qo(e);const t=Qo(this);return oo(t).has.call(t,e)||(t.add(e),Ln(t,"add",e,e)),this}function so(e,t){t=Qo(t);const n=Qo(this),{has:o,get:r}=oo(n);let i=o.call(n,e);i||(e=Qo(e),i=o.call(n,e));const a=r.call(n,e);return n.set(e,t),i?D(t,a)&&Ln(n,"set",e,t):Ln(n,"add",e,t),this}function co(e){const t=Qo(this),{has:n,get:o}=oo(t);let r=n.call(t,e);r||(e=Qo(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Ln(t,"delete",e,void 0),i}function uo(){const e=Qo(this),t=0!==e.size,n=e.clear();return t&&Ln(e,"clear",void 0,void 0),n}function po(e,t){return function(n,o){const r=this,i=r.__v_raw,a=Qo(i),l=t?no:e?qo:Mo;return!e&&qn(a,0,On),i.forEach(((e,t)=>n.call(o,l(e),l(t),r)))}}function fo(e,t,n){return function(...o){const r=this.__v_raw,i=Qo(r),a=x(i),l="entries"===e||e===Symbol.iterator&&a,s="keys"===e&&a,c=r[e](...o),u=n?no:t?qo:Mo;return!t&&qn(i,0,s?Nn:On),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:l?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function ho(e){return function(...t){return"delete"!==e&&this}}function go(){const e={get(e){return ro(this,e)},get size(){return ao(this)},has:io,add:lo,set:so,delete:co,clear:uo,forEach:po(!1,!1)},t={get(e){return ro(this,e,!1,!0)},get size(){return ao(this)},has:io,add:lo,set:so,delete:co,clear:uo,forEach:po(!1,!0)},n={get(e){return ro(this,e,!0)},get size(){return ao(this,!0)},has(e){return io.call(this,e,!0)},add:ho("add"),set:ho("set"),delete:ho("delete"),clear:ho("clear"),forEach:po(!0,!1)},o={get(e){return ro(this,e,!0,!0)},get size(){return ao(this,!0)},has(e){return io.call(this,e,!0)},add:ho("add"),set:ho("set"),delete:ho("delete"),clear:ho("clear"),forEach:po(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=fo(r,!1,!1),n[r]=fo(r,!0,!1),t[r]=fo(r,!1,!0),o[r]=fo(r,!0,!0)})),[e,n,t,o]}const[mo,yo,bo,So]=go();function xo(e,t){const n=t?e?So:bo:e?yo:mo;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(b(n,o)&&o in t?n:t,o,r)}const vo={get:xo(!1,!1)},wo={get:xo(!1,!0)},Ao={get:xo(!0,!1)},Bo=new WeakMap,Co=new WeakMap,ko=new WeakMap,Eo=new WeakMap;function Io(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>I(e).slice(8,-1))(e))}function Po(e){return jo(e)?e:No(e,!1,_n,vo,Bo)}function Oo(e){return No(e,!0,eo,Ao,ko)}function No(e,t,n,o,r){if(!C(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const a=Io(e);if(0===a)return e;const l=new Proxy(e,2===a?o:n);return r.set(e,l),l}function To(e){return jo(e)?To(e.__v_raw):!(!e||!e.__v_isReactive)}function jo(e){return!(!e||!e.__v_isReadonly)}function zo(e){return!(!e||!e.__v_isShallow)}function Qo(e){const t=e&&e.__v_raw;return t?Qo(t):e}function Fo(e){return((e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})})(e,"__v_skip",!0),e}const Mo=e=>C(e)?Po(e):e,qo=e=>C(e)?Oo(e):e;function Do(e){zn&&Pn&&Dn((e=Qo(e)).dep||(e.dep=An()))}function Lo(e,t){const n=(e=Qo(e)).dep;n&&Hn(n)}function Ho(e){return!(!e||!0!==e.__v_isRef)}function Uo(e){return function(e,t){if(Ho(e))return e;return new Vo(e,t)}(e,!1)}class Vo{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Qo(e),this._value=t?e:Mo(e)}get value(){return Do(this),this._value}set value(e){const t=this.__v_isShallow||zo(e)||jo(e);e=t?e:Qo(e),D(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Mo(e),Lo(this))}}function Ro(e){return Ho(e)?e.value:e}const $o={get:(e,t,n)=>Ro(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Ho(r)&&!Ho(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Wo(e){return To(e)?e:new Proxy(e,$o)}class Go{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=Qo(this._object),t=this._key,null===(n=kn.get(e))||void 0===n?void 0:n.get(t);var e,t,n}}function Jo(e,t,n){const o=e[t];return Ho(o)?o:new Go(e,t,n)}var Xo;class Ko{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[Xo]=!1,this._dirty=!0,this.effect=new Tn(e,(()=>{this._dirty||(this._dirty=!0,Lo(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Qo(this);return Do(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function Yo(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){_o(i,t,n)}return r}function Zo(e,t,n,o){if(w(e)){const r=Yo(e,t,n,o);return r&&k(r)&&r.catch((e=>{_o(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(Zo(e[i],t,n,o));return r}function _o(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void Yo(a,null,10,[e,r,i])}!function(e){console.error(e)}(e,0,0,o)}Xo="__v_isReadonly";let er=!1,tr=!1;const nr=[];let or=0;const rr=[];let ir=null,ar=0;const lr=Promise.resolve();let sr=null;function cr(e){const t=sr||lr;return e?t.then(this?e.bind(this):e):t}function ur(e){nr.length&&nr.includes(e,er&&e.allowRecurse?or+1:or)||(null==e.id?nr.push(e):nr.splice(function(e){let t=or+1,n=nr.length;for(;t<n;){const o=t+n>>>1;hr(nr[o])<e?t=o+1:n=o}return t}(e.id),0,e),pr())}function pr(){er||tr||(tr=!0,sr=lr.then(mr))}function fr(e){S(e)?rr.push(...e):ir&&ir.includes(e,e.allowRecurse?ar+1:ar)||rr.push(e),pr()}function dr(e,t=(er?or+1:0)){for(;t<nr.length;t++){const e=nr[t];e&&e.pre&&(nr.splice(t,1),t--,e())}}const hr=e=>null==e.id?1/0:e.id,gr=(e,t)=>{const n=hr(e)-hr(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function mr(e){tr=!1,er=!0,nr.sort(gr);try{for(or=0;or<nr.length;or++){const e=nr[or];e&&!1!==e.active&&Yo(e,null,14)}}finally{or=0,nr.length=0,function(){if(rr.length){const e=[...new Set(rr)];if(rr.length=0,ir)return void ir.push(...e);for(ir=e,ir.sort(((e,t)=>hr(e)-hr(t))),ar=0;ar<ir.length;ar++)ir[ar]();ir=null,ar=0}}(),er=!1,sr=null,(nr.length||rr.length)&&mr()}}function yr(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||s;let r=n;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:i}=o[e]||s;i&&(r=n.map((e=>A(e)?e.trim():e))),t&&(r=n.map(H))}let l,c=o[l=q(t)]||o[l=q(z(t))];!c&&i&&(c=o[l=q(F(t))]),c&&Zo(c,e,6,r);const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Zo(u,e,6,r)}}function br(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let a={},l=!1;if(!w(e)){const o=e=>{const n=br(e,t,!0);n&&(l=!0,g(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||l?(S(i)?i.forEach((e=>a[e]=null)):g(a,i),C(e)&&o.set(e,a),a):(C(e)&&o.set(e,null),null)}function Sr(e,t){return!(!e||!d(t))&&(t=t.slice(2).replace(/Once$/,""),b(e,t[0].toLowerCase()+t.slice(1))||b(e,F(t))||b(e,t))}let xr=null;function vr(e){const t=xr;return xr=e,e&&e.type.__scopeId,t}function wr(e,t,n=!1){const o=Ai||xr;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&w(t)?t.call(o.proxy):t}}const Ar={};function Br(e,t,n){return Cr(e,t,n)}function Cr(e,t,{immediate:n,deep:o,flush:r,onTrack:i,onTrigger:a}=s){const l=wn()===(null==Ai?void 0:Ai.scope)?Ai:null;let c,p,f=!1,d=!1;if(Ho(e)?(c=()=>e.value,f=zo(e)):To(e)?(c=()=>e,o=!0):S(e)?(d=!0,f=e.some((e=>To(e)||zo(e))),c=()=>e.map((e=>Ho(e)?e.value:To(e)?Ir(e):w(e)?Yo(e,l,2):void 0))):c=w(e)?t?()=>Yo(e,l,2):()=>{if(!l||!l.isUnmounted)return p&&p(),Zo(e,l,3,[h])}:u,t&&o){const e=c;c=()=>Ir(e())}let h=e=>{p=x.onStop=()=>{Yo(e,l,4)}},g=d?new Array(e.length).fill(Ar):Ar;const y=()=>{if(x.active)if(t){const e=x.run();(o||f||(d?e.some(((e,t)=>D(e,g[t]))):D(e,g)))&&(p&&p(),Zo(t,l,3,[e,g===Ar?void 0:d&&g[0]===Ar?[]:g,h]),g=e)}else x.run()};let b;y.allowRecurse=!!t,"sync"===r?b=y:"post"===r?b=()=>bi(y,l&&l.suspense):(y.pre=!0,l&&(y.id=l.uid),b=()=>ur(y));const x=new Tn(c,b);t?n?y():g=x.run():"post"===r?bi(x.run.bind(x),l&&l.suspense):x.run();return()=>{x.stop(),l&&l.scope&&m(l.scope.effects,x)}}function kr(e,t,n){const o=this.proxy,r=A(e)?e.includes(".")?Er(o,e):()=>o[e]:e.bind(o,o);let i;w(t)?i=t:(i=t.handler,n=t);const a=Ai;Ci(this);const l=Cr(r,i.bind(o),n);return a?Ci(a):ki(),l}function Er(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Ir(e,t){if(!C(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),Ho(e))Ir(e.value,t);else if(S(e))for(let n=0;n<e.length;n++)Ir(e[n],t);else if(v(e)||x(e))e.forEach((e=>{Ir(e,t)}));else if(P(e))for(const n in e)Ir(e[n],t);return e}function Pr(e,t){Nr(e,"a",t)}function Or(e,t){Nr(e,"da",t)}function Nr(e,t,n=Ai){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(jr(t,o,n),n){let e=n.parent;for(;e&&e.parent;)e.parent.vnode.type.__isKeepAlive&&Tr(o,t,n,e),e=e.parent}}function Tr(e,t,n,o){const r=jr(t,e,o,!0);Lr((()=>{m(o[t],r)}),n)}function jr(e,t,n=Ai,o=!1){if(n){(function(e){return Be.indexOf(e)>-1})(e)&&(n=n.root);const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Fn(),Ci(n);const r=Zo(t,n,e,o);return ki(),Mn(),r});return o?r.unshift(i):r.push(i),i}}const zr=e=>(t,n=Ai)=>(!Ii||"sp"===e)&&jr(e,((...e)=>t(...e)),n),Qr=zr("bm"),Fr=zr("m"),Mr=zr("bu"),qr=zr("u"),Dr=zr("bum"),Lr=zr("um"),Hr=zr("sp"),Ur=zr("rtg"),Vr=zr("rtc");function Rr(e,t=Ai){jr("ec",e,t)}const $r="components";function Wr(e,t){return e&&(e[t]||e[z(t)]||e[M(z(t))])}const Gr=e=>e?Ei(e)?Ni(e)||e.proxy:Gr(e.parent):null,Jr=g(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Gr(e.parent),$root:e=>Gr(e.root),$emit:e=>e.emit,$options:e=>ti(e),$forceUpdate:e=>e.f||(e.f=()=>ur(e.update)),$watch:e=>kr.bind(e)}),Xr=(e,t)=>e!==s&&!e.__isScriptSetup&&b(e,t),Kr={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Xr(o,t))return a[t]=1,o[t];if(r!==s&&b(r,t))return a[t]=2,r[t];if((u=e.propsOptions[0])&&b(u,t))return a[t]=3,i[t];if(n!==s&&b(n,t))return a[t]=4,n[t];Yr&&(a[t]=0)}}const p=Jr[t];let f,d;return p?("$attrs"===t&&qn(e,0,t),p(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==s&&b(n,t)?(a[t]=4,n[t]):(d=c.config.globalProperties,b(d,t)?d[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return Xr(r,t)?(r[t]=n,!0):o!==s&&b(o,t)?(o[t]=n,!0):!b(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},a){let l;return!!n[a]||e!==s&&b(e,a)||Xr(t,a)||(l=i[0])&&b(l,a)||b(o,a)||b(Jr,a)||b(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:b(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let Yr=!0;function Zr(e){const t=ti(e),n=e.proxy,o=e.ctx;Yr=!1,t.beforeCreate&&_r(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:a,watch:l,provide:s,inject:c,created:p,beforeMount:f,mounted:d,beforeUpdate:h,updated:g,activated:m,deactivated:y,beforeDestroy:b,beforeUnmount:x,destroyed:v,unmounted:A,render:B,renderTracked:k,renderTriggered:E,errorCaptured:I,serverPrefetch:P,expose:O,inheritAttrs:N,components:T,directives:j,filters:z}=t;if(c&&function(e,t,n=u,o=!1){S(e)&&(e=ii(e));for(const r in e){const n=e[r];let i;i=C(n)?"default"in n?wr(n.from||r,n.default,!0):wr(n.from||r):wr(n),Ho(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(c,o,null,e.appContext.config.unwrapInjectedRef),a)for(const u in a){const e=a[u];w(e)&&(o[u]=e.bind(n))}if(r){const t=r.call(n,n);C(t)&&(e.data=Po(t))}if(Yr=!0,i)for(const S in i){const e=i[S],t=w(e)?e.bind(n,n):w(e.get)?e.get.bind(n,n):u,r=!w(e)&&w(e.set)?e.set.bind(n):u,a=Ti({get:t,set:r});Object.defineProperty(o,S,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const u in l)ei(l[u],o,n,u);if(s){const e=w(s)?s.call(n):s;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(Ai){let n=Ai.provides;const o=Ai.parent&&Ai.parent.provides;o===n&&(n=Ai.provides=Object.create(o)),n[e]=t,"app"===Ai.type.mpType&&Ai.appContext.app.provide(e,t)}}(t,e[t])}))}function Q(e,t){S(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(p&&_r(p,e,"c"),Q(Qr,f),Q(Fr,d),Q(Mr,h),Q(qr,g),Q(Pr,m),Q(Or,y),Q(Rr,I),Q(Vr,k),Q(Ur,E),Q(Dr,x),Q(Lr,A),Q(Hr,P),S(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});B&&e.render===u&&(e.render=B),null!=N&&(e.inheritAttrs=N),T&&(e.components=T),j&&(e.directives=j),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function _r(e,t,n){Zo(S(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function ei(e,t,n,o){const r=o.includes(".")?Er(n,o):()=>n[o];if(A(e)){const n=t[e];w(n)&&Br(r,n)}else if(w(e))Br(r,e.bind(n));else if(C(e))if(S(e))e.forEach((e=>ei(e,t,n,o)));else{const o=w(e.handler)?e.handler.bind(n):t[e.handler];w(o)&&Br(r,o,e)}}function ti(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,l=i.get(t);let s;return l?s=l:r.length||n||o?(s={},r.length&&r.forEach((e=>ni(s,e,a,!0))),ni(s,t,a)):s=t,C(t)&&i.set(t,s),s}function ni(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&ni(e,i,n,!0),r&&r.forEach((t=>ni(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=oi[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const oi={data:ri,props:li,emits:li,methods:li,computed:li,beforeCreate:ai,created:ai,beforeMount:ai,mounted:ai,beforeUpdate:ai,updated:ai,beforeDestroy:ai,beforeUnmount:ai,destroyed:ai,unmounted:ai,activated:ai,deactivated:ai,errorCaptured:ai,serverPrefetch:ai,components:li,directives:li,watch:function(e,t){if(!e)return t;if(!t)return e;const n=g(Object.create(null),e);for(const o in t)n[o]=ai(e[o],t[o]);return n},provide:ri,inject:function(e,t){return li(ii(e),ii(t))}};function ri(e,t){return t?e?function(){return g(w(e)?e.call(this,this):e,w(t)?t.call(this,this):t)}:t:e}function ii(e){if(S(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ai(e,t){return e?[...new Set([].concat(e,t))]:t}function li(e,t){return e?g(g(Object.create(null),e),t):t}function si(e,t,n,o=!1){const r={},i={};e.propsDefaults=Object.create(null),ci(e,t,r,i);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);n?e.props=o?r:No(r,!1,to,wo,Co):e.type.props?e.props=r:e.props=i,e.attrs=i}function ci(e,t,n,o){const[r,i]=e.propsOptions;let a,l=!1;if(t)for(let s in t){if(N(s))continue;const c=t[s];let u;r&&b(r,u=z(s))?i&&i.includes(u)?(a||(a={}))[u]=c:n[u]=c:Sr(e.emitsOptions,s)||s in o&&c===o[s]||(o[s]=c,l=!0)}if(i){const t=Qo(n),o=a||s;for(let a=0;a<i.length;a++){const l=i[a];n[l]=ui(r,t,l,o[l],e,!b(o,l))}}return l}function ui(e,t,n,o,r,i){const a=e[n];if(null!=a){const e=b(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&w(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(Ci(r),o=i[n]=e.call(null,t),ki())}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==F(n)||(o=!0))}return o}function pi(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,a={},l=[];let u=!1;if(!w(e)){const o=e=>{u=!0;const[n,o]=pi(e,t,!0);g(a,n),o&&l.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!u)return C(e)&&o.set(e,c),c;if(S(i))for(let c=0;c<i.length;c++){const e=z(i[c]);fi(e)&&(a[e]=s)}else if(i)for(const s in i){const e=z(s);if(fi(e)){const t=i[s],n=a[e]=S(t)||w(t)?{type:t}:Object.assign({},t);if(n){const t=gi(Boolean,n.type),o=gi(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||b(n,"default"))&&l.push(e)}}}const p=[a,l];return C(e)&&o.set(e,p),p}function fi(e){return"$"!==e[0]}function di(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function hi(e,t){return di(e)===di(t)}function gi(e,t){return S(t)?t.findIndex((t=>hi(t,e))):w(t)&&hi(t,e)?0:-1}function mi(){return{app:null,config:{isNativeTag:p,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let yi=0;const bi=fr;function Si(e){return e?To(t=e)||jo(t)||"__vInternal"in e?g({},e):e:null;var t}const xi=mi();let vi=0;function wi(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||xi,i={uid:vi++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new xn(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:pi(o,r),emitsOptions:br(o,r),emit:null,emitted:null,propsDefaults:s,inheritAttrs:o.inheritAttrs,ctx:s,data:s,props:s,attrs:s,slots:s,refs:s,setupState:s,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=yr.bind(null,i),e.ce&&e.ce(i),i}let Ai=null;const Bi=()=>Ai||xr,Ci=e=>{Ai=e,e.scope.on()},ki=()=>{Ai&&Ai.scope.off(),Ai=null};function Ei(e){return 4&e.vnode.shapeFlag}let Ii=!1;function Pi(e,t=!1){Ii=t;const{props:n}=e.vnode,o=Ei(e);si(e,n,o,t);const r=o?function(e){const t=e.type;e.accessCache=Object.create(null),e.proxy=Fo(new Proxy(e.ctx,Kr));const{setup:n}=t;if(n){const t=e.setupContext=n.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(qn(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;Ci(e),Fn();const o=Yo(n,e,0,[e.props,t]);Mn(),ki(),k(o)?o.then(ki,ki):function(e,t){w(t)?e.render=t:C(t)&&(e.setupState=Wo(t));Oi(e)}(e,o)}else Oi(e)}(e):void 0;return Ii=!1,r}function Oi(e,t,n){const o=e.type;e.render||(e.render=o.render||u),Ci(e),Fn(),Zr(e),Mn(),ki()}function Ni(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Wo(Fo(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in Jr}))}const Ti=(e,t)=>function(e,t,n=!1){let o,r;const i=w(e);return i?(o=e,r=u):(o=e.get,r=e.set),new Ko(o,r,i||!r,n)}(e,0,Ii),ji="3.2.47";function zi(e){return Ro(e)}const Qi="[object Array]",Fi="[object Object]";function Mi(e,t){const n={};return qi(e,t),Di(e,t,"",n),n}function qi(e,t){if((e=zi(e))===t)return;const n=I(e),o=I(t);if(n==Fi&&o==Fi)for(let r in t){const n=e[r];void 0===n?e[r]=null:qi(n,t[r])}else n==Qi&&o==Qi&&e.length>=t.length&&t.forEach(((t,n)=>{qi(e[n],t)}))}function Di(e,t,n,o){if((e=zi(e))===t)return;const r=I(e),i=I(t);if(r==Fi)if(i!=Fi||Object.keys(e).length<Object.keys(t).length)Li(o,n,e);else for(let a in e){const r=zi(e[a]),i=t[a],l=I(r),s=I(i);if(l!=Qi&&l!=Fi)r!=i&&Li(o,(""==n?"":n+".")+a,r);else if(l==Qi)s!=Qi||r.length<i.length?Li(o,(""==n?"":n+".")+a,r):r.forEach(((e,t)=>{Di(e,i[t],(""==n?"":n+".")+a+"["+t+"]",o)}));else if(l==Fi)if(s!=Fi||Object.keys(r).length<Object.keys(i).length)Li(o,(""==n?"":n+".")+a,r);else for(let e in r)Di(r[e],i[e],(""==n?"":n+".")+a+"."+e,o)}else r==Qi?i!=Qi||e.length<t.length?Li(o,n,e):e.forEach(((e,r)=>{Di(e,t[r],n+"["+r+"]",o)})):Li(o,n,e)}function Li(e,t,n){e[t]=n}function Hi(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function Ui(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!function(e){return nr.includes(e.update)}(e))return cr(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push((()=>{t?Yo(t.bind(e.proxy),e,14):o&&o(e.proxy)})),new Promise((e=>{o=e}))}function Vi(e,t){const n=typeof(e=zi(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(S(e)){const o=e.length;n=new Array(o),t.set(e,n);for(let r=0;r<o;r++)n[r]=Vi(e[r],t)}else{n={},t.set(e,n);for(const o in e)b(e,o)&&(n[o]=Vi(e[o],t))}return n}if("symbol"!==n)return e}function Ri(e){return Vi(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function $i(e,t,n){if(!t)return;t=Ri(t);const o=e.ctx,r=o.mpType;if("page"===r||"component"===r){t.r0=1;const r=o.$scope,i=Object.keys(t),a=Mi(t,n||function(e,t){const n=e.data,o=Object.create(null);return t.forEach((e=>{o[e]=n[e]})),o}(r,i));Object.keys(a).length?(o.__next_tick_pending=!0,r.setData(a,(()=>{o.__next_tick_pending=!1,Hi(e)})),dr()):Hi(e)}}function Wi(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const e=Object.keys(o);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function Gi(e,t=!1){const{setupState:n,$templateRefs:o,ctx:{$scope:r,$mpPlatform:i}}=e;if("mp-alipay"===i)return;if(!o||!r)return;if(t)return o.forEach((e=>Ji(e,null,n)));const a="mp-baidu"===i||"mp-toutiao"===i,l=e=>{const t=(r.selectAllComponents(".r")||[]).concat(r.selectAllComponents(".r-i-f")||[]);return e.filter((e=>{const o=function(e,t){const n=e.find((e=>e&&(e.properties||e.props).uI===t));if(n){const e=n.$vm;return e?Ni(e.$)||e:function(e){C(e)&&Fo(e);return e}(n)}return null}(t,e.i);return!(!a||null!==o)||(Ji(e,o,n),!1)}))},s=()=>{const t=l(o);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},(()=>{l(t)}))};r._$setRef?r._$setRef(s):Ui(e,s)}function Ji({r:e,f:t},n,o){if(w(e))e(n,{});else{const r=A(e),i=Ho(e);if(r||i)if(t){if(!i)return;S(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;Dr((()=>m(t,n)),n.$)}}else r?b(o,e)&&(o[e]=n):Ho(e)&&(e.value=n)}}var Xi,Ki;(Ki=Xi||(Xi={})).APP="app",Ki.PAGE="page",Ki.COMPONENT="component";const Yi=fr;function Zi(e,t){const n=e.component=wi(e,t.parentComponent,null);return n.ctx.$onApplyOptions=Wi,n.ctx.$children=[],"app"===t.mpType&&(n.render=u),t.onBeforeSetup&&t.onBeforeSetup(n,t),Pi(n),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(Ni(n)||n.proxy),function(e){const t=oa.bind(e);e.$updateScopedSlots=()=>cr((()=>ur(t)));const n=()=>{if(e.isMounted){const{next:t,bu:n,u:o}=e;ra(e,!1),na(),n&&L(n),ra(e,!0),$i(e,ea(e)),o&&Yi(o)}else Dr((()=>{Gi(e,!0)}),e),$i(e,ea(e))},o=e.effect=new Tn(n,(()=>ur(e.update)),e.scope),r=e.update=o.run.bind(o);r.id=e.uid,ra(e,!0),r()}(n),n.proxy}const _i=e=>{let t;for(const n in e)("class"===n||"style"===n||d(n))&&((t||(t={}))[n]=e[n]);return t};function ea(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[a],slots:l,attrs:s,emit:c,render:u,renderCache:p,data:f,setupState:d,ctx:h,uid:g,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:m}}}},inheritAttrs:y}=e;let b;e.$templateRefs=[],e.$ei=0,m(g),e.__counter=0===e.__counter?1:0;const S=vr(e);try{if(4&n.shapeFlag){ta(y,i,a,s);const e=r||o;b=u.call(e,e,p,i,d,f,h)}else{ta(y,i,a,t.props?s:_i(s));const e=t;b=e.length>1?e(i,{attrs:s,slots:l,emit:c}):e(i,null)}}catch(x){_o(x,e,1),b=!1}return Gi(e),vr(S),b}function ta(e,t,n,o){if(t&&o&&!1!==e){const e=Object.keys(o).filter((e=>"class"!==e&&"style"!==e));if(!e.length)return;n&&e.some(h)?e.forEach((e=>{h(e)&&e.slice(9)in n||(t[e]=o[e])})):e.forEach((e=>t[e]=o[e]))}}const na=e=>{Fn(),dr(),Mn()};function oa(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach((({path:e,index:t,data:r})=>{const i=xe(n,e),a=A(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===i||void 0===i[t])o[a]=r;else{const e=Mi(r,i[t]);Object.keys(e).forEach((t=>{o[a+"."+t]=e[t]}))}})),e.length=0,Object.keys(o).length&&t.setData(o)}function ra({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}const ia=function(e,t=null){w(e)||(e=Object.assign({},e)),null==t||C(t)||(t=null);const n=mi(),o=new Set,r=n.app={_uid:yi++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:ji,get config(){return n.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&w(e.install)?(o.add(e),e.install(r,...t)):w(e)&&(o.add(e),e(r,...t))),r),mixin:e=>(n.mixins.includes(e)||n.mixins.push(e),r),component:(e,t)=>t?(n.components[e]=t,r):n.components[e],directive:(e,t)=>t?(n.directives[e]=t,r):n.directives[e],mount(){},unmount(){},provide:(e,t)=>(n.provides[e]=t,r)};return r};function aa(e,t=null){("undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0).__VUE__=!0;const n=ia(e,t),o=n._context;o.config.globalProperties.$nextTick=function(e){return Ui(this.$,e)};const r=e=>(e.appContext=o,e.shapeFlag=6,e),i=function(e,t){return Zi(r(e),t)},a=function(e){return e&&function(e){const{bum:t,scope:n,update:o,um:r}=e;t&&L(t),n.stop(),o&&(o.active=!1),r&&Yi(r),Yi((()=>{e.isUnmounted=!0}))}(e.$)};return n.mount=function(){e.render=u;const t=Zi(r({type:e}),{mpType:Xi.APP,mpInstance:null,parentComponent:null,slots:[],props:null});return n._instance=t.$,t.$app=n,t.$createComponent=i,t.$destroyComponent=a,o.$appInstance=t,t},n.unmount=function(){},n}function la(e,t,n,o){w(t)&&jr(e,t.bind(n),o)}function sa(e,t,n){!function(e,t,n){const o=e.mpType||n.$mpType;o&&"component"!==o&&Object.keys(e).forEach((o=>{if(Ee(o,e[o],!1)){const r=e[o];S(r)?r.forEach((e=>la(o,e,n,t))):la(o,r,n,t)}}))}(e,t,n)}function ca(e,t,n){return e[t]=n}function ua(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;r.proxy.$callHook(X,t)}}function pa(e,t){return e?[...new Set([].concat(e,t))]:t}let fa;const da="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ha=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function ga(){const e=bn.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(fa(o).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function ma(e){const t=e._context.config;var n;t.errorHandler=Oe(e,ua),n=t.optionMergeStrategies,Ce.forEach((e=>{n[e]=pa}));const o=t.globalProperties;!function(e){e.uniIDHasRole=function(e){const{role:t}=ga();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=ga();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=ga();return e>Date.now()}}(o),o.$set=ca,o.$applyOptions=sa,bn.invokeCreateVueAppHook(e)}fa="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!ha.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",i=0;i<e.length;)t=da.indexOf(e.charAt(i++))<<18|da.indexOf(e.charAt(i++))<<12|(n=da.indexOf(e.charAt(i++)))<<6|(o=da.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;const ya=Object.create(null);function ba(e){delete ya[e]}function Sa(e){if(!e)return;const[t,n]=e.split(",");return ya[t]?ya[t][parseInt(n)]:void 0}var xa={install(e){ma(e),e.config.globalProperties.pruneComponentPropsCache=ba;const t=e.mount;e.mount=function(n){const o=t.call(e,n),r=function(){const e="createApp";if("undefined"!=typeof global)return global[e];if("undefined"!=typeof my)return my[e]}();return r?r(o):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(o),o}}};function va(e,t){const n=Bi(),o=n.ctx,r=void 0===t||"mp-weixin"!==o.$mpPlatform&&"mp-qq"!==o.$mpPlatform||!A(t)&&"number"!=typeof t?"":"_"+t,i="e"+n.$ei+++r,a=o.$scope;if(!e)return delete a[i],i;const l=a[i];return l?l.value=e:a[i]=function(e,t){const n=e=>{var o;(o=e).type&&o.target&&(o.preventDefault=u,o.stopPropagation=u,o.stopImmediatePropagation=u,b(o,"detail")||(o.detail={}),b(o,"markerId")&&(o.detail="object"==typeof o.detail?o.detail:{},o.detail.markerId=o.markerId),P(o.detail)&&b(o.detail,"checked")&&!b(o.detail,"value")&&(o.detail.value=o.detail.checked),P(o.detail)&&(o.target=g({},o.target,o.detail)));let r=[e];e.detail&&e.detail.__args__&&(r=e.detail.__args__);const i=n.value,a=()=>Zo(function(e,t){if(S(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e(t)))}return t}(e,i),t,5,r),l=e.target,s=!!l&&(!!l.dataset&&"true"===String(l.dataset.eventsync));if(!wa.includes(e.type)||s){const t=a();if("input"===e.type&&(S(t)||k(t)))return;return t}setTimeout(a)};return n.value=e,n}(e,n),i}const wa=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function Aa(e,t={},n){const o=Bi(),{parent:r,isMounted:i,ctx:{$scope:a}}=o,l=(a.properties||a.props).uI;if(!l)return;if(!r&&!i)return void Fr((()=>{Aa(e,t,n)}),o);const s=function(e,t){let n=t.parent;for(;n;){const t=n.$ssi;if(t&&t[e])return t[e];n=n.parent}}(l,o);s&&s(e,t,n)}function Ba(e){return A(e)?e:function(e){let t="";if(!e||A(e))return t;for(const n in e)t+=`${n.startsWith("--")?n:F(n)}:${e[n]};`;return t}(t(e))}const Ca=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function ka(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,n.$mp={},n._self={},e.slots={},S(t.slots)&&t.slots.length&&(t.slots.forEach((t=>{e.slots[t]=!0})),e.slots.d&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=Ea,n.$callHook=Ia,e.emit=function(e,t){return function(n,...o){const r=t.$scope;if(r&&n){const e={__args__:o};r.triggerEvent(n,e)}return e.apply(this,[n,...o])}}(e.emit,n)}function Ea(e){const t=this.$[e];return!(!t||!t.length)}function Ia(e,t){"mounted"===e&&(Ia.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(n,t)}const Pa=[_,W,G,te,re,le,se,ce,pe];function Oa(e,t=new Set){if(e){Object.keys(e).forEach((n=>{Ee(n,e[n])&&t.add(n)}));{const{extends:n,mixins:o}=e;o&&o.forEach((e=>Oa(e,t))),n&&Oa(n,t)}}return t}function Na(e,t,n){-1!==n.indexOf(t)||b(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const Ta=[ee];function ja(e,t,n=Ta){t.forEach((t=>Na(e,t,n)))}function za(e,t,n=Ta){Oa(t).forEach((t=>Na(e,t,n)))}const Qa=Se((()=>{const e=[],t=w(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(S(n)){const t=Object.keys(ke);n.forEach((n=>{t.forEach((t=>{b(n,t)&&!e.includes(t)&&e.push(t)}))}))}}return e}));const Fa=[W,G,X,K,Y,Z];function Ma(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const o=n.ctx;this.$vm&&o.$scope||(ka(n,{mpType:"app",mpInstance:this,slots:[]}),o.globalData=this.globalData,e.$callHook(J,t))}};!function(e){const t=Uo($(wx.getSystemInfoSync().language)||R);Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(e);const r=e.$.type;ja(o,Fa),za(o,r);{const e=r.methods;e&&g(o,e)}return t&&t.parse(o),o}function qa(e,t){if(w(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}w(e.onShow)&&wx.onAppShow&&wx.onAppShow((e=>{t.$callHook("onShow",e)})),w(e.onHide)&&wx.onAppHide&&wx.onAppHide((e=>{t.$callHook("onHide",e)}))}const Da=["externalClasses"];const La=/_(.*)_worklet_factory_/;function Ha(e,t){const n=e.$children;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.$scope._$vueId===t)return e}let o;for(let r=n.length-1;r>=0;r--)if(o=Ha(n[r],t),o)return o}const Ua=["eO","uR","uRIF","uI","uT","uP","uS"];function Va(e){e.properties||(e.properties={}),g(e.properties,function(e,t=!1){const n={};return t||(Ua.forEach((e=>{n[e]={type:null,value:""}})),n.uS={type:null,value:[],observer:function(e){const t=Object.create(null);e&&e.forEach((e=>{t[e]=!0})),this.setData({$slots:t})}}),e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}(e),function(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""}),t}(e.options))}const Ra=[String,Number,Boolean,Object,Array,null];function $a(e,t){const n=function(e){return S(e)&&1===e.length?e[0]:e}(e);return-1!==Ra.indexOf(n)?n:null}function Wa(e,t){return(t?function(e){const t={};P(e)&&Object.keys(e).forEach((n=>{-1===Ua.indexOf(n)&&(t[n]=e[n])}));return t}(e):Sa(e.uP))||{}}function Ga(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?function(e,t){const n=Qo(t.props),o=Sa(e)||{};Ja(n,o)&&(!function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:a}}=e,l=Qo(r),[s]=e.propsOptions;let c=!1;if(!(o||a>0)||16&a){let o;ci(e,t,r,i)&&(c=!0);for(const i in l)t&&(b(t,i)||(o=F(i))!==i&&b(t,o))||(s?!n||void 0===n[i]&&void 0===n[o]||(r[i]=ui(s,l,i,void 0,e,!0)):delete r[i]);if(i!==l)for(const e in i)t&&b(t,e)||(delete i[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(Sr(e.emitsOptions,a))continue;const u=t[a];if(s)if(b(i,a))u!==i[a]&&(i[a]=u,c=!0);else{const t=z(a);r[t]=ui(s,l,t,u,e,!1)}else u!==i[a]&&(i[a]=u,c=!0)}}c&&Ln(e,"set","$attrs")}(t,o,n,!1),r=t.update,nr.indexOf(r)>-1&&function(e){const t=nr.indexOf(e);t>or&&nr.splice(t,1)}(t.update),t.update());var r}(e,this.$vm.$):"m"===this.properties.uT&&function(e,t){const n=t.properties,o=Sa(e)||{};Ja(n,o,!1)&&t.setData(o)}(e,this))};e.observers||(e.observers={}),e.observers.uP=t}function Ja(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const n=o[r];if(t[n]!==e[n])return!0}return!1}function Xa(e,t){e.data={},e.behaviors=function(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return S(t)&&t.forEach((e=>{o.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(S(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),o}(t)}function Ka(e,{parse:t,mocks:n,isPage:o,initRelation:r,handleLink:i,initLifetimes:a}){e=e.default||e;const l={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};S(e.mixins)&&e.mixins.forEach((e=>{C(e.options)&&g(l,e.options)})),e.options&&g(l,e.options);const s={options:l,lifetimes:a({mocks:n,isPage:o,initRelation:r,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:i}};var c,u,p,f;return Xa(s,e),Va(s),Ga(s),function(e,t){Da.forEach((n=>{b(t,n)&&(e[n]=t[n])}))}(s,e),c=s.methods,u=e.wxsCallMethods,S(u)&&u.forEach((e=>{c[e]=function(t){return this.$vm[e](t)}})),p=s.methods,(f=e.methods)&&Object.keys(f).forEach((e=>{const t=e.match(La);if(t){const n=t[1];p[e]=f[e],p[n]=f[n]}})),t&&t(s,{handleLink:i}),s}let Ya,Za;function _a(){return getApp().$vm}function el(e,t){const{parse:n,mocks:o,isPage:r,initRelation:i,handleLink:a,initLifetimes:l}=t,s=Ka(e,{mocks:o,isPage:r,initRelation:i,handleLink:a,initLifetimes:l});!function({properties:e},t){S(t)?t.forEach((t=>{e[t]={type:String,value:""}})):P(t)&&Object.keys(t).forEach((n=>{const o=t[n];if(P(o)){let t=o.default;w(t)&&(t=t());const r=o.type;o.type=$a(r),e[n]={type:o.type,value:t}}else e[n]={type:$a(o)}}))}(s,(e.default||e).props);const c=s.methods;return c.onLoad=function(e){var t;return this.options=e,this.$page={fullPath:(t=this.route+Ae(e),function(e){return 0===e.indexOf("/")}(t)?t:"/"+t)},this.$vm&&this.$vm.$callHook(_,e)},ja(c,Pa),za(c,e),function(e,t){if(!t)return;Object.keys(ke).forEach((n=>{t&ke[n]&&Na(e,n,[])}))}(c,e.__runtimeHooks),ja(c,Qa()),n&&n(s,{handleLink:a}),s}const tl=Page,nl=Component;function ol(e){const t=e.triggerEvent,n=function(n,...o){return t.apply(e,[(r=n,z(r.replace(be,"-"))),...o]);var r};try{e.triggerEvent=n}catch(o){e._triggerEvent=n}}function rl(e,t,n){const o=t[e];t[e]=o?function(...e){return ol(this),o.apply(this,e)}:function(){ol(this)}}Page=function(e){return rl(_,e),tl(e)},Component=function(e){rl("created",e);return e.properties&&e.properties.uP||(Va(e),Ga(e)),nl(e)};var il=Object.freeze({__proto__:null,handleLink:function(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=Ha(this.$vm,n)),o||(o=this.$vm),t.parent=o},initLifetimes:function({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let r=this.properties;!function(e,t){if(!e)return;const n=e.split(","),o=n.length;1===o?t._$vueId=n[0]:2===o&&(t._$vueId=n[0],t._$vuePid=n[1])}(r.uI,this);const i={vuePid:this._$vuePid};n(this,i);const a=this,l=t(a);let s=r;this.$vm=function(e,t){Ya||(Ya=_a().$createComponent);const n=Ya(e,t);return Ni(n.$)||n}({type:o,props:Wa(s,l)},{mpType:l?"page":"component",mpInstance:a,slots:r.uS||{},parentComponent:i.parent&&i.parent.$,onBeforeSetup(t,n){!function(e,t){Object.defineProperty(e,"refs",{get(){const e={};return function(e,t,n){e.selectAllComponents(t).forEach((e=>{const t=e.properties.uR;n[t]=e.$vm||e}))}(t,".r",e),t.selectAllComponents(".r-i-f").forEach((t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))})),e}})}(t,a),function(e,t,n){const o=e.ctx;n.forEach((n=>{b(t,n)&&(e[n]=o[n]=t[n])}))}(t,a,e),function(e,t){ka(e,t);const n=e.ctx;Ca.forEach((e=>{n[e]=function(...t){const o=n.$scope;if(o&&o[e])return o[e].apply(o,t)}}))}(t,n)}}),l||function(e){const t=e.$options;S(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",(()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})}),{immediate:!0})}(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook(ee))},detached(){var e;this.$vm&&(ba(this.$vm.$.uid),e=this.$vm,Za||(Za=_a().$destroyComponent),Za(e))}}},initRelation:function(e,t){e.triggerEvent("__l",t)},isPage:function(e){return!!e.route},mocks:["__route__","__wxExparserNodeId__","__wxWebviewId__"]});const al=function(e){return App(Ma(e,ll))};var ll;const sl=(cl=il,function(e){return Component(el(e,cl))});var cl;const ul=function(e){return function(t){return Component(Ka(t,e))}}(il),pl=function(e){return function(t){qa(Ma(t,e),t)}}(),fl=function(e){return function(t){const n=Ma(t,e),o=w(getApp)&&getApp({allowDefault:!0});if(!o)return;t.$.ctx.$scope=o;const r=o.globalData;r&&Object.keys(n.globalData).forEach((e=>{b(r,e)||(r[e]=n.globalData[e])})),Object.keys(n).forEach((e=>{b(o,e)||(o[e]=n[e])})),qa(n,t)}}();wx.createApp=global.createApp=al,wx.createPage=sl,wx.createComponent=ul,wx.createPluginApp=global.createPluginApp=pl,wx.createSubpackageApp=global.createSubpackageApp=fl;const dl=e=>(t,n=Bi())=>{!Ii&&jr(e,t,n)},hl=dl(W),gl=dl(G),ml=dl(J),yl=dl(_),bl=dl(ee),Sl=dl(te),xl=dl(ie),vl=dl(ae),wl=dl(se),Al=dl(ce),Bl=dl(ue),Cl=dl(fe);
/*!
  * pinia v2.0.33
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */
let kl;const El=e=>kl=e,Il=Symbol();function Pl(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Ol,Nl;(Nl=Ol||(Ol={})).direct="direct",Nl.patchObject="patch object",Nl.patchFunction="patch function";const Tl=()=>{};function jl(e,t,n,o=Tl){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};return!n&&wn()&&function(e){Sn&&Sn.cleanups.push(e)}(r),r}function zl(e,...t){e.slice().forEach((e=>{e(...t)}))}function Ql(e,t){e instanceof Map&&t instanceof Map&&t.forEach(((t,n)=>e.set(n,t))),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];Pl(r)&&Pl(o)&&e.hasOwnProperty(n)&&!Ho(o)&&!To(o)?e[n]=Ql(r,o):e[n]=o}return e}const Fl=Symbol();const{assign:Ml}=Object;function ql(e,t,n,o){const{state:r,actions:i,getters:a}=t,l=n.state.value[e];let s;return s=Dl(e,(function(){l||(n.state.value[e]=r?r():{});const t=function(e){const t=S(e)?new Array(e.length):{};for(const n in e)t[n]=Jo(e,n);return t}(n.state.value[e]);return Ml(t,i,Object.keys(a||{}).reduce(((t,o)=>(t[o]=Fo(Ti((()=>{El(n);const t=n._s.get(e);return a[o].call(t,t)}))),t)),{}))}),t,n,o,!0),s}function Dl(e,t,n={},o,r,i){let a;const l=Ml({actions:{}},n),s={deep:!0};let c,u,p,f=Fo([]),d=Fo([]);const h=o.state.value[e];let g;function m(t){let n;c=u=!1,"function"==typeof t?(t(o.state.value[e]),n={type:Ol.patchFunction,storeId:e,events:p}):(Ql(o.state.value[e],t),n={type:Ol.patchObject,payload:t,storeId:e,events:p});const r=g=Symbol();cr().then((()=>{g===r&&(c=!0)})),u=!0,zl(f,n,o.state.value[e])}i||h||(o.state.value[e]={}),Uo({});const y=i?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{Ml(e,t)}))}:Tl;function b(t,n){return function(){El(o);const r=Array.from(arguments),i=[],a=[];let l;zl(d,{args:r,name:t,store:S,after:function(e){i.push(e)},onError:function(e){a.push(e)}});try{l=n.apply(this&&this.$id===e?this:S,r)}catch(s){throw zl(a,s),s}return l instanceof Promise?l.then((e=>(zl(i,e),e))).catch((e=>(zl(a,e),Promise.reject(e)))):(zl(i,l),l)}}const S=Po({_p:o,$id:e,$onAction:jl.bind(null,d),$patch:m,$reset:y,$subscribe(t,n={}){const r=jl(f,t,n.detached,(()=>i())),i=a.run((()=>Br((()=>o.state.value[e]),(o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:Ol.direct,events:p},o)}),Ml({},s,n))));return r},$dispose:function(){a.stop(),f=[],d=[],o._s.delete(e)}});o._s.set(e,S);const x=o._e.run((()=>(a=vn(),a.run((()=>t())))));for(const A in x){const t=x[A];if(Ho(t)&&(!Ho(w=t)||!w.effect)||To(t))i||(!h||Pl(v=t)&&v.hasOwnProperty(Fl)||(Ho(t)?t.value=h[A]:Ql(t,h[A])),o.state.value[e][A]=t);else if("function"==typeof t){const e=b(A,t);x[A]=e,l.actions[A]=t}}var v,w;return Ml(S,x),Ml(Qo(S),x),Object.defineProperty(S,"$state",{get:()=>o.state.value[e],set:e=>{m((t=>{Ml(t,e)}))}}),o._p.forEach((e=>{Ml(S,a.run((()=>e({store:S,app:o._a,pinia:o,options:l}))))})),h&&i&&n.hydrate&&n.hydrate(S.$state,h),c=!0,u=!0,S}function Ll(e){return e}function Hl(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function Ul(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function Vl(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function Rl(e){return"[object Object]"===Object.prototype.toString.call(e)}function $l(e){return"function"==typeof e}const Wl={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},date:function(e){if(!e)return!1;if("number"==typeof e)return(10===e.toString().length||13===e.toString().length)&&!isNaN(new Date(e).getTime());if("string"==typeof e){const t=Number(e);if(!isNaN(t)&&(10===t.toString().length||13===t.toString().length))return!isNaN(new Date(t).getTime());if(e.length<10||e.length>19)return!1;if(!/^\d{4}[-\/]\d{2}[-\/]\d{2}( \d{1,2}:\d{2}(:\d{2})?)?$/.test(e))return!1;const n=new Date(e);return!isNaN(n.getTime())}return!1},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:Hl,digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:Ul,isEmpty:Ul,jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:Rl,array:Vl,code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},func:$l,promise:function(e){return Rl(e)&&$l(e.then)&&$l(e.catch)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)},image:function(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e}};function Gl(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function Jl(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function Xl(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=Jl(e);return t>0?Gl(Number(e)*Math.pow(10,t)):Number(e)}function Kl(e){(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&console.warn(`${e} 超出了精度限制，结果可能不正确`)}function Yl(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function Zl(...e){if(e.length>2)return Yl(e,Zl);const[t,n]=e,o=Xl(t),r=Xl(n),i=Jl(t)+Jl(n),a=o*r;return Kl(a),a/Math.pow(10,i)}function _l(...e){if(e.length>2)return Yl(e,_l);const[t,n]=e,o=Xl(t),r=Xl(n);return Kl(o),Kl(r),Zl(o/r,Gl(Math.pow(10,Jl(n)-Jl(t))))}const es={v:"3",version:"3",type:["primary","success","info","error","warning"],color:{"u-primary":"#2979ff","u-warning":"#ff9900","u-success":"#19be6b","u-error":"#fa3534","u-info":"#909399","u-main-color":"#303133","u-content-color":"#606266","u-tips-color":"#909399","u-light-color":"#c0c4cc","up-primary":"#2979ff","up-warning":"#ff9900","up-success":"#19be6b","up-error":"#fa3534","up-info":"#909399","up-main-color":"#303133","up-content-color":"#606266","up-tips-color":"#909399","up-light-color":"#c0c4cc"},unit:"px"};function ts(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))}function ns(e,t=!1){return Hl(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${bn.upx2px(parseInt(e))}px`:Number(bn.upx2px(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)}function os(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))}function rs(){return bn.getSystemInfoSync().platform.toLowerCase()}function is(){return bn.getSystemInfoSync()}function as(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0}function ls(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")}function ss(e=void 0){let t=this.$parent;for(;t;){if(e=e.replace(/up-([a-zA-Z0-9-_]+)/g,"u-$1"),!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function cs(e,t="object"){if(Ul(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=gs(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[gs(o[0])]=gs(o[1])}return n}let n="";return"object"==typeof e&&e.forEach(((e,t)=>{const o=t.replace(/([A-Z])/g,"-$1").toLowerCase();n+=`${o}:${e};`})),gs(n)}function us(e="auto",t=""){return t||(t=es.unit||"px"),"rpx"==t&&Hl(String(e))&&(e*=2),Hl(e=String(e))?`${e}${t}`:e}function ps(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;const t=Vl(e)?[]:{};for(const n in e)e.hasOwnProperty(n)&&(t[n]="object"==typeof e[n]?ps(e[n]):e[n]);return t}function fs(e={},t={}){let n=ps(e);if("object"!=typeof n||"object"!=typeof t)return!1;for(const o in t)t.hasOwnProperty(o)&&(o in n?null==t[o]||"object"!=typeof n[o]||"object"!=typeof t[o]?n[o]=t[o]:n[o].concat&&t[o].concat?n[o]=n[o].concat(t[o]):n[o]=fs(n[o],t[o]):n[o]=t[o]);return n}function ds(e){}function hs(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):new Date("string"==typeof e?e.replace(/-/g,"/"):e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function gs(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function ms(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function ys(e,t=2e3){bn.showToast({title:String(e),icon:"none",duration:t})}function bs(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,a=void 0===o?",":o,l=void 0===n?".":n;let s="";s=(i?function(e,t){const n=Math.pow(10,t);let o=_l(Math.round(Math.abs(Zl(e,n))),n);return e<0&&0!==o&&(o=Zl(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(s[0]);)s[0]=s[0].replace(c,`$1${a}$2`);return(s[1]||"").length<i&&(s[1]=s[1]||"",s[1]+=new Array(i-s[1].length+1).join("0")),s.join(l)}function Ss(e){return`00${e}`.slice(-2)}function xs(e,t){const n=ss.call(e,"u-form-item"),o=ss.call(e,"u-form");n&&o&&o.validateField(n.prop,(()=>{}),t)}function vs(e,t){if("object"!=typeof e||null==e)return"";if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}function ws(e,t,n){if("object"!=typeof e||null==e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n}function As(){const e=getCurrentPages();return`/${e[e.length-1].route||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const Bs={range:ts,getPx:ns,sleep:os,os:rs,sys:is,random:as,guid:ls,$parent:ss,addStyle:cs,addUnit:us,deepClone:ps,deepMerge:fs,shallowMerge:function e(t,n={}){if("object"!=typeof t||"object"!=typeof n)return!1;for(const o in n)n.hasOwnProperty(o)&&(o in t?null==n[o]||"object"!=typeof t[o]||"object"!=typeof n[o]?t[o]=n[o]:t[o].concat&&n[o].concat?t[o]=t[o].concat(n[o]):t[o]=e(t[o],n[o]):t[o]=n[o]);return t},error:ds,randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},timeFormat:hs,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:hs(e,t)}return o},trim:gs,queryParams:ms,toast:ys,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},priceFormat:bs,getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},padZero:Ss,formValidate:xs,getProperty:vs,setProperty:ws,page:As,pages:function(){return getCurrentPages()},getValueByPath:function(e,t){return t.split(".").reduce(((e,t)=>e&&void 0!==e[t]?e[t]:void 0),e)}};const Cs=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=ms(t,!1),e+`&${n}`):(n=ms(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=fs(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==As())if(t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=fs(this.config,n),"function"==typeof bn.$u.routeIntercept){await new Promise(((e,t)=>{bn.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||bn.navigateTo({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||bn.redirectTo({url:t}),"switchTab"!=e.type&&"tab"!=e.type||bn.switchTab({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||bn.reLaunch({url:t}),"navigateBack"!=e.type&&"back"!=e.type||bn.navigateBack({delta:o})}}).route,ks={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},created(){this.$u.getRect=this.$uGetRect},computed:{$u:()=>fs(bn.$u,{props:void 0,http:void 0,mixin:void 0}),bem:()=>function(e,t,n){const o=`u-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&Cs({type:this.linkType,url:t})},navTo(e="",t="navigateTo"){Cs({type:this.linkType,url:e})},$uGetRect(e,t){return new Promise((n=>{bn.createSelectorQuery().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=ss.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){bn.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&Wl.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},Es={options:{virtualHost:!0}},{toString:Is}=Object.prototype;function Ps(e){return"[object Array]"===Is.call(e)}function Os(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),Ps(e))for(let n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.call(null,e[n],n,e)}function Ns(){const e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=Ns(e[n],t):e[n]="object"==typeof t?Ns({},t):t}for(let n=0,o=arguments.length;n<o;n++)Os(arguments[n],t);return e}function Ts(e){return void 0===e}function js(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function zs(e,t){if(!t)return e;let n;if(o=t,"undefined"!=typeof URLSearchParams&&o instanceof URLSearchParams)n=t.toString();else{const e=[];Os(t,((t,n)=>{null!=t&&(Ps(t)?n=`${n}[]`:t=[t],Os(t,(t=>{!function(e){return"[object Date]"===Is.call(e)}(t)?function(e){return null!==e&&"object"==typeof e}(t)&&(t=JSON.stringify(t)):t=t.toISOString(),e.push(`${js(n)}=${js(t)}`)})))})),n=e.join("&")}var o;if(n){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}const Qs=(e,t)=>{const n={};return e.forEach((e=>{Ts(t[e])||(n[e]=t[e])})),n},Fs=e=>(e=>new Promise(((t,n)=>{const o=zs((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?`${e.replace(/\/+$/,"")}/${t.replace(/^\/+/,"")}`:e}(r,i):i),e.params);var r,i;const a={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e;try{"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(i){}!function(e,t,n){const{validateStatus:o}=n.config,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let l;if("UPLOAD"===e.method){delete a.header["content-type"],delete a.header["Content-Type"];const t={filePath:e.filePath,name:e.name},n=["formData"];l=bn.uploadFile({...a,...t,...Qs(n,e)})}else if("DOWNLOAD"===e.method)l=bn.downloadFile(a);else{const t=["data","method","timeout","dataType","responseType"];l=bn.request({...a,...Qs(t,e)})}e.getTask&&e.getTask(l,e)})))(e);function Ms(){this.handlers=[]}Ms.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Ms.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Ms.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const qs=(e,t,n)=>{const o={};return e.forEach((e=>{Ts(n[e])?Ts(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},Ds={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,validateStatus:function(e){return e>=200&&e<300}};var Ls=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(l){t=function(){}}try{n=Set}catch(l){n=function(){}}try{o=Promise}catch(l){o=function(){}}function r(i,l,s,c,u){"object"==typeof l&&(s=l.depth,c=l.prototype,u=l.includeNonEnumerable,l=l.circular);var p=[],f=[],d="undefined"!=typeof Buffer;return void 0===l&&(l=!0),void 0===s&&(s=1/0),function i(s,h){if(null===s)return null;if(0===h)return s;var g,m;if("object"!=typeof s)return s;if(e(s,t))g=new t;else if(e(s,n))g=new n;else if(e(s,o))g=new o((function(e,t){s.then((function(t){e(i(t,h-1))}),(function(e){t(i(e,h-1))}))}));else if(r.__isArray(s))g=[];else if(r.__isRegExp(s))g=new RegExp(s.source,a(s)),s.lastIndex&&(g.lastIndex=s.lastIndex);else if(r.__isDate(s))g=new Date(s.getTime());else{if(d&&Buffer.isBuffer(s))return Buffer.from?g=Buffer.from(s):(g=new Buffer(s.length),s.copy(g)),g;e(s,Error)?g=Object.create(s):void 0===c?(m=Object.getPrototypeOf(s),g=Object.create(m)):(g=Object.create(c),m=c)}if(l){var y=p.indexOf(s);if(-1!=y)return f[y];p.push(s),f.push(g)}for(var b in e(s,t)&&s.forEach((function(e,t){var n=i(t,h-1),o=i(e,h-1);g.set(n,o)})),e(s,n)&&s.forEach((function(e){var t=i(e,h-1);g.add(t)})),s){Object.getOwnPropertyDescriptor(s,b)&&(g[b]=i(s[b],h-1));try{if("undefined"===Object.getOwnPropertyDescriptor(s,b).set)continue;g[b]=i(s[b],h-1)}catch(B){if(B instanceof TypeError)continue;if(B instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var S=Object.getOwnPropertySymbols(s);for(b=0;b<S.length;b++){var x=S[b];(!(w=Object.getOwnPropertyDescriptor(s,x))||w.enumerable||u)&&(g[x]=i(s[x],h-1),Object.defineProperty(g,x,w))}}if(u){var v=Object.getOwnPropertyNames(s);for(b=0;b<v.length;b++){var w,A=v[b];(w=Object.getOwnPropertyDescriptor(s,A))&&w.enumerable||(g[A]=i(s[A],h-1),Object.defineProperty(g,A,w))}}return g}(i,s)}function i(e){return Object.prototype.toString.call(e)}function a(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=a,r}();function Hs(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=Us(e,!1),r=o[0],i=o[1],a=o[2],l=Us(t,!1),s=(l[0]-r)/n,c=(l[1]-i)/n,u=(l[2]-a)/n,p=[];for(let f=0;f<n;f++){let o=Vs(`rgb(${Math.round(s*f+r)},${Math.round(c*f+i)},${Math.round(u*f+a)})`);0===f&&(o=Vs(e)),f===n-1&&(o=Vs(t)),p.push(o)}return p}function Us(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function Vs(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}const Rs={colorGradient:Hs,hexToRgb:Us,rgbToHex:Vs,colorToRgba:function(e,t){e=Vs(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n}};let $s,Ws=null;function Gs(e,t=500,n=!0){n?$s||($s=!0,"function"==typeof e&&e(),setTimeout((()=>{$s=!1}),t)):$s||($s=!0,setTimeout((()=>{$s=!1,"function"==typeof e&&e()}),t))}const Js={calendar:{title:"日期选择",showTitle:!0,showSubtitle:!0,mode:"single",startText:"开始",endText:"结束",customList:[],color:"#3c9cff",minDate:0,maxDate:0,defaultDate:null,maxCount:Number.MAX_SAFE_INTEGER,rowHeight:56,formatter:null,showLunar:!1,showMark:!0,confirmText:"确定",confirmDisabledText:"确定",show:!1,closeOnClickOverlay:!1,readonly:!1,showConfirm:!0,maxRange:Number.MAX_SAFE_INTEGER,rangePrompt:"",showRangePrompt:!0,allowSameDay:!1,round:0,monthNum:3}},Xs={datetimePicker:{show:!1,popupMode:"bottom",showToolbar:!0,value:"",title:"",mode:"datetime",maxDate:new Date((new Date).getFullYear()+10,0,1).getTime(),minDate:new Date((new Date).getFullYear()-10,0,1).getTime(),minHour:0,maxHour:23,minMinute:0,maxMinute:59,filter:null,formatter:null,loading:!1,itemHeight:44,cancelText:"取消",confirmText:"确认",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,closeOnClickOverlay:!1,defaultIndex:[]}},{color:Ks}=es,Ys={icon:{name:"",color:Ks["u-content-color"],size:"16px",bold:!1,index:"",hoverClass:"",customPrefix:"uicon",label:"",labelPos:"right",labelSize:"15px",labelColor:Ks["u-content-color"],space:"3px",imgMode:"",width:"",height:"",top:0,stop:!1}},{color:Zs}=es,_s={link:{color:Zs["u-primary"],fontSize:15,underLine:!1,href:"",mpTips:"链接已复制，请在浏览器打开",lineColor:"",text:""}},{color:ec}=es,tc={loadingIcon:{show:!0,color:ec["u-tips-color"],textColor:ec["u-tips-color"],vertical:!1,mode:"spinner",size:24,textSize:15,text:"",timingFunction:"ease-in-out",duration:1200,inactiveColor:""}},nc={primary:"#3c9cff",info:"#909399",default:"#909399",warning:"#f9ae3d",error:"#f56c6c",success:"#5ac725",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},oc={actionSheet:{show:!1,title:"",description:"",actions:[],index:"",cancelText:"",closeOnClickAction:!0,safeAreaInsetBottom:!0,openType:"",closeOnClickOverlay:!0,round:0,wrapMaxHeight:"600px"},album:{urls:[],keyName:"",singleSize:180,multipleSize:70,space:6,singleMode:"scaleToFill",multipleMode:"aspectFill",maxCount:9,previewFullImage:!0,rowCount:3,showMore:!0,autoWrap:!1,unit:"px",stop:!0},alert:{title:"",type:"warning",description:"",closable:!1,showIcon:!1,effect:"light",center:!1,fontSize:14},avatar:{src:"",shape:"circle",size:40,mode:"scaleToFill",text:"",bgColor:"#c0c4cc",color:"#ffffff",fontSize:18,icon:"",mpAvatar:!1,randomBgColor:!1,defaultUrl:"",colorIndex:"",name:""},avatarGroup:{urls:[],maxCount:5,shape:"circle",mode:"scaleToFill",showMore:!0,size:40,keyName:"",gap:.5,extraValue:0},backtop:{mode:"circle",icon:"arrow-upward",text:"",duration:100,scrollTop:0,top:400,bottom:100,right:20,zIndex:9,iconStyle:{color:"#909399",fontSize:"19px"}},badge:{isDot:!1,value:"",show:!0,max:999,type:"error",showZero:!1,bgColor:null,color:null,shape:"circle",numberType:"overflow",offset:[],inverted:!1,absolute:!1},button:{hairline:!1,type:"info",size:"normal",shape:"square",plain:!1,disabled:!1,loading:!1,loadingText:"",loadingMode:"spinner",loadingSize:15,openType:"",formType:"",appParameter:"",hoverStopPropagation:!0,lang:"en",sessionFrom:"",sendMessageTitle:"",sendMessagePath:"",sendMessageImg:"",showMessageCard:!1,dataName:"",throttleTime:0,hoverStartTime:0,hoverStayTime:200,text:"",icon:"",iconColor:"",color:"",stop:!0},...Js,carKeyboard:{random:!1},cell:{customClass:"",title:"",label:"",value:"",icon:"",disabled:!1,border:!0,center:!1,url:"",linkType:"navigateTo",clickable:!1,isLink:!1,required:!1,arrowDirection:"",iconStyle:{},rightIconStyle:{},rightIcon:"arrow-right",titleStyle:{},size:"",stop:!0,name:""},cellGroup:{title:"",border:!0,customStyle:{}},checkbox:{name:"",shape:"",size:"",checkbox:!1,disabled:"",activeColor:"",inactiveColor:"",iconSize:"",iconColor:"",label:"",labelSize:"",labelColor:"",labelDisabled:""},checkboxGroup:{name:"",value:[],shape:"square",disabled:!1,activeColor:"#2979ff",inactiveColor:"#c8c9cc",size:18,placement:"row",labelSize:14,labelColor:"#303133",labelDisabled:!1,iconColor:"#ffffff",iconSize:12,iconPlacement:"left",borderBottom:!1},circleProgress:{percentage:30},code:{seconds:60,startText:"获取验证码",changeText:"X秒重新获取",endText:"重新获取",keepRunning:!1,uniqueKey:""},codeInput:{adjustPosition:!0,maxlength:6,dot:!1,mode:"box",hairline:!1,space:10,value:"",focus:!1,bold:!1,color:"#606266",fontSize:18,size:35,disabledKeyboard:!1,borderColor:"#c9cacc",disabledDot:!0},col:{span:12,offset:0,justify:"start",align:"stretch",textAlign:"left"},collapse:{value:null,accordion:!1,border:!0},collapseItem:{title:"",value:"",label:"",disabled:!1,isLink:!0,clickable:!0,border:!0,align:"left",name:"",icon:"",duration:300,showRight:!0},columnNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80,step:!1,duration:1500,disableTouch:!0},countDown:{time:0,format:"HH:mm:ss",autoStart:!0,millisecond:!1},countTo:{startVal:0,endVal:0,duration:2e3,autoplay:!0,decimals:0,useEasing:!0,decimal:".",color:"#606266",fontSize:22,bold:!1,separator:""},...Xs,divider:{dashed:!1,hairline:!0,dot:!1,textPosition:"center",text:"",textSize:14,textColor:"#909399",lineColor:"#dcdfe6"},empty:{icon:"",text:"",textColor:"#c0c4cc",textSize:14,iconColor:"#c0c4cc",iconSize:90,mode:"data",width:160,height:160,show:!0,marginTop:0},form:{model:{},rules:{},errorType:"message",borderBottom:!0,labelPosition:"left",labelWidth:45,labelAlign:"left",labelStyle:{}},formItem:{label:"",prop:"",rules:[],borderBottom:"",labelPosition:"",labelWidth:"",rightIcon:"",leftIcon:"",required:!1,leftIconStyle:""},gap:{bgColor:"transparent",height:20,marginTop:0,marginBottom:0,customStyle:{}},grid:{col:3,border:!1,align:"left"},gridItem:{name:null,bgColor:"transparent"},...Ys,image:{src:"",mode:"aspectFill",width:"300",height:"225",shape:"square",radius:0,lazyLoad:!0,showMenuByLongpress:!0,loadingIcon:"photo",errorIcon:"error-circle",showLoading:!0,showError:!0,fade:!0,webp:!1,duration:500,bgColor:"#f3f4f6"},indexAnchor:{text:"",color:"#606266",size:14,bgColor:"#dedede",height:32},indexList:{inactiveColor:"#606266",activeColor:"#5677fc",indexList:[],sticky:!0,customNavHeight:0,safeBottomFix:!1},input:{value:"",type:"text",fixed:!1,disabled:!1,disabledColor:"#f5f7fa",clearable:!1,password:!1,maxlength:140,placeholder:null,placeholderClass:"input-placeholder",placeholderStyle:"color: #c0c4cc",showWordLimit:!1,confirmType:"done",confirmHold:!1,holdKeyboard:!1,focus:!1,autoBlur:!1,disableDefaultPadding:!1,cursor:-1,cursorSpacing:30,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,inputAlign:"left",fontSize:"15px",color:"#303133",prefixIcon:"",prefixIconStyle:"",suffixIcon:"",suffixIconStyle:"",border:"surround",readonly:!1,shape:"square",formatter:null},keyboard:{mode:"number",dotDisabled:!1,tooltip:!0,showTips:!0,tips:"",showCancel:!0,showConfirm:!0,random:!1,safeAreaInsetBottom:!0,closeOnClickOverlay:!0,show:!1,overlay:!0,zIndex:10075,cancelText:"取消",confirmText:"确定",autoChange:!1},line:{color:"#d6d7d9",length:"100%",direction:"row",hairline:!0,margin:0,dashed:!1},lineProgress:{activeColor:"#19be6b",inactiveColor:"#ececec",percentage:0,showText:!0,height:12},..._s,list:{showScrollbar:!1,lowerThreshold:50,upperThreshold:0,scrollTop:0,offsetAccuracy:10,enableFlex:!1,pagingEnabled:!1,scrollable:!0,scrollIntoView:"",scrollWithAnimation:!1,enableBackToTop:!1,height:0,width:0,preLoadScreen:1},listItem:{anchor:""},...tc,loadingPage:{loadingText:"正在加载",image:"",loadingMode:"circle",loading:!1,bgColor:"#ffffff",color:"#C8C8C8",fontSize:19,iconSize:28,loadingColor:"#C8C8C8",zIndex:10},loadmore:{status:"loadmore",bgColor:"transparent",icon:!0,fontSize:14,iconSize:17,color:"#606266",loadingIcon:"spinner",loadmoreText:"加载更多",loadingText:"正在加载...",nomoreText:"没有更多了",isDot:!1,iconColor:"#b7b7b7",marginTop:10,marginBottom:10,height:"auto",line:!1,lineColor:"#E6E8EB",dashed:!1},modal:{show:!1,title:"",content:"",confirmText:"确认",cancelText:"取消",showConfirmButton:!0,showCancelButton:!1,confirmColor:"#2979ff",cancelColor:"#606266",buttonReverse:!1,zoom:!0,asyncClose:!1,closeOnClickOverlay:!1,negativeTop:0,width:"650rpx",confirmButtonShape:"",contentTextAlign:"left"},...{navbar:{safeAreaInsetTop:!0,placeholder:!1,fixed:!0,border:!1,leftIcon:"arrow-left",leftText:"",rightText:"",rightIcon:"",title:"",titleColor:"",bgColor:"#ffffff",titleWidth:"400rpx",height:"44px",leftIconSize:20,leftIconColor:nc.mainColor,autoBack:!1,titleStyle:""}},noNetwork:{tips:"哎呀，网络信号丢失",zIndex:"",image:"data:image/png;base64,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"},noticeBar:{text:[],direction:"row",step:!1,icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",speed:80,fontSize:14,duration:2e3,disableTouch:!0,url:"",linkType:"navigateTo"},notify:{top:0,type:"primary",color:"#ffffff",bgColor:"",message:"",duration:3e3,fontSize:15,safeAreaInsetTop:!1},...{numberBox:{name:"",value:0,min:1,max:Number.MAX_SAFE_INTEGER,step:1,integer:!1,disabled:!1,disabledInput:!1,asyncChange:!1,inputWidth:35,showMinus:!0,showPlus:!0,decimalLength:null,longPress:!0,color:"#323233",buttonSize:30,bgColor:"#EBECEE",cursorSpacing:100,disableMinus:!1,disablePlus:!1,iconStyle:""}},numberKeyboard:{mode:"number",dotDisabled:!1,random:!1},overlay:{show:!1,zIndex:10070,duration:300,opacity:.5},parse:{copyLink:!0,errorImg:"",lazyLoad:!1,loadingImg:"",pauseVideo:!0,previewImg:!0,setTitle:!0,showImgMenu:!0},picker:{show:!1,popupMode:"bottom",showToolbar:!0,title:"",columns:[],loading:!1,itemHeight:44,cancelText:"取消",confirmText:"确定",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,keyName:"text",closeOnClickOverlay:!1,defaultIndex:[],immediateChange:!0},popup:{show:!1,overlay:!0,mode:"bottom",duration:300,closeable:!1,overlayStyle:{},closeOnClickOverlay:!0,zIndex:10075,safeAreaInsetBottom:!0,safeAreaInsetTop:!1,closeIconPos:"top-right",round:0,zoom:!0,bgColor:"",overlayOpacity:.5},radio:{name:"",shape:"",disabled:"",labelDisabled:"",activeColor:"",inactiveColor:"",iconSize:"",labelSize:"",label:"",labelColor:"",size:"",iconColor:"",placement:""},radioGroup:{value:"",disabled:!1,shape:"circle",activeColor:"#2979ff",inactiveColor:"#c8c9cc",name:"",size:18,placement:"row",label:"",labelColor:"#303133",labelSize:14,labelDisabled:!1,iconColor:"#ffffff",iconSize:12,borderBottom:!1,iconPlacement:"left",gap:"10px"},rate:{value:1,count:5,disabled:!1,size:18,inactiveColor:"#b2b2b2",activeColor:"#FA3534",gutter:4,minCount:1,allowHalf:!1,activeIcon:"star-fill",inactiveIcon:"star",touchable:!0},readMore:{showHeight:400,toggle:!1,closeText:"展开阅读全文",openText:"收起",color:"#2979ff",fontSize:14,textIndent:"2em",name:""},row:{gutter:0,justify:"start",align:"center"},rowNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80},scrollList:{indicatorWidth:50,indicatorBarWidth:20,indicator:!0,indicatorColor:"#f2f2f2",indicatorActiveColor:"#3c9cff",indicatorStyle:""},search:{shape:"round",bgColor:"#f2f2f2",placeholder:"请输入关键字",clearabled:!0,focus:!1,showAction:!0,actionStyle:{},actionText:"搜索",inputAlign:"left",inputStyle:{},disabled:!1,borderColor:"transparent",searchIconColor:"#909399",searchIconSize:22,color:"#606266",placeholderColor:"#909399",searchIcon:"search",margin:"0",animation:!1,value:"",maxlength:"-1",height:32,label:null},section:{title:"",subTitle:"更多",right:!0,fontSize:15,bold:!0,color:"#303133",subColor:"#909399",showLine:!0,lineColor:"",arrow:!0},skeleton:{loading:!0,animate:!0,rows:0,rowsWidth:"100%",rowsHeight:18,title:!0,titleWidth:"50%",titleHeight:18,avatar:!1,avatarSize:32,avatarShape:"circle"},slider:{value:0,blockSize:18,min:0,max:100,step:1,activeColor:"#2979ff",inactiveColor:"#c0c4cc",blockColor:"#ffffff",showValue:!1,disabled:!1,blockStyle:{},useNative:!1,height:"2px"},statusBar:{bgColor:"transparent"},steps:{direction:"row",current:0,activeColor:"#3c9cff",inactiveColor:"#969799",activeIcon:"",inactiveIcon:"",dot:!1},stepsItem:{title:"",desc:"",iconSize:17,error:!1},sticky:{offsetTop:0,customNavHeight:0,disabled:!1,bgColor:"transparent",zIndex:"",index:""},subsection:{list:[],current:0,activeColor:"#3c9cff",inactiveColor:"#303133",mode:"button",fontSize:12,bold:!0,bgColor:"#eeeeef",keyName:"name"},swipeAction:{autoClose:!0},swipeActionItem:{show:!1,closeOnClick:!0,name:"",disabled:!1,threshold:20,autoClose:!0,options:[],duration:300},swiper:{list:[],indicator:!1,indicatorActiveColor:"#FFFFFF",indicatorInactiveColor:"rgba(255, 255, 255, 0.35)",indicatorStyle:"",indicatorMode:"line",autoplay:!0,current:0,currentItemId:"",interval:3e3,duration:300,circular:!1,previousMargin:0,nextMargin:0,acceleration:!1,displayMultipleItems:1,easingFunction:"default",keyName:"url",imgMode:"aspectFill",height:130,bgColor:"#f3f4f6",radius:4,loading:!1,showTitle:!1},swiperIndicator:{length:0,current:0,indicatorActiveColor:"",indicatorInactiveColor:"",indicatorMode:"line"},switch:{loading:!1,disabled:!1,size:25,activeColor:"#2979ff",inactiveColor:"#ffffff",value:!1,activeValue:!0,inactiveValue:!1,asyncChange:!1,space:0},tabbar:{value:null,safeAreaInsetBottom:!0,border:!0,zIndex:1,activeColor:"#1989fa",inactiveColor:"#7d7e80",fixed:!0,placeholder:!0},tabbarItem:{name:null,icon:"",badge:null,dot:!1,text:"",badgeStyle:"top: 6px;right:2px;"},tabs:{duration:300,list:[],lineColor:"#3c9cff",activeStyle:{color:"#303133"},inactiveStyle:{color:"#606266"},lineWidth:20,lineHeight:3,lineBgSize:"cover",itemStyle:{height:"44px"},scrollable:!0,current:0,keyName:"name"},tag:{type:"primary",disabled:!1,size:"medium",shape:"square",text:"",bgColor:"",color:"",borderColor:"",closeColor:"#C6C7CB",name:"",plainFill:!1,plain:!1,closable:!1,show:!0,icon:"",iconColor:""},text:{type:"",show:!0,text:"",prefixIcon:"",suffixIcon:"",mode:"",href:"",format:"",call:!1,openType:"",bold:!1,block:!1,lines:"",color:"#303133",size:15,iconStyle:{fontSize:"15px"},decoration:"none",margin:0,lineHeight:"",align:"left",wordWrap:"normal"},textarea:{value:"",placeholder:"",placeholderClass:"textarea-placeholder",placeholderStyle:"color: #c0c4cc",height:70,confirmType:"done",disabled:!1,count:!1,focus:!1,autoHeight:!1,fixed:!1,cursorSpacing:0,cursor:"",showConfirmBar:!0,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,disableDefaultPadding:!1,holdKeyboard:!1,maxlength:140,border:"surround",formatter:null},toast:{zIndex:10090,loading:!1,text:"",icon:"",type:"",loadingMode:"",show:"",overlay:!1,position:"center",params:{},duration:2e3,isTab:!1,url:"",callback:null,back:!1},toolbar:{show:!0,cancelText:"取消",confirmText:"确认",cancelColor:"#909193",confirmColor:"#3c9cff",title:""},tooltip:{text:"",copyText:"",size:14,color:"#606266",bgColor:"transparent",direction:"top",zIndex:10071,showCopy:!0,buttons:[],overlay:!0,showToast:!0},transition:{show:!1,mode:"fade",duration:"300",timingFunction:"ease-out"},...{upload:{accept:"image",extension:[],capture:["album","camera"],compressed:!0,camera:"back",maxDuration:60,uploadIcon:"camera-fill",uploadIconColor:"#D3D4D6",useBeforeRead:!1,previewFullImage:!0,maxCount:52,disabled:!1,imageMode:"aspectFill",name:"",sizeType:["original","compressed"],multiple:!1,deletable:!0,maxSize:Number.MAX_VALUE,fileList:[],uploadText:"",width:80,height:80,previewImage:!0}}},rc={toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965};let ic="none";ic="vue3",ic="mp",ic="weixin";const ac=new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},console.warn("设置全局参数必须接收一个Object")),this.config=Ls({...Ds,...e}),this.interceptors={request:new Ms,response:new Ms}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:Ns(e.header||{},t.header||{})};if(o={...o,...qs(["getTask","validateStatus"],e,t)},"DOWNLOAD"===n);else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["filePath","name","formData"].forEach((e=>{Ts(t[e])||(o[e]=t[e])}));else{const n=["data","timeout","dataType","responseType"];o={...o,...qs(n,e,t)}}return o})(this.config,e);const t=[Fs,void 0];let n=Promise.resolve(e);for(this.interceptors.request.forEach((e=>{t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((e=>{t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}};Bs.setConfig=function(e){Bs.shallowMerge(es,e.config||{}),Bs.shallowMerge(oc,e.props||{}),Bs.shallowMerge(nc,e.color||{}),Bs.shallowMerge(rc,e.zIndex||{})};const lc={route:Cs,date:Bs.timeFormat,colorGradient:Rs.colorGradient,hexToRgb:Rs.hexToRgb,rgbToHex:Rs.rgbToHex,colorToRgba:Rs.colorToRgba,test:Wl,type:["primary","success","error","warning","info"],http:ac,config:es,zIndex:rc,debounce:function(e,t=500,n=!1){if(null!==Ws&&clearTimeout(Ws),n){const n=!Ws;Ws=setTimeout((()=>{Ws=null}),t),n&&"function"==typeof e&&e()}else Ws=setTimeout((()=>{"function"==typeof e&&e()}),t)},throttle:Gs,mixin:ks,mpMixin:Es,props:oc,...Bs,color:nc,platform:"weixin"},sc={install:e=>{bn.$u=lc,e.config.globalProperties.$u=lc,e.mixin(ks)}},cc={props:{type:{type:String,default:()=>oc.text.type},show:{type:Boolean,default:()=>oc.text.show},text:{type:[String,Number],default:()=>oc.text.text},prefixIcon:{type:String,default:()=>oc.text.prefixIcon},suffixIcon:{type:String,default:()=>oc.text.suffixIcon},mode:{type:String,default:()=>oc.text.mode},href:{type:String,default:()=>oc.text.href},format:{type:[String,Function],default:()=>oc.text.format},call:{type:Boolean,default:()=>oc.text.call},openType:{type:String,default:()=>oc.text.openType},bold:{type:Boolean,default:()=>oc.text.bold},block:{type:Boolean,default:()=>oc.text.block},lines:{type:[String,Number],default:()=>oc.text.lines},color:{type:String,default:()=>oc.text.color},size:{type:[String,Number],default:()=>oc.text.size},iconStyle:{type:[Object,String],default:()=>oc.text.iconStyle},decoration:{tepe:String,default:()=>oc.text.decoration},margin:{type:[Object,String,Number],default:()=>oc.text.margin},lineHeight:{type:[String,Number],default:()=>oc.text.lineHeight},align:{type:String,default:()=>oc.text.align},wordWrap:{type:String,default:()=>oc.text.wordWrap}}},uc={computed:{value(){const{text:e,mode:t,format:n,href:o}=this;return"price"===t?Wl.func(n)?n(e):bs(e,2):"date"===t?(Wl.date(e),Wl.func(n)?n(e):hs(e,n||"yyyy-mm-dd")):"phone"===t?Wl.func(n)?n(e):"encrypt"===n?`${e.substr(0,3)}****${e.substr(7)}`:e:"name"===t?Wl.func(n)?n(e):"encrypt"===n?this.formatName(e):e:"link"===t?(Wl.url(o),e):e}},methods:{formatName(e){let t="";if(2===e.length)t=e.substr(0,1)+"*";else if(e.length>2){let n="";for(let t=0,o=e.length-2;t<o;t++)n+="*";t=e.substr(0,1)+n+e.substr(-1,1)}else t=e;return t}}},pc={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}},fc={props:{openType:String},methods:{onGetUserInfo(e){this.$emit("getuserinfo",e.detail)},onContact(e){this.$emit("contact",e.detail)},onGetPhoneNumber(e){this.$emit("getphonenumber",e.detail)},onError(e){this.$emit("error",e.detail)},onLaunchApp(e){this.$emit("launchapp",e.detail)},onOpenSetting(e){this.$emit("opensetting",e.detail)}}},dc={props:{color:{type:String,default:()=>oc.line.color},length:{type:[String,Number],default:()=>oc.line.length},direction:{type:String,default:()=>oc.line.direction},hairline:{type:Boolean,default:()=>oc.line.hairline},margin:{type:[String,Number],default:()=>oc.line.margin},dashed:{type:Boolean,default:()=>oc.line.dashed}}},hc={props:{modelValue:{type:[String,Number],default:()=>oc.input.value},type:{type:String,default:()=>oc.input.type},fixed:{type:Boolean,default:()=>oc.input.fixed},disabled:{type:Boolean,default:()=>oc.input.disabled},disabledColor:{type:String,default:()=>oc.input.disabledColor},clearable:{type:Boolean,default:()=>oc.input.clearable},password:{type:Boolean,default:()=>oc.input.password},maxlength:{type:[String,Number],default:()=>oc.input.maxlength},placeholder:{type:String,default:()=>oc.input.placeholder},placeholderClass:{type:String,default:()=>oc.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>oc.input.placeholderStyle},showWordLimit:{type:Boolean,default:()=>oc.input.showWordLimit},confirmType:{type:String,default:()=>oc.input.confirmType},confirmHold:{type:Boolean,default:()=>oc.input.confirmHold},holdKeyboard:{type:Boolean,default:()=>oc.input.holdKeyboard},focus:{type:Boolean,default:()=>oc.input.focus},autoBlur:{type:Boolean,default:()=>oc.input.autoBlur},disableDefaultPadding:{type:Boolean,default:()=>oc.input.disableDefaultPadding},cursor:{type:[String,Number],default:()=>oc.input.cursor},cursorSpacing:{type:[String,Number],default:()=>oc.input.cursorSpacing},selectionStart:{type:[String,Number],default:()=>oc.input.selectionStart},selectionEnd:{type:[String,Number],default:()=>oc.input.selectionEnd},adjustPosition:{type:Boolean,default:()=>oc.input.adjustPosition},inputAlign:{type:String,default:()=>oc.input.inputAlign},fontSize:{type:[String,Number],default:()=>oc.input.fontSize},color:{type:String,default:()=>oc.input.color},prefixIcon:{type:String,default:()=>oc.input.prefixIcon},prefixIconStyle:{type:[String,Object],default:()=>oc.input.prefixIconStyle},suffixIcon:{type:String,default:()=>oc.input.suffixIcon},suffixIconStyle:{type:[String,Object],default:()=>oc.input.suffixIconStyle},border:{type:String,default:()=>oc.input.border},readonly:{type:Boolean,default:()=>oc.input.readonly},shape:{type:String,default:()=>oc.input.shape},formatter:{type:[Function,null],default:()=>oc.input.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}},gc={props:{offsetTop:{type:[String,Number],default:()=>oc.sticky.offsetTop},customNavHeight:{type:[String,Number],default:()=>oc.sticky.customNavHeight},disabled:{type:Boolean,default:()=>oc.sticky.disabled},bgColor:{type:String,default:()=>oc.sticky.bgColor},zIndex:{type:[String,Number],default:()=>oc.sticky.zIndex},index:{type:[String,Number],default:()=>oc.sticky.index}}},mc={props:{bgColor:{type:String,default:()=>oc.gap.bgColor},height:{type:[String,Number],default:()=>oc.gap.height},marginTop:{type:[String,Number],default:()=>oc.gap.marginTop},marginBottom:{type:[String,Number],default:()=>oc.gap.marginBottom}}},yc={props:{show:{type:Boolean,default:()=>oc.loadingIcon.show},color:{type:String,default:()=>oc.loadingIcon.color},textColor:{type:String,default:()=>oc.loadingIcon.textColor},vertical:{type:Boolean,default:()=>oc.loadingIcon.vertical},mode:{type:String,default:()=>oc.loadingIcon.mode},size:{type:[String,Number],default:()=>oc.loadingIcon.size},textSize:{type:[String,Number],default:()=>oc.loadingIcon.textSize},text:{type:[String,Number],default:()=>oc.loadingIcon.text},timingFunction:{type:String,default:()=>oc.loadingIcon.timingFunction},duration:{type:[String,Number],default:()=>oc.loadingIcon.duration},inactiveColor:{type:String,default:()=>oc.loadingIcon.inactiveColor}}},bc={props:{list:{type:Array,default:()=>oc.swiper.list},indicator:{type:Boolean,default:()=>oc.swiper.indicator},indicatorActiveColor:{type:String,default:()=>oc.swiper.indicatorActiveColor},indicatorInactiveColor:{type:String,default:()=>oc.swiper.indicatorInactiveColor},indicatorStyle:{type:[String,Object],default:()=>oc.swiper.indicatorStyle},indicatorMode:{type:String,default:()=>oc.swiper.indicatorMode},autoplay:{type:Boolean,default:()=>oc.swiper.autoplay},current:{type:[String,Number],default:()=>oc.swiper.current},currentItemId:{type:String,default:()=>oc.swiper.currentItemId},interval:{type:[String,Number],default:()=>oc.swiper.interval},duration:{type:[String,Number],default:()=>oc.swiper.duration},circular:{type:Boolean,default:()=>oc.swiper.circular},previousMargin:{type:[String,Number],default:()=>oc.swiper.previousMargin},nextMargin:{type:[String,Number],default:()=>oc.swiper.nextMargin},acceleration:{type:Boolean,default:()=>oc.swiper.acceleration},displayMultipleItems:{type:Number,default:()=>oc.swiper.displayMultipleItems},easingFunction:{type:String,default:()=>oc.swiper.easingFunction},keyName:{type:String,default:()=>oc.swiper.keyName},imgMode:{type:String,default:()=>oc.swiper.imgMode},height:{type:[String,Number],default:()=>oc.swiper.height},bgColor:{type:String,default:()=>oc.swiper.bgColor},radius:{type:[String,Number],default:()=>oc.swiper.radius},loading:{type:Boolean,default:()=>oc.swiper.loading},showTitle:{type:Boolean,default:()=>oc.swiper.showTitle}}},Sc={props:{src:{type:String,default:()=>oc.avatar.src},shape:{type:String,default:()=>oc.avatar.shape},size:{type:[String,Number],default:()=>oc.avatar.size},mode:{type:String,default:()=>oc.avatar.mode},text:{type:String,default:()=>oc.avatar.text},bgColor:{type:String,default:()=>oc.avatar.bgColor},color:{type:String,default:()=>oc.avatar.color},fontSize:{type:[String,Number],default:()=>oc.avatar.fontSize},icon:{type:String,default:()=>oc.avatar.icon},mpAvatar:{type:Boolean,default:()=>oc.avatar.mpAvatar},randomBgColor:{type:Boolean,default:()=>oc.avatar.randomBgColor},defaultUrl:{type:String,default:()=>oc.avatar.defaultUrl},colorIndex:{type:[String,Number],validator:e=>Wl.range(e,[0,19])||""===e,default:()=>oc.avatar.colorIndex},name:{type:String,default:()=>oc.avatar.name}}},xc={props:{mode:{type:String,default:()=>oc.backtop.mode},icon:{type:String,default:()=>oc.backtop.icon},text:{type:String,default:()=>oc.backtop.text},duration:{type:[String,Number],default:()=>oc.backtop.duration},scrollTop:{type:[String,Number],default:()=>oc.backtop.scrollTop},top:{type:[String,Number],default:()=>oc.backtop.top},bottom:{type:[String,Number],default:()=>oc.backtop.bottom},right:{type:[String,Number],default:()=>oc.backtop.right},zIndex:{type:[String,Number],default:()=>oc.backtop.zIndex},iconStyle:{type:Object,default:()=>oc.backtop.iconStyle}}},vc={props:{modelValue:{type:Array,default:()=>[]},hasInput:{type:Boolean,default:!1},placeholder:{type:String,default:()=>"请选择"},show:{type:Boolean,default:()=>oc.picker.show},popupMode:{type:String,default:()=>oc.picker.popupMode},showToolbar:{type:Boolean,default:()=>oc.picker.showToolbar},title:{type:String,default:()=>oc.picker.title},columns:{type:Array,default:()=>oc.picker.columns},loading:{type:Boolean,default:()=>oc.picker.loading},itemHeight:{type:[String,Number],default:()=>oc.picker.itemHeight},cancelText:{type:String,default:()=>oc.picker.cancelText},confirmText:{type:String,default:()=>oc.picker.confirmText},cancelColor:{type:String,default:()=>oc.picker.cancelColor},confirmColor:{type:String,default:()=>oc.picker.confirmColor},visibleItemCount:{type:[String,Number],default:()=>oc.picker.visibleItemCount},keyName:{type:String,default:()=>oc.picker.keyName},closeOnClickOverlay:{type:Boolean,default:()=>oc.picker.closeOnClickOverlay},defaultIndex:{type:Array,default:()=>oc.picker.defaultIndex},immediateChange:{type:Boolean,default:()=>oc.picker.immediateChange},toolbarRightSlot:{type:Boolean,default:!1}}},wc={props:{hairline:{type:Boolean,default:()=>oc.button.hairline},type:{type:String,default:()=>oc.button.type},size:{type:String,default:()=>oc.button.size},shape:{type:String,default:()=>oc.button.shape},plain:{type:Boolean,default:()=>oc.button.plain},disabled:{type:Boolean,default:()=>oc.button.disabled},loading:{type:Boolean,default:()=>oc.button.loading},loadingText:{type:[String,Number],default:()=>oc.button.loadingText},loadingMode:{type:String,default:()=>oc.button.loadingMode},loadingSize:{type:[String,Number],default:()=>oc.button.loadingSize},openType:{type:String,default:()=>oc.button.openType},formType:{type:String,default:()=>oc.button.formType},appParameter:{type:String,default:()=>oc.button.appParameter},hoverStopPropagation:{type:Boolean,default:()=>oc.button.hoverStopPropagation},lang:{type:String,default:()=>oc.button.lang},sessionFrom:{type:String,default:()=>oc.button.sessionFrom},sendMessageTitle:{type:String,default:()=>oc.button.sendMessageTitle},sendMessagePath:{type:String,default:()=>oc.button.sendMessagePath},sendMessageImg:{type:String,default:()=>oc.button.sendMessageImg},showMessageCard:{type:Boolean,default:()=>oc.button.showMessageCard},dataName:{type:String,default:()=>oc.button.dataName},throttleTime:{type:[String,Number],default:()=>oc.button.throttleTime},hoverStartTime:{type:[String,Number],default:()=>oc.button.hoverStartTime},hoverStayTime:{type:[String,Number],default:()=>oc.button.hoverStayTime},text:{type:[String,Number],default:()=>oc.button.text},icon:{type:String,default:()=>oc.button.icon},iconColor:{type:String,default:()=>oc.button.icon},color:{type:String,default:()=>oc.button.color},stop:{type:Boolean,default:()=>oc.button.stop}}},Ac={props:{urls:{type:Array,default:()=>oc.album.urls},keyName:{type:String,default:()=>oc.album.keyName},singleSize:{type:[String,Number],default:()=>oc.album.singleSize},multipleSize:{type:[String,Number],default:()=>oc.album.multipleSize},space:{type:[String,Number],default:()=>oc.album.space},singleMode:{type:String,default:()=>oc.album.singleMode},multipleMode:{type:String,default:()=>oc.album.multipleMode},maxCount:{type:[String,Number],default:()=>oc.album.maxCount},previewFullImage:{type:Boolean,default:()=>oc.album.previewFullImage},rowCount:{type:[String,Number],default:()=>oc.album.rowCount},showMore:{type:Boolean,default:()=>oc.album.showMore},shape:{type:String,default:()=>oc.image.shape},radius:{type:[String,Number],default:()=>oc.image.radius},autoWrap:{type:Boolean,default:()=>oc.album.autoWrap},unit:{type:[String],default:()=>oc.album.unit},stop:{type:Boolean,default:()=>oc.album.stop}}},Bc={props:{name:{type:String,default:()=>oc.icon.name},color:{type:String,default:()=>oc.icon.color},size:{type:[String,Number],default:()=>oc.icon.size},bold:{type:Boolean,default:()=>oc.icon.bold},index:{type:[String,Number],default:()=>oc.icon.index},hoverClass:{type:String,default:()=>oc.icon.hoverClass},customPrefix:{type:String,default:()=>oc.icon.customPrefix},label:{type:[String,Number],default:()=>oc.icon.label},labelPos:{type:String,default:()=>oc.icon.labelPos},labelSize:{type:[String,Number],default:()=>oc.icon.labelSize},labelColor:{type:String,default:()=>oc.icon.labelColor},space:{type:[String,Number],default:()=>oc.icon.space},imgMode:{type:String,default:()=>oc.icon.imgMode},width:{type:[String,Number],default:()=>oc.icon.width},height:{type:[String,Number],default:()=>oc.icon.height},top:{type:[String,Number],default:()=>oc.icon.top},stop:{type:Boolean,default:()=>oc.icon.stop}}},Cc={props:{show:{type:Boolean,default:()=>oc.popup.show},overlay:{type:Boolean,default:()=>oc.popup.overlay},mode:{type:String,default:()=>oc.popup.mode},duration:{type:[String,Number],default:()=>oc.popup.duration},closeable:{type:Boolean,default:()=>oc.popup.closeable},overlayStyle:{type:[Object,String],default:()=>oc.popup.overlayStyle},closeOnClickOverlay:{type:Boolean,default:()=>oc.popup.closeOnClickOverlay},zIndex:{type:[String,Number],default:()=>oc.popup.zIndex},safeAreaInsetBottom:{type:Boolean,default:()=>oc.popup.safeAreaInsetBottom},safeAreaInsetTop:{type:Boolean,default:()=>oc.popup.safeAreaInsetTop},closeIconPos:{type:String,default:()=>oc.popup.closeIconPos},round:{type:[Boolean,String,Number],default:()=>oc.popup.round},zoom:{type:Boolean,default:()=>oc.popup.zoom},bgColor:{type:String,default:()=>oc.popup.bgColor},overlayOpacity:{type:[Number,String],default:()=>oc.popup.overlayOpacity}}},kc={props:{show:{type:Boolean,default:()=>oc.modal.show},title:{type:[String],default:()=>oc.modal.title},content:{type:String,default:()=>oc.modal.content},confirmText:{type:String,default:()=>oc.modal.confirmText},cancelText:{type:String,default:()=>oc.modal.cancelText},showConfirmButton:{type:Boolean,default:()=>oc.modal.showConfirmButton},showCancelButton:{type:Boolean,default:()=>oc.modal.showCancelButton},confirmColor:{type:String,default:()=>oc.modal.confirmColor},cancelColor:{type:String,default:()=>oc.modal.cancelColor},buttonReverse:{type:Boolean,default:()=>oc.modal.buttonReverse},zoom:{type:Boolean,default:()=>oc.modal.zoom},asyncClose:{type:Boolean,default:()=>oc.modal.asyncClose},closeOnClickOverlay:{type:Boolean,default:()=>oc.modal.closeOnClickOverlay},negativeTop:{type:[String,Number],default:()=>oc.modal.negativeTop},width:{type:[String,Number],default:()=>oc.modal.width},confirmButtonShape:{type:String,default:()=>oc.modal.confirmButtonShape},contentTextAlign:{type:String,default:()=>oc.modal.contentTextAlign}}},Ec={props:{duration:{type:Number,default:()=>oc.tabs.duration},list:{type:Array,default:()=>oc.tabs.list},lineColor:{type:String,default:()=>oc.tabs.lineColor},activeStyle:{type:[String,Object],default:()=>oc.tabs.activeStyle},inactiveStyle:{type:[String,Object],default:()=>oc.tabs.inactiveStyle},lineWidth:{type:[String,Number],default:()=>oc.tabs.lineWidth},lineHeight:{type:[String,Number],default:()=>oc.tabs.lineHeight},lineBgSize:{type:String,default:()=>oc.tabs.lineBgSize},itemStyle:{type:[String,Object],default:()=>oc.tabs.itemStyle},scrollable:{type:Boolean,default:()=>oc.tabs.scrollable},current:{type:[Number,String],default:()=>oc.tabs.current},keyName:{type:String,default:()=>oc.tabs.keyName}}},Ic={props:{bgColor:{type:String,default:()=>oc.statusBar.bgColor}}},Pc={trustTags:Qc("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,ruby,rt,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),blockTags:Qc("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),inlineTags:Qc("abbr,b,big,code,del,em,i,ins,label,q,small,span,strong,sub,sup"),ignoreTags:Qc("area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,rp,script,source,style,textarea,title,track,wbr"),voidTags:Qc("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),entities:{lt:"<",gt:">",quot:'"',apos:"'",ensp:" ",emsp:" ",nbsp:" ",semi:";",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…",larr:"←",uarr:"↑",rarr:"→",darr:"↓"},tagStyle:{address:"font-style:italic",big:"display:inline;font-size:1.2em",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",strike:"text-decoration:line-through",u:"text-decoration:underline"},svgDict:{animatetransform:"animateTransform",lineargradient:"linearGradient",viewbox:"viewBox",attributename:"attributeName",repeatcount:"repeatCount",repeatdur:"repeatDur"}},Oc={},{windowWidth:Nc,system:Tc}=bn.getSystemInfoSync(),jc=Qc(" ,\r,\n,\t,\f");let zc=0;function Qc(e){const t=Object.create(null),n=e.split(",");for(let o=n.length;o--;)t[n[o]]=!0;return t}function Fc(e,t){let n=e.indexOf("&");for(;-1!==n;){const o=e.indexOf(";",n+3);let r;if(-1===o)break;"#"===e[n+1]?(r=parseInt(("x"===e[n+2]?"0":"")+e.substring(n+2,o)),isNaN(r)||(e=e.substr(0,n)+String.fromCharCode(r)+e.substr(o+1))):(r=e.substring(n+1,o),(Pc.entities[r]||"amp"===r&&t)&&(e=e.substr(0,n)+(Pc.entities[r]||"&")+e.substr(o+1))),n=e.indexOf("&",n+1)}return e}function Mc(e){let t=e.length-1;for(let n=t;n>=-1;n--)(-1===n||e[n].c||!e[n].name||"div"!==e[n].name&&"p"!==e[n].name&&"h"!==e[n].name[0]||(e[n].attrs.style||"").includes("inline"))&&(t-n>=5&&e.splice(n+1,t-n,{name:"div",attrs:{},children:e.slice(n+1,t+1)}),t=n-1)}function qc(e){this.options=e||{},this.tagStyle=Object.assign({},Pc.tagStyle,this.options.tagStyle),this.imgList=e.imgList||[],this.imgList._unloadimgs=0,this.plugins=e.plugins||[],this.attrs=Object.create(null),this.stack=[],this.nodes=[],this.pre=(this.options.containerStyle||"").includes("white-space")&&this.options.containerStyle.includes("pre")?2:0}function Dc(e){this.handler=e}qc.prototype.parse=function(e){for(let t=this.plugins.length;t--;)this.plugins[t].onUpdate&&(e=this.plugins[t].onUpdate(e,Pc)||e);for(new Dc(this).parse(e);this.stack.length;)this.popNode();return this.nodes.length>50&&Mc(this.nodes),this.nodes},qc.prototype.expose=function(){for(let e=this.stack.length;e--;){const t=this.stack[e];if(t.c||"a"===t.name||"video"===t.name||"audio"===t.name)return;t.c=1}},qc.prototype.hook=function(e){for(let t=this.plugins.length;t--;)if(this.plugins[t].onParse&&!1===this.plugins[t].onParse(e,this))return!1;return!0},qc.prototype.getUrl=function(e){const t=this.options.domain;return"/"===e[0]?"/"===e[1]?e=(t?t.split("://")[0]:"http")+":"+e:t&&(e=t+e):e.includes("data:")||e.includes("://")||t&&(e=t+"/"+e),e},qc.prototype.parseStyle=function(e){const t=e.attrs,n=(this.tagStyle[e.name]||"").split(";").concat((t.style||"").split(";")),o={};let r="";t.id&&!this.xml&&(this.options.useAnchor?this.expose():"img"!==e.name&&"a"!==e.name&&"video"!==e.name&&"audio"!==e.name&&(t.id=void 0)),t.width&&(o.width=parseFloat(t.width)+(t.width.includes("%")?"%":"px"),t.width=void 0),t.height&&(o.height=parseFloat(t.height)+(t.height.includes("%")?"%":"px"),t.height=void 0);for(let i=0,a=n.length;i<a;i++){const e=n[i].split(":");if(e.length<2)continue;const t=e.shift().trim().toLowerCase();let a=e.join(":").trim();if("-"===a[0]&&a.lastIndexOf("-")>0||a.includes("safe"))r+=`;${t}:${a}`;else if(!o[t]||a.includes("import")||!o[t].includes("import")){if(a.includes("url")){let e=a.indexOf("(")+1;if(e){for(;'"'===a[e]||"'"===a[e]||jc[a[e]];)e++;a=a.substr(0,e)+this.getUrl(a.substr(e))}}else a.includes("rpx")&&(a=a.replace(/[0-9.]+\s*rpx/g,(e=>parseFloat(e)*Nc/750+"px")));o[t]=a}}return e.attrs.style=r,o},qc.prototype.onTagName=function(e){this.tagName=this.xml?e:e.toLowerCase(),"svg"===this.tagName&&(this.xml=(this.xml||0)+1)},qc.prototype.onAttrName=function(e){"data-"===(e=this.xml?e:e.toLowerCase()).substr(0,5)?"data-src"!==e||this.attrs.src?"img"===this.tagName||"a"===this.tagName?this.attrName=e:this.attrName=void 0:this.attrName="src":(this.attrName=e,this.attrs[e]="T")},qc.prototype.onAttrVal=function(e){const t=this.attrName||"";"style"===t||"href"===t?this.attrs[t]=Fc(e,!0):t.includes("src")?this.attrs[t]=this.getUrl(Fc(e,!0)):t&&(this.attrs[t]=e)},qc.prototype.onOpenTag=function(e){const t=Object.create(null);t.name=this.tagName,t.attrs=this.attrs,this.options.nodes.length&&(t.type="node"),this.attrs=Object.create(null);const n=t.attrs,o=this.stack[this.stack.length-1],r=o?o.children:this.nodes,i=this.xml?e:Pc.voidTags[t.name];if(Oc[t.name]&&(n.class=Oc[t.name]+(n.class?" "+n.class:"")),"embed"===t.name){const e=n.src||"";e.includes(".mp4")||e.includes(".3gp")||e.includes(".m3u8")||(n.type||"").includes("video")?t.name="video":(e.includes(".mp3")||e.includes(".wav")||e.includes(".aac")||e.includes(".m4a")||(n.type||"").includes("audio"))&&(t.name="audio"),n.autostart&&(n.autoplay="T"),n.controls="T"}if("video"!==t.name&&"audio"!==t.name||("video"!==t.name||n.id||(n.id="v"+zc++),n.controls||n.autoplay||(n.controls="T"),t.src=[],n.src&&(t.src.push(n.src),n.src=void 0),this.expose()),i){if(!this.hook(t)||Pc.ignoreTags[t.name])return void("base"!==t.name||this.options.domain?"source"===t.name&&o&&("video"===o.name||"audio"===o.name)&&n.src&&o.src.push(n.src):this.options.domain=n.href);const e=this.parseStyle(t);if("img"===t.name){if(n.src&&(n.src.includes("webp")&&(t.webp="T"),n.src.includes("data:")&&!n["original-src"]&&(n.ignore="T"),!n.ignore||t.webp||n.src.includes("cloud://"))){for(let r=this.stack.length;r--;){const o=this.stack[r];"a"===o.name&&(t.a=o.attrs),"table"!==o.name||t.webp||n.src.includes("cloud://")||(!e.display||e.display.includes("inline")?t.t="inline-block":t.t=e.display,e.display=void 0);const i=o.attrs.style||"";if(!i.includes("flex:")||i.includes("flex:0")||i.includes("flex: 0")||e.width&&!(parseInt(e.width)>100))if(i.includes("flex")&&"100%"===e.width)for(let t=r+1;t<this.stack.length;t++){const n=this.stack[t].attrs.style||"";if(!n.includes(";width")&&!n.includes(" width")&&0!==n.indexOf("width")){e.width="";break}}else i.includes("inline-block")&&(e.width&&"%"===e.width[e.width.length-1]?(o.attrs.style+=";max-width:"+e.width,e.width=""):o.attrs.style+=";max-width:100%");else{e.width="100% !important",e.height="";for(let e=r+1;e<this.stack.length;e++)this.stack[e].attrs.style=(this.stack[e].attrs.style||"").replace("inline-","")}o.c=1}n.i=this.imgList.length.toString();let o=n["original-src"]||n.src;if(this.imgList.includes(o)){let e=o.indexOf("://");if(-1!==e){e+=3;let t=o.substr(0,e);for(;e<o.length&&"/"!==o[e];e++)t+=Math.random()>.5?o[e].toUpperCase():o[e];t+=o.substr(e),o=t}}this.imgList.push(o),t.t||(this.imgList._unloadimgs+=1)}"inline"===e.display&&(e.display=""),n.ignore&&(e["max-width"]=e["max-width"]||"100%",n.style+=";-webkit-touch-callout:none"),parseInt(e.width)>Nc&&(e.height=void 0),isNaN(parseInt(e.width))||(t.w="T"),!isNaN(parseInt(e.height))&&(!e.height.includes("%")||o&&(o.attrs.style||"").includes("height"))&&(t.h="T")}else if("svg"===t.name)return r.push(t),this.stack.push(t),void this.popNode();for(const t in e)e[t]&&(n.style+=`;${t}:${e[t].replace(" !important","")}`);n.style=n.style.substr(1)||void 0,n.style||delete n.style}else("pre"===t.name||(n.style||"").includes("white-space")&&n.style.includes("pre"))&&2!==this.pre&&(this.pre=t.pre=1),t.children=[],this.stack.push(t);r.push(t)},qc.prototype.onCloseTag=function(e){let t;for(e=this.xml?e:e.toLowerCase(),t=this.stack.length;t--&&this.stack[t].name!==e;);if(-1!==t)for(;this.stack.length>t;)this.popNode();else if("p"===e||"br"===e){(this.stack.length?this.stack[this.stack.length-1].children:this.nodes).push({name:e,attrs:{class:Oc[e]||"",style:this.tagStyle[e]||""}})}},qc.prototype.popNode=function(){const e=this.stack.pop();let t=e.attrs;const n=e.children,o=this.stack[this.stack.length-1],r=o?o.children:this.nodes;if(!this.hook(e)||Pc.ignoreTags[e.name])return"title"===e.name&&n.length&&"text"===n[0].type&&this.options.setTitle&&bn.setNavigationBarTitle({title:n[0].text}),void r.pop();if(e.pre&&2!==this.pre){this.pre=e.pre=void 0;for(let e=this.stack.length;e--;)this.stack[e].pre&&(this.pre=1)}const i={};if("svg"===e.name){if(this.xml>1)return void this.xml--;let n="";const o=t.style;return t.style="",t.xmlns="http://www.w3.org/2000/svg",function e(t){if("text"===t.type)return void(n+=t.text);const o=Pc.svgDict[t.name]||t.name;n+="<"+o;for(const r in t.attrs){const e=t.attrs[r];e&&(n+=` ${Pc.svgDict[r]||r}="${e}"`)}if(t.children){n+=">";for(let n=0;n<t.children.length;n++)e(t.children[n]);n+="</"+o+">"}else n+="/>"}(e),e.name="img",e.attrs={src:"data:image/svg+xml;utf8,"+n.replace(/#/g,"%23"),style:o,ignore:"T"},e.children=void 0,void(this.xml=!1)}if(t.align&&("table"===e.name?"center"===t.align?i["margin-inline-start"]=i["margin-inline-end"]="auto":i.float=t.align:i["text-align"]=t.align,t.align=void 0),t.dir&&(i.direction=t.dir,t.dir=void 0),"font"===e.name&&(t.color&&(i.color=t.color,t.color=void 0),t.face&&(i["font-family"]=t.face,t.face=void 0),t.size)){let e=parseInt(t.size);isNaN(e)||(e<1?e=1:e>7&&(e=7),i["font-size"]=["x-small","small","medium","large","x-large","xx-large","xxx-large"][e-1]),t.size=void 0}if((t.class||"").includes("align-center")&&(i["text-align"]="center"),Object.assign(i,this.parseStyle(e)),"table"!==e.name&&parseInt(i.width)>Nc&&(i["max-width"]="100%",i["box-sizing"]="border-box"),Pc.blockTags[e.name]?e.name="div":Pc.trustTags[e.name]||this.xml||(e.name="span"),"a"===e.name||"ad"===e.name)this.expose();else if("video"===e.name)(i.height||"").includes("auto")&&(i.height=void 0);else if("ul"!==e.name&&"ol"!==e.name||!e.c){if("table"===e.name){let o=parseFloat(t.cellpadding),r=parseFloat(t.cellspacing);const a=parseFloat(t.border),l=i["border-color"],s=i["border-style"];if(e.c&&(isNaN(o)&&(o=2),isNaN(r)&&(r=2)),a&&(t.style+=`;border:${a}px ${s||"solid"} ${l||"gray"}`),e.flag&&e.c){i.display="grid",r?(i["grid-gap"]=r+"px",i.padding=r+"px"):a&&(t.style+=";border-left:0;border-top:0");const c=[],u=[],p=[],f={};!function e(t){for(let n=0;n<t.length;n++)"tr"===t[n].name?u.push(t[n]):e(t[n].children||[])}(n);for(let e=1;e<=u.length;e++){let t=1;for(let n=0;n<u[e-1].children.length;n++){const i=u[e-1].children[n];if("td"===i.name||"th"===i.name){for(;f[e+"."+t];)t++;let n=i.attrs.style||"",u=n.indexOf("width")?n.indexOf(";width"):0;if(-1!==u){let e=n.indexOf(";",u+6);-1===e&&(e=n.length),i.attrs.colspan||(c[t]=n.substring(u?u+7:6,e)),n=n.substr(0,u)+n.substr(e)}if(n+=";display:flex",u=n.indexOf("vertical-align"),-1!==u){const e=n.substr(u+15,10);e.includes("middle")?n+=";align-items:center":e.includes("bottom")&&(n+=";align-items:flex-end")}else n+=";align-items:center";if(u=n.indexOf("text-align"),-1!==u){const e=n.substr(u+11,10);e.includes("center")?n+=";justify-content: center":e.includes("right")&&(n+=";justify-content: right")}if(n=(a?`;border:${a}px ${s||"solid"} ${l||"gray"}`+(r?"":";border-right:0;border-bottom:0"):"")+(o?`;padding:${o}px`:"")+";"+n,i.attrs.colspan&&(n+=`;grid-column-start:${t};grid-column-end:${t+parseInt(i.attrs.colspan)}`,i.attrs.rowspan||(n+=`;grid-row-start:${e};grid-row-end:${e+1}`),t+=parseInt(i.attrs.colspan)-1),i.attrs.rowspan){n+=`;grid-row-start:${e};grid-row-end:${e+parseInt(i.attrs.rowspan)}`,i.attrs.colspan||(n+=`;grid-column-start:${t};grid-column-end:${t+1}`);for(let n=1;n<i.attrs.rowspan;n++)for(let o=0;o<(i.attrs.colspan||1);o++)f[e+n+"."+(t-o)]=1}n&&(i.attrs.style=n),p.push(i),t++}}if(1===e){let e="";for(let n=1;n<t;n++)e+=(c[n]?c[n]:"auto")+" ";i["grid-template-columns"]=e}}e.children=p}else e.c&&(i.display="table"),isNaN(r)||(i["border-spacing"]=r+"px"),(a||o)&&function e(t){for(let n=0;n<t.length;n++){const r=t[n];"th"===r.name||"td"===r.name?(a&&(r.attrs.style=`border:${a}px ${s||"solid"} ${l||"gray"};${r.attrs.style||""}`),o&&(r.attrs.style=`padding:${o}px;${r.attrs.style||""}`)):r.children&&e(r.children)}}(n);if(this.options.scrollTable&&!(t.style||"").includes("inline")){const n=Object.assign({},e);e.name="div",e.attrs={style:"overflow:auto"},e.children=[n],t=n.attrs}}else if("td"!==e.name&&"th"!==e.name||!t.colspan&&!t.rowspan)if("ruby"===e.name){e.name="span";for(let e=0;e<n.length-1;e++)"text"===n[e].type&&"rt"===n[e+1].name&&(n[e]={name:"div",attrs:{style:"display:inline-block;text-align:center"},children:[{name:"div",attrs:{style:"font-size:50%;"+(n[e+1].attrs.style||"")},children:n[e+1].children},n[e]]},n.splice(e+1,1))}else e.c&&function e(t){t.c=2;for(let n=t.children.length;n--;){const o=t.children[n];o.name&&(Pc.inlineTags[o.name]||(o.attrs.style||"").includes("inline")&&o.children)&&!o.c&&e(o),o.c&&"table"!==o.name||(t.c=1)}}(e);else for(let l=this.stack.length;l--;)if("table"===this.stack[l].name){this.stack[l].flag=1;break}}else{const e={a:"lower-alpha",A:"upper-alpha",i:"lower-roman",I:"upper-roman"};e[t.type]&&(t.style+=";list-style-type:"+e[t.type],t.type=void 0);for(let t=n.length;t--;)"li"===n[t].name&&(n[t].c=1)}if((i.display||"").includes("flex")&&!e.c)for(let l=n.length;l--;){const e=n[l];e.f&&(e.attrs.style=(e.attrs.style||"")+e.f,e.f=void 0)}const a=o&&((o.attrs.style||"").includes("flex")||(o.attrs.style||"").includes("grid"))&&!(e.c&&yn.getNFCAdapter);a&&(e.f=";max-width:100%"),n.length>=50&&e.c&&!(i.display||"").includes("flex")&&Mc(n);for(const l in i)if(i[l]){const n=`;${l}:${i[l].replace(" !important","")}`;a&&(l.includes("flex")&&"flex-direction"!==l||"align-self"===l||l.includes("grid")||"-"===i[l][0]||l.includes("width")&&n.includes("%"))?(e.f+=n,"width"===l&&(t.style+=";width:100%")):t.style+=n}t.style=t.style.substr(1)||void 0;for(const l in t)t[l]||delete t[l]},qc.prototype.onText=function(e){if(!this.pre){let t,n="";for(let o=0,r=e.length;o<r;o++)jc[e[o]]?(" "!==n[n.length-1]&&(n+=" "),"\n"!==e[o]||t||(t=!0)):n+=e[o];if(" "===n){if(t)return;{const e=this.stack[this.stack.length-1];if(e&&"t"===e.name[0])return}}e=n}const t=Object.create(null);if(t.type="text",t.text=Fc(e),this.hook(t)){"force"===this.options.selectable&&Tc.includes("iOS")&&!bn.canIUse("rich-text.user-select")&&this.expose();(this.stack.length?this.stack[this.stack.length-1].children:this.nodes).push(t)}},Dc.prototype.parse=function(e){this.content=e||"",this.i=0,this.start=0,this.state=this.text;for(let t=this.content.length;-1!==this.i&&this.i<t;)this.state()},Dc.prototype.checkClose=function(e){const t="/"===this.content[this.i];return!!(">"===this.content[this.i]||t&&">"===this.content[this.i+1])&&(e&&this.handler[e](this.content.substring(this.start,this.i)),this.i+=t?2:1,this.start=this.i,this.handler.onOpenTag(t),"script"===this.handler.tagName?(this.i=this.content.indexOf("</",this.i),-1!==this.i&&(this.i+=2,this.start=this.i),this.state=this.endTag):this.state=this.text,!0)},Dc.prototype.text=function(){if(this.i=this.content.indexOf("<",this.i),-1===this.i)return void(this.start<this.content.length&&this.handler.onText(this.content.substring(this.start,this.content.length)));const e=this.content[this.i+1];if(e>="a"&&e<="z"||e>="A"&&e<="Z")this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i)),this.start=++this.i,this.state=this.tagName;else if("/"===e||"!"===e||"?"===e){this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i));const t=this.content[this.i+2];if("/"===e&&(t>="a"&&t<="z"||t>="A"&&t<="Z"))return this.i+=2,this.start=this.i,void(this.state=this.endTag);let n="--\x3e";"!"===e&&"-"===this.content[this.i+2]&&"-"===this.content[this.i+3]||(n=">"),this.i=this.content.indexOf(n,this.i),-1!==this.i&&(this.i+=n.length,this.start=this.i)}else this.i++},Dc.prototype.tagName=function(){if(jc[this.content[this.i]]){for(this.handler.onTagName(this.content.substring(this.start,this.i));jc[this.content[++this.i]];);this.i<this.content.length&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)}else this.checkClose("onTagName")||this.i++},Dc.prototype.attrName=function(){let e=this.content[this.i];if(jc[e]||"="===e){this.handler.onAttrName(this.content.substring(this.start,this.i));let t="="===e;const n=this.content.length;for(;++this.i<n;)if(e=this.content[this.i],!jc[e]){if(this.checkClose())return;if(t)return this.start=this.i,void(this.state=this.attrVal);if("="!==this.content[this.i])return this.start=this.i,void(this.state=this.attrName);t=!0}}else this.checkClose("onAttrName")||this.i++},Dc.prototype.attrVal=function(){const e=this.content[this.i],t=this.content.length;if('"'===e||"'"===e){if(this.start=++this.i,this.i=this.content.indexOf(e,this.i),-1===this.i)return;this.handler.onAttrVal(this.content.substring(this.start,this.i))}else for(;this.i<t;this.i++){if(jc[this.content[this.i]]){this.handler.onAttrVal(this.content.substring(this.start,this.i));break}if(this.checkClose("onAttrVal"))return}for(;jc[this.content[++this.i]];);this.i<t&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)},Dc.prototype.endTag=function(){const e=this.content[this.i];if(jc[e]||">"===e||"/"===e){if(this.handler.onCloseTag(this.content.substring(this.start,this.i)),">"!==e&&(this.i=this.content.indexOf(">",this.i),-1===this.i))return;this.start=++this.i,this.state=this.text}else this.i++};const Lc={props:{src:{type:String,default:()=>oc.image.src},mode:{type:String,default:()=>oc.image.mode},width:{type:[String,Number],default:()=>oc.image.width},height:{type:[String,Number],default:()=>oc.image.height},shape:{type:String,default:()=>oc.image.shape},radius:{type:[String,Number],default:()=>oc.image.radius},lazyLoad:{type:Boolean,default:()=>oc.image.lazyLoad},showMenuByLongpress:{type:Boolean,default:()=>oc.image.showMenuByLongpress},loadingIcon:{type:String,default:()=>oc.image.loadingIcon},errorIcon:{type:String,default:()=>oc.image.errorIcon},showLoading:{type:Boolean,default:()=>oc.image.showLoading},showError:{type:Boolean,default:()=>oc.image.showError},fade:{type:Boolean,default:()=>oc.image.fade},webp:{type:Boolean,default:()=>oc.image.webp},duration:{type:[String,Number],default:()=>oc.image.duration},bgColor:{type:String,default:()=>oc.image.bgColor}}},Hc={props:{title:{type:[String,Number],default:()=>oc.cell.title},label:{type:[String,Number],default:()=>oc.cell.label},value:{type:[String,Number],default:()=>oc.cell.value},icon:{type:String,default:()=>oc.cell.icon},disabled:{type:Boolean,default:()=>oc.cell.disabled},border:{type:Boolean,default:()=>oc.cell.border},center:{type:Boolean,default:()=>oc.cell.center},url:{type:String,default:()=>oc.cell.url},linkType:{type:String,default:()=>oc.cell.linkType},clickable:{type:Boolean,default:()=>oc.cell.clickable},isLink:{type:Boolean,default:()=>oc.cell.isLink},required:{type:Boolean,default:()=>oc.cell.required},rightIcon:{type:String,default:()=>oc.cell.rightIcon},arrowDirection:{type:String,default:()=>oc.cell.arrowDirection},iconStyle:{type:[Object,String],default:()=>oc.cell.iconStyle},rightIconStyle:{type:[Object,String],default:()=>oc.cell.rightIconStyle},titleStyle:{type:[Object,String],default:()=>oc.cell.titleStyle},size:{type:String,default:()=>oc.cell.size},stop:{type:Boolean,default:()=>oc.cell.stop},name:{type:[Number,String],default:()=>oc.cell.name}}},Uc={props:{name:{type:[String,Number,Boolean],default:()=>oc.radio.name},shape:{type:String,default:()=>oc.radio.shape},disabled:{type:[String,Boolean],default:()=>oc.radio.disabled},labelDisabled:{type:[String,Boolean],default:()=>oc.radio.labelDisabled},activeColor:{type:String,default:()=>oc.radio.activeColor},inactiveColor:{type:String,default:()=>oc.radio.inactiveColor},iconSize:{type:[String,Number],default:()=>oc.radio.iconSize},labelSize:{type:[String,Number],default:()=>oc.radio.labelSize},label:{type:[String,Number],default:()=>oc.radio.label},size:{type:[String,Number],default:()=>oc.radio.size},color:{type:String,default:()=>oc.radio.color},labelColor:{type:String,default:()=>oc.radio.labelColor},iconColor:{type:String,default:()=>oc.radio.iconColor}}},Vc={props:{modelValue:{type:[String,Number,Boolean],default:()=>oc.radioGroup.value},disabled:{type:Boolean,default:()=>oc.radioGroup.disabled},shape:{type:String,default:()=>oc.radioGroup.shape},activeColor:{type:String,default:()=>oc.radioGroup.activeColor},inactiveColor:{type:String,default:()=>oc.radioGroup.inactiveColor},name:{type:String,default:()=>oc.radioGroup.name},size:{type:[String,Number],default:()=>oc.radioGroup.size},placement:{type:String,default:()=>oc.radioGroup.placement},label:{type:[String],default:()=>oc.radioGroup.label},labelColor:{type:[String],default:()=>oc.radioGroup.labelColor},labelSize:{type:[String,Number],default:()=>oc.radioGroup.labelSize},labelDisabled:{type:Boolean,default:()=>oc.radioGroup.labelDisabled},iconColor:{type:String,default:()=>oc.radioGroup.iconColor},iconSize:{type:[String,Number],default:()=>oc.radioGroup.iconSize},borderBottom:{type:Boolean,default:()=>oc.radioGroup.borderBottom},iconPlacement:{type:String,default:()=>oc.radio.iconPlacement},gap:{type:[String,Number],default:()=>oc.radioGroup.gap}}},Rc={props:{}},$c={props:{value:{type:[String,Number],default:()=>oc.textarea.value},modelValue:{type:[String,Number],default:()=>oc.textarea.value},placeholder:{type:[String,Number],default:()=>oc.textarea.placeholder},placeholderClass:{type:String,default:()=>oc.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>oc.input.placeholderStyle},height:{type:[String,Number],default:()=>oc.textarea.height},confirmType:{type:String,default:()=>oc.textarea.confirmType},disabled:{type:Boolean,default:()=>oc.textarea.disabled},count:{type:Boolean,default:()=>oc.textarea.count},focus:{type:Boolean,default:()=>oc.textarea.focus},autoHeight:{type:Boolean,default:()=>oc.textarea.autoHeight},fixed:{type:Boolean,default:()=>oc.textarea.fixed},cursorSpacing:{type:Number,default:()=>oc.textarea.cursorSpacing},cursor:{type:[String,Number],default:()=>oc.textarea.cursor},showConfirmBar:{type:Boolean,default:()=>oc.textarea.showConfirmBar},selectionStart:{type:Number,default:()=>oc.textarea.selectionStart},selectionEnd:{type:Number,default:()=>oc.textarea.selectionEnd},adjustPosition:{type:Boolean,default:()=>oc.textarea.adjustPosition},disableDefaultPadding:{type:Boolean,default:()=>oc.textarea.disableDefaultPadding},holdKeyboard:{type:Boolean,default:()=>oc.textarea.holdKeyboard},maxlength:{type:[String,Number],default:()=>oc.textarea.maxlength},border:{type:String,default:()=>oc.textarea.border},formatter:{type:[Function,null],default:()=>oc.textarea.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}},Wc={props:{loading:{type:Boolean,default:()=>oc.switch.loading},disabled:{type:Boolean,default:()=>oc.switch.disabled},size:{type:[String,Number],default:()=>oc.switch.size},activeColor:{type:String,default:()=>oc.switch.activeColor},inactiveColor:{type:String,default:()=>oc.switch.inactiveColor},modelValue:{type:[Boolean,String,Number],default:()=>oc.switch.value},activeValue:{type:[String,Number,Boolean],default:()=>oc.switch.activeValue},inactiveValue:{type:[String,Number,Boolean],default:()=>oc.switch.inactiveValue},asyncChange:{type:Boolean,default:()=>oc.switch.asyncChange},space:{type:[String,Number],default:()=>oc.switch.space}}},Gc={props:{name:{type:[String,Number,Boolean],default:()=>oc.checkbox.name},shape:{type:String,default:()=>oc.checkbox.shape},size:{type:[String,Number],default:()=>oc.checkbox.size},checked:{type:Boolean,default:()=>oc.checkbox.checked},disabled:{type:[String,Boolean],default:()=>oc.checkbox.disabled},activeColor:{type:String,default:()=>oc.checkbox.activeColor},inactiveColor:{type:String,default:()=>oc.checkbox.inactiveColor},iconSize:{type:[String,Number],default:()=>oc.checkbox.iconSize},iconColor:{type:String,default:()=>oc.checkbox.iconColor},label:{type:[String,Number],default:()=>oc.checkbox.label},labelSize:{type:[String,Number],default:()=>oc.checkbox.labelSize},labelColor:{type:String,default:()=>oc.checkbox.labelColor},labelDisabled:{type:[String,Boolean],default:()=>oc.checkbox.labelDisabled},usedAlone:{type:[Boolean],default:()=>!1}}},Jc={props:{name:{type:String,default:()=>oc.checkboxGroup.name},modelValue:{type:Array,default:()=>oc.checkboxGroup.value},shape:{type:String,default:()=>oc.checkboxGroup.shape},disabled:{type:Boolean,default:()=>oc.checkboxGroup.disabled},activeColor:{type:String,default:()=>oc.checkboxGroup.activeColor},inactiveColor:{type:String,default:()=>oc.checkboxGroup.inactiveColor},size:{type:[String,Number],default:()=>oc.checkboxGroup.size},placement:{type:String,default:()=>oc.checkboxGroup.placement},labelSize:{type:[String,Number],default:()=>oc.checkboxGroup.labelSize},labelColor:{type:[String],default:()=>oc.checkboxGroup.labelColor},labelDisabled:{type:Boolean,default:()=>oc.checkboxGroup.labelDisabled},iconColor:{type:String,default:()=>oc.checkboxGroup.iconColor},iconSize:{type:[String,Number],default:()=>oc.checkboxGroup.iconSize},iconPlacement:{type:String,default:()=>oc.checkboxGroup.iconPlacement},borderBottom:{type:Boolean,default:()=>oc.checkboxGroup.borderBottom}}};function Xc(e,t){return["[object Object]","[object File]"].includes(Object.prototype.toString.call(e))?Object.keys(e).reduce(((n,o)=>(t.includes(o)||(n[o]=e[o]),n)),{}):{}}function Kc(e){return e.tempFiles.map((e=>({...Xc(e,["path"]),url:e.path,size:e.size})))}const Yc={watch:{accept:{immediate:!0,handler(e){}}}},Zc={props:{accept:{type:String,default:()=>oc.upload.accept},extension:{type:Array,default:()=>oc.upload.extension},capture:{type:[String,Array],default:()=>oc.upload.capture},compressed:{type:Boolean,default:()=>oc.upload.compressed},camera:{type:String,default:()=>oc.upload.camera},maxDuration:{type:Number,default:()=>oc.upload.maxDuration},uploadIcon:{type:String,default:()=>oc.upload.uploadIcon},uploadIconColor:{type:String,default:()=>oc.upload.uploadIconColor},useBeforeRead:{type:Boolean,default:()=>oc.upload.useBeforeRead},afterRead:{type:Function,default:null},beforeRead:{type:Function,default:null},previewFullImage:{type:Boolean,default:()=>oc.upload.previewFullImage},maxCount:{type:[String,Number],default:()=>oc.upload.maxCount},disabled:{type:Boolean,default:()=>oc.upload.disabled},imageMode:{type:String,default:()=>oc.upload.imageMode},name:{type:String,default:()=>oc.upload.name},sizeType:{type:Array,default:()=>oc.upload.sizeType},multiple:{type:Boolean,default:()=>oc.upload.multiple},deletable:{type:Boolean,default:()=>oc.upload.deletable},maxSize:{type:[String,Number],default:()=>oc.upload.maxSize},fileList:{type:Array,default:()=>oc.upload.fileList},uploadText:{type:String,default:()=>oc.upload.uploadText},width:{type:[String,Number],default:()=>oc.upload.width},height:{type:[String,Number],default:()=>oc.upload.height},previewImage:{type:Boolean,default:()=>oc.upload.previewImage}}},_c={props:{label:{type:String,default:()=>oc.formItem.label},prop:{type:String,default:()=>oc.formItem.prop},rules:{type:Array,default:()=>oc.formItem.rules},borderBottom:{type:[String,Boolean],default:()=>oc.formItem.borderBottom},labelPosition:{type:String,default:()=>oc.formItem.labelPosition},labelWidth:{type:[String,Number],default:()=>oc.formItem.labelWidth},rightIcon:{type:String,default:()=>oc.formItem.rightIcon},leftIcon:{type:String,default:()=>oc.formItem.leftIcon},required:{type:Boolean,default:()=>oc.formItem.required},leftIconStyle:{type:[String,Object],default:()=>oc.formItem.leftIconStyle}}},eu={props:{model:{type:Object,default:()=>oc.form.model},rules:{type:[Object,Function,Array],default:()=>oc.form.rules},errorType:{type:String,default:()=>oc.form.errorType},borderBottom:{type:Boolean,default:()=>oc.form.borderBottom},labelPosition:{type:String,default:()=>oc.form.labelPosition},labelWidth:{type:[String,Number],default:()=>oc.form.labelWidth},labelAlign:{type:String,default:()=>oc.form.labelAlign},labelStyle:{type:Object,default:()=>oc.form.labelStyle}}},tu=/%[sdj%]/g;let nu=function(){};function ou(e){if(!e||!e.length)return null;const t={};return e.forEach((e=>{const{field:n}=e;t[n]=t[n]||[],t[n].push(e)})),t}function ru(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let o=1;const r=t[0],i=t.length;if("function"==typeof r)return r.apply(null,t.slice(1));if("string"==typeof r){let e=String(r).replace(tu,(e=>{if("%%"===e)return"%";if(o>=i)return e;switch(e){case"%s":return String(t[o++]);case"%d":return Number(t[o++]);case"%j":try{return JSON.stringify(t[o++])}catch(n){return"[Circular]"}break;default:return e}}));for(let n=t[o];o<i;n=t[++o])e+=` ${n}`;return e}return r}function iu(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}(t)||"string"!=typeof e||e))}function au(e,t,n){let o=0;const r=e.length;!function i(a){if(a&&a.length)return void n(a);const l=o;o+=1,l<r?t(e[l],i):n([])}([])}function lu(e,t,n,o){if(t.first){const t=new Promise(((t,r)=>{const i=function(e){const t=[];return Object.keys(e).forEach((n=>{t.push.apply(t,e[n])})),t}(e);au(i,n,(function(e){return o(e),e.length?r({errors:e,fields:ou(e)}):t()}))}));return t.catch((e=>e)),t}let r=t.firstFields||[];!0===r&&(r=Object.keys(e));const i=Object.keys(e),a=i.length;let l=0;const s=[],c=new Promise(((t,c)=>{const u=function(e){if(s.push.apply(s,e),l++,l===a)return o(s),s.length?c({errors:s,fields:ou(s)}):t()};i.length||(o(s),t()),i.forEach((t=>{const o=e[t];-1!==r.indexOf(t)?au(o,n,u):function(e,t,n){const o=[];let r=0;const i=e.length;function a(e){o.push.apply(o,e),r++,r===i&&n(o)}e.forEach((e=>{t(e,a)}))}(o,n,u)}))}));return c.catch((e=>e)),c}function su(e){return function(t){return t&&t.message?(t.field=t.field||e.fullField,t):{message:"function"==typeof t?t():t,field:t.field||e.fullField}}}function cu(e,t){if(t)for(const n in t)if(t.hasOwnProperty(n)){const o=t[n];"object"==typeof o&&"object"==typeof e[n]?e[n]={...e[n],...o}:e[n]=o}return e}function uu(e,t,n,o,r,i){!e.required||n.hasOwnProperty(e.field)&&!iu(t,i||e.type)||o.push(ru(r.messages.required,e.fullField))}"undefined"!=typeof process&&process.env;const pu={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i};var fu={integer:function(e){return/^(-)?\d+$/.test(e)},float:function(e){return/^(-)?\d+(\.\d+)?$/.test(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear},number:function(e){return!isNaN(e)&&"number"==typeof+e},object:function(e){return"object"==typeof e&&!fu.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&!!e.match(pu.email)&&e.length<255},url:function(e){return"string"==typeof e&&!!e.match(pu.url)},hex:function(e){return"string"==typeof e&&!!e.match(pu.hex)}};const du="enum";const hu={required:uu,whitespace:function(e,t,n,o,r){(/^\s+$/.test(t)||""===t)&&o.push(ru(r.messages.whitespace,e.fullField))},type:function(e,t,n,o,r){if(e.required&&void 0===t)return void uu(e,t,n,o,r);const i=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(i)>-1?fu[i](t)||o.push(ru(r.messages.types[i],e.fullField,e.type)):i&&typeof t!==e.type&&o.push(ru(r.messages.types[i],e.fullField,e.type))},range:function(e,t,n,o,r){const i="number"==typeof e.len,a="number"==typeof e.min,l="number"==typeof e.max,s=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;let c=t,u=null;const p="number"==typeof t,f="string"==typeof t,d=Array.isArray(t);if(p?u="number":f?u="string":d&&(u="array"),!u)return!1;d&&(c=t.length),f&&(c=t.replace(s,"_").length),i?c!==e.len&&o.push(ru(r.messages[u].len,e.fullField,e.len)):a&&!l&&c<e.min?o.push(ru(r.messages[u].min,e.fullField,e.min)):l&&!a&&c>e.max?o.push(ru(r.messages[u].max,e.fullField,e.max)):a&&l&&(c<e.min||c>e.max)&&o.push(ru(r.messages[u].range,e.fullField,e.min,e.max))},enum:function(e,t,n,o,r){e[du]=Array.isArray(e[du])?e[du]:[],-1===e[du].indexOf(t)&&o.push(ru(r.messages[du],e.fullField,e[du].join(", ")))},pattern:function(e,t,n,o,r){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||o.push(ru(r.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||o.push(ru(r.messages.pattern.mismatch,e.fullField,t,e.pattern))}}};function gu(e,t,n,o,r){const i=e.type,a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t,i)&&!e.required)return n();hu.required(e,t,o,a,r,i),iu(t,i)||hu.type(e,t,o,a,r)}n(a)}const mu={string:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t,"string")&&!e.required)return n();hu.required(e,t,o,i,r,"string"),iu(t,"string")||(hu.type(e,t,o,i,r),hu.range(e,t,o,i,r),hu.pattern(e,t,o,i,r),!0===e.whitespace&&hu.whitespace(e,t,o,i,r))}n(i)},method:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&hu.type(e,t,o,i,r)}n(i)},number:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(""===t&&(t=void 0),iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&(hu.type(e,t,o,i,r),hu.range(e,t,o,i,r))}n(i)},boolean:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&hu.type(e,t,o,i,r)}n(i)},regexp:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),iu(t)||hu.type(e,t,o,i,r)}n(i)},integer:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&(hu.type(e,t,o,i,r),hu.range(e,t,o,i,r))}n(i)},float:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&(hu.type(e,t,o,i,r),hu.range(e,t,o,i,r))}n(i)},array:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t,"array")&&!e.required)return n();hu.required(e,t,o,i,r,"array"),iu(t,"array")||(hu.type(e,t,o,i,r),hu.range(e,t,o,i,r))}n(i)},object:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&hu.type(e,t,o,i,r)}n(i)},enum:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r),void 0!==t&&hu.enum(e,t,o,i,r)}n(i)},pattern:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t,"string")&&!e.required)return n();hu.required(e,t,o,i,r),iu(t,"string")||hu.pattern(e,t,o,i,r)}n(i)},date:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();if(hu.required(e,t,o,i,r),!iu(t)){let n;n="number"==typeof t?new Date(t):t,hu.type(e,n,o,i,r),n&&hu.range(e,n.getTime(),o,i,r)}}n(i)},url:gu,hex:gu,email:gu,required:function(e,t,n,o,r){const i=[],a=Array.isArray(t)?"array":typeof t;hu.required(e,t,o,i,r,a),n(i)},any:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(iu(t)&&!e.required)return n();hu.required(e,t,o,i,r)}n(i)}};function yu(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){const e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}const bu=yu();function Su(e){this.rules=null,this._messages=bu,this.define(e)}Su.prototype={messages:function(e){return e&&(this._messages=cu(yu(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");let t,n;for(t in this.rules={},e)e.hasOwnProperty(t)&&(n=e[t],this.rules[t]=Array.isArray(n)?n:[n])},validate:function(e,t,n){const o=this;void 0===t&&(t={}),void 0===n&&(n=function(){});let r,i,a=e,l=t,s=n;if("function"==typeof l&&(s=l,l={}),!this.rules||0===Object.keys(this.rules).length)return s&&s(),Promise.resolve();if(l.messages){let e=this.messages();e===bu&&(e=yu()),cu(e,l.messages),l.messages=e}else l.messages=this.messages();const c={};(l.keys||Object.keys(this.rules)).forEach((t=>{r=o.rules[t],i=a[t],r.forEach((n=>{let r=n;"function"==typeof r.transform&&(a===e&&(a={...a}),i=a[t]=r.transform(i)),r="function"==typeof r?{validator:r}:{...r},r.validator=o.getValidationMethod(r),r.field=t,r.fullField=r.fullField||t,r.type=o.getType(r),r.validator&&(c[t]=c[t]||[],c[t].push({rule:r,value:i,source:a,field:t}))}))}));const u={};return lu(c,l,((e,t)=>{const{rule:n}=e;let o,r=!("object"!==n.type&&"array"!==n.type||"object"!=typeof n.fields&&"object"!=typeof n.defaultField);function i(e,t){return{...t,fullField:`${n.fullField}.${e}`}}function a(o){void 0===o&&(o=[]);let a=o;if(Array.isArray(a)||(a=[a]),!l.suppressWarning&&a.length&&Su.warning("async-validator:",a),a.length&&n.message&&(a=[].concat(n.message)),a=a.map(su(n)),l.first&&a.length)return u[n.field]=1,t(a);if(r){if(n.required&&!e.value)return a=n.message?[].concat(n.message).map(su(n)):l.error?[l.error(n,ru(l.messages.required,n.field))]:[],t(a);let o={};if(n.defaultField)for(const t in e.value)e.value.hasOwnProperty(t)&&(o[t]=n.defaultField);o={...o,...e.rule.fields};for(const e in o)if(o.hasOwnProperty(e)){const t=Array.isArray(o[e])?o[e]:[o[e]];o[e]=t.map(i.bind(null,e))}const r=new Su(o);r.messages(l.messages),e.rule.options&&(e.rule.options.messages=l.messages,e.rule.options.error=l.error),r.validate(e.value,e.rule.options||l,(e=>{const n=[];a&&a.length&&n.push.apply(n,a),e&&e.length&&n.push.apply(n,e),t(n.length?n:null)}))}else t(a)}r=r&&(n.required||!n.required&&e.value),n.field=e.field,n.asyncValidator?o=n.asyncValidator(n,e.value,a,e.source,l):n.validator&&(o=n.validator(n,e.value,a,e.source,l),!0===o?a():!1===o?a(n.message||`${n.field} fails`):o instanceof Array?a(o):o instanceof Error&&a(o.message)),o&&o.then&&o.then((()=>a()),(e=>a(e)))}),(e=>{!function(e){let t,n=[],o={};function r(e){if(Array.isArray(e)){let t;n=(t=n).concat.apply(t,e)}else n.push(e)}for(t=0;t<e.length;t++)r(e[t]);n.length?o=ou(n):(n=null,o=null),s(n,o)}(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!mu.hasOwnProperty(e.type))throw new Error(ru("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"==typeof e.validator)return e.validator;const t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?mu.required:mu[this.getType(e)]||!1}},Su.register=function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");mu[e]=t},Su.warning=nu,Su.messages=bu;const xu={props:{hasInput:{type:Boolean,default:()=>!1},placeholder:{type:String,default:()=>"请选择"},format:{type:String,default:()=>""},show:{type:Boolean,default:()=>oc.datetimePicker.show},popupMode:{type:String,default:()=>oc.picker.popupMode},showToolbar:{type:Boolean,default:()=>oc.datetimePicker.showToolbar},toolbarRightSlot:{type:Boolean,default:!1},modelValue:{type:[String,Number],default:()=>oc.datetimePicker.value},title:{type:String,default:()=>oc.datetimePicker.title},mode:{type:String,default:()=>oc.datetimePicker.mode},maxDate:{type:Number,default:()=>oc.datetimePicker.maxDate},minDate:{type:Number,default:()=>oc.datetimePicker.minDate},minHour:{type:Number,default:()=>oc.datetimePicker.minHour},maxHour:{type:Number,default:()=>oc.datetimePicker.maxHour},minMinute:{type:Number,default:()=>oc.datetimePicker.minMinute},maxMinute:{type:Number,default:()=>oc.datetimePicker.maxMinute},filter:{type:[Function,null],default:()=>oc.datetimePicker.filter},formatter:{type:[Function,null],default:()=>oc.datetimePicker.formatter},loading:{type:Boolean,default:()=>oc.datetimePicker.loading},itemHeight:{type:[String,Number],default:()=>oc.datetimePicker.itemHeight},cancelText:{type:String,default:()=>oc.datetimePicker.cancelText},confirmText:{type:String,default:()=>oc.datetimePicker.confirmText},cancelColor:{type:String,default:()=>oc.datetimePicker.cancelColor},confirmColor:{type:String,default:()=>oc.datetimePicker.confirmColor},visibleItemCount:{type:[String,Number],default:()=>oc.datetimePicker.visibleItemCount},closeOnClickOverlay:{type:Boolean,default:()=>oc.datetimePicker.closeOnClickOverlay},defaultIndex:{type:Array,default:()=>oc.datetimePicker.defaultIndex}}};var vu=1e3,wu=6e4,Au=36e5,Bu="millisecond",Cu="second",ku="minute",Eu="hour",Iu="day",Pu="week",Ou="month",Nu="quarter",Tu="year",ju="date",zu="Invalid Date",Qu=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,Fu=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;const Mu={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],n=e%100;return"["+e+(t[(n-20)%10]||t[n]||t[0])+"]"}};var qu=function(e,t,n){var o=String(e);return!o||o.length>=t?e:""+Array(t+1-o.length).join(n)+e};const Du={s:qu,z:function(e){var t=-e.utcOffset(),n=Math.abs(t),o=Math.floor(n/60),r=n%60;return(t<=0?"+":"-")+qu(o,2,"0")+":"+qu(r,2,"0")},m:function e(t,n){if(t.date()<n.date())return-e(n,t);var o=12*(n.year()-t.year())+(n.month()-t.month()),r=t.clone().add(o,Ou),i=n-r<0,a=t.clone().add(o+(i?-1:1),Ou);return+(-(o+(n-r)/(i?r-a:a-r))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:Ou,y:Tu,w:Pu,d:Iu,D:ju,h:Eu,m:ku,s:Cu,ms:Bu,Q:Nu}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};var Lu="en",Hu={};Hu[Lu]=Mu;var Uu="$isDayjsObject",Vu=function(e){return e instanceof Gu||!(!e||!e[Uu])},Ru=function e(t,n,o){var r;if(!t)return Lu;if("string"==typeof t){var i=t.toLowerCase();Hu[i]&&(r=i),n&&(Hu[i]=n,r=i);var a=t.split("-");if(!r&&a.length>1)return e(a[0])}else{var l=t.name;Hu[l]=t,r=l}return!o&&r&&(Lu=r),r||!o&&Lu},$u=function(e,t){if(Vu(e))return e.clone();var n="object"==typeof t?t:{};return n.date=e,n.args=arguments,new Gu(n)},Wu=Du;Wu.l=Ru,Wu.i=Vu,Wu.w=function(e,t){return $u(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var Gu=function(){function e(e){this.$L=Ru(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[Uu]=!0}var t=e.prototype;return t.parse=function(e){this.$d=function(e){var t=e.date,n=e.utc;if(null===t)return new Date(NaN);if(Wu.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var o=t.match(Qu);if(o){var r=o[2]-1||0,i=(o[7]||"0").substring(0,3);return n?new Date(Date.UTC(o[1],r,o[3]||1,o[4]||0,o[5]||0,o[6]||0,i)):new Date(o[1],r,o[3]||1,o[4]||0,o[5]||0,o[6]||0,i)}}return new Date(t)}(e),this.init()},t.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},t.$utils=function(){return Wu},t.isValid=function(){return!(this.$d.toString()===zu)},t.isSame=function(e,t){var n=$u(e);return this.startOf(t)<=n&&n<=this.endOf(t)},t.isAfter=function(e,t){return $u(e)<this.startOf(t)},t.isBefore=function(e,t){return this.endOf(t)<$u(e)},t.$g=function(e,t,n){return Wu.u(e)?this[t]:this.set(n,e)},t.unix=function(){return Math.floor(this.valueOf()/1e3)},t.valueOf=function(){return this.$d.getTime()},t.startOf=function(e,t){var n=this,o=!!Wu.u(t)||t,r=Wu.p(e),i=function(e,t){var r=Wu.w(n.$u?Date.UTC(n.$y,t,e):new Date(n.$y,t,e),n);return o?r:r.endOf(Iu)},a=function(e,t){return Wu.w(n.toDate()[e].apply(n.toDate("s"),(o?[0,0,0,0]:[23,59,59,999]).slice(t)),n)},l=this.$W,s=this.$M,c=this.$D,u="set"+(this.$u?"UTC":"");switch(r){case Tu:return o?i(1,0):i(31,11);case Ou:return o?i(1,s):i(0,s+1);case Pu:var p=this.$locale().weekStart||0,f=(l<p?l+7:l)-p;return i(o?c-f:c+(6-f),s);case Iu:case ju:return a(u+"Hours",0);case Eu:return a(u+"Minutes",1);case ku:return a(u+"Seconds",2);case Cu:return a(u+"Milliseconds",3);default:return this.clone()}},t.endOf=function(e){return this.startOf(e,!1)},t.$set=function(e,t){var n,o=Wu.p(e),r="set"+(this.$u?"UTC":""),i=(n={},n[Iu]=r+"Date",n[ju]=r+"Date",n[Ou]=r+"Month",n[Tu]=r+"FullYear",n[Eu]=r+"Hours",n[ku]=r+"Minutes",n[Cu]=r+"Seconds",n[Bu]=r+"Milliseconds",n)[o],a=o===Iu?this.$D+(t-this.$W):t;if(o===Ou||o===Tu){var l=this.clone().set(ju,1);l.$d[i](a),l.init(),this.$d=l.set(ju,Math.min(this.$D,l.daysInMonth())).$d}else i&&this.$d[i](a);return this.init(),this},t.set=function(e,t){return this.clone().$set(e,t)},t.get=function(e){return this[Wu.p(e)]()},t.add=function(e,t){var n,o=this;e=Number(e);var r=Wu.p(t),i=function(t){var n=$u(o);return Wu.w(n.date(n.date()+Math.round(t*e)),o)};if(r===Ou)return this.set(Ou,this.$M+e);if(r===Tu)return this.set(Tu,this.$y+e);if(r===Iu)return i(1);if(r===Pu)return i(7);var a=(n={},n[ku]=wu,n[Eu]=Au,n[Cu]=vu,n)[r]||1,l=this.$d.getTime()+e*a;return Wu.w(l,this)},t.subtract=function(e,t){return this.add(-1*e,t)},t.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return n.invalidDate||zu;var o=e||"YYYY-MM-DDTHH:mm:ssZ",r=Wu.z(this),i=this.$H,a=this.$m,l=this.$M,s=n.weekdays,c=n.months,u=n.meridiem,p=function(e,n,r,i){return e&&(e[n]||e(t,o))||r[n].slice(0,i)},f=function(e){return Wu.s(i%12||12,e,"0")},d=u||function(e,t,n){var o=e<12?"AM":"PM";return n?o.toLowerCase():o};return o.replace(Fu,(function(e,o){return o||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return Wu.s(t.$y,4,"0");case"M":return l+1;case"MM":return Wu.s(l+1,2,"0");case"MMM":return p(n.monthsShort,l,c,3);case"MMMM":return p(c,l);case"D":return t.$D;case"DD":return Wu.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(n.weekdaysMin,t.$W,s,2);case"ddd":return p(n.weekdaysShort,t.$W,s,3);case"dddd":return s[t.$W];case"H":return String(i);case"HH":return Wu.s(i,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return d(i,a,!0);case"A":return d(i,a,!1);case"m":return String(a);case"mm":return Wu.s(a,2,"0");case"s":return String(t.$s);case"ss":return Wu.s(t.$s,2,"0");case"SSS":return Wu.s(t.$ms,3,"0");case"Z":return r}return null}(e)||r.replace(":","")}))},t.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},t.diff=function(e,t,n){var o,r=this,i=Wu.p(t),a=$u(e),l=(a.utcOffset()-this.utcOffset())*wu,s=this-a,c=function(){return Wu.m(r,a)};switch(i){case Tu:o=c()/12;break;case Ou:o=c();break;case Nu:o=c()/3;break;case Pu:o=(s-l)/6048e5;break;case Iu:o=(s-l)/864e5;break;case Eu:o=s/Au;break;case ku:o=s/wu;break;case Cu:o=s/vu;break;default:o=s}return n?o:Wu.a(o)},t.daysInMonth=function(){return this.endOf(Ou).$D},t.$locale=function(){return Hu[this.$L]},t.locale=function(e,t){if(!e)return this.$L;var n=this.clone(),o=Ru(e,t,!0);return o&&(n.$L=o),n},t.clone=function(){return Wu.w(this.$d,this)},t.toDate=function(){return new Date(this.valueOf())},t.toJSON=function(){return this.isValid()?this.toISOString():null},t.toISOString=function(){return this.$d.toISOString()},t.toString=function(){return this.$d.toUTCString()},e}(),Ju=Gu.prototype;$u.prototype=Ju,[["$ms",Bu],["$s",Cu],["$m",ku],["$H",Eu],["$W",Iu],["$M",Ou],["$y",Tu],["$D",ju]].forEach((function(e){Ju[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),$u.extend=function(e,t){return e.$i||(e(t,Gu,$u),e.$i=!0),$u},$u.locale=Ru,$u.isDayjs=Vu,$u.unix=function(e){return $u(1e3*e)},$u.en=Hu[Lu],$u.Ls=Hu,$u.p={};const Xu={props:{top:{type:[String,Number],default:()=>oc.notify.top},type:{type:String,default:()=>oc.notify.type},color:{type:String,default:()=>oc.notify.color},bgColor:{type:String,default:()=>oc.notify.bgColor},message:{type:String,default:()=>oc.notify.message},duration:{type:[String,Number],default:()=>oc.notify.duration},fontSize:{type:[String,Number],default:()=>oc.notify.fontSize},safeAreaInsetTop:{type:Boolean,default:()=>oc.notify.safeAreaInsetTop}}},Ku={props:{show:{type:Boolean,default:()=>oc.actionSheet.show},title:{type:String,default:()=>oc.actionSheet.title},description:{type:String,default:()=>oc.actionSheet.description},actions:{type:Array,default:()=>oc.actionSheet.actions},cancelText:{type:String,default:()=>oc.actionSheet.cancelText},closeOnClickAction:{type:Boolean,default:()=>oc.actionSheet.closeOnClickAction},safeAreaInsetBottom:{type:Boolean,default:()=>oc.actionSheet.safeAreaInsetBottom},openType:{type:String,default:()=>oc.actionSheet.openType},closeOnClickOverlay:{type:Boolean,default:()=>oc.actionSheet.closeOnClickOverlay},round:{type:[Boolean,String,Number],default:()=>oc.actionSheet.round},wrapMaxHeight:{type:[String],default:()=>oc.actionSheet.wrapMaxHeight}}},Yu={props:{modelValue:{type:[String,Number],default:()=>oc.rate.value},count:{type:[String,Number],default:()=>oc.rate.count},disabled:{type:Boolean,default:()=>oc.rate.disabled},readonly:{type:Boolean,default:()=>oc.rate.readonly},size:{type:[String,Number],default:()=>oc.rate.size},inactiveColor:{type:String,default:()=>oc.rate.inactiveColor},activeColor:{type:String,default:()=>oc.rate.activeColor},gutter:{type:[String,Number],default:()=>oc.rate.gutter},minCount:{type:[String,Number],default:()=>oc.rate.minCount},allowHalf:{type:Boolean,default:()=>oc.rate.allowHalf},activeIcon:{type:String,default:()=>oc.rate.activeIcon},inactiveIcon:{type:String,default:()=>oc.rate.inactiveIcon},touchable:{type:Boolean,default:()=>oc.rate.touchable}}},Zu={props:{type:{type:String,default:()=>oc.tag.type},disabled:{type:[Boolean,String],default:()=>oc.tag.disabled},size:{type:String,default:()=>oc.tag.size},shape:{type:String,default:()=>oc.tag.shape},text:{type:[String,Number],default:()=>oc.tag.text},bgColor:{type:String,default:()=>oc.tag.bgColor},color:{type:String,default:()=>oc.tag.color},borderColor:{type:String,default:()=>oc.tag.borderColor},closeColor:{type:String,default:()=>oc.tag.closeColor},name:{type:[String,Number],default:()=>oc.tag.name},plainFill:{type:Boolean,default:()=>oc.tag.plainFill},plain:{type:Boolean,default:()=>oc.tag.plain},closable:{type:Boolean,default:()=>oc.tag.closable},show:{type:Boolean,default:()=>oc.tag.show},icon:{type:String,default:()=>oc.tag.icon},iconColor:{type:String,default:()=>oc.tag.iconColor}}},_u={props:{loadingText:{type:[String,Number],default:()=>oc.loadingPage.loadingText},image:{type:String,default:()=>oc.loadingPage.image},loadingMode:{type:String,default:()=>oc.loadingPage.loadingMode},loading:{type:Boolean,default:()=>oc.loadingPage.loading},bgColor:{type:String,default:()=>oc.loadingPage.bgColor},color:{type:String,default:()=>oc.loadingPage.color},fontSize:{type:[String,Number],default:()=>oc.loadingPage.fontSize},iconSize:{type:[String,Number],default:()=>oc.loadingPage.fontSize},loadingColor:{type:String,default:()=>oc.loadingPage.loadingColor},zIndex:{type:[Number],default:()=>oc.loadingPage.zIndex}}},ep={props:{icon:{type:String,default:()=>oc.empty.icon},text:{type:String,default:()=>oc.empty.text},textColor:{type:String,default:()=>oc.empty.textColor},textSize:{type:[String,Number],default:()=>oc.empty.textSize},iconColor:{type:String,default:()=>oc.empty.iconColor},iconSize:{type:[String,Number],default:()=>oc.empty.iconSize},mode:{type:String,default:()=>oc.empty.mode},width:{type:[String,Number],default:()=>oc.empty.width},height:{type:[String,Number],default:()=>oc.empty.height},show:{type:Boolean,default:()=>oc.empty.show},marginTop:{type:[String,Number],default:()=>oc.empty.marginTop}}},tp={props:{shape:{type:String,default:()=>oc.search.shape},bgColor:{type:String,default:()=>oc.search.bgColor},placeholder:{type:String,default:()=>oc.search.placeholder},clearabled:{type:Boolean,default:()=>oc.search.clearabled},focus:{type:Boolean,default:()=>oc.search.focus},showAction:{type:Boolean,default:()=>oc.search.showAction},actionStyle:{type:Object,default:()=>oc.search.actionStyle},actionText:{type:String,default:()=>oc.search.actionText},inputAlign:{type:String,default:()=>oc.search.inputAlign},inputStyle:{type:Object,default:()=>oc.search.inputStyle},disabled:{type:Boolean,default:()=>oc.search.disabled},borderColor:{type:String,default:()=>oc.search.borderColor},searchIconColor:{type:String,default:()=>oc.search.searchIconColor},color:{type:String,default:()=>oc.search.color},placeholderColor:{type:String,default:()=>oc.search.placeholderColor},searchIcon:{type:String,default:()=>oc.search.searchIcon},searchIconSize:{type:[Number,String],default:()=>oc.search.searchIconSize},margin:{type:String,default:()=>oc.search.margin},animation:{type:Boolean,default:()=>oc.search.animation},modelValue:{type:String,default:()=>oc.search.value},value:{type:String,default:()=>oc.search.value},maxlength:{type:[String,Number],default:()=>oc.search.maxlength},height:{type:[String,Number],default:()=>oc.search.height},label:{type:[String,Number,null],default:()=>oc.search.label},adjustPosition:{type:Boolean,default:()=>!0},autoBlur:{type:Boolean,default:()=>!1}}},np={props:{status:{type:String,default:()=>oc.loadmore.status},bgColor:{type:String,default:()=>oc.loadmore.bgColor},icon:{type:Boolean,default:()=>oc.loadmore.icon},fontSize:{type:[String,Number],default:()=>oc.loadmore.fontSize},iconSize:{type:[String,Number],default:()=>oc.loadmore.iconSize},color:{type:String,default:()=>oc.loadmore.color},loadingIcon:{type:String,default:()=>oc.loadmore.loadingIcon},loadmoreText:{type:String,default:()=>oc.loadmore.loadmoreText},loadingText:{type:String,default:()=>oc.loadmore.loadingText},nomoreText:{type:String,default:()=>oc.loadmore.nomoreText},isDot:{type:Boolean,default:()=>oc.loadmore.isDot},iconColor:{type:String,default:()=>oc.loadmore.iconColor},marginTop:{type:[String,Number],default:()=>oc.loadmore.marginTop},marginBottom:{type:[String,Number],default:()=>oc.loadmore.marginBottom},height:{type:[String,Number],default:()=>oc.loadmore.height},line:{type:Boolean,default:()=>oc.loadmore.line},lineColor:{type:String,default:()=>oc.loadmore.lineColor},dashed:{type:Boolean,default:()=>oc.loadmore.dashed}}},op={props:{safeAreaInsetTop:{type:Boolean,default:()=>oc.navbar.safeAreaInsetTop},placeholder:{type:Boolean,default:()=>oc.navbar.placeholder},fixed:{type:Boolean,default:()=>oc.navbar.fixed},border:{type:Boolean,default:()=>oc.navbar.border},leftIcon:{type:String,default:()=>oc.navbar.leftIcon},leftText:{type:String,default:()=>oc.navbar.leftText},rightText:{type:String,default:()=>oc.navbar.rightText},rightIcon:{type:String,default:()=>oc.navbar.rightIcon},title:{type:[String,Number],default:()=>oc.navbar.title},titleColor:{type:String,default:()=>oc.navbar.titleColor},bgColor:{type:String,default:()=>oc.navbar.bgColor},titleWidth:{type:[String,Number],default:()=>oc.navbar.titleWidth},height:{type:[String,Number],default:()=>oc.navbar.height},leftIconSize:{type:[String,Number],default:()=>oc.navbar.leftIconSize},leftIconColor:{type:String,default:()=>oc.navbar.leftIconColor},autoBack:{type:Boolean,default:()=>oc.navbar.autoBack},titleStyle:{type:[String,Object],default:()=>oc.navbar.titleStyle}}},rp={props:{color:{type:String,default:()=>oc.link.color},fontSize:{type:[String,Number],default:()=>oc.link.fontSize},underLine:{type:Boolean,default:()=>oc.link.underLine},href:{type:String,default:()=>oc.link.href},mpTips:{type:String,default:()=>oc.link.mpTips},lineColor:{type:String,default:()=>oc.link.lineColor},text:{type:String,default:()=>oc.link.text}}},ip={props:{length:{type:[String,Number],default:()=>oc.swiperIndicator.length},current:{type:[String,Number],default:()=>oc.swiperIndicator.current},indicatorActiveColor:{type:String,default:()=>oc.swiperIndicator.indicatorActiveColor},indicatorInactiveColor:{type:String,default:()=>oc.swiperIndicator.indicatorInactiveColor},indicatorMode:{type:String,default:()=>oc.swiperIndicator.indicatorMode}}},ap={props:{show:{type:Boolean,default:()=>oc.transition.show},mode:{type:String,default:()=>oc.transition.mode},duration:{type:[String,Number],default:()=>oc.transition.duration},timingFunction:{type:String,default:()=>oc.transition.timingFunction}}},lp=e=>({enter:`u-${e}-enter u-${e}-enter-active`,"enter-to":`u-${e}-enter-to u-${e}-enter-active`,leave:`u-${e}-leave u-${e}-leave-active`,"leave-to":`u-${e}-leave-to u-${e}-leave-active`}),sp={methods:{clickHandler(){this.$emit("click")},async vueEnter(){const e=lp(this.mode);this.status="enter",this.$emit("beforeEnter"),this.inited=!0,this.display=!0,this.classes=e.enter,await cr(),await os(20),this.$emit("enter"),this.transitionEnded=!1,this.$emit("afterEnter"),this.classes=e["enter-to"]},async vueLeave(){if(!this.display)return;const e=lp(this.mode);this.status="leave",this.$emit("beforeLeave"),this.classes=e.leave,await cr(),this.transitionEnded=!1,this.$emit("leave"),setTimeout(this.onTransitionEnd,this.duration),this.classes=e["leave-to"]},onTransitionEnd(){this.transitionEnded||(this.transitionEnded=!0,this.$emit("leave"===this.status?"afterLeave":"afterEnter"),!this.show&&this.display&&(this.display=!1,this.inited=!1))}}},cp={props:{show:{type:Boolean,default:()=>oc.toolbar.show},cancelText:{type:String,default:()=>oc.toolbar.cancelText},confirmText:{type:String,default:()=>oc.toolbar.confirmText},cancelColor:{type:String,default:()=>oc.toolbar.cancelColor},confirmColor:{type:String,default:()=>oc.toolbar.confirmColor},title:{type:String,default:()=>oc.toolbar.title},rightSlot:{type:Boolean,default:!1}}},up={props:{show:{type:Boolean,default:()=>oc.overlay.show},zIndex:{type:[String,Number],default:()=>oc.overlay.zIndex},duration:{type:[String,Number],default:()=>oc.overlay.duration},opacity:{type:[String,Number],default:()=>oc.overlay.opacity}}},pp={props:{isDot:{type:Boolean,default:()=>oc.badge.isDot},value:{type:[Number,String],default:()=>oc.badge.value},modelValue:{type:[Number,String],default:()=>oc.badge.modelValue},show:{type:Boolean,default:()=>oc.badge.show},max:{type:[Number,String],default:()=>oc.badge.max},type:{type:String,default:()=>oc.badge.type},showZero:{type:Boolean,default:()=>oc.badge.showZero},bgColor:{type:[String,null],default:()=>oc.badge.bgColor},color:{type:[String,null],default:()=>oc.badge.color},shape:{type:String,default:()=>oc.badge.shape},numberType:{type:String,default:()=>oc.badge.numberType},offset:{type:Array,default:()=>oc.badge.offset},inverted:{type:Boolean,default:()=>oc.badge.inverted},absolute:{type:Boolean,default:()=>oc.badge.absolute}}};exports.Parser=qc,exports.Schema=Su,exports._export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},exports.addStyle=cs,exports.addUnit=us,exports.buttonMixin=pc,exports.chooseFile=function({accept:e,multiple:t,capture:n,compressed:o,maxDuration:r,sizeType:i,camera:a,maxCount:l,extension:s}){return new Promise(((s,c)=>{switch(e){case"image":bn.chooseImage({count:t?Math.min(l,9):1,sourceType:n,sizeType:i,success:e=>s(function(e){return e.tempFiles.map((e=>({...Xc(e,["path"]),type:"image",url:e.path,thumb:e.path,size:e.size})))}(e)),fail:c});break;case"media":yn.chooseMedia({count:t?Math.min(l,9):1,sourceType:n,maxDuration:r,sizeType:i,camera:a,success:e=>s(function(e){return e.tempFiles.map((t=>({...Xc(t,["fileType","thumbTempFilePath","tempFilePath"]),type:e.type,url:t.tempFilePath,thumb:"video"===e.type?t.thumbTempFilePath:t.tempFilePath,size:t.size})))}(e)),fail:c});break;case"video":bn.chooseVideo({sourceType:n,compressed:o,maxDuration:r,camera:a,success:e=>s(function(e){return[{...Xc(e,["tempFilePath","thumbTempFilePath","errMsg"]),type:"video",url:e.tempFilePath,thumb:e.thumbTempFilePath,size:e.size}]}(e)),fail:c});break;case"file":yn.chooseMessageFile({count:t?l:1,type:e,success:e=>s(Kc(e)),fail:c});break;default:yn.chooseMessageFile({count:t?l:1,type:"all",success:e=>s(Kc(e)),fail:c})}}))},exports.color=nc,exports.colorGradient=Hs,exports.computed=Ti,exports.config=es,exports.createApp=function(e,t=null){return e&&(e.mpType="app"),aa(e,t).use(xa)},exports.createPinia=function(){const e=vn(!0),t=e.run((()=>Uo({})));let n=[],o=[];const r=Fo({install(e){El(r),r._a=e,e.provide(Il,r),e.config.globalProperties.$pinia=r,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r},exports.createUnistorage=function(e={}){const{key:t=Ll}=e||{};return(null==e?void 0:e.key)&&delete e.key,function(n){{const{store:r,options:i}=n;let{unistorage:a}=i||{};if(!a)return;const{paths:l=null,afterRestore:s,beforeRestore:c,serializer:u={serialize:JSON.stringify,deserialize:JSON.parse},key:p=r.$id}=((e,t)=>{var n;return e="object"==typeof(n=e)&&null!==n?e:Object.create(null),new Proxy(e,{get:(e,n,o)=>Reflect.get(e,n,o)||Reflect.get(t,n,o)})})(a,e);null==c||c(n);const f=t(p);try{const e=bn.getStorageSync(f);e&&r.$patch(u.deserialize(e))}catch(o){}null==s||s(n),r.$subscribe(((e,t)=>{try{const e=Array.isArray(l)?function(e,t){return t.reduce(((t,n)=>{const o=n.split(".");return function(e,t,n){return t.slice(0,-1).reduce(((e,t)=>/^(__proto__)$/.test(t)?{}:e[t]=e[t]||{}),e)[t[t.length-1]]=n,e}(t,o,function(e,t){return t.reduce(((e,t)=>null==e?void 0:e[t]),e)}(e,o))}),{})}(t,l):t;bn.setStorageSync(f,u.serialize(e))}catch(o){}}),{detached:!0})}}},exports.dayjs=$u,exports.deepClone=ps,exports.deepMerge=fs,exports.defProps=oc,exports.defineStore=function(e,t,n){let o,r;const i="function"==typeof t;function a(e,n){const a=Bi();(e=e||a&&wr(Il,null))&&El(e),(e=kl)._s.has(o)||(i?Dl(o,t,r,e):ql(o,r,e));return e._s.get(o)}return"string"==typeof e?(o=e,r=i?n:t):(r=e,o=e.id),a.$id=o,a},exports.e=(e,...t)=>g(e,...t),exports.error=ds,exports.f=(e,t)=>function(e,t){let n;if(S(e)||A(e)){n=new Array(e.length);for(let o=0,r=e.length;o<r;o++)n[o]=t(e[o],o,o)}else if("number"==typeof e){n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(C(e))if(e[Symbol.iterator])n=Array.from(e,((e,n)=>t(e,n,n)));else{const o=Object.keys(e);n=new Array(o.length);for(let r=0,i=o.length;r<i;r++){const i=o[r];n[r]=t(e[i],i,r)}}else n=[];return n}(e,t),exports.formValidate=xs,exports.getCurrentInstance=Bi,exports.getProperty=vs,exports.getPx=ns,exports.guid=ls,exports.icons={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""},exports.index=bn,exports.mixin=ks,exports.mixinUpload=Yc,exports.mpMixin=Es,exports.n=e=>a(e),exports.nextTick$1=cr,exports.o=(e,t)=>va(e,t),exports.onActivated=Pr,exports.onBackPress=xl,exports.onHide=gl,exports.onLaunch=ml,exports.onLoad=yl,exports.onMounted=Fr,exports.onPageScroll=vl,exports.onPullDownRefresh=Al,exports.onReachBottom=wl,exports.onReady=bl,exports.onShareAppMessage=Cl,exports.onShareTimeline=Bl,exports.onShow=hl,exports.onUnload=Sl,exports.onUnmounted=Lr,exports.openType=fc,exports.os=rs,exports.p=e=>function(e){const{uid:t,__counter:n}=Bi();return t+","+((ya[t]||(ya[t]=[])).push(Si(e))-1)+","+n}(e),exports.padZero=Ss,exports.props=cc,exports.props$1=dc,exports.props$10=wc,exports.props$11=Ac,exports.props$12=Bc,exports.props$13=Cc,exports.props$14=kc,exports.props$15=Ec,exports.props$16=Ic,exports.props$17=Lc,exports.props$18=Hc,exports.props$19=Uc,exports.props$2=hc,exports.props$20=Vc,exports.props$21=Rc,exports.props$22=$c,exports.props$23=Wc,exports.props$24=Gc,exports.props$25=Jc,exports.props$26=Zc,exports.props$27=_c,exports.props$28=eu,exports.props$29=xu,exports.props$3=gc,exports.props$30=Xu,exports.props$31=Ku,exports.props$32=Yu,exports.props$33=Zu,exports.props$34=_u,exports.props$35=ep,exports.props$36=tp,exports.props$37=np,exports.props$38=op,exports.props$39=rp,exports.props$4=mc,exports.props$40=ip,exports.props$41=ap,exports.props$42=cp,exports.props$43=up,exports.props$44=pp,exports.props$5=yc,exports.props$6=bc,exports.props$7=Sc,exports.props$8=xc,exports.props$9=vc,exports.r=(e,t,n)=>Aa(e,t,n),exports.random=as,exports.range=ts,exports.reactive=Po,exports.ref=Uo,exports.resolveComponent=function(e,t){return function(e,t,n=!0,o=!1){const r=xr||Ai;if(r){const n=r.type;if(e===$r){const e=function(e,t=!0){return w(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===z(t)||e===M(z(t))))return n}const i=Wr(r[e]||n[e],t)||Wr(r.appContext[e],t);return!i&&o?n:i}}($r,e,!0,t)||e},exports.s=e=>Ba(e),exports.setProperty=ws,exports.sleep=os,exports.sr=(e,t,n)=>function(e,t,n={}){const{$templateRefs:o}=Bi();o.push({i:t,r:e,k:n.k,f:n.f})}(e,t,n),exports.sys=is,exports.t=e=>(e=>A(e)?e:null==e?"":S(e)||C(e)&&(e.toString===E||!w(e.toString))?JSON.stringify(e,l,2):String(e))(e),exports.test=Wl,exports.throttle=Gs,exports.toast=ys,exports.transitionMixin=sp,exports.unref=Ro,exports.uviewPlus=sc,exports.value=uc,exports.watch=Br,exports.wx$1=yn,exports.zIndex=rc;
//# sourceMappingURL=vendor.js.map
