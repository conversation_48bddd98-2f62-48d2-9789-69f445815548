"use strict";const e=require("../../../common/vendor.js");require("../../../utils/request.js"),require("../../../store/index.js");const o=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),s=require("../../../utils/index.js");if(require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../api/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-avatar")+e.resolveComponent("u-text")+e.resolveComponent("mescroll-uni"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js"))();const r={__name:"profitList",setup(r){e.ref({id:null,page:1,page_size:10});const t=e.ref([]),{mescrollInit:u,downCallback:i,getMescroll:n}=o.useMescroll(e.onPageScroll,e.onReachBottom),l=e.ref("");e.onReady((async()=>{l.value=await s.setListHeight()+"px"}));const a=async e=>{};return(o,r)=>({a:e.f(t.value,((o,r,t)=>({a:"51eed044-1-"+t+",51eed044-0",b:e.p({size:"68rpx",src:o.goodImg,mode:"aspectFill"}),c:"51eed044-2-"+t+",51eed044-0",d:e.p({size:"28rpx",text:o.goodName}),e:"51eed044-3-"+t+",51eed044-0",f:e.o((r=>e.unref(s.navto)(`/pages/bundle/msg/personage?to_uid=${o.uid||o.to_uid}`)),r),g:"51eed044-4-"+t+",51eed044-0",h:e.p({align:"right",color:"#FF2A00",mode:"price",size:"32rpx",text:o.goodPrice}),i:r}))),b:e.p({size:"22rpx",color:"#aaa",text:"“世界那么大丨总要出去看看吧！"}),c:e.o(e.unref(u)),d:e.o(e.unref(i)),e:e.o(a),f:e.o((e=>e.scrollTo(0))),g:e.p({height:l.value,up:{page:{num:0,size:20,time:null}}})})},__runtimeHooks:1};wx.createPage(r);
//# sourceMappingURL=profitList.js.map
