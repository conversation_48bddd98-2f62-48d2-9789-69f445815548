"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),n=require("../../../store/index.js"),t=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),l=require("../../../utils/index.js"),i=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-tabs")+e.resolveComponent("u-icon")+e.resolveComponent("u-text")+e.resolveComponent("u-button")+e.resolveComponent("mescroll-uni"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js"))();const a={__name:"myActivity",setup(a){const u=e.ref([{name:"我的报名"},{name:"我的发布"},{name:"我的提问"},{name:"我的收藏"}]),r=e.ref(2),s=e.ref([]),{mescrollInit:d,downCallback:c,getMescroll:g}=t.useMescroll(e.onPageScroll,e.onReachBottom),v=e.ref(""),f=e.ref(0),p=e.ref(!1),h=e.ref(null),_=e.ref(-1);e.onLoad((e=>{if(console.log("页面加载参数:",e),!i.isLoggedIn())return console.log("用户未登录，跳转到登录页面"),void i.requireLogin("/pages/bundle/user/myActivity","请先登录后查看我的活动");console.log("用户已登录，用户信息:",i.getCurrentUser()),(e=>{if(e){const o={1:1,2:0,3:2,4:3};void 0!==o[e]?(f.value=o[e],r.value=parseInt(e)):(f.value=0,r.value=2)}else f.value=0,r.value=2;console.log(`初始化页面 - 选项卡索引: ${f.value}, 数据类型: ${r.value}`)})(e.type)})),e.onReady((async()=>{v.value=await l.setListHeight()+"px"})),e.onShow((()=>{console.log("当前选项卡索引:",f.value),console.log("当前数据类型:",r.value),g().resetUpScroll(!0)}));const m=async n=>{try{console.log("请求我的活动列表，参数:",{page:n.num,page_size:n.size,type:r.value,userInfo:i.getCurrentUser()});const t=await o.huodongget_my_list({page:n.num,page_size:n.size,type:r.value});if(console.log("我的活动列表API响应:",t),"n"===t||null==t){1==n.num&&(s.value=[]),n.endBySize(0,0);const e={1:"暂无发布活动",2:"暂无报名活动",3:"暂无收藏内容",4:"暂无提问记录"};if(1==n.num){const o=e[r.value]||"暂无数据";console.log(o)}return}if("ok"===t.status){const e=t.data||[];1==n.num&&(s.value=[]),s.value=s.value.concat(e),n.endBySize(e.length,t.count),console.log(`加载成功，当前页数据: ${e.length} 条，总数: ${t.count}`)}else"empty"===t.status?(console.log("暂无活动数据"),1==n.num&&(s.value=[]),n.endBySize(0,0)):"relogin"===t.status?(console.warn("需要重新登录:",t.msg),n.endErr(),i.requireLogin("/pages/bundle/user/myActivity",t.msg||"登录已过期，请重新登录")):(console.error("获取我的活动列表失败:",t),n.endErr(),1==n.num&&e.index.showToast({title:t.msg||"获取数据失败",icon:"none"}))}catch(t){console.error("请求我的活动列表异常:",t),n.endErr(),1==n.num&&e.index.showToast({title:"网络错误，请检查网络连接",icon:"none"})}},x=e=>e?e.replace(/-/g,"/"):null,b=e=>{if(!e)return"";const o=new Date(x(e)),n=(o.getMonth()+1).toString().padStart(2,"0"),t=o.getDate().toString().padStart(2,"0"),l=new Date,i=Math.abs(o-l),a=Math.ceil(i/864e5),u=["周日","周一","周二","周三","周四","周五","周六"][o.getDay()];return a<=10?`${n}月${t}日 ${u}`:`${n}月${t}日`},w=e=>{r.value={0:2,1:1,2:4,3:3}[e.index],f.value=e.index,s.value=[],console.log(`切换选项卡 - 新索引: ${e.index}, 新数据类型: ${r.value}`),g().resetUpScroll(!0)},y=()=>{p.value=!1,h.value=null,_.value=-1},$=e=>{var o;if(!(null==(o=null==e?void 0:e.huodong_info)?void 0:o.start_time))return!1;return new Date<new Date(x(e.huodong_info.start_time))},S=e=>{var o;const n=(null==e?void 0:e.start_time)||(null==(o=null==e?void 0:e.huodong_info)?void 0:o.start_time);if(!n)return!1;return new Date>=new Date(x(n))},z=n=>{if(!h.value)return;const t=$(h.value);switch(n){case"cancel":if(!t)return void e.index.$u.toast("活动已开始，无法取消");e.index.showModal({title:"确认取消",content:"确定要取消这个活动吗？取消后无法恢复。",success:n=>{n.confirm&&(async()=>{var n;if(h.value)try{const t=(null==(n=h.value.huodong_info)?void 0:n.id)||h.value.huodong_id,l=await o.huodongcancel_huodong({huodong_id:t});"ok"===l.status?(e.index.$u.toast("活动已取消"),g().resetUpScroll(!0),y()):e.index.$u.toast(l.msg||"取消失败")}catch(t){console.error("取消活动失败:",t),e.index.$u.toast("取消失败，请重试")}})()}});break;case"edit":if(!t)return void e.index.$u.toast("活动已开始，无法修改");(()=>{var e;if(!h.value)return;const o=(null==(e=h.value.huodong_info)?void 0:e.id)||h.value.huodong_id;y(),l.navto(`/pages/bundle/index/addActive?huodong_id=${o}`)})();break;case"duplicate":(()=>{var e;if(!h.value)return;const o=(null==(e=h.value.huodong_info)?void 0:e.id)||h.value.huodong_id;console.log("再办一场活动，ID:",o),y(),l.navto(`/pages/bundle/index/addActive?duplicate=1&duplicate_id=${o}`)})();break;case"close":y()}},j=e=>{var o;const n=(null==e?void 0:e.start_time)||(null==(o=null==e?void 0:e.huodong_info)?void 0:o.start_time);if(!n)return!1;return new Date>=new Date(x(n))},q=e=>{var o;return 1===f.value&&3!==(null==(o=e.huodong_info)?void 0:o.status)&&j(e)},C=e=>{l.navto(`/pages/bundle/activity/album?activity_id=${e}`)},k=(e,o)=>(console.log(`=== 活动信息调试 [Tab: ${f.value}, Index: ${o}] ===`),console.log("完整活动数据:",e),console.log("活动字段检查:"),console.log("- val.huodong_id:",e.huodong_id),console.log("- val.start_time:",e.start_time),console.log("- val.time:",e.time),console.log("- val.status:",e.status),e.huodong_info?(console.log("huodong_info字段检查:"),console.log("- val.huodong_info.huodong_id:",e.huodong_info.huodong_id),console.log("- val.huodong_info.id:",e.huodong_info.id),console.log("- val.huodong_info.start_time:",e.huodong_info.start_time),console.log("- val.huodong_info.status:",e.huodong_info.status),console.log("- val.huodong_info.name:",e.huodong_info.name),console.log("- val.huodong_info.baoming_start_time:",e.huodong_info.baoming_start_time),console.log("- val.huodong_info.baoming_end_time:",e.huodong_info.baoming_end_time)):console.log("huodong_info: null 或 undefined"),console.log("=== 调试结束 ==="),"");return(t,i)=>{var a,I;return e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"176rpx",title:"我的活动",color:"#ffffff",blod:!0}),b:e.o(w),c:e.p({current:f.value,list:u.value,"active-style":{borderRadius:"25rpx",textAlign:"center",lineHeight:"56rpx",fontSize:"28rpx",color:"#ffffff",fontWeight:"600",backgroundColor:"#6AC086",boxShadow:"0 8rpx 24rpx rgba(106, 192, 134, 0.3), 0 4rpx 12rpx rgba(106, 192, 134, 0.2)",transition:"all 0.3s ease",transform:"translateY(-2rpx)"},inactiveStyle:{fontSize:"26rpx",color:"#6c757d",fontWeight:"400",borderRadius:"20rpx",backgroundColor:"transparent"},itemStyle:{padding:"20rpx 32rpx",margin:"0 8rpx",minWidth:"120rpx"},lineWidth:"0"}),d:e.f(s.value,((t,i,a)=>{var u,d,c,v,m,w,y,$,z,I,D,A,M,B;return e.e({a:e.t(k(t,i))},1===f.value?{b:"663b5601-3-"+a+",663b5601-2",c:e.p({name:"more-dot-fill",color:"#6AC086",size:"24"}),d:e.o((e=>{return o=t,n=i,h.value=o,_.value=n,void(p.value=!0);var o,n}),i)}:{},{e:(null==(u=t.huodong_info)?void 0:u.img_url_fallback)?`${e.unref(n.store)().$state.url}default_activity.png`:null==(d=t.huodong_info)?void 0:d.img_url,f:e.o((e=>(e=>{s.value[e]&&s.value[e].huodong_info&&(s.value[e].huodong_info.img_url_fallback=!0,console.log(`活动图片加载失败，已切换到默认图片: 索引${e}`))})(i)),i)},0===f.value||1===f.value?{g:e.t(e.unref(l.getItem)(["未开始","报名中","已结束"],1*Date.now()<1*new Date(x((null==(c=t.huodong_info)?void 0:c.baoming_start_time)||t.baoming_start_time))?0:1*Date.now()>=1*new Date(x((null==(v=t.huodong_info)?void 0:v.start_time)||t.start_time))?2:1))}:{},0===f.value?{h:e.t(e.unref(l.getItem)(["未支付","已报名","已取消","退款中","退款成功","退款失败","支付失败已删除"],t.status))}:{},1===f.value?{i:e.t(e.unref(l.getItem)(["审核中","审核通过","审核未通过","活动已取消"],null==(m=t.huodong_info)?void 0:m.status))}:{},{j:"663b5601-4-"+a+",663b5601-2",k:e.p({size:"30rpx",bold:!0,lines:"1",text:null==(w=t.huodong_info)?void 0:w.name}),l:"663b5601-5-"+a+",663b5601-2",m:e.p({name:`${e.unref(n.store)().$state.url}time.png`,size:"24rpx",label:b(null==(y=t.huodong_info)?void 0:y.start_time),space:"8rpx",color:"#666666","label-size":"24rpx","label-color":"#666666"}),n:"663b5601-6-"+a+",663b5601-2",o:e.p({"prefix-icon":`${e.unref(n.store)().$state.url}place.png`,"icon-style":"margin-right:8rpx;width:24rpx;height:24rpx;",text:(null==($=t.huodong_info)?void 0:$.sheng)+(null==(z=t.huodong_info)?void 0:z.shi)+(null==(I=t.huodong_info)?void 0:I.qu)+(null==(D=t.huodong_info)?void 0:D.addr),lines:"1",color:"#666666",size:"24rpx"}),p:"663b5601-7-"+a+",663b5601-2",q:e.p({name:`${e.unref(n.store)().$state.url}man.png`,size:"24rpx",label:`${null==(A=t.huodong_info)?void 0:A.baoming_num}人报名`,"label-color":"#666666",space:"8rpx","label-size":"24rpx"}),r:1==f.value&&3!=(null==(M=t.huodong_info)?void 0:M.status)},1==f.value&&3!=(null==(B=t.huodong_info)?void 0:B.status)?e.e({s:j(t)},j(t)?{t:e.o((e=>{var o;return C((null==(o=t.huodong_info)?void 0:o.id)||t.huodong_id)}),i),v:"663b5601-8-"+a+",663b5601-2",w:e.p({text:"上传活动相册",shape:"circle",color:"linear-gradient(135deg, #fff8dc 0%, #f0f8e8 50%, #e8f5e8 100%)",customStyle:{margin:"15rpx auto 0",width:"80%",height:"70rpx",fontSize:"28rpx",color:"#333",fontWeight:"600",boxShadow:"0 4rpx 12rpx rgba(106, 192, 134, 0.2)",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"}})}:{}):{},0===f.value?e.e({x:1===t.status&&!S(t.huodong_info)},1!==t.status||S(t.huodong_info)?{}:{y:e.o((n=>(async n=>{const t=await o.huodongcancel_baoming({order_id:n});"ok"===t.status?g().resetUpScroll(!0):e.index.$u.toast(t.msg)})(t.order_id)),i),z:"663b5601-9-"+a+",663b5601-2",A:e.p({text:"取消报名",shape:"circle",color:"linear-gradient(135deg, #dc3545 0%, #c82333 100%)",customStyle:{margin:"10rpx 10rpx 0",width:"150rpx",height:"50rpx",fontSize:"24rpx",color:"#fff",fontWeight:"500",boxShadow:"0 2rpx 8rpx rgba(220, 53, 69, 0.25)"}})},{B:j(t)},j(t)?{C:e.o((e=>{var o;return C((null==(o=t.huodong_info)?void 0:o.id)||t.huodong_id)}),i),D:"663b5601-10-"+a+",663b5601-2",E:e.p({text:"上传活动相册",shape:"circle",color:"linear-gradient(135deg, #fff8dc 0%, #f0f8e8 50%, #e8f5e8 100%)",customStyle:{margin:"15rpx auto 0",width:"80%",height:"70rpx",fontSize:"28rpx",color:"#333",fontWeight:"600",boxShadow:"0 4rpx 12rpx rgba(106, 192, 134, 0.2)",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"}})}:{}):{},{F:q(t)?"":1,G:i,H:e.o((e=>(e=>{var o,n,t,i;switch(console.log("点击项目:",e,"类型:",r.value),f.value){case 0:const a=(null==(o=e.huodong_info)?void 0:o.id)||e.huodong_id;a&&l.navto(`/pages/bundle/index/activeInfo?id=${a}`);break;case 1:const u=(null==(n=e.huodong_info)?void 0:n.id)||e.huodong_id;u&&l.navto(`/pages/bundle/index/activeInfo?id=${u}`);break;case 2:const r=(null==(t=e.huodong_info)?void 0:t.id)||e.huodong_id;r&&l.navto(`/pages/bundle/index/activeInfo?id=${r}`);break;case 3:const s=(null==(i=e.huodong_info)?void 0:i.id)||e.huodong_id;s?l.navto(`/pages/bundle/index/activeInfo?id=${s}`):e.feed_id?l.navto(`/pages/bundle/world/feed/detail?id=${e.feed_id}`):e.card_id?l.navto(`/pages/bundle/world/card/detail?cardId=${e.card_id}`):e.quote_id&&l.navto(`/pages/bundle/world/quote/detail?id=${e.quote_id}`);break;default:console.log("未知选项卡类型")}})(t)),i)})})),e:1===f.value,f:0===f.value||1===f.value,g:0===f.value,h:1===f.value,i:0===f.value,j:0===f.value?1:"",k:e.o(e.unref(d)),l:e.o(e.unref(c)),m:e.o(m),n:e.o((e=>e.scrollTo(0))),o:e.p({height:v.value,down:{auto:!1}}),p:p.value},p.value?{q:e.t((null==(I=null==(a=h.value)?void 0:a.huodong_info)?void 0:I.name)||"活动操作"),r:e.p({name:"close-circle",color:"#dc3545",size:"20"}),s:$(h.value)?"#333":"#999",t:$(h.value)?"":1,v:e.o((e=>z("cancel"))),w:e.p({name:"edit-pen",color:"#6AC086",size:"20"}),x:$(h.value)?"#333":"#999",y:$(h.value)?"":1,z:e.o((e=>z("edit"))),A:e.p({name:"plus-circle",color:"#17a2b8",size:"20"}),B:e.o((e=>z("duplicate"))),C:e.o((e=>z("close"))),D:e.o((()=>{})),E:e.o(y)}:{})}}},u=e._export_sfc(a,[["__scopeId","data-v-663b5601"]]);a.__runtimeHooks=1,wx.createPage(u);
//# sourceMappingURL=myActivity.js.map
