"use strict";const e=require("../../../common/vendor.js"),o=require("../../../utils/auth.js"),t=require("../../../api/index.js"),n=require("../../../store/index.js"),i=require("../../../utils/permissions.js");if(require("../../../store/counter.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-button")+e.resolveComponent("u-empty")+e.resolveComponent("u-textarea")+e.resolveComponent("u-popup")+e.resolveComponent("u-loading-page"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../node-modules/uview-plus/components/u-popup/u-popup.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-page/u-loading-page.js"))();const a={__name:"pending_activities",setup(a){const s=e.ref([]),r=e.ref(!0),l=e.ref(!1),u=e.ref(!0),d=e.ref(1),c=e.ref(!1),v=e.ref(""),p=e.ref(!1),g=e.ref(null);e.onLoad((()=>{o.requireLogin()&&(i.hasActivityReviewPermission()?m():e.index.showModal({title:"权限不足",content:"您没有活动审核权限，无法访问此页面",showCancel:!1,success:()=>{e.index.navigateBack()}}))})),e.onShow((()=>{f()}));const m=async(o=!1)=>{try{if(o)d.value=1,u.value=!0,r.value=!0;else{if(!u.value)return;r.value=!0}const i=n.store().$state.userInfo;if(!i||!i.uid||!i.token)return void e.index.showToast({title:"请先登录",icon:"none"});console.log("加载待审核活动，页码:",d.value);const a=await t.branch_presidentpending_activities({uid:i.uid,token:i.token,page:d.value,page_size:20});if(console.log("待审核活动API响应:",a),"ok"===a.status){const e=a.data||[];e.forEach((e=>{e.reviewing=!1})),o?s.value=e:s.value.push(...e),u.value=20===e.length}else"empty"===a.status?(o&&(s.value=[]),u.value=!1):"relogin"===a.status?e.index.showToast({title:"登录已过期，请重新登录",icon:"none"}):e.index.showToast({title:a.msg||"加载失败",icon:"none"})}catch(i){console.error("加载活动列表失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}finally{r.value=!1,l.value=!1}},f=()=>{m(!0)},h=()=>{u.value&&!l.value&&(l.value=!0,d.value++,m())},x=async(o,i)=>{if(o&&o.id)try{o.reviewing=!0;const a=n.store().$state.userInfo;if(!a||!a.uid||!a.token)return void e.index.showToast({title:"用户信息错误，请重新登录",icon:"none"});const r=await t.branch_presidentreview_activity({uid:a.uid,token:a.token,huodong_id:o.id,status:i,comment:2===i?v.value:""});if(console.log("审核活动API响应:",r),"ok"===r.status){e.index.showToast({title:1===i?"审核通过":"已拒绝",icon:"success"});const t=s.value.findIndex((e=>e.id===o.id));-1!==t&&s.value.splice(t,1)}else"relogin"===r.status?e.index.showToast({title:"登录已过期，请重新登录",icon:"none"}):e.index.showToast({title:r.msg||"审核失败",icon:"none"})}catch(a){console.error("审核活动失败:",a),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}finally{o.reviewing=!1}else e.index.showToast({title:"活动信息错误",icon:"none"})},w=()=>{c.value=!1,g.value=null,v.value=""},y=()=>{g.value&&(p.value=!0,x(g.value,2).finally((()=>{p.value=!1,w()})))},b=e=>{if(!e)return"";const o=e.replace(/-/g,"/"),t=new Date(o);if(isNaN(t.getTime()))return e;const n=new Date-t;return n<6e4?"刚刚":n<36e5?Math.floor(n/6e4)+"分钟前":n<864e5?Math.floor(n/36e5)+"小时前":t.toLocaleDateString()};return(o,t)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"待审核活动",color:"#ffffff",blod:!0}),b:s.value.length>0},s.value.length>0?{c:e.f(s.value,((o,t,n)=>({a:o.img_url||"/static/default-activity.png",b:e.t(o.name),c:e.t(o.title),d:e.t(o.organizer_name),e:e.t(b(o.create_time)),f:e.o((e=>x(o,1)),o.id),g:"d8cc56a9-1-"+n,h:e.p({type:"success",size:"small",loading:o.reviewing,customStyle:"background: #6AC086; border: none; border-radius: 30rpx; margin-right: 20rpx;"}),i:e.o((e=>(e=>{g.value=e,v.value="",c.value=!0})(o)),o.id),j:"d8cc56a9-2-"+n,k:e.p({type:"error",size:"small",loading:o.reviewing,customStyle:"background: #ff4757; border: none; border-radius: 30rpx;"}),l:e.o((()=>{}),o.id),m:o.id,n:e.o((t=>(o=>{e.index.navigateTo({url:`/pages/bundle/index/activeInfo?id=${o.id}`})})(o)),o.id)})))}:r.value?{}:{e:e.p({mode:"list",text:"暂无待审核活动",textColor:"#999999",textSize:"28"})},{d:!r.value,f:u.value&&!r.value},u.value&&!r.value?{g:e.t(l.value?"加载中...":"加载更多"),h:e.o(h),i:e.p({type:"primary",loading:l.value,customStyle:"background: #6AC086; border: none; border-radius: 50rpx;"})}:{},{j:e.o((e=>v.value=e)),k:e.p({placeholder:"请输入拒绝理由（可选）",maxlength:"200",count:!0,height:"120rpx",modelValue:v.value}),l:e.o(w),m:e.p({type:"info",customStyle:"margin-right: 20rpx; border-radius: 30rpx;"}),n:e.o(y),o:e.p({type:"error",loading:p.value,customStyle:"background: #ff4757; border: none; border-radius: 30rpx;"}),p:e.o(w),q:e.p({show:c.value,mode:"center",round:20,closeable:!0}),r:e.p({loading:r.value,"loading-text":"加载中...","bg-color":"#f8f9fa"})})}},s=e._export_sfc(a,[["__scopeId","data-v-d8cc56a9"]]);wx.createPage(s);
//# sourceMappingURL=pending_activities.js.map
