{"version": 3, "file": "post.js", "sources": ["../../../../../../../src/pages/bundle/world/quote/post.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXHF1b3RlXHBvc3QudnVl"], "sourcesContent": ["<script setup>\nimport { ref } from 'vue';\nimport { publishQuote, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\nimport AuthorSelector from '@/components/AuthorSelector.vue';\nimport SourceSelector from '@/components/SourceSelector.vue';\n\n// --- State Refs ---\nconst content = ref('');\n// {{ AURA-X: Modify - 更新作者和出处为对象类型支持新的选择器. Confirmed via 寸止 }}\nconst selectedAuthor = ref(null); // 选中的作者对象\nconst selectedSource = ref(null); // 选中的出处对象\nconst tags = ref('');\nconst privacy = ref('public');\nconst allowOfficial = ref(false); // 是否允许官方使用\nconst isSubmitting = ref(false);\nconst images = ref([]); // 图片列表，摘录最多上传1张\n\n// --- Event Handlers ---\nconst handleClose = () => {\n    uni.navigateBack();\n};\n\n// 处理图片上传\nconst handleAfterRead = async (event) => {\n    let lists = [].concat(event.file);\n\n    // 摘录只能上传一张图片，如果已有图片则替换\n    if (images.value.length > 0) {\n        uni.showToast({ title: '摘录只能上传一张图片，将替换现有图片', icon: 'none' });\n        images.value = []; // 清空现有图片\n    }\n\n    // 只取第一张图片\n    if (lists.length > 1) {\n        uni.showToast({ title: '摘录只能上传一张图片，已自动选择第一张', icon: 'none' });\n        lists = [lists[0]];\n    }\n\n    let fileListLen = images.value.length;\n\n    lists.map((item) => {\n        images.value.push({\n            ...item,\n            status: 'uploading',\n            message: '上传中'\n        });\n    });\n\n    for (let i = 0; i < lists.length; i++) {\n        const currentFileIndex = fileListLen + i;\n        try {\n            const res = await upload_img(lists[i].url);\n\n            if (res.status === 'ok' && res.data) {\n                let item = images.value[currentFileIndex];\n                if (item) {\n                    images.value.splice(currentFileIndex, 1, {\n                        ...item,\n                        status: 'success',\n                        message: '',\n                        url: res.data\n                    });\n                }\n            } else {\n                if (images.value[currentFileIndex]) {\n                    images.value[currentFileIndex].status = 'failed';\n                    images.value[currentFileIndex].message = res.msg || '上传失败';\n                }\n                uni.showToast({ title: res.msg || '图片上传失败', icon: 'none' });\n            }\n        } catch (error) {\n            if (images.value[currentFileIndex]) {\n                images.value[currentFileIndex].status = 'failed';\n                images.value[currentFileIndex].message = '上传失败';\n            }\n            uni.showToast({ title: '图片上传失败，请重试', icon: 'none' });\n        }\n    }\n};\n\n// 删除图片\nconst handleDeletePic = (event) => {\n    images.value.splice(event.index, 1);\n};\n\nconst handleSubmit = async () => {\n    // 验证必填字段\n    if (!content.value.trim()) {\n        uni.showToast({ title: '摘录内容不能为空', icon: 'none' });\n        return;\n    }\n\n    // 验证用户登录状态\n    if (!store().$state.userInfo?.uid || !store().$state.userInfo?.token) {\n        uni.showToast({ title: '请先登录', icon: 'none' });\n        return;\n    }\n\n    // 获取所有上传成功的图片URL\n    const uploadedImageUrls = images.value\n        .filter(img => img.status === 'success' && img.url)\n        .map(img => img.url);\n\n    // 验证图片上传 - 摘录必须上传一张图片\n    if (uploadedImageUrls.length === 0) {\n        uni.showToast({ title: '摘录必须上传一张图片', icon: 'none' });\n        return;\n    }\n\n    if (isSubmitting.value) return;\n\n    isSubmitting.value = true;\n\n    // {{ AURA-X: Modify - 更新参数格式支持新的外键字段. Confirmed via 寸止 }}\n    // 准备参数 - 与后端API保持一致\n    const params = {\n        uid: store().$state.userInfo.uid,\n        token: store().$state.userInfo.token,\n        content: content.value.trim(),\n        author_id: selectedAuthor.value?.id || null, // 使用作者ID\n        source_id: selectedSource.value?.id || null, // 使用出处ID\n        tags: tags.value.trim(),\n        privacy: privacy.value,\n        allow_official: allowOfficial.value ? 1 : 0, // 添加给官方投稿字段\n        images: uploadedImageUrls // 添加图片参数\n    };\n\n    try {\n        const res = await publishQuote(params);\n        if (res.status === 'ok') {\n            uni.showToast({ title: '摘录成功', icon: 'success' });\n\n            // {{ AURA-X: Add - 发布成功后触发摘录列表刷新. Confirmed via 寸止 }}\n            // 触发摘录列表刷新\n            uni.$emit('refreshQuoteList');\n\n            setTimeout(() => {\n                uni.navigateBack();\n            }, 1000);\n        } else if (res.status === 'relogin') {\n            uni.showToast({ title: '请先登录', icon: 'none' });\n        } else {\n            uni.showToast({ title: res.msg || '摘录失败', icon: 'none' });\n        }\n    } catch (error) {\n        uni.showToast({ title: '摘录失败，请重试', icon: 'none' });\n    } finally {\n        isSubmitting.value = false;\n    }\n};\n\n</script>\n\n<template>\n    <view class=\"quote-post-page\">\n        <!-- 统一导航栏 -->\n        <customNavbar\n            title=\"摘录\"\n            backIcon=\"close\"\n            @back=\"handleClose\"\n        />\n\n        <!-- Main Content Area -->\n        <scroll-view scroll-y class=\"main-content\">\n\n            <!-- Content Textarea -->\n            <view class=\"textarea-wrapper\">\n                 <u--textarea\n                    v-model=\"content\"\n                    placeholder=\"记录书摘格言，名家语录\"\n                    height=\"300\"\n                    maxlength=\"-1\"\n                    border=\"none\"\n                    :customStyle=\"{ padding: '32rpx', lineHeight: '1.7', fontSize: '32rpx', color: '#333333', fontFamily: '-apple-system, BlinkMacSystemFont, PingFang SC, Hiragino Sans GB, sans-serif', backgroundColor: 'transparent' }\"\n                ></u--textarea>\n            </view>\n\n            <!-- Image Upload -->\n            <view class=\"upload-wrapper\">\n                <u-upload\n                    :fileList=\"images\"\n                    @afterRead=\"handleAfterRead\"\n                    @delete=\"handleDeletePic\"\n                    name=\"file\"\n                    multiple\n                    :maxCount=\"1\"\n                    :previewImage=\"true\"\n                    width=\"200rpx\"\n                    height=\"200rpx\"\n                    uploadIconColor=\"#ccc\"\n                ></u-upload>\n                <view class=\"upload-tip\">\n                    <text class=\"tip-text\">摘录必须上传1张图片</text>\n                </view>\n            </view>\n\n            <!-- Meta Info Section -->\n            <view class=\"meta-section\">\n                <!-- {{ AURA-X: Modify - 替换为新的选择器组件. Confirmed via 寸止 }} -->\n                <!-- 作者选择器 -->\n                <AuthorSelector\n                    v-model=\"selectedAuthor\"\n                    placeholder=\"选择作者 (选填)\"\n                />\n\n                <!-- 出处选择器 -->\n                <SourceSelector\n                    v-model=\"selectedSource\"\n                    placeholder=\"选择出处 (选填)\"\n                />\n                 <view class=\"meta-item\">\n                     <u-icon name=\"tags-fill\" size=\"20\" color=\"#999\"></u-icon>\n                      <input class=\"meta-input\" v-model=\"tags\" placeholder=\"标签 (选填, 逗号分隔)\" />\n                 </view>\n                 <view class=\"meta-item\">\n                     <u-icon name=\"lock-fill\" size=\"20\" color=\"#999\"></u-icon>\n                     <text class=\"meta-label\">谁可以看</text>\n                      <view class=\"privacy-switch\">\n                         <u-radio-group v-model=\"privacy\" placement=\"row\">\n                            <u-radio label=\"公开\" name=\"public\" :customStyle=\"{marginRight: '16rpx'}\"></u-radio>\n                            <u-radio label=\"私密\" name=\"private\"></u-radio>\n                        </u-radio-group>\n                     </view>\n                 </view>\n                 <view class=\"meta-item\">\n                     <u-icon name=\"checkmark-circle-fill\" size=\"20\" color=\"#999\"></u-icon>\n                     <text class=\"meta-label\">给官方投稿</text>\n                     <view class=\"official-switch\">\n                         <u-switch v-model=\"allowOfficial\" activeColor=\"#6AC086\" size=\"24\"></u-switch>\n                     </view>\n                 </view>\n            </view>\n        </scroll-view>\n\n        <!-- 发布按钮 - 固定在页面底部右侧 -->\n        <view class=\"publish-button-container\">\n            <view\n                class=\"publish-btn\"\n                :class=\"{ 'disabled': isSubmitting || !content.trim() }\"\n                @click=\"handleSubmit\"\n            >\n                <u-icon name=\"checkmark\" size=\"44rpx\" color=\"#ffffff\" v-if=\"!isSubmitting\"></u-icon>\n                <u-loading-icon v-if=\"isSubmitting\" color=\"#ffffff\" size=\"40rpx\"></u-loading-icon>\n                <text class=\"publish-text\" v-if=\"!isSubmitting\">摘录</text>\n            </view>\n        </view>\n\n    </view>\n</template>\n\n<style lang=\"scss\" scoped>\n/* 统一设计变量 */\n:root {\n  --spacing-md: 24rpx;\n  --spacing-lg: 32rpx;\n  --radius-card: 20rpx;\n  --radius-button: 50rpx;\n  --color-bg-page: #f8f9fa;\n  --color-bg-card: #ffffff;\n  --color-text-title: #333333;\n  --color-text-body: #666666;\n  --color-text-caption: #999999;\n  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);\n}\n\n.quote-post-page {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: var(--color-bg-page);\n  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;\n}\n\n/* 发布按钮容器 */\n.publish-button-container {\n  position: fixed;\n  bottom: 40rpx;\n  right: 40rpx;\n  z-index: 1000;\n}\n\n/* 发布按钮样式 */\n.publish-btn {\n  width: 120rpx;\n  height: 120rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: var(--radius-button);\n  box-shadow: var(--shadow-card);\n  transition: all 0.3s ease;\n\n  .publish-text {\n    font-size: 24rpx;\n    color: #ffffff;\n    margin-top: 8rpx;\n    font-weight: 500;\n  }\n\n  &.disabled {\n    opacity: 0.6;\n    pointer-events: none;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n/* {{ AURA-X: Modify - 去除卡片样式，扩大输入区域. Confirmed via 寸止 }} */\n.main-content {\n    flex: 1;\n    overflow-y: auto;\n    padding: 0;\n    padding-bottom: 200rpx; /* 为底部发布按钮留出空间 */\n    background-color: #ffffff;\n    width: 100%;\n    box-sizing: border-box;\n}\n\n.textarea-wrapper {\n    margin-bottom: 0;\n    background-color: transparent;\n    border-radius: 0;\n    padding: 0;\n    box-shadow: none;\n    min-height: 50vh;\n\n    :deep(.u-textarea) {\n        font-size: 32rpx;\n        color: var(--color-text-body);\n        line-height: 1.7;\n\n        .u-textarea__field::placeholder {\n            color: var(--color-text-caption);\n            font-size: 32rpx;\n        }\n    }\n}\n\n/* {{ AURA-X: Modify - 去除图片上传卡片样式. Confirmed via 寸止 }} */\n.upload-wrapper {\n    margin-bottom: var(--spacing-lg);\n    background-color: transparent;\n    border-radius: 0;\n    padding: var(--spacing-lg);\n    box-shadow: none;\n\n    :deep(.u-upload__wrap) {\n        gap: var(--spacing-md);\n    }\n\n    :deep(.u-upload__button) {\n        background-color: var(--color-bg-page);\n        border: 2rpx dashed #e0e0e0;\n        border-radius: var(--radius-card);\n        transition: all 0.3s ease;\n    }\n\n    :deep(.u-upload__item) {\n        border-radius: var(--radius-card);\n        overflow: hidden;\n        box-shadow: var(--shadow-card);\n    }\n\n    /* {{ AURA-X: Modify - 缩小提示条高度和字体. Confirmed via 寸止 }} */\n    .upload-tip {\n        margin-top: var(--spacing-md);\n        padding: 16rpx var(--spacing-md);\n        background-color: #fff3cd;\n        border: 1rpx solid #ffeaa7;\n        border-radius: var(--radius-card);\n\n        .tip-text {\n            font-size: 22rpx;\n            color: #856404;\n            line-height: 1.3;\n        }\n    }\n}\n\n/* {{ AURA-X: Modify - 去除元数据区域卡片样式. Confirmed via 寸止 }} */\n.meta-section {\n    background-color: transparent;\n    border-radius: 0;\n    box-shadow: none;\n    overflow: hidden;\n    padding: var(--spacing-lg);\n\n    /* {{ AURA-X: Modify - 修改元数据项样式与其他页面保持一致. Confirmed via 寸止 }} */\n    .meta-item {\n        display: flex;\n        align-items: center;\n        padding: var(--spacing-lg);\n        border-bottom: 1rpx solid #f0f0f0;\n        transition: background-color 0.3s ease;\n        background-color: #ffffff;\n        border-radius: 12rpx;\n        margin-bottom: 16rpx;\n\n        &:last-child {\n            border-bottom: none;\n        }\n\n        .meta-input {\n             flex: 1;\n             margin-left: var(--spacing-md);\n             font-size: 28rpx;\n             color: var(--color-text-body);\n             text-align: right;\n             background: transparent;\n             border: none;\n             outline: none;\n             width: 100%;\n             max-width: 300rpx;\n\n             &::placeholder {\n                 color: var(--color-text-caption);\n             }\n        }\n        .meta-label {\n            margin-left: var(--spacing-md);\n            font-size: 28rpx;\n            color: var(--color-text-body);\n        }\n        .privacy-switch {\n            margin-left: auto;\n            \n            :deep(.u-radio) {\n                font-size: 28rpx;\n                \n                .u-radio__label {\n                    color: #666666;\n                }\n            }\n            \n            :deep(.u-radio--checked) {\n                .u-radio__label {\n                    color: #6AC086;\n                    font-weight: 500;\n                }\n            }\n            \n            :deep(.u-radio__icon-wrap--checked) {\n                background-color: #6AC086 !important;\n                border-color: #6AC086 !important;\n            }\n        }\n\n        .official-switch {\n            margin-left: auto;\n        }\n    }\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/quote/post.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "AuthorSelector", "SourceSelector", "content", "ref", "<PERSON><PERSON><PERSON><PERSON>", "selectedSource", "tags", "privacy", "allowOfficial", "isSubmitting", "images", "handleClose", "uni", "index", "navigateBack", "handleAfterRead", "async", "event", "lists", "concat", "file", "value", "length", "showToast", "title", "icon", "fileListLen", "map", "item", "push", "status", "message", "i", "currentFileIndex", "res", "upload_img", "url", "data", "splice", "msg", "error", "handleDeletePic", "handleSubmit", "trim", "store", "$state", "userInfo", "uid", "token", "uploadedImageUrls", "filter", "img", "params", "author_id", "_c", "id", "source_id", "_d", "allow_official", "publishQuote", "$emit", "setTimeout", "wx", "createPage", "MiniProgramPage"], "mappings": "itCAIA,MAAMA,EAAe,IAAW,yCAC1BC,EAAiB,IAAW,2CAC5BC,EAAiB,IAAW,qEAG5B,MAAAC,EAAUC,EAAAA,IAAI,IAEdC,EAAiBD,EAAAA,IAAI,MACrBE,EAAiBF,EAAAA,IAAI,MACrBG,EAAOH,EAAAA,IAAI,IACXI,EAAUJ,EAAAA,IAAI,UACdK,EAAgBL,EAAAA,KAAI,GACpBM,EAAeN,EAAAA,KAAI,GACnBO,EAASP,EAAAA,IAAI,IAGbQ,EAAc,KAChBC,EAAGC,MAACC,cAAY,EAIdC,EAAkBC,MAAOC,IAC3B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MAGxBV,EAAOW,MAAMC,OAAS,IACtBV,EAAGC,MAACU,UAAU,CAAEC,MAAO,qBAAsBC,KAAM,SACnDf,EAAOW,MAAQ,IAIfH,EAAMI,OAAS,IACfV,EAAGC,MAACU,UAAU,CAAEC,MAAO,sBAAuBC,KAAM,SAC5CP,EAAA,CAACA,EAAM,KAGf,IAAAQ,EAAchB,EAAOW,MAAMC,OAEzBJ,EAAAS,KAAKC,IACPlB,EAAOW,MAAMQ,KAAK,IACXD,EACHE,OAAQ,YACRC,QAAS,OACZ,IAGL,IAAA,IAASC,EAAI,EAAGA,EAAId,EAAMI,OAAQU,IAAK,CACnC,MAAMC,EAAmBP,EAAcM,EACnC,IACA,MAAME,QAAYC,EAAUA,WAACjB,EAAMc,GAAGI,KAEtC,GAAmB,OAAfF,EAAIJ,QAAmBI,EAAIG,KAAM,CAC7B,IAAAT,EAAOlB,EAAOW,MAAMY,GACpBL,GACOlB,EAAAW,MAAMiB,OAAOL,EAAkB,EAAG,IAClCL,EACHE,OAAQ,UACRC,QAAS,GACTK,IAAKF,EAAIG,MAGjC,MACoB3B,EAAOW,MAAMY,KACNvB,EAAAW,MAAMY,GAAkBH,OAAS,SACxCpB,EAAOW,MAAMY,GAAkBF,QAAUG,EAAIK,KAAO,gBAEpDhB,UAAU,CAAEC,MAAOU,EAAIK,KAAO,SAAUd,KAAM,QAEzD,OAAQe,GACD9B,EAAOW,MAAMY,KACNvB,EAAAW,MAAMY,GAAkBH,OAAS,SACjCpB,EAAAW,MAAMY,GAAkBF,QAAU,QAE7CnB,EAAGC,MAACU,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC/C,CACJ,GAIEgB,EAAmBxB,IACrBP,EAAOW,MAAMiB,OAAOrB,EAAMJ,MAAO,EAAC,EAGhC6B,EAAe1B,sBAEjB,IAAKd,EAAQmB,MAAMsB,OAEf,YADA/B,EAAGC,MAACU,UAAU,CAAEC,MAAO,WAAYC,KAAM,SAK7C,KAAKmB,OAAAA,EAAAA,EAAKA,QAAGC,OAAOC,eAAfF,EAAAA,EAAyBG,QAAQH,OAAAA,IAAAA,QAAQC,OAAOC,mBAAUE,OAE3D,YADApC,EAAGC,MAACU,UAAU,CAAEC,MAAO,OAAQC,KAAM,SAKzC,MAAMwB,EAAoBvC,EAAOW,MAC5B6B,WAA6B,YAAfC,EAAIrB,QAAwBqB,EAAIf,MAC9CT,KAAIwB,GAAOA,EAAIf,MAGhB,GAA6B,IAA7Ba,EAAkB3B,OAElB,YADAV,EAAGC,MAACU,UAAU,CAAEC,MAAO,aAAcC,KAAM,SAI/C,GAAIhB,EAAaY,MAAO,OAExBZ,EAAaY,OAAQ,EAIrB,MAAM+B,EAAS,CACXL,IAAKH,EAAKA,QAAGC,OAAOC,SAASC,IAC7BC,MAAOJ,EAAKA,QAAGC,OAAOC,SAASE,MAC/B9C,QAASA,EAAQmB,MAAMsB,OACvBU,WAAW,OAAAC,EAAAlD,EAAeiB,YAAf,EAAAiC,EAAsBC,KAAM,KACvCC,WAAW,OAAAC,EAAApD,EAAegB,YAAf,EAAAoC,EAAsBF,KAAM,KACvCjD,KAAMA,EAAKe,MAAMsB,OACjBpC,QAASA,EAAQc,MACjBqC,eAAgBlD,EAAca,MAAQ,EAAI,EAC1CX,OAAQuC,GAGR,IACA,MAAMf,QAAYyB,eAAaP,GACZ,OAAflB,EAAIJ,QACJlB,EAAGC,MAACU,UAAU,CAAEC,MAAO,OAAQC,KAAM,oBAIjCmC,MAAM,oBAEVC,YAAW,KACPjD,EAAGC,MAACC,cAAY,GACjB,MACmB,YAAfoB,EAAIJ,OACXlB,EAAGC,MAACU,UAAU,CAAEC,MAAO,OAAQC,KAAM,iBAEjCF,UAAU,CAAEC,MAAOU,EAAIK,KAAO,OAAQd,KAAM,QAEvD,OAAQe,GACL5B,EAAGC,MAACU,UAAU,CAAEC,MAAO,WAAYC,KAAM,QACjD,CAAc,QACNhB,EAAaY,OAAQ,CACzB,63CCrJJyC,GAAGC,WAAWC"}