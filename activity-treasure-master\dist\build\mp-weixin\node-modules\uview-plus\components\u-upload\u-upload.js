"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-upload",mixins:[e.mpMixin,e.mixin,e.mixinUpload,e.props$26],data:()=>({lists:[],isInCount:!0}),watch:{fileList:{handler(){this.formatFileList()},immediate:!0,deep:!0},deletable(e){this.formatFileList()},maxCount(e){this.formatFileList()},accept(e){this.formatFileList()}},emits:["error","beforeRead","oversize","afterRead","delete","clickPreview"],methods:{addUnit:e.addUnit,addStyle:e.addStyle,formatFileList(){const{fileList:t=[],maxCount:i}=this,s=t.map((t=>Object.assign(Object.assign({},t),{isImage:"image"===this.accept||e.test.image(t.url||t.thumb),isVideo:"video"===this.accept||e.test.video(t.url||t.thumb),deletable:"boolean"==typeof t.deletable?t.deletable:this.deletable})));this.lists=s,this.isInCount=s.length<i},chooseFile(){const{maxCount:t,multiple:i,lists:s,disabled:a}=this;if(a)return;let o;try{o=e.test.array(this.capture)?this.capture:this.capture.split(",")}catch(l){o=[]}e.chooseFile(Object.assign({accept:this.accept,extension:this.extension,multiple:this.multiple,capture:o,compressed:this.compressed,maxDuration:this.maxDuration,sizeType:this.sizeType,camera:this.camera},{maxCount:t-s.length})).then((e=>{this.onBeforeRead(i?e:e[0])})).catch((e=>{this.$emit("error",e)}))},onBeforeRead(t){const{beforeRead:i,useBeforeRead:s}=this;let a=!0;e.test.func(i)&&(a=i(t,this.getDetail())),s&&(a=new Promise(((e,i)=>{this.$emit("beforeRead",Object.assign(Object.assign({file:t},this.getDetail()),{callback:t=>{t?e():i()}}))}))),a&&(e.test.promise(a)?a.then((e=>this.onAfterRead(e||t))):this.onAfterRead(t))},getDetail(e){return{name:this.name,index:null==e?this.fileList.length:e}},onAfterRead(e){const{maxSize:t,afterRead:i}=this;(Array.isArray(e)?e.some((e=>e.size>t)):e.size>t)?this.$emit("oversize",Object.assign({file:e},this.getDetail())):("function"==typeof i&&i(e,this.getDetail()),this.$emit("afterRead",Object.assign({file:e},this.getDetail())))},deleteItem(e){this.$emit("delete",Object.assign(Object.assign({},this.getDetail(e)),{file:this.fileList[e]}))},onPreviewImage(t,i){if(!t.isImage||!this.previewFullImage)return;let s=0;const a=[];let o=0;for(var l=0;l<this.lists.length;l++){const e=this.lists[l];(e.isImage||e.type&&"image"===e.type)&&(a.push(e.url||e.thumb),l===i&&(s=o),o+=1)}a.length<1||e.index.previewImage({urls:a,current:s,fail(){e.toast("预览图片失败")}})},onPreviewVideo(t){if(!this.previewFullImage)return;let i=0;const s=[];let a=0;for(var o=0;o<this.lists.length;o++){const e=this.lists[o];(e.isVideo||e.type&&"video"===e.type)&&(s.push(Object.assign(Object.assign({},e),{type:"video"})),o===t&&(i=a),a+=1)}s.length<1||e.wx$1.previewMedia({sources:s,current:i,fail(){e.toast("预览视频失败")}})},onClickPreview(e,t){if(this.previewFullImage){if("video"===e.type)this.onPreviewVideo(t);this.$emit("clickPreview",Object.assign(Object.assign({},e),this.getDetail(t)))}}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-loading-icon/u-loading-icon.js"))();const i=e._export_sfc(t,[["render",function(t,i,s,a,o,l){return e.e({a:t.previewImage},t.previewImage?{b:e.f(o.lists,((i,s,a)=>e.e({a:i.isImage||i.type&&"image"===i.type},i.isImage||i.type&&"image"===i.type?{b:i.thumb||i.url,c:t.imageMode,d:e.o((e=>l.onPreviewImage(i,s)),s),e:e.s({width:l.addUnit(t.width),height:l.addUnit(t.height)})}:{f:"fc8d344c-0-"+a,g:e.p({color:"#80CBF9",size:"26",name:i.isVideo||i.type&&"video"===i.type?"movie":"folder"}),h:e.t(i.isVideo||i.type&&"video"===i.type?"视频":"文件"),i:e.o((e=>l.onClickPreview(i,s)),s)},{j:"uploading"===i.status||"failed"===i.status},"uploading"===i.status||"failed"===i.status?e.e({k:"failed"===i.status},"failed"===i.status?{l:"fc8d344c-1-"+a,m:e.p({name:"close-circle",color:"#ffffff",size:"25"})}:{n:"fc8d344c-2-"+a,o:e.p({size:"22",mode:"circle",color:"#ffffff"})},{p:i.message},i.message?{q:e.t(i.message)}:{}):{},{r:"uploading"!==i.status&&(t.deletable||i.deletable)},"uploading"!==i.status&&(t.deletable||i.deletable)?{s:"fc8d344c-3-"+a,t:e.p({name:"close",color:"#ffffff",size:"10"}),v:e.o((e=>l.deleteItem(s)),s)}:{},{w:"success"===i.status},"success"===i.status?{x:"fc8d344c-4-"+a,y:e.p({name:"checkmark",color:"#ffffff",size:"12"})}:{},{z:s})))}:{},{c:o.isInCount},o.isInCount?e.e({d:t.$slots.trigger},t.$slots.trigger?{e:e.o(((...e)=>l.chooseFile&&l.chooseFile(...e)))}:t.$slots.trigger||!t.$slots.default&&!t.$slots.$default?e.e({h:e.p({name:t.uploadIcon,size:"26",color:t.uploadIconColor}),i:t.uploadText},t.uploadText?{j:e.t(t.uploadText)}:{},{k:t.disabled?"":"u-upload__button--hover",l:e.o(((...e)=>l.chooseFile&&l.chooseFile(...e))),m:e.n(t.disabled&&"u-upload__button--disabled"),n:e.s({width:l.addUnit(t.width),height:l.addUnit(t.height)})}):{g:e.o(((...e)=>l.chooseFile&&l.chooseFile(...e)))},{f:!t.$slots.trigger&&(t.$slots.default||t.$slots.$default)}):{},{o:e.s(l.addStyle(t.customStyle))})}],["__scopeId","data-v-fc8d344c"]]);wx.createComponent(i);
//# sourceMappingURL=u-upload.js.map
