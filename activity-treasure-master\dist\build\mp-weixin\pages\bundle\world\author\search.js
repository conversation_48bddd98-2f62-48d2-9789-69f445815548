"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),a=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||(t+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const t=()=>"../../../../components/customNavbar.js",r={__name:"search",setup(t){const r=e.ref(""),u=e.ref([]),l=e.ref(!1),n=e.ref(!1),s=e.ref(!0),i=e.ref(1),c=getCurrentPages(),d="select"===c[c.length-1].options.type,v=async(t=!1)=>{if(!(l.value||t&&n.value)){t?n.value=!0:(l.value=!0,i.value=1,u.value=[],s.value=!0);try{const l=await o.searchAuthors({uid:a.store.userInfo.uid,token:a.store.userInfo.token,keyword:r.value,page:i.value,page_size:20});if("ok"===l.status){const e=l.data.authors||[];u.value=t?[...u.value,...e]:e,s.value=20===e.length,s.value&&i.value++}else e.index.showToast({title:l.msg||"搜索失败",icon:"none"})}catch(c){console.error("搜索作者失败:",c),e.index.showToast({title:"搜索失败，请稍后重试",icon:"none"})}finally{l.value=!1,n.value=!1}}},p=o=>{d?(e.index.$emit("authorSelected",o),e.index.navigateBack()):e.index.navigateTo({url:`/pages/bundle/world/author/detail?id=${o.id}`})},g=()=>{d?e.index.navigateTo({url:`/pages/bundle/world/author/create?type=select&keyword=${encodeURIComponent(r.value)}`}):e.index.navigateTo({url:"/pages/bundle/world/author/create"})};let m=null;const h=()=>{clearTimeout(m),m=setTimeout((()=>{v()}),500)},f=()=>{s.value&&!n.value&&v(!0)};return e.onMounted((()=>{v()})),e.index.$on("authorCreated",(e=>{d?p(e):v()})),e.onUnmounted((()=>{e.index.$off("authorCreated")})),(o,a)=>e.e({a:e.p({title:d?"选择作者":"搜索作者",backIcon:"arrow-left"}),b:e.p({name:"search",size:"20",color:"#999"}),c:e.o([e=>r.value=e.detail.value,h]),d:e.o(v),e:r.value,f:r.value},r.value?{g:e.p({name:"close-circle-fill",size:"18",color:"#ccc"}),h:e.o((e=>{r.value="",v()}))}:{},{i:l.value&&0===u.value.length},l.value&&0===u.value.length?{j:e.p({mode:"spinner",color:"#6AC086",size:"40"})}:0!==u.value.length||l.value?e.e({o:e.f(u.value,((o,a,t)=>e.e({a:o.avatar},o.avatar?{b:o.avatar}:{c:e.t(o.name.charAt(0))},{d:e.t(o.name),e:o.category},o.category?{f:e.t(o.category)}:{},{g:o.description},o.description?{h:e.t(o.description)}:{},{i:e.t(o.quote_count||0),j:"8e902539-6-"+t,k:o.id,l:e.o((e=>p(o)),o.id)}))),p:e.p({name:"arrow-right",size:"16",color:"#ccc"}),q:s.value},s.value?e.e({r:n.value},n.value?{s:e.p({mode:"spinner",color:"#6AC086",size:"20"})}:{},{t:e.t(n.value?"加载中...":"加载更多"),v:e.o(f)}):{},{w:e.p({name:"plus",size:"16",color:"#6AC086"}),x:e.o(g)}):{l:e.p({name:"account",size:"80",color:"#ddd"}),m:e.p({name:"plus",size:"16",color:"#6AC086"}),n:e.o(g)},{k:0===u.value.length&&!l.value})}},u=e._export_sfc(r,[["__scopeId","data-v-8e902539"]]);wx.createPage(u);
//# sourceMappingURL=search.js.map
