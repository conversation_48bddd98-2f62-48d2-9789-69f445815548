"use strict";const e=require("../common/vendor.js");if(!Array){(e.resolveComponent("stop")+e.resolveComponent("linearGradient")+e.resolveComponent("defs")+e.resolveComponent("path")+e.resolveComponent("svg"))()}const t={__name:"CustomTabBar",props:{current:{type:Number,default:0}},emits:["change"],setup(t,{emit:a}){const n=t,o=e.ref(n.current),i=[{pagePath:"/pages/index",iconPath:"/static/index.png",selectedIconPath:"/static/index.svg",text:"活动",index:0},{pagePath:"/pages/world",iconPath:"/static/world.png",selectedIconPath:"/static/world.svg",text:"世界",index:1},{pagePath:"/pages/addActive",iconPath:"/static/addActive.svg",selectedIconPath:"/static/addActive.svg",text:"发布",index:2,isSpecial:!0},{pagePath:"/pages/notifications",iconPath:"/static/notification.svg",selectedIconPath:"/static/notification-active.svg",text:"通知",index:3},{pagePath:"/pages/my",iconPath:"/static/my.png",selectedIconPath:"/static/myd.png",text:"我的",index:4}];e.onMounted((()=>{const e=getCurrentPages();if(e.length>0){const t="/"+e[e.length-1].route,a=i.findIndex((e=>e.pagePath===t));-1!==a&&(o.value=a)}}));const s=e=>o.value===e;return(t,n)=>({a:e.p({offset:"0%"}),b:e.p({offset:"30%"}),c:e.p({offset:"70%"}),d:e.p({offset:"100%"}),e:e.p({id:"waveGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%"}),f:e.p({offset:"0%"}),g:e.p({offset:"100%"}),h:e.p({id:"maskGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%"}),i:e.p({d:"M 0,80\n           Q 75,75 150,70\n           Q 187.5,45 225,70\n           Q 300,75 375,80\n           L 375,120\n           L 0,120 Z",fill:"url(#waveGradient)"}),j:e.p({viewBox:"0 0 375 120",preserveAspectRatio:"none"}),k:e.f(i,((t,n,i)=>({a:s(n)?t.selectedIconPath:t.iconPath,b:e.t(t.text),c:s(n)?1:"",d:n,e:s(n)?1:"",f:t.isSpecial?1:"",g:e.o((i=>((t,n)=>{if(o.value!==n){try{e.index.vibrateShort({type:"light"})}catch(i){}o.value=n,a("change",n),"/pages/addActive"===t.pagePath?e.index.navigateTo({url:"/pages/addActive",animationType:"slide-in-bottom",animationDuration:200,fail:t=>{console.error("跳转发布页面失败:",t),e.index.reLaunch({url:"/pages/addActive"})}}):e.index.redirectTo({url:t.pagePath,fail:a=>{console.error("页面跳转失败:",a),e.index.navigateTo({url:t.pagePath,fail:a=>{console.error("navigateTo也失败:",a),e.index.reLaunch({url:t.pagePath,fail:t=>{console.error("reLaunch也失败:",t),e.index.showToast({title:"页面跳转失败",icon:"none"})}})}})}})}})(t,n)),n)})))})}},a=e._export_sfc(t,[["__scopeId","data-v-6dc5eef1"]]);wx.createComponent(a);
//# sourceMappingURL=CustomTabBar.js.map
