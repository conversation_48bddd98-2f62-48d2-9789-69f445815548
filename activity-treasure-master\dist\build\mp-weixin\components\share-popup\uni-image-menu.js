"use strict";var t,e;exports.NvImageMenu=class{constructor(t){this.isShow=!1}show({list:i,cancelText:o},s){i||(i=[{img:"/static/sharemenu/wechatfriend.png",text:"图标文字"}]);var n=plus.screen.resolutionWidth,h=20,l=60,c=h/360*n,p=(n-2*c-4*l)/3;p<=5&&(p=(n-2*(c=(h=15)/360*n)-4*(l=40))/3);var r=c+l+p,a=c+2*(l+p),g=c+3*(l+p),x=c+l+5+12+c;const d={top1:c,top2:x},w={left1:c,left2:r,left3:a,left4:g};t=new plus.nativeObj.View("nvMask",{top:"0px",left:"0px",height:"100%",width:"100%",backgroundColor:"rgba(0,0,0,0.2)"}),e=new plus.nativeObj.View("nvImageMenu",{bottom:"0px",left:"0px",height:(l+12+2*h)*Math.ceil(i.length/4)+44+"px",width:"100%",backgroundColor:"rgb(255,255,255)"}),t.addEventListener("click",(()=>{this.hide(),s({event:"clickMask"})}));let f=[];i.forEach(((t,e)=>{f.push({tag:"img",src:t.img,position:{top:d["top"+(parseInt(e/4)+1)],left:w["left"+(1+e%4)],width:l,height:l}}),f.push({tag:"font",text:t.text,textStyles:{size:12},position:{top:d["top"+(parseInt(e/4)+1)]+l+5,left:w["left"+(1+e%4)],width:l,height:12}})})),e.draw([{tag:"rect",color:"#e7e7e7",position:{top:"0px",height:"1px"}},{tag:"font",text:o,textStyles:{size:"14px"},position:{bottom:"0px",height:"44px"}},{tag:"rect",color:"#e7e7e7",position:{bottom:"45px",height:"1px"}},...f]),t.show(),e.show(),this.isShow=!0,e.addEventListener("click",(t=>{if(t.screenY>plus.screen.resolutionHeight-44)this.hide();else if(t.clientX<5||t.clientX>n-5||t.clientY<5);else{var e=t.clientY<x-c/2?0:1,i=-1;i=t.clientX<r-p/2?0:t.clientX<a-p/2?1:t.clientX<g-p/2?2:3,s({event:"clickMenu",index:0==e?i:i+4})}}))}hide(){this.isShow&&(t.hide(),e.hide(),this.isShow=!1)}};
//# sourceMappingURL=uni-image-menu.js.map
