"use strict";const o=require("../../../../common/vendor.js"),n={name:"u-modal",mixins:[o.mpMixin,o.mixin,o.props$14],data:()=>({loading:!1}),watch:{show(o){o&&this.loading&&(this.loading=!1)}},emits:["confirm","cancel","close","update:show"],methods:{addUnit:o.addUnit,confirmHandler(){this.asyncClose?this.loading=!0:this.$emit("update:show",!1),this.$emit("confirm")},cancelHandler(){this.$emit("update:show",!1),this.$emit("cancel")},clickHandler(){this.closeOnClickOverlay&&(this.$emit("update:show",!1),this.$emit("close"))}}};if(!Array){(o.resolveComponent("u-line")+o.resolveComponent("u-loading-icon")+o.resolveComponent("u-popup"))()}Math||((()=>"../u-line/u-line.js")+(()=>"../u-loading-icon/u-loading-icon.js")+(()=>"../u-popup/u-popup.js"))();const t=o._export_sfc(n,[["render",function(n,t,e,i,l,s){return o.e({a:n.title},n.title?{b:o.t(n.title)}:{},{c:o.t(n.content),d:n.contentTextAlign,e:(n.title?12:25)+"px",f:n.$slots.confirmButton},n.$slots.confirmButton?{}:o.e({g:n.showCancelButton},n.showCancelButton?{h:o.t(n.cancelText),i:n.cancelColor,j:o.n(n.showCancelButton&&!n.showConfirmButton&&"u-modal__button-group__wrapper--only-cancel"),k:o.o(((...o)=>s.cancelHandler&&s.cancelHandler(...o)))}:{},{l:n.showConfirmButton&&n.showCancelButton},n.showConfirmButton&&n.showCancelButton?{m:o.p({direction:"column"})}:{},{n:n.showConfirmButton},n.showConfirmButton?o.e({o:l.loading},l.loading?{}:{p:o.t(n.confirmText),q:n.confirmColor},{r:o.n(!n.showCancelButton&&n.showConfirmButton&&"u-modal__button-group__wrapper--only-confirm"),s:o.o(((...o)=>s.confirmHandler&&s.confirmHandler(...o)))}):{},{t:n.buttonReverse?"row-reverse":"row"}),{v:s.addUnit(n.width),w:o.n(n.customClass),x:o.o(s.clickHandler),y:o.p({mode:"center",zoom:n.zoom,show:n.show,customStyle:{borderRadius:"6px",overflow:"hidden",marginTop:`-${s.addUnit(n.negativeTop)}`},closeOnClickOverlay:n.closeOnClickOverlay,safeAreaInsetBottom:!1,duration:400})})}],["__scopeId","data-v-180acee1"]]);wx.createComponent(t);
//# sourceMappingURL=u-modal.js.map
