"use strict";const e=require("../common/vendor.js"),t=require("../api/index.js"),o=require("../store/index.js"),l=require("../utils/index.js"),n=require("../utils/china.js");if(require("../utils/request.js"),require("../utils/BaseUrl.js"),require("../utils/auth.js"),require("../store/counter.js"),require("../utils/cacheManager.js"),require("../utils/systemInfo.js"),!Array){(e.resolveComponent("u-text")+e.resolveComponent("u-line")+e.resolveComponent("u-input")+e.resolveComponent("u-sticky")+e.resolveComponent("u-gap")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-swiper")+e.resolveComponent("u-avatar")+e.resolveComponent("up-back-top")+e.resolveComponent("u-picker"))()}Math||((()=>"../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../node-modules/uview-plus/components/u-line/u-line.js")+(()=>"../node-modules/uview-plus/components/u-input/u-input.js")+a+(()=>"../node-modules/uview-plus/components/u-sticky/u-sticky.js")+(()=>"../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../node-modules/uview-plus/components/u-swiper/u-swiper.js")+(()=>"../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../node-modules/uview-plus/components/u-back-top/u-back-top.js")+(()=>"../node-modules/uview-plus/components/u-picker/u-picker.js")+i)();const a=()=>"../components/myTitle.js",i=()=>"../components/CustomTabBar.js",r={__name:"index",setup(a){const i=e.ref(!1),r=e.ref("全城"),s=e.ref({}),u=e.ref([{name:"全部活动",icon:"/static/quanbuhuodong.svg"},{name:"线上活动",icon:"/static/xianshanghuodong.svg"},{name:"历史活动",icon:"/static/lishihuodong.svg"}]),c=e.ref([]),d=e.ref(0);e.ref(!0);const g=e.ref(!1),p=e.ref([[]]),v=e.ref([]),m=e.reactive({page:1,page_size:20,type_id:0,is_tuijian:1,keyword:"",shi_id:"",qu_id:"",sort:1,lng:"",lat:"",huodong_date:"",list_type:1}),h=e.ref(!0),f=e.ref(0),_=e.ref("全城"),x=e.ref("500rpx"),y=e.ref(0);e.ref("时间优先");const w=e.ref([["时间优先","距离优先"]]),$=e.ref(!1),b=e.ref(!1),C=e.ref(!1),k=e.ref(!0);e.onShareTimeline((()=>{var e,t,l,n,a;return{title:null==(t=null==(e=o.store().$state.config.config)?void 0:e.app_name)?void 0:t.val,imageUrl:null==(n=null==(l=o.store().$state.config.img_config)?void 0:l.app_logo)?void 0:n.val,path:`/pages/index?pid=${null==(a=o.store().$state.userInfo)?void 0:a.uid}`}})),e.onShareAppMessage((()=>{var e,t,l,n,a;return{title:null==(t=null==(e=o.store().$state.config.config)?void 0:e.app_name)?void 0:t.val,imageUrl:null==(n=null==(l=o.store().$state.config.img_config)?void 0:l.app_logo)?void 0:n.val,path:`/pages/index?pid=${null==(a=o.store().$state.userInfo)?void 0:a.uid}`}})),e.onPageScroll((e=>{k.value&&f&&void 0!==f.value&&(f.value=e.scrollTop)})),e.onPullDownRefresh((()=>{i.value&&T()})),e.onReachBottom((()=>{i.value&&(h&&h.value?(m.page++,I()):e.index.$u.toast("暂无更多"))})),e.onLoad((async n=>{(null==n?void 0:n.pid)&&o.store().changePid(n.pid);try{const n=await t.huodongget_type();s&&(s.value.active=n.data),o.store().changeActiveTypeList(n.data),R(30);try{const t=e.index.getMenuButtonBoundingClientRect();if(t&&t.left>0){const e=l.pxToRpx(t.left-39);x.value=e>200?e+"rpx":"500rpx"}}catch(a){console.warn("搜索栏宽度计算失败，使用默认宽度:",a),x.value="500rpx"}}catch(a){}})),e.onHide((()=>{k.value=!1})),e.onShow((async()=>{var n,a;if(k.value=!0,"500rpx"===x.value)try{const t=e.index.getMenuButtonBoundingClientRect();if(t&&t.left>0){const e=l.pxToRpx(t.left-39);e>200&&(x.value=e+"rpx")}}catch(u){console.warn("onShow中搜索栏宽度计算失败:",u)}try{const t=e.index.getStorageSync("selectedCity");t&&t.adcode&&t.name&&(m.shi_id&&m.shi_id===t.adcode||(m.shi_id=t.adcode,r.value=t.name,console.log("恢复已选择的城市:",t.name,"adcode:",t.adcode)))}catch(u){console.warn("恢复城市状态失败:",u)}const i=await t.lunbotuindex({type:1});if(s.value.lunbotu=(null==i?void 0:i.data)||[],console.log("轮播图加载完成，数量:",s.value.lunbotu.length),!(null==(n=s.value)?void 0:n.active)||!s.value.active.length)try{const e=await t.huodongget_type();"ok"===(null==e?void 0:e.status)&&(s.value.active=e.data,o.store().changeActiveTypeList(e.data))}catch(u){console.warn("获取活动类型失败:",u)}(null==(a=v.value)?void 0:a.length)||(+o.store().$state.config.config.is_shenhe.val?T():q({location:"116.39747,39.908823",lng:"116.39747",lat:"39.908823"}))})),e.onMounted((()=>{i.value=!0,T(),e.index.$on("citySelected",D)})),e.onUnload((()=>{i.value=!1,e.index.$off("citySelected",D)})),e.onShow((()=>{const t=e.index.getStorageSync("selectedCity");t&&t.adcode&&(D(t),e.index.removeStorageSync("selectedCity"))}));const S=()=>{i.value&&e.index.navigateTo({url:"/pages/city-select"})},D=e=>{i.value&&e&&(r&&(r.value=e.name),m.shi_id=e.adcode,m.lng="",m.lat="",1!==y.value&&2!==y.value||(y.value=0,m.list_type=1,console.log("城市选择后自动切换到全部活动tab")),Q(),console.log("城市已切换:",e.name,"adcode:",e.adcode))},T=()=>{i.value&&(m.shi_id?Q():e.index.getSetting({success(t){t.authSetting["scope.userLocation"]?j():e.index.authorize({scope:"scope.userLocation",success(){j()},fail(t){e.index.showModal({title:"提示",content:"若点击不授权，将无法使用位置功能",cancelText:"不授权",cancelColor:"#999",confirmText:"授权",confirmColor:"#f94218",success(t){t.confirm?e.index.openSetting({success:e=>{j()},fail:e=>{j()}}):t.cancel&&j()}})}})},fail(e){console.error("获取设置失败:",e),j()}}))},j=async()=>{i.value&&e.index.getLocation({type:"wgs84",isHighAccuracy:!0,success:async e=>{if(i.value){const t={location:`${e.longitude},${e.latitude}`,lng:e.longitude.toString(),lat:e.latitude.toString()};await q(t)}},fail:async t=>{console.error("获取位置失败:",t),i.value&&(e.index.$u.toast("获取位置失败，请检查定位权限设置"),m.lng="",m.lat="",m.shi_id="",r&&(r.value="全城"),Q())}})},q=async o=>{if(i.value){m.lng=o.lng,m.lat=o.lat;try{const l=await t.getAddr(o);if(l&&1==l.status){const t=l.regeocode.addressComponent;if(!i.value||!r)return;const o=e.index.$u.test.isEmpty(t.city)?t.province:t.city;if(r&&(r.value=o),t.adcode&&t.adcode.length>=4){const e=t.adcode.substring(0,2)+"0000",l={11e4:"110100",12e4:"120100",31e4:"310100",5e5:"500100"};if(l[e]?m.shi_id=l[e]:m.shi_id=t.adcode.substring(0,4)+"00",console.log("成功设置城市adcode:",m.shi_id,"城市名称:",o,"原始adcode:",t.adcode),!i.value||!p||!p.value)return;n.china_area.forEach((e=>e.children.findIndex((e=>e.id===`${m.shi_id}`?p.value[0]=[{name:"全城",id:0},...e.children]:""))))}else console.log("未获取到adcode，使用默认设置"),m.shi_id="",r&&(r.value="全城")}else console.log("地址解析失败，使用默认设置"),m.shi_id="",r&&(r.value="全城"),e.index.$u.toast("定位失败，显示全部活动")}catch(l){console.error("地址解析异常:",l),m.shi_id="",r&&(r.value="全城")}i.value&&Q()}},I=async(o=0)=>{if(i.value)try{if("function"!=typeof t.huodongget_list)return console.error("huodongget_list 函数不存在"),void e.index.$u.toast("系统错误，请稍后重试");const l=await t.huodongget_list(m);if(!i.value)return;if("n"===l)console.log("服务器返回空数据"),1===m.page&&(v.value=[]),h.value=!1;else if(l&&"ok"===l.status&&Array.isArray(l.data)){const t=l.data||[];if(1===m.page)if(v.value=t,3===m.list_type){const e=B();P.setCache(e,t),console.log("缓存第一页数据 [历史活动]:",t.length,"条记录，缓存键:",e)}else{const e=1===m.list_type?"全部活动":"线上活动";console.log(`${e}不使用缓存，跳过缓存设置`)}else{const e=v.value.concat(t),o=100;if(e.length>o){e.slice(0,e.length-o).forEach((e=>{e.img_url_original&&(e.img_url_original=null),e.img_url&&(e.img_url=null)})),v.value=e.slice(-o)}else v.value=e}e.nextTick$1((()=>{})),l.data.length<m.page_size&&(h.value=!1)}else if(l&&"error"===l.status){if(console.error("服务器返回错误:",l.msg),e.index.$u.toast(l.msg||"获取活动列表失败"),o<2)return console.log(`尝试重新获取数据，第${o+1}次重试`),void setTimeout((()=>I(o+1)),1e3)}else if(console.error("未知响应格式:",l),o<2)return console.log(`尝试重新获取数据，第${o+1}次重试`),void setTimeout((()=>I(o+1)),1e3);l&&l.msg&&e.index.$u.toast(l.msg)}catch(l){if(console.error("获取活动列表异常:",l),e.index.$u.toast("加载失败，请稍后重试"),o<2)return console.log(`尝试重新获取数据，第${o+1}次重试`),void setTimeout((()=>I(o+1)),1e3)}finally{if(!i.value)return;b&&(b.value=!1,console.log("getList完成，设置loading状态为false")),e.index.stopPullDownRefresh()}},R=e=>{let t=new Date,o=new Date,l=t.getDate();o.setDate(l+e);let n=["日","一","二","三","四","五","六"];for(;o.getTime()-t.getTime()>=0;){let e=t.getFullYear(),o=t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1,l=t.getDate()<10?"0"+t.getDate():t.getDate(),a=`周${n[t.getDay()]}`;c.value.length>0?c.value.push({week:a,day:e+"-"+o+"-"+l}):c.value.push({week:"全部",day:""},{week:"今日",day:e+"-"+o+"-"+l}),t.setDate(t.getDate()+1)}},E=e=>{var t;if(console.log("轮播图点击事件，索引:",e),s&&s.value.lunbotu&&s.value.lunbotu[e]){const t=s.value.lunbotu[e];console.log("点击的轮播图项目:",t),1==t.url_type&&l.navto(`/pages/bundle/index/webview?url=${t.url_params}`),2==t.url_type&&l.navto(`/pages/bundle/index/activeInfo?id=${t.url_params}`),3==t.url_type&&l.navto(`${t.url_params}`)}else console.warn("轮播图点击失败，数据不存在:",{e:e,lunbotu:null==(t=s.value)?void 0:t.lunbotu})},A=e=>{if(!e)return"";return e.length>10?e.substring(0,10)+"...":e},M=e=>{0==e.value[0].id?m.qu_id="":m.qu_id=e.value[0].id,_&&(_.value=e.value[0].name),g&&(g.value=!1),Q()},z=e=>{m.sort=e.indexs[0]+1,$&&($.value=!1),Q()},L=e=>{Q()},Q=()=>{if(!i.value)return;console.log("resetList被调用，当前活动类型:",m.list_type);if(3===m.list_type){const e=B(),t=P.getCache(e);if(t&&t.length>0)return console.log("使用有效缓存数据 [历史活动]:",t.length,"条记录"),v&&(v.value=t),b&&(b.value=!1),void(C&&(C.value=t.length<m.page_size));console.log("无有效缓存数据 [历史活动]，开始加载新数据")}else{const e=1===m.list_type?"全部活动":"线上活动";console.log(`${e}不使用缓存，直接获取最新数据`)}m.page=1,h&&(h.value=!0),v&&v.value&&(v.value.forEach((e=>{e.img_url_original&&(e.img_url_original=null),e.img_url&&(e.img_url=null)})),v.value=[]),b&&(b.value=!0),I()},B=()=>`activity_list_type${m.list_type}_${m.type_id}_${m.shi_id}_${m.huodong_date}_${m.sort}_${m.keyword}`,P={CACHE_EXPIRE_TIME:12e4,isCacheValid:e=>!(!e||!e.timestamp)&&Date.now()-e.timestamp<P.CACHE_EXPIRE_TIME,setCache:(e,t)=>{const l={data:t,timestamp:Date.now()};o.store().cacheActivityList(e,l),console.log(`设置缓存 [${e}]:`,t.length,"条记录，过期时间:",P.CACHE_EXPIRE_TIME/1e3,"秒")},getCache:e=>{const t=o.store().getCachedActivityList(e);return P.isCacheValid(t)?(console.log(`缓存命中 [${e}]:`,t.data.length,"条记录"),t.data):(console.log(`缓存失效或不存在 [${e}]`),null)}},F=e=>{if(!e)return"";return["周日","周一","周二","周三","周四","周五","周六"][new Date(e.split(" ")[0]).getDay()]},H=e=>{if(!e)return"";if((e=>{if(!e)return!1;const t=new Date,o=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`;return e.split(" ")[0]===o})(e))return"今天";if((e=>{if(!e)return!1;const t=new Date;t.setDate(t.getDate()+1);const o=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`;return e.split(" ")[0]===o})(e))return"明天";{const t=e.split(" ")[0].split("-");return`${t[1]}-${t[2]}`}},U={getDevicePerformance(){try{const t=e.index.getWindowInfo(),o=e.index.getDeviceInfo(),l=t.pixelRatio||2,n=o.platform||"unknown",a=o.model||"unknown";let i="medium";return l>=3&&"ios"===n||l>=2.5?i="high":l<=1.5&&(i="low"),{performanceLevel:i,pixelRatio:l,platform:n,model:a}}catch(t){return console.error("获取设备信息失败:",t),{performanceLevel:"medium",pixelRatio:2,platform:"unknown",model:"unknown"}}},selectOptimalFormat(e,t){const{performanceLevel:o,platform:l}=t;return"devtools"!==l?"webp":"jpg"},selectOptimalQuality(e,t="activity"){const{performanceLevel:o,pixelRatio:l}=e;return{activity:{low:60,medium:75,high:85},avatar:{low:70,medium:80,high:90},banner:{low:65,medium:80,high:90}}[t][o]}},O=(e,t,l="activity")=>{try{if(t||!e)return`${o.store().$state.url}default_activity.png`;const n=U.getDevicePerformance();if(!n)return console.warn("无法获取设备信息，使用原始URL"),e;const a=U.selectOptimalFormat(e,n),i=U.selectOptimalQuality(n,l);if(e.includes("linqingkeji.com")){const t=e.includes("?")?"&":"?";return`${e}${t}quality=${i}&format=${a}&optimize=true`}return e}catch(n){return console.error("图片URL优化处理失败:",n),e||`${o.store().$state.url}default_activity.png`}},V={queue:[],loading:new Set,maxConcurrent:3,addToQueue(e,t=1){try{if(!e||this.loading.has(e))return;this.queue.push({url:e,priority:t,timestamp:Date.now()}),this.queue.sort(((e,t)=>t.priority-e.priority)),this.processQueue()}catch(o){console.error("添加预加载队列失败:",o)}},processQueue(){try{if(this.loading.size>=this.maxConcurrent||0===this.queue.length)return;const t=this.queue.shift();if(!t)return;if(this.loading.add(t.url),"undefined"!=typeof Image){const e=new Image;e.onload=()=>{this.loading.delete(t.url),this.processQueue()},e.onerror=()=>{this.loading.delete(t.url),this.processQueue()},e.src=t.url}else e.index.downloadFile({url:t.url,success:()=>{this.loading.delete(t.url),this.processQueue()},fail:()=>{this.loading.delete(t.url),this.processQueue()}});setTimeout((()=>{this.loading.has(t.url)&&(this.loading.delete(t.url),this.processQueue())}),1e4)}catch(t){console.error("处理预加载队列失败:",t)}},preloadBatch(e,t=1){try{Array.isArray(e)&&e.forEach((e=>this.addToQueue(e,t)))}catch(o){console.error("批量预加载失败:",o)}},clear(){try{this.queue=[],this.loading.clear()}catch(e){console.error("清空预加载队列失败:",e)}}};return(t,n)=>{var a,i;return e.e({a:e.p({"prefix-icon":"map-fill",color:"#333",size:"22rpx",text:r.value||"全城","icon-style":{margin:"0 8rpx 0 0",fontSize:"20rpx",color:"#6AC086"},customStyle:{whiteSpace:"nowrap",maxWidth:"120rpx",overflow:"hidden",textOverflow:"ellipsis"},bold:!0}),b:e.p({length:"26rpx",color:"#E6E6E6",direction:"col",margin:"0 20rpx"}),c:e.o(S),d:e.o(L),e:e.o(L),f:e.o((e=>m.keyword=e)),g:e.p({placeholder:"搜索感兴趣的项目活动",border:"none",shape:"circle","placeholder-style":"font-size:24rpx;color:#aaa",customStyle:{padding:"0 28rpx",width:x.value,height:"64rpx",lineHeight:"64rpx",background:"rgba(255, 255, 255, 0.95)",boxShadow:"0 12rpx 32rpx rgba(106, 192, 134, 0.15)",fontSize:"26rpx"},modelValue:m.keyword}),h:e.p({img:"bg.png",height:"178rpx",backShow:!1}),i:e.p({height:"30rpx"}),j:e.p({height:"20rpx"}),k:!(null==(a=s.value)?void 0:a.lunbotu)||0===s.value.lunbotu.length},(null==(i=s.value)?void 0:i.lunbotu)&&0!==s.value.lunbotu.length?{m:e.o(E),n:e.p({height:"280rpx",radius:"30rpx","key-name":"img_url",list:s.value.lunbotu,indicator:!0,"indicator-mode":"circle",circular:!0,autoplay:!0})}:{l:e.p({mode:"circle",size:"30",color:"#6AC086"})},{o:e.p({height:"20rpx"}),p:e.f(u.value,((t,o,l)=>({a:t.icon,b:e.t(t.name),c:y.value===o?1:"",d:y.value===o?1:"",e:o,f:e.o((t=>(t=>{y&&(y.value=t);const o=m.list_type;if(m.list_type=t+1,console.log(`Tab切换: ${o} -> ${m.list_type}`,{tabNames:["全部活动","线上活动","历史活动"],currentTab:["全部活动","线上活动","历史活动"][t]}),0===t)try{const t=e.index.getStorageSync("selectedCity");t&&t.adcode&&t.name&&(m.shi_id=t.adcode,r&&(r.value=t.name))}catch(l){console.warn("恢复城市状态失败:",l)}else(1===t||2===t)&&(m.shi_id="",m.qu_id="",r&&(r.value="全城"),_&&(_.value="全城"));m.huodong_date="",d&&(d.value=0),v&&v.value&&(v.value=[]),Q()})(o)),o)}))),q:e.p({height:"20rpx"}),r:e.p({height:"10rpx"}),s:e.p({"offset-top":"176rpx","bg-color":"#fff"}),t:b.value},(b.value,{}),{v:!(b.value||v.value&&0!==v.value.length)},b.value||v.value&&0!==v.value.length?!b.value&&v.value&&v.value.length>0?{y:e.f(v.value,((t,n,a)=>e.e({a:0===n||t.start_time.split(" ")[0]!==v.value[n-1].start_time.split(" ")[0]},0===n||t.start_time.split(" ")[0]!==v.value[n-1].start_time.split(" ")[0]?{b:e.t(H(t.start_time)),c:e.t(F(t.start_time))}:{},{d:O(t.img_url,t.img_url_fallback),e:e.o((o=>((t,o)=>{try{if(!v.value||!v.value[t])return void console.warn("图片错误处理: 无效的索引或列表项",{index:t,hasItem:!!o});const l=v.value[t],n=l.imageRetryCount||0;if(console.warn(`图片加载失败 [${t}]:`,{url:l.img_url,retryCount:n,itemId:l.id}),n<2){l.imageRetryCount=n+1;const o=Math.min(1e3*Math.pow(2,n),5e3);setTimeout((()=>{if(v.value&&v.value[t]&&v.value[t].id===l.id){const o=l.img_url;l.img_url="",e.nextTick$1((()=>{v.value&&v.value[t]&&v.value[t].id===l.id&&(v.value[t].img_url=o)}))}}),o)}else l.img_url_fallback=!0,l.img_url_original=l.img_url,l.img_url=null,console.error(`图片加载最终失败 [${t}]:`,l.img_url_original)}catch(l){console.error("图片错误处理异常:",l)}})(n,t)),t.id||n),f:e.o((e=>((e,t)=>{try{if(!v.value||!v.value[e])return void console.warn("图片加载成功处理: 无效的索引或列表项",{index:e,hasItem:!!t});const n=v.value[e];n.img_url_fallback=!1,n.imageRetryCount=0,n.img_loaded=!0;const a=3,i=[];for(let t=Math.max(0,e-a);t<=Math.min(v.value.length-1,e+a);t++)if(t!==e&&v.value[t]&&v.value[t].img_url&&!v.value[t].img_preloaded)try{const e=O(v.value[t].img_url,v.value[t].img_url_fallback);e&&(i.push(e),v.value[t].img_preloaded=!0)}catch(o){console.warn("预加载URL处理失败:",o)}if(i.length>0)try{V.preloadBatch(i,2)}catch(l){console.warn("批量预加载失败:",l)}}catch(n){console.error("图片加载成功处理异常:",n)}})(n,t)),t.id||n),g:1*Date.now()>1*new Date(t.start_time.replaceAll("-","/"))},(Date.now(),new Date(t.start_time.replaceAll("-","/")),{}),{h:"2a78252d-13-"+a,i:e.p({src:t.user.avatar,mode:"aspectFill",size:"48rpx"}),j:"2a78252d-14-"+a,k:e.p({margin:"0 0 0 12rpx",color:"#666666",size:"24rpx",lines:"1",text:t.user.nickname}),l:"2a78252d-15-"+a,m:e.p({size:"32rpx",bold:!0,lines:"2",text:t.name,color:"#1a1a1a"}),n:"2a78252d-16-"+a,o:e.p({"prefix-icon":"account-fill","icon-style":{marginRight:"8rpx",fontSize:"24rpx",color:"#999999"},text:`${t.baoming_num}人报名`,color:"#999999",size:"24rpx",margin:"8rpx 0"}),p:t.is_online},t.is_online?{q:"2a78252d-17-"+a,r:e.p({"prefix-icon":"wifi","icon-style":{marginRight:"8rpx",fontSize:"24rpx",color:"#999999"},text:"线上活动",color:"#999999",size:"24rpx",lines:"1"})}:{s:e.t(A(t.sheng+t.shi+t.qu+t.addr))},{t:e.t(t.is_online?"线上":"线下"),v:t.is_online?1:"",w:t.is_online?"":1,x:e.o((n=>(t=>{const n=o.store().$state.userInfo;n&&n.uid&&n.token?l.navto(`/pages/bundle/index/activeInfo?id=${t}`):e.index.showModal({title:"提示",content:"请先登录后查看活动详情",confirmText:"去登录",cancelText:"取消",success:function(e){e.confirm&&l.navto("/pages/bundle/common/login")}})})(t.id)),t.id||n),y:t.id||n})))}:{}:{w:`${e.unref(o.store)().$state.url}empty.png`},{x:!b.value&&v.value&&v.value.length>0,z:e.p({bottom:"30rpx",right:"75rpx","scroll-top":f.value}),A:e.o((e=>g.value=!1)),B:e.o((e=>g.value=!1)),C:e.o(M),D:e.p({show:g.value,"key-name":"name","close-on-click-overlay":!0,columns:p.value}),E:e.o((e=>$.value=!1)),F:e.o((e=>$.value=!1)),G:e.o(z),H:e.p({show:$.value,"close-on-click-overlay":!0,columns:w.value}),I:e.p({current:0})})}}},s=e._export_sfc(r,[["__scopeId","data-v-2a78252d"]]);r.__runtimeHooks=7,wx.createPage(s);
//# sourceMappingURL=index.js.map
