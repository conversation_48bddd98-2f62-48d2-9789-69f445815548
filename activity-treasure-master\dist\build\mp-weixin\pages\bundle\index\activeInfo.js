"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),a=require("../../../utils/auth.js"),l=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),t=require("../../../store/index.js"),n=require("../../../utils/index.js"),r=require("../../../utils/systemInfo.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-avatar")+e.resolveComponent("u-text")+e.resolveComponent("u-button")+e.resolveComponent("u-line")+e.resolveComponent("u-gap")+e.resolveComponent("u-parse")+e.resolveComponent("u-safe-bottom")+e.resolveComponent("mescroll-uni")+e.resolveComponent("u-popup")+e.resolveComponent("u-textarea"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-line/u-line.js")+u+i+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-parse/u-parse.js")+(()=>"../../../node-modules/uview-plus/components/u-safe-bottom/u-safe-bottom.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js")+(()=>"../../../node-modules/uview-plus/components/u-popup/u-popup.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js"))();const i=()=>"../../../components/myLine.js",u=()=>"../../../components/myStitle.js",s={__name:"activeInfo",setup(i){const u=e.ref(null),s=e.ref({}),d=e.ref(0),c=e.ref(!1),v=e.ref(2),p=e.ref(!1),m=e.ref("pingjia"),g=e.ref(""),x=e.ref(""),f=e.ref(null),b=e.ref(!1),h=e.ref([{num:0,money:20,zongMoney:0},{num:0,money:30,zongMoney:0}]),{mescrollInit:_,downCallback:y,getMescroll:w}=l.useMescroll(e.onPageScroll,e.onReachBottom);e.ref(!1);const z=e.ref({is_choujiang:0,lianxi_name:"",lianxi_mobile:"",lianxi_sex:""}),$=e.ref(!1),k=e.ref(!1),S=e.ref(""),A=e.ref(!1),j=e.ref(!1),F=e.ref(!1),C=e.ref(!1),L=e.ref(!1),I=e.ref(!1);e.ref(!1),e.ref(!1);const q=e.ref({isLiked:!1,likeCount:0}),D=e.ref(!1),T=e.ref([]),E=e.computed((()=>T.value.slice(0,6))),U=e.ref(!1),M=e.ref(!1),P=e.ref(!1),W=e.ref({disabled:!1,icon:"checkmark-circle",color:"#6AC086",checked:!1}),B=e.ref({latitude:null,longitude:null}),R=e.computed((()=>{var e,o;return a.getUserId()==(null==(o=null==(e=s.value)?void 0:e.user)?void 0:o.uid)})),N=e.computed((()=>{var e,o,l,t;const n=a.getUserId();return!(!n||!(null==(o=null==(e=s.value)?void 0:e.baoming_order)?void 0:o.order_id))&&n!=(null==(t=null==(l=s.value)?void 0:l.user)?void 0:t.uid)}));e.onShareTimeline((()=>({title:s.value.name,path:`/pages/bundle/index/activeInfo?id=${u.value}&pid=${t.store().$state.userInfo.uid}`,imageUrl:s.value.img_url}))),e.onShareAppMessage((async()=>await V()));const V=async()=>{var e,o,a,l,n;const r=u.value,i={title:"精彩活动等你来",path:`/pages/bundle/index/activeInfo?id=${r}&pid=${(null==(e=t.store().$state.userInfo)?void 0:e.uid)||0}`,imageUrl:(null==(l=null==(a=null==(o=t.store().$state.config)?void 0:o.img_config)?void 0:a.app_logo)?void 0:l.val)||""};try{if(!s.value||!s.value.name)return console.warn("活动信息未加载完成，使用默认分享信息"),await O(r,"default_share"),i;const e={title:s.value.name||i.title,path:`/pages/bundle/index/activeInfo?id=${r}&pid=${(null==(n=t.store().$state.userInfo)?void 0:n.uid)||0}`,imageUrl:s.value.img_url||i.imageUrl};O(r,"activity_share");const o=t.store().getActivityShareState(r),a=((null==o?void 0:o.shareCount)||0)+1;return t.store().updateActivityShare(r,a),console.log("分享数据构建成功:",e),e}catch(d){return console.error("分享配置失败:",d),await O(r,"share_error",d.message),i}},O=async(e,a,l=null)=>{let t=0;const n=async()=>{try{await o.userfenxiang_event({type:2,item_id:e,event_type:a,error_msg:l}),console.log(`分享统计成功 - 活动ID: ${e}, 事件类型: ${a}`)}catch(r){t++,console.warn(`分享统计失败 (第${t}次尝试):`,r),t<3?setTimeout((()=>n()),1e3*t):(console.error("分享统计最终失败，已达到最大重试次数"),H(e,a,l))}};setTimeout((()=>n()),0)},H=(o,a,l)=>{try{const t=e.index.getStorageSync("failedShareEvents")||[];t.push({activityId:o,eventType:a,errorMsg:l,timestamp:Date.now()}),t.length>100&&t.splice(0,t.length-100),e.index.setStorageSync("failedShareEvents",t)}catch(t){console.error("存储失败的分享事件时出错:",t)}};e.onPageScroll((e=>{d.value=e.scrollTop,e.scrollTop<300?L.value=!1:L.value=!0})),e.onLoad((async o=>{(null==o?void 0:o.id)&&(u.value=o.id),(null==o?void 0:o.pid)&&t.store().changePid(o.pid),(()=>{try{const e=a.getCurrentUser();e&&(z.value.lianxi_name=e.nickname||"",z.value.lianxi_mobile=e.mobile||"",z.value.lianxi_sex=0==e.sex?"1":(e.sex||"")+"")}catch(e){console.warn("初始化用户信息失败:",e)}})(),G(),t.store().loadActivityStateFromLocal();const l=t.store().getCachedActivityDetail(o.id);if(l&&Date.now()-l.cached_at<3e5)return console.log("使用缓存的活动详情"),s.value=l,void e.nextTick$1((()=>{setTimeout((()=>Z()),50)}));e.index.$on("closeAllPopups",Le)})),e.onShow((()=>{Z()}));const G=()=>{const e=a.getCurrentUser();F.value=1===(null==e?void 0:e.is_huiyuan)},J=async()=>{A.value=!1,m.value="xinxi",p.value=!0},K=()=>{A.value=!1,n.navto("/pages/bundle/user/vip")},Q=()=>{p.value||(p.value=!0,j.value=!0,m.value="erweima")},X=()=>{j.value=!1,m.value="erweima",p.value=!0},Y=async l=>{const t=1===v.value?a.getUserId()==s.value.user.uid?o.huodongget_baoming_list:o.huodongget_baoming_list_public:o.huodongget_pingjia;try{const e=await t({huodong_id:u.value,page:l.num,page_size:l.size});if(e&&"ok"===e.status){const o=e.data||[];1==l.num&&(h.value=[]),h.value=h.value.concat(o),l.endBySize(o.length,e.count||0)}else e&&"empty"===e.status?(1==l.num&&(h.value=[]),l.endBySize(0,0),console.log("数据为空:",e.msg||"暂无数据")):(console.warn("API返回异常:",e),l.endErr())}catch(n){console.error("请求失败:",n),l.endErr(),1==l.num&&e.index.$u.toast("网络错误，请检查网络连接")}},Z=async()=>{var l,n,r;C.value=!0;try{let d;oe();try{const l=a.getAuthParams(),t={huodong_id:u.value,uid:l.uid||0,token:l.token||""};if(console.log("调用活动详情API，参数:",t),d=await o.huodongget_info(t),console.log("活动详情API响应:",d),"ok"!==d.status){if(console.error("获取活动信息失败:",d),"relogin"===d.status){console.log("用户认证失败，清理本地状态");const{logout:o}=await"../../../utils/auth.js";o(),e.index.$u.toast("登录已过期，请重新登录"),setTimeout((()=>{e.index.navigateTo({url:"/pages/bundle/common/login"})}),1500)}else if("error"===d.status){const o=d.msg||d.message||"活动信息获取失败";e.index.$u.toast(o)}return void(C.value=!1)}}catch(i){return console.error("获取活动信息异常:",i),"NetworkError"===i.name||i.message.includes("network")||i.message.includes("timeout")?e.index.$u.toast("网络连接失败，请检查网络后重试"):console.warn("活动详情加载遇到问题，但可能不影响正常使用:",i),void(C.value=!1)}s.value=e.reactive({...d.data});try{const e=await o.userguanzhu_check({to_uid:d.data.user.uid});"ok"===e.status&&(s.value.is_guanzhu=e.data)}catch(i){console.warn("获取关注状态失败:",i),s.value.is_guanzhu=!1}try{const e=await o.huodongget_baoming_list_public({huodong_id:u.value,page:1,page_size:50});e&&e.data?s.value.peopleList=e.data:s.value.peopleList=[]}catch(i){console.warn("获取报名列表失败:",i),s.value.peopleList=[]}try{const e=await o.huodongget_pingjia({page:1,page_size:10,huodong_id:u.value});e&&e.data?(s.value.pingjiaList=e.data,s.value.pingjiaCount=e.count||0):(s.value.pingjiaList=[],s.value.pingjiaCount=0)}catch(i){console.warn("获取评价失败:",i),s.value.pingjiaList=[],s.value.pingjiaCount=0}try{await ue()}catch(i){console.warn("获取活动相册失败:",i)}if(I.value=!1,a.isLoggedIn()){const e=a.getUserId(),o=null==(l=s.value.peopleList)?void 0:l.some((o=>{var a;return(null==(a=o.user)?void 0:a.uid)===e}));let t=!1;(d.data.baoming_order&&d.data.baoming_order.order_id||o)&&(t=!0),I.value=t}try{const e=await o.configget_deep_link({path:"/pages/bundle/index/activeInfo",query:`id=${u.value}&pid=${(null==(n=t.store().$state.userInfo)?void 0:n.uid)||0}`});if(e&&"ok"===e.status){let o="";(null==(r=s.value.peopleList)?void 0:r.length)>0&&s.value.peopleList.forEach(((e,a)=>o+=`${a+1}. ${e.user.nickname}\r\n`)),S.value=`${s.value.name}\r\n时间：${s.value.time}\r\n地点：${s.value.sheng}-${s.value.shi}-${s.value.qu}-${s.value.addr}\r\n组织者：${s.value.lianxi_name}\r\n费用：${s.value.money}\r\n已报名：${s.value.baoming_num}/${s.value.num}\r\n${o}报名链接：${e.data}`}}catch(i){console.warn("获取分享链接失败:",i)}ge(),Fe(),Ce()}catch(i){console.error("获取活动信息失败:",i)}finally{C.value=!1}},ee=async()=>{if(x.value.trim())try{const l=await o.huodongreply_pingjia({uid:a.getUserId(),token:getToken(),pingjia_id:f.value.id,reply_content:x.value.trim()});"ok"===l.status?(b.value=!1,x.value="",f.value=null,e.index.$u.toast("回答成功"),await Z()):e.index.$u.toast(l.msg)}catch(l){console.error("回答提问失败:",l),e.index.$u.toast("回答失败，请重试")}else e.index.$u.toast("请输入回答内容")},oe=()=>{U.value=!1,M.value=!0,P.value=!1},ae=async()=>{if("pingjia"===m.value){if(!a.requireLogin("","请先登录后再提问"))return;if(!g.value.trim())return void e.index.$u.toast("请输入提问内容");const l=await o.huodongadd_pingjia({huodong_id:u.value,contents:g.value.trim()});"ok"===l.status?(p.value=!1,g.value="",z.value.lianxi_name="",z.value.lianxi_mobile="",e.index.$u.toast("提问提交成功"),await Z()):e.index.$u.toast(l.msg)}else if("xinxi"===m.value){p.value=!1;try{const a=await o.huodongadd_baoming({huodong_id:u.value,...z.value});if("ok"===a.status){const l=parseFloat(a.money||0),t=1===s.value.pay_type;if(0!==l&&t){const l={pay_type:1,type:2,order_id:a.order_id,money:a.money},t=await o.payweixin_pay(l);if("ok"===t.status){const l=await o.payget_weixinpay_sign({prepay_id:t.prepay_id});"requestPayment:ok"===(await n.pay(l)).errMsg?(e.index.$u.toast("支付成功！"),await Z(),m.value="erweima",p.value=!0):(e.index.$u.toast("支付失败，订单已取消"),await o.huodongdelete_baoming({order_id:a.order_id}))}else e.index.$u.toast(t.msg||"支付接口调用失败"),await o.huodongdelete_baoming({order_id:a.order_id})}else 0===l?e.index.$u.toast("报名成功！"):e.index.$u.toast("报名成功！请线下向发布方付款"),await Z(),m.value="erweima",p.value=!0}else e.index.$u.toast(a.msg||"报名失败")}catch(l){console.error("报名过程出错:",l),e.index.$u.toast("报名失败，请稍后重试")}}},le=async()=>{const a=s.value.is_shoucang?o.huodongshoucang_del({ids:u.value}):o.huodongshoucang_add({huodong_id:u.value}),l=await a;"ok"===l.status&&(s.value.is_shoucang=s.value.is_shoucang?0:1),e.index.$u.toast(l.msg)},te=async()=>{var o,a;W.value.checked?e.index.$u.toast("您已经签到过了"):W.value.disabled?e.index.$u.toast("当前不在签到时间范围内"):(null==(a=null==(o=s.value)?void 0:o.baoming_order)?void 0:a.order_id)?(0===s.value.is_online&&await ne(),await re()):e.index.$u.toast("请先报名参加活动")},ne=()=>new Promise(((o,a)=>{e.index.getLocation({type:"gcj02",success:e=>{B.value.latitude=e.latitude,B.value.longitude=e.longitude,o(e)},fail:o=>{console.error("获取位置失败:",o),e.index.showModal({title:"位置权限",content:"签到需要获取您的位置信息，请在设置中开启位置权限",showCancel:!1}),a(o)}})})),re=async()=>{var a,l,n;e.index.showLoading({title:"签到中..."});try{const r={huodong_id:u.value};if(0===s.value.is_online){if(!B.value.latitude||!B.value.longitude)return e.index.hideLoading(),void e.index.$u.toast("获取位置信息失败，请重试");r.lat=B.value.latitude,r.lng=B.value.longitude}const i=await o.huodongcheckin(r);if(e.index.hideLoading(),"ok"===i.status){W.value.checked=!0,W.value.disabled=!0,W.value.icon="checkmark-circle-fill",W.value.color="#6AC086";const o=(null==(a=i.data)?void 0:a.points_awarded)?`，获得${i.data.points_awarded}积分`:"",r=(null==(l=i.data)?void 0:l.distance)?`，距离活动地点${i.data.distance}米`:"";e.index.showModal({title:"签到成功",content:`签到成功${o}${r}`,showCancel:!1}),(null==(n=i.data)?void 0:n.points_balance)&&(t.store().$state.userInfo.points=i.data.points_balance)}else e.index.$u.toast(i.msg||"签到失败")}catch(r){e.index.hideLoading(),console.error("签到失败:",r),e.index.$u.toast("签到失败，请稍后重试")}},ie=()=>{var e,o;if(!s.value||!s.value.start_time)return"unknown";const a=new Date,l=s.value.baoming_end_time||s.value.start_time;new Date(null==(e=s.value.start_time)?void 0:e.replaceAll("-","/"));const t=s.value.end_time?new Date(null==(o=s.value.end_time)?void 0:o.replaceAll("-","/")):null;return a<new Date(null==l?void 0:l.replaceAll("-","/"))?"registration":t&&a>t?"ended":"ongoing"},ue=async()=>{try{const e=await o.huodongget_activity_photos({activity_id:u.value,page:1,page_size:10});"ok"===e.status?T.value=e.data||[]:"empty"===e.status&&(T.value=[])}catch(e){console.error("加载活动相册失败:",e),T.value=[]}},se=()=>{n.navto(`/pages/bundle/activity/album?activity_id=${u.value}`)},de=()=>{const e=ie();return"registration"===e||"ongoing"===e},ce=e.computed((()=>{if(!s.value)return!1;return parseFloat(s.value.money)>0||parseFloat(s.value.member_money)>0})),ve=e.computed((()=>!!s.value&&2===s.value.pay_type)),pe=e.computed((()=>{if(!s.value||!s.value.refund_rule)return[];switch(parseInt(s.value.refund_rule)){case 1:return[{time:"活动开始前",refund:"100%"},{time:"活动开始后",refund:"不支持退款"}];case 2:return[{time:"开始前12小时前",refund:"100%"},{time:"开始前12小时内",refund:"50%"},{time:"活动开始后",refund:"不支持退款"}];case 3:return[{time:"开始前24小时前",refund:"100%"},{time:"开始前24小时内",refund:"50%"},{time:"活动开始后",refund:"不支持退款"}];case 4:return[{time:"开始前48小时前",refund:"100%"},{time:"开始前48小时内",refund:"30%"},{time:"活动开始后",refund:"不支持退款"}];case 5:return[{time:"开始前72小时前",refund:"100%"},{time:"开始前72小时内",refund:"30%"},{time:"活动开始后",refund:"不支持退款"}];default:return[]}})),me=e.computed((()=>{if(!s.value||!s.value.refund_rule)return"";return{1:"随时退款",2:"12小时退款规则",3:"24小时退款规则",4:"48小时退款规则",5:"72小时退款规则"}[parseInt(s.value.refund_rule)]||""})),ge=()=>{var e;if(!s.value)return;if(!s.value.enable_checkin||"0"===s.value.enable_checkin)return W.value.disabled=!0,W.value.icon="close-circle",void(W.value.color="#999");const o=new Date,a=new Date(null==(e=s.value.start_time)?void 0:e.replaceAll("-","/")),l=new Date(a.getTime()-18e5),t=new Date(a.getTime()+18e5);o>=l&&o<=t?(W.value.disabled=!1,W.value.icon="checkmark-circle",W.value.color="#6AC086"):(W.value.disabled=!0,W.value.icon="clock",W.value.color="#999"),W.value.checked=!1},xe=async()=>{var l,t;const r=fe();if("取消报名"!==r){if("我要报名"===r){if(!a.requireLogin("","请先登录后再报名参加活动"))return;if(1==s.value.member_only){const o=a.getCurrentUser();if(!o||1!=o.is_huiyuan)return void e.index.showModal({title:"会员专享活动",content:"该活动仅限会员参加，是否前往开通会员？",confirmText:"开通会员",cancelText:"取消",success:function(e){e.confirm&&n.navto("/pages/bundle/user/vip")}})}return m.value="xinxi",void(p.value=!0)}e.index.$u.toast(r)}else{if(!(null==(t=null==(l=s.value)?void 0:l.baoming_order)?void 0:t.order_id))return void e.index.$u.toast("订单信息有误，请刷新重试");const a=(()=>{var e;if(!s.value||!s.value.baoming_order)return{amount:0,percentage:"0%"};const o=parseFloat(s.value.baoming_order.money||0);if(o<=0)return{amount:0,percentage:"0%"};const a=parseInt(s.value.refund_rule||1),l=new Date(null==(e=s.value.start_time)?void 0:e.replaceAll("-","/")),t=new Date;if(t>=l)return{amount:0,percentage:"0%"};const n=(l.getTime()-t.getTime())/36e5;let r=1;switch(a){case 1:default:r=1;break;case 2:r=n>=12?1:.5;break;case 3:r=n>=24?1:.5;break;case 4:r=n>=48?1:.3;break;case 5:r=n>=72?1:.3}return{amount:(o*r).toFixed(2),percentage:(100*r).toFixed(0)+"%"}})(),n=a.amount>0?`\n\n根据退款规则，您将获得 ${a.amount} 元退款（${a.percentage}）`:"\n\n根据退款规则，活动已开始不支持退款";e.index.showModal({title:"取消报名",content:`确定要取消报名吗？${n}`,success:async function(a){var l,t;if(a.confirm)try{const a=await o.huodongcancel_baoming({order_id:null==(t=null==(l=s.value)?void 0:l.baoming_order)?void 0:t.order_id});"ok"===a.status?(e.index.$u.toast("已取消报名"),s.value.baoming_order=null,I.value=!1,await Z()):e.index.$u.toast(a.msg||"取消失败")}catch(n){e.index.$u.toast("操作失败，请稍后重试")}}})}},fe=()=>{var e,o;if(!!(null==(o=null==(e=s.value)?void 0:e.baoming_order)?void 0:o.order_id)){const e=s.value.baoming_end_time||s.value.start_time;return 1*new Date(null==e?void 0:e.replaceAll("-","/"))>1*Date.now()?(k.value=!1,"取消报名"):0==s.value.choujiang_status?(k.value=!0,"等待抽奖"):1==s.value.choujiang_status?+s.value.choujiang_zhekou&&0<+s.value.choujiang_zhekou<=100?(k.value=!0,+s.value.choujiang_zhekou+"折"):(k.value=!0,"暂未中奖"):(k.value=!0,"暂无抽奖")}return s.value.baoming_num>=s.value.num?(k.value=!0,"报名已满"):(k.value=!1,"我要报名")},be=()=>{e.index.openLocation({latitude:Number(s.value.lat),longitude:Number(s.value.lng)})},he=()=>{var o;(null==(o=s.value)?void 0:o.img_url)&&e.index.previewImage({current:0,urls:[s.value.img_url]})};let _e;try{_e=r.getWindowInfo().screenWidth}catch(Me){console.warn("获取屏幕宽度失败，使用默认值:",Me),_e=375}const ye=4/750*_e,we=e.ref(!1),ze=e.ref(""),$e=e.ref(""),ke=e.ref(!0),Se=e.ref(null),Ae=()=>{we.value=!1},je=()=>{Se.value&&Se.value(),we.value=!1},Fe=()=>{var e,o,l,n;const r=u.value;if(!r)return;const i=(()=>{var e,o,l,n,r,i,d;const c=u.value,v=a.getUserId();if(!c||!v)return!1;const p=t.store().getActivityEnrollmentState(c);if(p&&p.isEnrolled)return console.log("从全局状态获取报名状态: 已报名"),!0;if(null==(o=null==(e=s.value)?void 0:e.baoming_order)?void 0:o.order_id)return console.log("从API数据获取报名状态: 已报名 (有订单ID)"),t.store().updateActivityEnrollment(c,!0,s.value.baoming_order.order_id,s.value.baoming_order.pay_status||1),!0;const m=(null==(n=null==(l=s.value)?void 0:l.peopleList)?void 0:n.some((e=>{var o;return(null==(o=e.user)?void 0:o.uid)===v})))||!1,g=a.getCurrentUser(),x=(null==(r=s.value)?void 0:r.baoming_num)>0&&(null==(d=null==(i=s.value)?void 0:i.peopleList)?void 0:d.some((e=>e.lianxi_name===(null==g?void 0:g.nickname)||e.lianxi_mobile===(null==g?void 0:g.mobile)))),f=m||x;return f&&(console.log("从报名列表推断报名状态: 已报名"),t.store().updateActivityEnrollment(c,!0,"inferred_enrollment",1)),f})(),d=(null==(o=null==(e=s.value)?void 0:e.baoming_order)?void 0:o.order_id)||null,c=(null==(n=null==(l=s.value)?void 0:l.baoming_order)?void 0:n.pay_status)||0;t.store().updateActivityEnrollment(r,i,d,c),I.value=i,console.log(`同步报名状态完成 - 活动ID: ${r}, 已报名: ${i}, 订单ID: ${d}`)},Ce=()=>{const e=u.value;if(!e)return;const o=t.store().getActivityLikeState(e);o?(q.value=o,s.value.is_zan=o.isLiked,s.value.zan_num=o.likeCount):q.value={isLiked:s.value.is_zan||!1,likeCount:s.value.zan_num||0};const a=t.store().getActivityFavoriteState(e);null!==a?(D.value=a,s.value.is_shoucang=a):D.value=s.value.is_shoucang||!1},Le=()=>{j.value=!1,"erweima"===m.value&&(p.value=!1,setTimeout((()=>{m.value=""}),300))};e.onUnload((()=>{e.index.$off("closeAllPopups")})),e.onMounted((()=>{oe()}));const Ie=()=>{e.index.navigateBack({delta:1,fail:o=>e.index.reLaunch({url:"/pages/index"})})},qe=()=>{var e;(null==(e=s.value.user)?void 0:e.uid)&&n.navto(`/pages/bundle/msg/personage?uid=${s.value.user.uid}`)},De=()=>{a.getAuthParams()?n.navto(`/pages/bundle/report/index?activity_id=${u.value}`):e.index.$u.toast("请先登录")},Te=e=>{(null==e?void 0:e.uid)&&n.navto(`/pages/bundle/msg/personage?uid=${e.uid}`)},Ee=()=>{n.navto(`/pages/bundle/index/addActive?huodong_id=${u.value}`)},Ue=()=>{e.index.showModal({title:"确认取消活动",content:"取消后活动将无法恢复，已报名用户将收到通知，确定要取消吗？",confirmText:"确定取消",confirmColor:"#dc3545",success:async a=>{if(a.confirm)try{e.index.showLoading({title:"正在取消活动..."});const a=await o.huodongcancel_huodong({huodong_id:u.value});e.index.hideLoading(),"ok"===a.status?(e.index.showToast({title:"活动已取消",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)):e.index.showToast({title:a.msg||"取消失败",icon:"none"})}catch(Me){e.index.hideLoading(),console.error("取消活动失败:",Me),e.index.showToast({title:"网络错误，请重试",icon:"none"})}}})};return(o,a)=>{var l,r,i,u,d,z,F,q,D;return e.e({a:C.value},C.value?{}:e.e({b:e.p({name:"arrow-left",color:L.value?"#333":"#fff",size:"60rpx"}),c:L.value?1:"",d:e.o(Ie),e:s.value.img_url,f:e.o(he),g:e.p({size:"140rpx",mode:"aspectFill",src:null==(l=s.value.user)?void 0:l.avatar,borderColor:"#fff",borderWidth:ye}),h:e.p({size:"32rpx",bold:!0,color:"#333",text:null==(r=s.value.user)?void 0:r.nickname}),i:e.p({size:"24rpx",color:"#666",text:s.value.name}),j:e.o(qe),k:I.value},I.value?{l:e.p({text:"活动联系方式",size:"32rpx",bold:!0,color:"#333"}),m:e.o(Q),n:e.p({text:"查看活动群二维码",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",shape:"circle",customStyle:{color:"#fff",fontWeight:"bold",fontSize:"24rpx",height:"70rpx",width:"45%"}})}:{},{o:e.t("registration"===ie()?"报名中":"ongoing"===ie()?"进行中":"已结束"),p:"registration"===ie()?1:"",q:"ongoing"===ie()?1:"",r:"ended"===ie()?1:"",s:e.p({name:"account-fill",color:"#FF6B35",size:"32rpx"}),t:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"活动人数："}),v:e.p({color:"#000",bold:!0,size:"28rpx",text:`${s.value.num||0}人`}),w:e.p({name:"clock-fill",color:"#6AC086",size:"32rpx"}),x:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"活动时间： "}),y:e.p({color:"#000",bold:!0,size:"28rpx",text:s.value.start_time||"待定"}),z:e.p({name:"calendar-fill",color:"#FF9500",size:"32rpx"}),A:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"报名截止："}),B:e.p({color:"#000",bold:!0,size:"28rpx",text:s.value.baoming_end_time||s.value.start_time}),C:1==s.value.member_only},1==s.value.member_only?{D:e.p({name:"star-fill",color:"#FFD700",size:"32rpx"}),E:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"参与权限："}),F:e.p({color:"#FFD700",bold:!0,size:"28rpx",text:"仅会员可报名"})}:{},{G:s.value.is_online},s.value.is_online?{H:e.p({name:"wifi",color:"#4caf50",size:"32rpx"}),I:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"活动方式："}),J:e.p({color:"#000",bold:!0,size:"28rpx",text:"线上活动"})}:{},{K:!s.value.is_online},s.value.is_online?{}:{L:e.p({name:"map-fill",color:"#007AFF",size:"32rpx"}),M:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"活动地点："}),N:e.o(be),O:e.p({"suffix-icon":"map-fill",color:"#000",bold:!0,size:"28rpx",text:`${s.value.sheng}-${s.value.shi}-${s.value.qu}-${s.value.addr}`})},{P:e.p({name:"rmb-circle-fill",color:"#FF3B30",size:"32rpx"}),Q:e.p({bold:!0,margin:"0 10rpx 0 0",color:"#000",size:"28rpx",text:"活动费用："}),R:0===parseFloat(s.value.money||0)&&0===parseFloat(s.value.member_money||0)},0===parseFloat(s.value.money||0)&&0===parseFloat(s.value.member_money||0)?{S:e.p({color:"#000",bold:!0,size:"28rpx",text:"免费"})}:parseFloat(s.value.money||0)===parseFloat(s.value.member_money||0)?{U:e.p({color:"#000",bold:!0,size:"28rpx",text:`¥${s.value.money||0}`})}:{V:e.p({color:"#000",bold:!0,size:"28rpx",text:`普通用户¥${s.value.money||0}/会员¥${s.value.member_money||0}`})},{T:parseFloat(s.value.money||0)===parseFloat(s.value.member_money||0),W:e.p({direction:"row",color:"rgba(106, 192, 134, 0.2)",margin:"20rpx 0 24rpx"}),X:e.f(s.value.peopleList,((o,a,l)=>{var t;return e.e({a:a<5},a<5?{b:"38b61e23-31-"+l+",38b61e23-30",c:e.p({size:"60rpx",mode:"aspectFill",src:null==(t=null==o?void 0:o.user)?void 0:t.avatar}),d:e.o((e=>Te(null==o?void 0:o.user)),a)}:{},{e:a})})),Y:(null==(i=s.value.peopleList)?void 0:i.length)>5},(null==(u=s.value.peopleList)?void 0:u.length)>5?{Z:e.p({text:"...",color:"#666",size:"30rpx"})}:{},{aa:e.o((o=>{c.value=!0,v.value=1,e.unref(w)().resetUpScroll(!0)})),ab:e.p({color:"#333",blod:!0,rTitle:"查看报名",icon:"arrow-down-fill","icon-style":{top:"4rpx",marginLeft:"10rpx",color:"#333",fontSize:"16rpx"},rSize:"26rpx"}),ac:e.unref(ce)},e.unref(ce)?e.e({ad:e.p({w:"6",h:"28",bg:"#FF6B35",c:"#333333",title:"退款政策",size:"32",blod:!0,margin:"0 0 30rpx 0"}),ae:e.unref(ve)},e.unref(ve)?{af:e.p({text:"线下收款退款政策",size:"28rpx",color:"#FF6B35",bold:!0,margin:"0 0 20rpx 0"}),ag:e.p({name:"info-circle-fill",color:"#FF9500",size:"28rpx"}),ah:e.p({text:"发布方选择线下付款，退款请与发布方协商",size:"26rpx",color:"#333333"})}:{ai:e.p({text:e.unref(me),size:"28rpx",color:"#FF6B35",bold:!0,margin:"0 0 20rpx 0"}),aj:e.f(e.unref(pe),((o,a,l)=>({a:e.t(o.time),b:e.t(o.refund),c:a,d:a===e.unref(pe).length-1?1:""}))),ak:e.p({name:"info-circle-fill",color:"#FF9500",size:"28rpx"}),al:e.p({text:"退款将在3-7个工作日内原路返回",size:"24rpx",color:"#666666"})}):{},{am:s.value.contents&&s.value.contents.trim()},s.value.contents&&s.value.contents.trim()?{an:e.p({w:"6",h:"28",bg:"#333333",c:"#333333",title:"活动介绍",size:"32"}),ao:e.p({height:"20rpx"}),ap:e.p({content:null==(d=s.value.contents)?void 0:d.replaceAll(" "," ")}),aq:e.p({height:"20rpx"}),ar:e.f(s.value.imgs,((o,a,l)=>({a:o,b:e.o((o=>(o=>{e.index.previewImage({current:o,urls:s.value.imgs})})(a)),a),c:"38b61e23-44-"+l,d:a}))),as:e.p({height:"20rpx"})}:{},{at:T.value.length>0},T.value.length>0?{av:e.o(se),aw:e.p({title:"往期图片",size:"28rpx",color:"#000",blod:!0,rTitle:"查看全部",icon:"arrow-down-fill","icon-style":{top:"4rpx",marginLeft:"10rpx",color:"#000",fontSize:"16rpx",transform:"rotate(-90deg)"},rSize:"20rpx"}),ax:e.p({height:"30rpx"}),ay:e.f(e.unref(E),((o,a,l)=>e.e({a:o.photo_url,b:5===a&&T.value.length>6},5===a&&T.value.length>6?{c:e.t(T.value.length-6)}:{},{d:o.id,e:e.o((o=>(o=>{const a=T.value.map((e=>e.photo_url));e.index.previewImage({urls:a,current:o})})(a)),o.id)})))}:{},{az:e.o((o=>{c.value=!0,v.value=2,e.unref(w)().resetUpScroll(!0)})),aA:e.p({title:`活动提问${s.value.pingjiaCount}`,size:"28rpx",color:"#000",blod:!0,rTitle:"全部提问",icon:"arrow-down-fill","icon-style":{top:"4rpx",marginLeft:"10rpx",color:"#000",fontSize:"16rpx",transform:"rotate(-90deg)"},rSize:"20rpx"}),aB:e.p({height:"30rpx"}),aC:e.f(s.value.pingjiaList,((o,a,l)=>{var t,n;return e.e({a:a<3},a<3?e.e({b:"38b61e23-49-"+l,c:e.p({size:"80rpx",mode:"aspectFill",src:null==(t=o.user)?void 0:t.avatar}),d:"38b61e23-50-"+l,e:e.p({margin:"0 10rpx 0",text:null==(n=o.user)?void 0:n.nickname,color:"#000",bold:!0,size:"24rpx"}),f:"38b61e23-51-"+l,g:e.p({content:o.contents}),h:"38b61e23-52-"+l,i:e.p({margin:"0 10rpx 0",text:o.time,color:"#000",size:"20rpx"}),j:o.is_replied&&o.reply_content},o.is_replied&&o.reply_content?{k:"38b61e23-53-"+l,l:e.p({text:"活动发布者回答：",color:"#6AC086",size:"22rpx",bold:!0}),m:"38b61e23-54-"+l,n:e.p({text:o.reply_content,color:"#333",size:"24rpx"}),o:"38b61e23-55-"+l,p:e.p({text:o.reply_time,color:"#999",size:"20rpx"})}:{},{q:e.unref(R)&&!o.is_replied},e.unref(R)&&!o.is_replied?{r:e.o((e=>{return a=o,f.value=a,x.value="",void(b.value=!0);var a}),a),s:"38b61e23-56-"+l,t:e.p({color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",text:"回答",size:"mini",customStyle:{color:"#fff",fontWeight:"bold",borderRadius:"20rpx",fontSize:"22rpx",width:"120rpx",height:"60rpx"}})}:{}):{},{v:a})})),aD:e.o((e=>{p.value=!0,m.value="pingjia"})),aE:e.p({color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",text:"提问",customStyle:{color:"#fff",fontWeight:"bold",borderRadius:"30rpx",fontSize:"26rpx"}}),aF:e.p({height:"60rpx"}),aG:e.p({src:null==(z=s.value.user)?void 0:z.avatar,mode:"aspectFill",size:"120rpx"}),aH:e.p({text:`活动发布者：${s.value.lianxi_name}`,size:"30rpx",bold:!0}),aI:e.t(e.unref(t.store)().$state.userInfo.gexingqianming),aJ:e.o((o=>{c.value=!0,v.value=1,e.unref(w)().resetUpScroll(!0)})),aK:e.p({text:`${s.value.baoming_num}人已报名`,color:"#333",size:"24rpx","suffix-icon":"arrow-down-fill","icon-style":{top:"2rpx",fontSize:"20rpx",color:"#333",transform:"rotate(-90deg)"}}),aL:e.o((o=>e.unref(n.navto)("/pages/bundle/common/xieyi?type=2"))),aM:e.p({align:"right",color:"#333",size:"24rpx","prefix-icon":"info-circle-fill","icon-style":{top:"2rpx",fontSize:"30rpx",color:"#333"},text:"报名须知"}),aN:e.f(s.value.peopleList,((o,a,l)=>e.e({a:a<=4},a<=4?{b:"38b61e23-63-"+l,c:e.p({size:"98rpx",mode:"aspectFill",src:o.user.avatar}),d:"38b61e23-64-"+l,e:e.p({margin:"16rpx 0 0",align:"center",size:"18rpx",text:o.user.nickname})}:{},{f:e.n(4===a?"":"mr50"),g:a,h:e.o((e=>Te(o.user)),a)}))),aO:e.unref(N)},e.unref(N)?{aP:e.p({text:"投诉/举报",size:"28rpx",color:"#007AFF",bold:!1}),aQ:e.o(De)}:{},{aR:e.p({height:"40","bg-color":""}),aS:e.p({name:"share-fill",size:"48rpx",color:"#6AC086"}),aT:e.o((e=>$.value=!0)),aU:e.p({color:"red",name:s.value.is_shoucang?"star-fill":"star",size:"60rpx"}),aV:e.o(le),aW:s.value.enable_checkin&&"0"!==s.value.enable_checkin&&s.value.baoming_order&&1===s.value.baoming_order.status},s.value.enable_checkin&&"0"!==s.value.enable_checkin&&s.value.baoming_order&&1===s.value.baoming_order.status?{aX:e.p({name:W.value.icon,color:W.value.color,size:"60rpx"}),aY:e.o(te),aZ:W.value.disabled}:{},{ba:(null==(F=s.value.user)?void 0:F.uid)!=e.unref(t.store)().$state.userInfo.uid},(null==(q=s.value.user)?void 0:q.uid)!=e.unref(t.store)().$state.userInfo.uid?{bb:e.o(xe),bc:e.p({text:fe(),color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",disabled:k.value,customStyle:{width:"200rpx",height:"80rpx",color:"#fff",fontSize:"30rpx",fontWeight:"bold",borderRadius:"40rpx"}})}:e.e({bd:de()},de()?{be:e.o(Ue),bf:e.p({text:"取消活动",color:"linear-gradient(135deg, #dc3545 0%, #c82333 100%)",customStyle:{width:"120rpx",height:"65rpx",color:"#fff",fontSize:"22rpx",fontWeight:"500",borderRadius:"32rpx",boxShadow:"0 4rpx 12rpx rgba(220, 53, 69, 0.25)"}}),bg:e.o(Ee),bh:e.p({text:"修改活动",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{width:"120rpx",height:"65rpx",color:"#fff",fontSize:"22rpx",fontWeight:"500",borderRadius:"32rpx",boxShadow:"0 4rpx 12rpx rgba(106, 192, 134, 0.25)"}})}:{bi:e.p({text:"活动已结束",color:"#999",size:"26rpx"})}),{bj:1===v.value},1===v.value?{bk:e.f(h.value,((o,a,l)=>{var t,r,i,u,s,d;return e.e({a:e.o((e=>Te(o.user)),a),b:"38b61e23-78-"+l+",38b61e23-77",c:e.p({size:"80rpx",src:null==(t=o.user)?void 0:t.avatar,mode:"aspectFill"}),d:"38b61e23-79-"+l+",38b61e23-77",e:e.p({"suffix-icon":o.lianxi_sex?1==o.lianxi_sex?"man":"woman":0==(null==(r=o.user)?void 0:r.sex)?"未知":1==(null==(i=o.user)?void 0:i.sex)?"man":"woman",iconStyle:{marginLeft:"10rpx",fontSize:"26rpx",color:o.lianxi_sex?1==o.lianxi_sex?"#5aa0ee":"#ec7a9e":0==(null==(u=o.user)?void 0:u.sex)?"未知":1==(null==(s=o.user)?void 0:s.sex)?"#5aa0ee":"#ec7a9e"},margin:"0 10rpx 0",text:o.lianxi_name||(null==(d=o.user)?void 0:d.nickname),color:"#000",bold:!0,size:"28rpx"}),f:o.lianxi_mobile},o.lianxi_mobile?{g:e.o((a=>e.unref(n.callPhone)(o.lianxi_mobile)),a),h:"38b61e23-80-"+l+",38b61e23-77",i:e.p({margin:"0 10rpx 0",text:o.lianxi_mobile,color:"#000",bold:!0,size:"28rpx"})}:{},{j:"38b61e23-81-"+l+",38b61e23-77",k:e.p({margin:"0 10rpx 0",text:o.pay_time,color:"#aaa",size:"20rpx"}),l:a})}))}:2===v.value?{bm:e.f(h.value,((o,a,l)=>{var t,n;return{a:"38b61e23-82-"+l+",38b61e23-77",b:e.p({size:"50rpx",src:null==(t=o.user)?void 0:t.avatar,mode:"aspectFill"}),c:"38b61e23-83-"+l+",38b61e23-77",d:e.p({margin:"0 10rpx 0",text:null==(n=o.user)?void 0:n.nickname,color:"#fff",bold:!0,size:"24rpx"}),e:"38b61e23-84-"+l+",38b61e23-77",f:e.p({content:o.contents}),g:"38b61e23-85-"+l+",38b61e23-77",h:e.p({margin:"0 10rpx 0",text:o.time,color:"#aaa",size:"20rpx"}),i:a}}))}:{},{bl:2===v.value,bn:e.o(e.unref(_)),bo:e.o(e.unref(y)),bp:e.o(Y),bq:e.o((e=>e.scrollTo(0))),br:e.p({up:{page:{num:0,size:20,time:null}},height:"1000rpx"}),bs:e.o((()=>{})),bt:e.o((e=>c.value=!1)),bv:e.p({show:c.value,"close-on-click-overlay":!0,round:"30rpx","safe-area-inset-bottom":!0,"bg-color":1===v.value?"#f7f7f7":"#333333"}),bw:"erweima"!=m.value},"erweima"!=m.value?e.e({bx:e.t("pingjia"===m.value?"提问输入框":"报名信息输入框"),by:"pingjia"===m.value},"pingjia"===m.value?{bz:e.o((e=>g.value=e)),bA:e.p({"adjust-position":!1,border:"surround",placeholder:"请输入您的提问...",height:240,maxlength:500,"show-confirm-bar":!1,customStyle:{borderColor:"#6AC086",borderRadius:"20rpx",padding:"10rpx 20rpx"},modelValue:g.value})}:"xinxi"===m.value?{bC:e.p({text:"点击确认即可完成报名，系统将自动使用您的账户信息",color:"#666",size:"28rpx",align:"center"})}:{},{bB:"xinxi"===m.value,bD:e.p({height:"50rpx"}),bE:e.o(ae),bF:e.p({shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{color:"#fff",fontWeight:"bold",fontSize:"30rpx",height:"90rpx"},text:"pingjia"===m.value?"提交":"确认报名"})}):e.e({bG:!j.value},j.value?j.value?e.e({bM:s.value.qun_qrcode},s.value.qun_qrcode?{bN:-1===s.value.qun_qrcode.indexOf("http")?e.unref(t.store)().$state.url+s.value.qun_qrcode:s.value.qun_qrcode}:{},{bO:e.p({text:"长按识别二维码或保存图片，以便进群获取详细信息",align:"center",bold:!0,color:"#333",size:"28rpx"}),bP:e.p({height:"40rpx"}),bQ:e.o(X),bR:e.p({text:"查看联系人二维码",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",shape:"circle",customStyle:{color:"#fff",fontWeight:"bold",fontSize:"24rpx",height:"70rpx",width:"45%"}})}):{}:e.e({bH:s.value.lianxi_qrcode},s.value.lianxi_qrcode?{bI:-1===s.value.lianxi_qrcode.indexOf("http")?e.unref(t.store)().$state.url+s.value.lianxi_qrcode:s.value.lianxi_qrcode}:{},{bJ:e.p({text:"长按识别二维码或保存图片，以便联系活动发起人",align:"center",bold:!0,color:"#333",size:"28rpx"}),bK:e.p({height:"40rpx"})}),{bL:j.value}),{bS:e.o((e=>p.value=!1)),bT:e.p({show:p.value,"close-on-click-overlay":!0,round:"30rpx",mode:"center","safe-area-inset-bottom":!1}),bU:e.p({"label-pos":"bottom",space:"20rpx",size:"88rpx",name:"weixin-circle-fill",color:"#17cd19",label:"分享页面"}),bV:e.o((o=>e.unref(n.copy)(S.value))),bW:e.p({"label-pos":"bottom",space:"20rpx",size:"88rpx",name:"file-text-fill",color:"#11bee9",label:"复制文案"}),bX:e.o((e=>$.value=!1)),bY:e.p({show:$.value,"close-on-click-overlay":!0,round:"30rpx","safe-area-inset-bottom":!0}),bZ:e.o((e=>b.value=!1)),ca:e.p({name:"close",size:"20"}),cb:e.t(null==(D=f.value)?void 0:D.contents),cc:e.o((e=>x.value=e)),cd:e.p({placeholder:"请输入您的回答...",height:200,maxlength:500,"show-confirm-bar":!1,customStyle:{borderColor:"#6AC086",borderRadius:"20rpx",padding:"20rpx"},modelValue:x.value}),ce:e.o((e=>b.value=!1)),cf:e.p({text:"取消",color:"#f5f5f5",customStyle:{color:"#666",fontWeight:"bold",borderRadius:"30rpx",fontSize:"26rpx",width:"45%"}}),cg:e.o(ee),ch:e.p({text:"提交回答",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{color:"#fff",fontWeight:"bold",borderRadius:"30rpx",fontSize:"26rpx",width:"45%"}}),ci:e.o((e=>b.value=!1)),cj:e.p({show:b.value,"close-on-click-overlay":!1,round:"30rpx","safe-area-inset-bottom":!0}),ck:e.p({text:"您还不是会员",size:"40rpx",bold:!0,color:"#333"}),cl:e.p({text:"成为会员可享受更多权益与优惠",size:"30rpx",color:"#666"}),cm:e.p({text:"是否立即加入会员？",size:"30rpx",color:"#666"}),cn:e.o(J),co:e.p({text:"单次报名(58元)",color:"linear-gradient(135deg, #FFB6C1 0%, #EE82EE 100%)",shape:"circle",customStyle:{marginBottom:"30rpx",color:"#fff",fontWeight:"bold",width:"70%"}}),cp:e.o(K),cq:e.p({text:"成为会员",color:"linear-gradient(135deg, #E6E6FA 0%, #DDA0DD 100%)",shape:"circle",customStyle:{color:"#fff",fontWeight:"bold",width:"70%"}}),cr:e.o((e=>A.value=!1)),cs:e.p({show:A.value,"close-on-click-overlay":!0,round:"30rpx",mode:"center"}),ct:e.t(ze.value),cv:e.t($e.value),cw:ke.value},ke.value?{cx:e.o(Ae),cy:e.p({text:"取消",size:"medium","custom-style":{margin:"0 10rpx"}})}:{},{cz:e.o(je),cA:e.p({text:"确定",type:"primary",size:"medium",color:"linear-gradient(103deg, #EE82EE 0%, #EE82EE 100%)"}),cB:e.o((e=>we.value=e)),cC:e.p({mode:"center",width:"80%","border-radius":"16",modelValue:we.value})}))}},__runtimeHooks:7};wx.createPage(s);
//# sourceMappingURL=activeInfo.js.map
