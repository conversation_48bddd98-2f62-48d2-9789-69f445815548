{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages/bundle/world/quote/index.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL3BhZ2VzL2J1bmRsZS93b3JsZC9xdW90ZS9pbmRleC52dWU"], "sourcesContent": ["<script setup>\nimport { ref, onMounted, onActivated, onUnmounted } from 'vue';\n// {{ AURA-X: Modify - 添加摘录评论和收藏功能API. Confirmed via 寸止 }}\nimport { getQuotes, likeQuote, favoriteQuote, getQuoteComments, postQuoteComment } from '@/api/index.js';\nimport { store } from '@/store';\nimport { navto } from '@/utils';\nimport { requireLogin } from '@/utils/auth';\n\n// 状态管理\nconst quotes = ref([]);\nconst loading = ref(true);\nconst refreshing = ref(false);\nconst loadingMore = ref(false);\nconst hasMore = ref(true);\nconst currentPage = ref(1);\nconst pageSize = 20;\n\n// 修复：添加摘录缓存机制，避免重复加载\nconst quoteCache = ref(new Map()); // 缓存摘录数据\nconst quoteCacheKey = 'quotes_list'; // 摘录缓存键\n\n// 生成缓存键\nconst generateQuoteCacheKey = () => {\n  const uid = store().$state.userInfo?.uid || 'anonymous';\n  return `${quoteCacheKey}_user_${uid}`;\n};\n\n// 加载摘录列表 - 修复：添加缓存机制和调试信息\nconst loadQuotes = async (page = 1, isRefresh = false) => {\n  try {\n    console.log('QuoteIndex: 开始加载摘录数据', { page, isRefresh });\n\n    if (isRefresh) {\n      refreshing.value = true;\n      currentPage.value = 1;\n    } else if (page > 1) {\n      loadingMore.value = true;\n    } else {\n      loading.value = true;\n\n      // 检查缓存（仅第一页且非刷新时）\n      if (page === 1 && !isRefresh) {\n        const cacheKey = generateQuoteCacheKey();\n        const cachedData = quoteCache.value.get(cacheKey);\n        if (cachedData && cachedData.timestamp && (Date.now() - cachedData.timestamp < 5 * 60 * 1000)) {\n          quotes.value = cachedData.data;\n          currentPage.value = cachedData.page;\n          hasMore.value = cachedData.hasMore;\n          loading.value = false;\n          console.log(`QuoteIndex: 从缓存恢复摘录数据，共 ${quotes.value.length} 条`);\n          return;\n        }\n      }\n    }\n\n    const params = {\n      page: page,\n      page_size: pageSize,\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || ''\n    };\n\n    console.log('QuoteIndex: API请求参数', params);\n    const res = await getQuotes(params);\n    console.log('QuoteIndex: API响应结果', res);\n\n    if (res.status === 'ok') {\n      const newQuotes = res.data?.list || [];\n      console.log('QuoteIndex: 获取到摘录数据', newQuotes.length, '条');\n\n      if (isRefresh || page === 1) {\n        quotes.value = newQuotes;\n\n        // 缓存第一页数据\n        if (page === 1) {\n          const cacheKey = generateQuoteCacheKey();\n          quoteCache.value.set(cacheKey, {\n            data: [...newQuotes],\n            page: page,\n            hasMore: newQuotes.length === pageSize,\n            timestamp: Date.now()\n          });\n          console.log(`QuoteIndex: 缓存摘录数据，共 ${newQuotes.length} 条`);\n        }\n      } else {\n        quotes.value = [...quotes.value, ...newQuotes];\n      }\n\n      hasMore.value = newQuotes.length === pageSize;\n      currentPage.value = page;\n    } else if (res.status === 'empty') {\n      console.log('QuoteIndex: 服务器返回空数据');\n      if (isRefresh || page === 1) {\n        quotes.value = [];\n      }\n      hasMore.value = false;\n    } else {\n      console.warn('QuoteIndex: API返回错误状态', res.status, res.msg);\n      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('QuoteIndex: 加载摘录失败:', error);\n    uni.showToast({ title: '加载失败', icon: 'none' });\n  } finally {\n    loading.value = false;\n    refreshing.value = false;\n    loadingMore.value = false;\n  }\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  loadQuotes(1, true);\n};\n\n// 上拉加载更多\nconst onLoadMore = () => {\n  if (!loadingMore.value && hasMore.value) {\n    loadQuotes(currentPage.value + 1);\n  }\n};\n\n// 查看摘录详情\nconst viewQuote = (quote) => {\n  console.log('查看摘录:', quote);\n  navto(`/pages/bundle/world/quote/detail?id=${quote.id}`);\n};\n\n// 点赞摘录\nconst handleLike = async (quote, event) => {\n  event.stopPropagation(); // 阻止事件冒泡\n\n  // 使用统一的登录校验\n  if (!requireLogin('', '请先登录后再点赞')) {\n    return;\n  }\n\n  try {\n    const res = await likeQuote({\n      id: quote.id,\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token\n    });\n\n    if (res.status === 'ok') {\n      // 更新本地状态\n      quote.is_liked = !quote.is_liked;\n      quote.like_count = quote.is_liked ? (quote.like_count || 0) + 1 : (quote.like_count || 1) - 1;\n\n      uni.showToast({\n        title: quote.is_liked ? '点赞成功' : '取消点赞',\n        icon: 'success'\n      });\n    } else {\n      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('点赞失败:', error);\n    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n  }\n};\n\n// 收藏摘录\nconst handleFavorite = async (quote, event) => {\n  event.stopPropagation(); // 阻止事件冒泡\n\n  // 使用统一的登录校验\n  if (!requireLogin('', '请先登录后再收藏')) {\n    return;\n  }\n\n  try {\n    const res = await favoriteQuote({\n      id: quote.id,\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token\n    });\n\n    if (res.status === 'ok') {\n      // 更新本地状态\n      quote.is_favorited = !quote.is_favorited;\n      quote.favorite_count = quote.is_favorited ? (quote.favorite_count || 0) + 1 : (quote.favorite_count || 1) - 1;\n\n      uni.showToast({\n        title: quote.is_favorited ? '收藏成功' : '取消收藏',\n        icon: 'success'\n      });\n    } else {\n      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('收藏失败:', error);\n    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n  }\n};\n\n// {{ AURA-X: Add - 添加摘录评论功能. Confirmed via 寸止 }}\n// 评论摘录\nconst handleComment = (quote, event) => {\n  event.stopPropagation(); // 阻止事件冒泡\n\n  // 使用统一的登录校验\n  if (!requireLogin('', '请先登录后再评论')) {\n    return;\n  }\n\n  // 跳转到摘录详情页面，显示评论区\n  navto(`/pages/bundle/world/quote/detail?id=${quote.id}&showComments=true`);\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n\n  // 修复iOS日期格式问题\n  const formattedTimeStr = timeStr.replace(/-/g, '/');\n  const time = new Date(formattedTimeStr);\n  const now = new Date();\n  const diff = now - time;\n\n  // 1小时内显示xx分钟前\n  if (diff < 3600000) { // 1小时 = 3600000毫秒\n    const minutes = Math.floor(diff / 60000);\n    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;\n  }\n\n  // 1天内显示xx小时前\n  if (diff < 86400000) { // 1天 = 86400000毫秒\n    const hours = Math.floor(diff / 3600000);\n    return `${hours}小时前`;\n  }\n\n  // 超过1天显示具体日期\n  const year = time.getFullYear();\n  const month = String(time.getMonth() + 1).padStart(2, '0');\n  const day = String(time.getDate()).padStart(2, '0');\n  const hours = String(time.getHours()).padStart(2, '0');\n  const minutes = String(time.getMinutes()).padStart(2, '0');\n\n  // 判断是否是今年\n  if (year === now.getFullYear()) {\n    return `${month}-${day} ${hours}:${minutes}`;\n  } else {\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\n  }\n};\n\n// {{ AURA-X: Remove - 移除重复的onMounted，统一在下方处理. Confirmed via 寸止. }}\n\n// {{ AURA-X: Add - 添加获取摘录图片和图片预览方法. Confirmed via 寸止 }}\n// 获取摘录的第一张图片\nconst getQuoteImage = (quote) => {\n  if (!quote.images) return null;\n\n  try {\n    // 如果images是字符串，尝试解析为JSON\n    if (typeof quote.images === 'string') {\n      const imageArray = JSON.parse(quote.images);\n      return Array.isArray(imageArray) && imageArray.length > 0 ? imageArray[0] : null;\n    }\n    // 如果images已经是数组\n    if (Array.isArray(quote.images)) {\n      return quote.images.length > 0 ? quote.images[0] : null;\n    }\n  } catch (error) {\n    console.warn('解析摘录图片失败:', error, quote.images);\n  }\n\n  return null;\n};\n\n// 预览图片\nconst previewImage = (imageUrl) => {\n  uni.previewImage({\n    urls: [imageUrl],\n    current: imageUrl\n  });\n};\n\n// {{ AURA-X: Add - 监听发布成功事件，自动刷新摘录列表. Confirmed via 寸止 }}\n// 监听发布成功事件\nconst handleRefreshList = () => {\n  console.log('QuoteIndex: 收到刷新事件，重新加载数据');\n  loadQuotes(1, true);\n};\n\n// {{ AURA-X: Modify - 修改为按需加载，不在挂载时自动加载. Confirmed via 寸止. }}\n// 组件挂载时不自动加载数据\nonMounted(() => {\n  console.log('QuoteIndex: 组件挂载，等待按需加载');\n\n  // 监听发布成功事件\n  uni.$on('refreshQuoteList', handleRefreshList);\n});\n\n// {{ AURA-X: Add - 添加外部调用接口，供父组件按需加载数据. Confirmed via 寸止. }}\n/**\n * 外部调用接口：加载摘录数据\n * 供父组件在切换到摘录tab时调用\n */\nconst loadQuoteData = () => {\n  console.log('QuoteIndex loadQuoteData - 被父组件调用');\n  loadQuotes();\n};\n\n// 暴露给父组件的方法\ndefineExpose({\n  loadQuoteData\n});\n\n// 组件卸载时移除事件监听\nonUnmounted(() => {\n  uni.$off('refreshQuoteList', handleRefreshList);\n});\n</script>\n\n<template>\n  <view class=\"quote-container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <u-loading-icon mode=\"circle\" size=\"30\" color=\"#6AC086\" />\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 摘录列表 -->\n    <scroll-view \n      v-else\n      class=\"quote-scroll\"\n      scroll-y\n      refresher-enabled\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"onLoadMore\"\n    >\n      <!-- 空状态 -->\n      <view v-if=\"quotes.length === 0\" class=\"empty-container\">\n        <u-empty mode=\"list\" text=\"还没有摘录哦\" description=\"收藏美好的文字片段\" />\n      </view>\n\n      <!-- {{ AURA-X: Modify - 从双列瀑布流改为单列布局 }} -->\n      <!-- 单列摘录列表 -->\n      <view v-else class=\"quote-list\">\n        <view\n          v-for=\"quote in quotes\"\n          :key=\"quote.id\"\n          class=\"quote-card\"\n          @click=\"viewQuote(quote)\"\n        >\n            <!-- 摘录内容 -->\n            <view class=\"quote-content\">\n              <text class=\"quote-text\">{{ quote.content }}</text>\n            </view>\n\n            <!-- {{ AURA-X: Add - 添加摘录图片显示功能. Confirmed via 寸止 }} -->\n            <!-- 摘录图片 -->\n            <view v-if=\"getQuoteImage(quote)\" class=\"quote-image-container\">\n              <image\n                :src=\"getQuoteImage(quote)\"\n                class=\"quote-image\"\n                mode=\"aspectFill\"\n                @click.stop=\"previewImage(getQuoteImage(quote))\"\n              ></image>\n            </view>\n\n            <!-- 作者和出处 -->\n            <view v-if=\"quote.author || quote.source\" class=\"quote-meta\">\n              <text v-if=\"quote.author\" class=\"quote-author\">— {{ quote.author }}</text>\n              <text v-if=\"quote.source\" class=\"quote-source\">《{{ quote.source }}》</text>\n            </view>\n\n            <!-- 底部信息 -->\n            <view class=\"quote-footer\">\n              <!-- 用户信息 -->\n              <view class=\"user-info\">\n                <image\n                  :src=\"quote.user?.avatar_url || '/static/default-avatar.png'\"\n                  class=\"user-avatar\"\n                  mode=\"aspectFill\"\n                ></image>\n                <text class=\"user-nickname\">{{ quote.user?.nickname || '匿名' }}</text>\n              </view>\n\n              <!-- 私密标识 -->\n              <view v-if=\"quote.privacy === 'private'\" class=\"privacy-badge\">\n                <u-icon name=\"lock\" size=\"10\" color=\"#999\" />\n              </view>\n            </view>\n\n            <!-- 时间和操作按钮 -->\n            <view class=\"quote-bottom\">\n              <view class=\"quote-time\">\n                <text class=\"time-text\">{{ formatTime(quote.created_at) }}</text>\n              </view>\n\n              <!-- 操作按钮 -->\n              <view class=\"quote-actions\">\n                <view class=\"action-btn\" @click=\"handleLike(quote, $event)\">\n                  <u-icon\n                    :name=\"quote.is_liked ? 'heart-fill' : 'heart'\"\n                    :color=\"quote.is_liked ? '#ff4757' : '#999'\"\n                    size=\"16\"\n                  />\n                  <text class=\"action-count\">{{ quote.like_count || 0 }}</text>\n                </view>\n\n                <view class=\"action-btn\" @click=\"handleFavorite(quote, $event)\">\n                  <u-icon\n                    :name=\"quote.is_favorited ? 'star-fill' : 'star'\"\n                    :color=\"quote.is_favorited ? '#ffa502' : '#999'\"\n                    size=\"16\"\n                  />\n                  <text class=\"action-count\">{{ quote.favorite_count || 0 }}</text>\n                </view>\n\n                <!-- {{ AURA-X: Add - 添加摘录评论按钮. Confirmed via 寸止 }} -->\n                <view class=\"action-btn\" @click=\"handleComment(quote, $event)\">\n                  <u-icon\n                    name=\"chat\"\n                    color=\"#999\"\n                    size=\"16\"\n                  />\n                  <text class=\"action-count\">{{ quote.comment_count || 0 }}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"loadingMore\" class=\"loading-more\">\n        <u-loading-icon mode=\"circle\" size=\"20\" color=\"#6AC086\" />\n        <text class=\"loading-more-text\">加载更多...</text>\n      </view>\n\n      <!-- 没有更多 -->\n      <view v-if=\"!hasMore && quotes.length > 0\" class=\"no-more\">\n        <text class=\"no-more-text\">没有更多了</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.quote-container {\n  height: 100%;\n  background-color: var(--color-background, #f8f9fa);\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400rpx;\n\n  .loading-text {\n    margin-top: var(--spacing-md, 20rpx);\n    font-size: var(--font-size-md, 28rpx);\n    color: var(--color-text-secondary, #666);\n  }\n}\n\n.quote-scroll {\n  height: 100%;\n}\n\n.empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n}\n\n/* {{ AURA-X: Modify - 从双列瀑布流改为单列布局样式 }} */\n.quote-list {\n  display: flex;\n  flex-direction: column;\n  padding: var(--spacing-md, 20rpx);\n  gap: var(--spacing-md, 20rpx);\n}\n\n.quote-card {\n  background: var(--color-surface, #ffffff);\n  border-radius: var(--radius-md, 16rpx);\n  padding: var(--spacing-lg, 24rpx);\n  box-shadow: var(--shadow-sm, 0 2rpx 12rpx rgba(0, 0, 0, 0.05));\n  border-left: 4rpx solid var(--color-primary, #6AC086);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  /* 扩大点击区域 */\n  margin: 8rpx;\n  min-height: 88rpx; /* 确保最小触摸区域 */\n}\n\n.quote-card:active {\n  transform: translateY(-2rpx);\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.15);\n  background: #f8f9fa;\n}\n\n.quote-card:hover {\n  box-shadow: 0 6rpx 20rpx rgba(106, 192, 134, 0.12);\n}\n\n.quote-content {\n  margin-bottom: 20rpx;\n\n  .quote-text {\n    font-size: 30rpx;\n    line-height: 1.8;\n    color: #333;\n    font-style: italic;\n  }\n}\n\n// {{ AURA-X: Add - 添加摘录图片样式. Confirmed via 寸止 }}\n.quote-image-container {\n  margin-bottom: 20rpx;\n\n  .quote-image {\n    width: 100%;\n    height: 300rpx;\n    border-radius: 12rpx;\n    background: #f5f5f5;\n  }\n}\n\n.quote-meta {\n  margin-bottom: 20rpx;\n  \n  .quote-author {\n    display: block;\n    font-size: 26rpx;\n    color: #666;\n    text-align: right;\n    margin-bottom: 8rpx;\n  }\n  \n  .quote-source {\n    display: block;\n    font-size: 24rpx;\n    color: #999;\n    text-align: right;\n  }\n}\n\n.quote-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 16rpx;\n  \n  .user-info {\n    display: flex;\n    align-items: center;\n    \n    .user-avatar {\n      width: 40rpx;\n      height: 40rpx;\n      border-radius: 50%;\n      margin-right: 12rpx;\n    }\n    \n    .user-nickname {\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n  \n  .privacy-badge {\n    padding: 4rpx 8rpx;\n    background: #f5f5f5;\n    border-radius: 12rpx;\n  }\n}\n\n.quote-bottom {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .quote-time {\n    .time-text {\n      font-size: 22rpx;\n      color: #999;\n    }\n  }\n\n  .quote-actions {\n    display: flex;\n    gap: 24rpx;\n\n    .action-btn {\n      display: flex;\n      align-items: center;\n      gap: 8rpx;\n      padding: 8rpx;\n      border-radius: 12rpx;\n      transition: background-color 0.2s ease;\n\n      &:active {\n        background-color: #f5f5f5;\n      }\n\n      .action-count {\n        font-size: 20rpx;\n        color: #999;\n        min-width: 20rpx;\n      }\n    }\n  }\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n  \n  .loading-more-text {\n    margin-left: 16rpx;\n    font-size: 28rpx;\n    color: #666;\n  }\n}\n\n.no-more {\n  display: flex;\n  justify-content: center;\n  padding: 40rpx;\n  \n  .no-more-text {\n    font-size: 28rpx;\n    color: #999;\n  }\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/quote/index.vue'\nwx.createComponent(Component)"], "names": ["quotes", "ref", "loading", "refreshing", "loadingMore", "hasMore", "currentPage", "quoteCache", "Map", "generateQuoteCacheKey", "store", "$state", "userInfo", "uid", "loadQuotes", "async", "page", "isRefresh", "console", "log", "value", "cache<PERSON>ey", "cachedData", "get", "timestamp", "Date", "now", "data", "length", "params", "page_size", "token", "res", "getQuotes", "status", "newQuotes", "_c", "list", "set", "warn", "msg", "showToast", "title", "icon", "error", "uni", "index", "onRefresh", "onLoadMore", "formatTime", "timeStr", "formattedTimeStr", "replace", "time", "diff", "minutes", "Math", "floor", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "getMinutes", "getQuoteImage", "quote", "images", "imageArray", "JSON", "parse", "Array", "isArray", "handleRefreshList", "onMounted", "$on", "expose", "loadQuoteData", "onUnmounted", "$off", "imageUrl", "previewImage", "urls", "current", "event", "stopPropagation", "requireLogin", "likeQuote", "id", "is_liked", "like_count", "favoriteQuote", "is_favorited", "favorite_count", "utils_index", "navto", "wx", "createComponent", "Component"], "mappings": "yzBASA,MAAMA,EAASC,EAAAA,IAAI,IACbC,EAAUD,EAAAA,KAAI,GACdE,EAAaF,EAAAA,KAAI,GACjBG,EAAcH,EAAAA,KAAI,GAClBI,EAAUJ,EAAAA,KAAI,GACdK,EAAcL,EAAAA,IAAI,GAIlBM,EAAaN,EAAGA,IAAC,IAAIO,KAIrBC,EAAwB,WAE5B,MAAO,qBADKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,MAAO,aACZ,EAI5BC,EAAaC,MAAOC,EAAO,EAAGC,GAAY,eAC1C,IAGF,GAFAC,QAAQC,IAAI,uBAAwB,CAAEH,OAAMC,cAExCA,EACFd,EAAWiB,OAAQ,EACnBd,EAAYc,MAAQ,OAC1B,GAAeJ,EAAO,EAChBZ,EAAYgB,OAAQ,OAKhB,GAHJlB,EAAQkB,OAAQ,EAGH,IAATJ,IAAeC,EAAW,CAC5B,MAAMI,EAAWZ,IACXa,EAAaf,EAAWa,MAAMG,IAAIF,GACpC,GAAAC,GAAcA,EAAWE,WAAcC,KAAKC,MAAQJ,EAAWE,UAAY,IAM7E,OALAxB,EAAOoB,MAAQE,EAAWK,KAC1BrB,EAAYc,MAAQE,EAAWN,KAC/BX,EAAQe,MAAQE,EAAWjB,QAC3BH,EAAQkB,OAAQ,OAChBF,QAAQC,IAAI,2BAA2BnB,EAAOoB,MAAMQ,WAGxD,CAGF,MAAMC,EAAS,CACbb,OACAc,UA1CW,GA2CXjB,KAAKH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,MAAO,EACrCkB,OAAOrB,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUmB,QAAS,IAGnCb,QAAAC,IAAI,sBAAuBU,GACnC,MAAMG,QAAYC,YAAUJ,GAGxB,GAFIX,QAAAC,IAAI,sBAAuBa,GAEhB,OAAfA,EAAIE,OAAiB,CACvB,MAAMC,GAAY,OAAAC,EAAAJ,EAAIL,WAAJ,EAAAS,EAAUC,OAAQ,GAGhC,GAFJnB,QAAQC,IAAI,sBAAuBgB,EAAUP,OAAQ,KAEjDX,GAAsB,IAATD,GAIf,GAHAhB,EAAOoB,MAAQe,EAGF,IAATnB,EAAY,CACd,MAAMK,EAAWZ,IACNF,EAAAa,MAAMkB,IAAIjB,EAAU,CAC7BM,KAAM,IAAIQ,GACVnB,OACAX,QAhEK,KAgEI8B,EAAUP,OACnBJ,UAAWC,KAAKC,QAEVR,QAAAC,IAAI,wBAAwBgB,EAAUP,WAChD,OAEA5B,EAAOoB,MAAQ,IAAIpB,EAAOoB,SAAUe,GAG9B9B,EAAAe,MAzEG,KAyEKe,EAAUP,OAC1BtB,EAAYc,MAAQJ,CAC1B,KAA8B,UAAfgB,EAAIE,QACbhB,QAAQC,IAAI,yBACRF,GAAsB,IAATD,KACfhB,EAAOoB,MAAQ,IAEjBf,EAAQe,OAAQ,IAEhBF,QAAQqB,KAAK,wBAAyBP,EAAIE,OAAQF,EAAIQ,aAClDC,UAAU,CAAEC,MAAOV,EAAIQ,KAAO,OAAQG,KAAM,SAEnD,OAAQC,GACC1B,QAAA0B,MAAM,sBAAuBA,GACrCC,EAAGC,MAACL,UAAU,CAAEC,MAAO,OAAQC,KAAM,QACzC,CAAY,QACRzC,EAAQkB,OAAQ,EAChBjB,EAAWiB,OAAQ,EACnBhB,EAAYgB,OAAQ,CACtB,GAII2B,EAAY,KAChBjC,EAAW,GAAG,EAAI,EAIdkC,EAAa,MACZ5C,EAAYgB,OAASf,EAAQe,OACrBN,EAAAR,EAAYc,MAAQ,EACjC,EA4FI6B,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAGrB,MAAMC,EAAmBD,EAAQE,QAAQ,KAAM,KACzCC,EAAO,IAAI5B,KAAK0B,GAChBzB,EAAM,IAAID,KACV6B,EAAO5B,EAAM2B,EAGnB,GAAIC,EAAO,KAAS,CAClB,MAAMC,EAAUC,KAAKC,MAAMH,EAAO,KAC3BC,OAAAA,GAAW,EAAI,KAAO,GAAGA,MAClC,CAGA,GAAID,EAAO,MAAU,CAEnB,MAAO,GADOE,KAAKC,MAAMH,EAAO,UAElC,CAGM,MAAAI,EAAOL,EAAKM,cACZC,EAAQC,OAAOR,EAAKS,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOR,EAAKY,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOR,EAAKc,YAAYJ,SAAS,EAAG,KAC5CR,EAAUM,OAAOR,EAAKe,cAAcL,SAAS,EAAG,KAGlD,OAAAL,IAAShC,EAAIiC,cACR,GAAGC,KAASI,KAAOE,KAASX,IAE5B,GAAGG,KAAQE,KAASI,KAAOE,KAASX,GAC7C,EAOIc,EAAiBC,IACrB,IAAKA,EAAMC,OAAe,OAAA,KAEtB,IAEE,GAAwB,iBAAjBD,EAAMC,OAAqB,CACpC,MAAMC,EAAaC,KAAKC,MAAMJ,EAAMC,QAC7B,OAAAI,MAAMC,QAAQJ,IAAeA,EAAW5C,OAAS,EAAI4C,EAAW,GAAK,IAC9E,CAEA,GAAIG,MAAMC,QAAQN,EAAMC,QACtB,OAAOD,EAAMC,OAAO3C,OAAS,EAAI0C,EAAMC,OAAO,GAAK,IAEtD,OAAQ3B,GACP1B,QAAQqB,KAAK,YAAaK,EAAO0B,EAAMC,OACzC,CAEO,OAAA,IAAA,EAaHM,EAAoB,KACxB3D,QAAQC,IAAI,6BACZL,EAAW,GAAG,EAAI,EAKpBgE,EAAAA,WAAU,KACR5D,QAAQC,IAAI,2BAGZ0B,EAAAA,MAAIkC,IAAI,mBAAoBF,EAAiB,WAclCG,EAAA,CACXC,cAPoB,KACpB/D,QAAQC,IAAI,4CAUd+D,EAAAA,aAAY,KACVrC,EAAAA,MAAIsC,KAAK,mBAAoBN,EAAiB,uSAxC1BO,YACpBvC,EAAAA,MAAIwC,aAAa,CACfC,KAAM,CAACF,GACPG,QAASH,IAHQ,IAACA,uiBA/IHrE,OAAOuD,EAAOkB,KAI/B,GAHAA,EAAMC,kBAGDC,EAAYA,aAAC,GAAI,YAIlB,IACI,MAAA1D,QAAY2D,YAAU,CAC1BC,GAAItB,EAAMsB,GACV/E,IAAKH,EAAKA,QAAGC,OAAOC,SAASC,IAC7BkB,MAAOrB,EAAKA,QAAGC,OAAOC,SAASmB,QAGd,OAAfC,EAAIE,QAEAoC,EAAAuB,UAAYvB,EAAMuB,SAClBvB,EAAAwB,WAAaxB,EAAMuB,UAAYvB,EAAMwB,YAAc,GAAK,GAAKxB,EAAMwB,YAAc,GAAK,EAE5FjD,EAAAA,MAAIJ,UAAU,CACZC,MAAO4B,EAAMuB,SAAW,OAAS,OACjClD,KAAM,qBAGJF,UAAU,CAAEC,MAAOV,EAAIQ,KAAO,OAAQG,KAAM,QAEnD,OAAQC,GACC1B,QAAA0B,MAAM,QAASA,GACvBC,EAAGC,MAACL,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC7C,uKAIqB5B,OAAOuD,EAAOkB,KAInC,GAHAA,EAAMC,kBAGDC,EAAYA,aAAC,GAAI,YAIlB,IACI,MAAA1D,QAAY+D,gBAAc,CAC9BH,GAAItB,EAAMsB,GACV/E,IAAKH,EAAKA,QAAGC,OAAOC,SAASC,IAC7BkB,MAAOrB,EAAKA,QAAGC,OAAOC,SAASmB,QAGd,OAAfC,EAAIE,QAEAoC,EAAA0B,cAAgB1B,EAAM0B,aACtB1B,EAAA2B,eAAiB3B,EAAM0B,cAAgB1B,EAAM2B,gBAAkB,GAAK,GAAK3B,EAAM2B,gBAAkB,GAAK,EAE5GpD,EAAAA,MAAIJ,UAAU,CACZC,MAAO4B,EAAM0B,aAAe,OAAS,OACrCrD,KAAM,qBAGJF,UAAU,CAAEC,MAAOV,EAAIQ,KAAO,OAAQG,KAAM,QAEnD,OAAQC,GACC1B,QAAA0B,MAAM,QAASA,GACvBC,EAAGC,MAACL,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC7C,sEAKoB,EAAC2B,EAAOkB,KAC5BA,EAAMC,kBAGDC,EAAYA,aAAC,GAAI,aAKjBQ,EAAAC,MAAC,uCAAuC7B,EAAMsB,uBAAsB,gCApFzD,CAACtB,IACTpD,QAAAC,IAAI,QAASmD,GACrB6B,EAAAA,MAAM,uCAAuC7B,EAAMsB,KAAI,6RC5HzDQ,GAAGC,gBAAgBC"}