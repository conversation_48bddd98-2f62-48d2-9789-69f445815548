{"version": 3, "file": "uni-share.js", "sources": ["../../../../../src/components/share-popup/uni-share.js"], "sourcesContent": ["import UniImageMenu from './uni-image-menu.js';\nclass UniShare extends UniImageMenu{\n\tconstructor(arg) {\n\t\tsuper()\n\t\tthis.isShow = super.isShow\n\t}\n\tasync show(param, callback){\n\t\tvar menus = []\n\t\tplus.share.getServices(services => { //只显示有服务的项目\n\t\t\tservices = services.filter(item => item.nativeClient)\n\t\t\tlet servicesList = services.map(e => e.id)\n\t\t\tparam.menus.forEach(item => {\n\t\t\t\tif (servicesList.includes(item.share.provider) || typeof(item.share) == 'string') {\n\t\t\t\t\tmenus.push(item)\n\t\t\t\t}\n\t\t\t})\n\t\t\tsuper.show({\n\t\t\t\tlist: menus,\n\t\t\t\tcancelText: param.cancelText\n\t\t\t}, e => {\n\t\t\t\tcallback(e)\n\t\t\t\tif(e.event == 'clickMenu'){\n\t\t\t\t\tif (typeof(menus[e.index]['share']) == 'string') {\n\t\t\t\t\t\tthis[menus[e.index]['share']](param)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.share({\n\t\t\t\t\t\t\t...param.content,\n\t\t\t\t\t\t\t...menus[e.index].share,\n\t\t\t\t\t\t\tsuccess: res=> {\n\t\t\t\t\t\t\t\tconsole.log(\"success:\" + JSON.stringify(res));\n\t\t\t\t\t\t\t\tsuper.hide()\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\tconsole.log(\"fail:\" + JSON.stringify(err));\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\tcontent: JSON.stringify(err),\n\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\tconfirmText: \"知道了\"\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t}, err => {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '获取服务供应商失败：' + JSON.stringify(err),\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '知道了'\n\t\t\t});\n\t\t\tconsole.error('获取服务供应商失败：' + JSON.stringify(err));\n\t\t})\n\t}\n\thide(){\n\t\tsuper.hide()\n\t}\n\tcopyurl(param) {\n\t\tconsole.log('copyurl',param);\n\t\tuni.setClipboardData({\n\t\t\tdata: param.content.href,\n\t\t\tsuccess: ()=>{\n\t\t\t\tconsole.log('success');\n\t\t\t\tuni.hideToast() //关闭自带的toast\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '复制成功',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsuper.hide();\n\t\t\t},\n\t\t\tfail: (err) => {\n\t\t\t\tuni.showModal({\n\t\t\t\t\tcontent: JSON.stringify(err),\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n\t// 使用系统分享发送分享消息 \n\tshareSystem(param) {\n\t\tconsole.log('shareSystem',param);\n\t\tplus.share.sendWithSystem({\n\t\t\ttype: 'text',\n\t\t\tcontent: param.content.title + param.content.summary || '',\n\t\t\thref: param.content.href,\n\t\t}, (e)=> {\n\t\t\tconsole.log('分享成功');\n\t\t\tsuper.hide()\n\t\t}, (err)=> {\n\t\t\tconsole.log('分享失败：' + JSON.stringify(err));\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '获取服务供应商失败：' + JSON.stringify(err),\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '知道了'\n\t\t\t});\n\t\t});\n\t}\n}\nexport default UniShare"], "names": ["UniShare", "UniImageMenu", "NvImageMenu", "constructor", "arg", "super", "this", "isShow", "show", "param", "callback", "menus", "plus", "share", "getServices", "services", "servicesList", "filter", "item", "nativeClient", "map", "e", "id", "for<PERSON>ach", "includes", "provider", "push", "list", "cancelText", "event", "index", "uni", "content", "success", "res", "console", "log", "JSON", "stringify", "hide", "fail", "err", "showModal", "showCancel", "confirmText", "title", "error", "copyurl", "setClipboardData", "data", "href", "hideToast", "showToast", "icon", "shareSystem", "sendWithSystem", "type", "summary"], "mappings": "wFACA,MAAMA,UAAiBC,EAAYC,YAClC,WAAAC,CAAYC,GACLC,QACNC,KAAKC,OAASF,MAAME,MACrB,CACA,UAAMC,CAAKC,EAAOC,GACjB,IAAIC,EAAQ,GACPC,KAAAC,MAAMC,aAAwBC,IAElC,IAAIC,GADJD,EAAWA,EAASE,QAAeC,GAAAA,EAAKC,gBACZC,KAAIC,GAAKA,EAAEC,KACjCb,EAAAE,MAAMY,SAAgBL,KACvBF,EAAaQ,SAASN,EAAKL,MAAMY,WAAmC,iBAAfP,EAAKL,QAC7DF,EAAMe,KAAKR,EACZ,IAEDb,MAAMG,KAAK,CACVmB,KAAMhB,EACNiB,WAAYnB,EAAMmB,aACXP,IACPX,EAASW,GACK,aAAXA,EAAEQ,QACmC,iBAA5BlB,EAAMU,EAAES,OAAc,MAChCxB,KAAKK,EAAMU,EAAES,OAAc,OAAGrB,GAE9BsB,EAAAA,MAAIlB,MAAM,IACNJ,EAAMuB,WACNrB,EAAMU,EAAES,OAAOjB,MAClBoB,QAAeC,IACdC,QAAQC,IAAI,WAAaC,KAAKC,UAAUJ,IACxC7B,MAAMkC,MAAK,EAEZC,KAAM,SAASC,GACdN,QAAQC,IAAI,QAAUC,KAAKC,UAAUG,IACrCV,EAAAA,MAAIW,UAAU,CACbV,QAASK,KAAKC,UAAUG,GACxBE,YAAY,EACZC,YAAa,OAEf,IAGH,GACA,IACQH,IACTV,EAAAA,MAAIW,UAAU,CACbG,MAAO,aAAeR,KAAKC,UAAUG,GACrCE,YAAY,EACZC,YAAa,QAEdT,QAAQW,MAAM,aAAeT,KAAKC,UAAUG,GAAI,GAElD,CACA,IAAAF,GACClC,MAAMkC,MACP,CACA,OAAAQ,CAAQtC,GACC0B,QAAAC,IAAI,UAAU3B,GACtBsB,EAAAA,MAAIiB,iBAAiB,CACpBC,KAAMxC,EAAMuB,QAAQkB,KACpBjB,QAAS,KACRE,QAAQC,IAAI,WACZL,EAAAA,MAAIoB,YACJpB,EAAAA,MAAIqB,UAAU,CACbP,MAAO,OACPQ,KAAM,SAEPhD,MAAMkC,MAAI,EAEXC,KAAOC,IACNV,EAAAA,MAAIW,UAAU,CACbV,QAASK,KAAKC,UAAUG,GACxBE,YAAY,GACZ,GAGJ,CAEA,WAAAW,CAAY7C,GACH0B,QAAAC,IAAI,cAAc3B,GAC1BG,KAAKC,MAAM0C,eAAe,CACzBC,KAAM,OACNxB,QAASvB,EAAMuB,QAAQa,MAAQpC,EAAMuB,QAAQyB,SAAW,GACxDP,KAAMzC,EAAMuB,QAAQkB,OACjB7B,IACHc,QAAQC,IAAI,QACZ/B,MAAMkC,MAAK,IACRE,IACHN,QAAQC,IAAI,QAAUC,KAAKC,UAAUG,IACrCV,EAAAA,MAAIW,UAAU,CACbG,MAAO,aAAeR,KAAKC,UAAUG,GACrCE,YAAY,EACZC,YAAa,OACb,GAEH"}