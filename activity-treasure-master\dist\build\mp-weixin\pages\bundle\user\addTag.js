"use strict";const e=require("../../../common/vendor.js");if(require("../../../utils/request.js"),require("../../../store/index.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/index.js"),require("../../../api/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-button")+e.resolveComponent("u-icon")+e.resolveComponent("u-gap")+e.resolveComponent("u-input")+e.resolveComponent("u-popup"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+o+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../node-modules/uview-plus/components/u-popup/u-popup.js"))();const o=()=>"../../../components/myLine.js",t={__name:"addTag",setup(o){const t=e.ref([1,2,3,4,5,6,7,8,9]),u=e.ref(!1);return(o,s)=>({a:e.o((e=>u.value=!0)),b:e.p({shape:"circle",color:"#5F84EF",text:"添加标签",customStyle:{width:"140rpx",height:"46rpx",fontSize:"20rpx"}}),c:e.p({bg:"#333333",w:"4",h:"26",title:"添加我的标签",size:"28"}),d:e.f(t.value,((o,u,s)=>({a:e.t(o),b:e.o((e=>t.value.splice(u,1)),u),c:"aba4c00a-2-"+s,d:u}))),e:e.p({size:"30rpx",name:"close-circle-fill"}),f:e.p({height:"40rpx"}),g:e.o((e=>u.value=!0)),h:e.p({shape:"circle",color:"#4DAAF2",text:"添加标签",customStyle:{width:"140rpx",height:"46rpx",fontSize:"20rpx"}}),i:e.p({bg:"#333333",w:"4",h:"26",title:"添加个人信息标签",size:"28"}),j:e.o((e=>o.text=e)),k:e.p({placeholder:"请输入标签（最多10字）",border:"none",maxlength:"10",customStyle:{width:"500rpx"},modelValue:o.text}),l:e.o((e=>{o.form.lableList.length<10?o.form.lableList.unshift(o.text):o.$u.toast("最多添加10个标签")})),m:e.p({shape:"circle",text:"添加",color:"linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)",customStyle:{margin:"0",width:"88rpx",border:"none",color:"#333333",fontSize:"30rpx"}}),n:e.o((e=>u.value=!1)),o:e.p({show:u.value,round:"20",mode:"center","safe-area-inset-bottom":!1})})},__runtimeHooks:1};wx.createPage(t);
//# sourceMappingURL=addTag.js.map
