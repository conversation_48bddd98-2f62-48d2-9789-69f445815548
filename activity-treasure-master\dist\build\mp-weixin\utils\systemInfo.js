"use strict";const e=require("../common/vendor.js");let t=null,n=0;const o=()=>{try{if(e.index.getWindowInfo)return e.index.getWindowInfo();if(void 0!==e.wx$1)try{const t=e.wx$1.getWindowInfo?e.wx$1.getWindowInfo():{};return{windowWidth:t.windowWidth||375,windowHeight:t.windowHeight||667,screenWidth:t.screenWidth||375,screenHeight:t.screenHeight||667,pixelRatio:t.pixelRatio||2,statusBarHeight:t.statusBarHeight||20,safeArea:t.safeArea||{top:0,left:0,right:375,bottom:667,width:375,height:667},safeAreaInsets:t.safeAreaInsets||{top:0,left:0,right:0,bottom:0}}}catch(t){console.warn("微信原生API获取窗口信息失败:",t)}console.warn("降级使用getSystemInfoSync获取窗口信息");try{const t=e.index.getSystemInfoSync();return{windowWidth:t.windowWidth,windowHeight:t.windowHeight,screenWidth:t.screenWidth,screenHeight:t.screenHeight,pixelRatio:t.pixelRatio,statusBarHeight:t.statusBarHeight,safeArea:t.safeArea,safeAreaInsets:t.safeAreaInsets}}catch(n){return console.error("获取窗口信息失败，使用默认值:",n),{windowWidth:375,windowHeight:667,screenWidth:375,screenHeight:667,pixelRatio:2,statusBarHeight:20,safeArea:{top:0,left:0,right:375,bottom:667,width:375,height:667},safeAreaInsets:{top:0,left:0,right:0,bottom:0}}}}catch(n){return console.warn("获取窗口信息失败:",n),{windowWidth:375,windowHeight:667,screenWidth:375,screenHeight:667,pixelRatio:2,statusBarHeight:20,safeArea:{top:0,left:0,right:375,bottom:667,width:375,height:667},safeAreaInsets:{top:0,left:0,right:0,bottom:0}}}};exports.getSystemInfo=()=>{const r=Date.now();if(t&&r-n<3e5)return t;try{const i=(()=>{try{if(e.index.getDeviceInfo)return e.index.getDeviceInfo();if(void 0!==e.wx$1)try{const t=e.wx$1.getDeviceInfo?e.wx$1.getDeviceInfo():{};return{brand:t.brand||"unknown",model:t.model||"unknown",platform:t.platform||"unknown",system:t.system||"unknown"}}catch(t){console.warn("微信原生API获取设备信息失败:",t)}console.warn("所有新API都不可用，降级使用getSystemInfoSync");try{const t=e.index.getSystemInfoSync();return{brand:t.brand,model:t.model,platform:t.platform,system:t.system}}catch(n){return console.error("旧API也失败，使用默认值:",n),{brand:"unknown",model:"unknown",platform:"unknown",system:"unknown"}}}catch(n){return console.warn("获取设备信息失败:",n),{brand:"unknown",model:"unknown",platform:"unknown",system:"unknown"}}})(),s=(()=>{try{if(e.index.getAppBaseInfo)return e.index.getAppBaseInfo();if(void 0!==e.wx$1)try{const t=e.wx$1.getAppBaseInfo?e.wx$1.getAppBaseInfo():{};return{version:t.version||"unknown",language:t.language||"zh_CN",theme:t.theme||"light"}}catch(t){console.warn("微信原生API获取应用信息失败:",t)}console.warn("降级使用getSystemInfoSync获取应用信息");try{const t=e.index.getSystemInfoSync();return{version:t.version,language:t.language,theme:t.theme}}catch(n){return console.error("获取应用信息失败，使用默认值:",n),{version:"unknown",language:"zh_CN",theme:"light"}}}catch(n){return console.warn("获取应用基础信息失败:",n),{version:"unknown",language:"zh_CN",theme:"light"}}})(),a=o(),h=(()=>{try{return e.index.getSystemSetting?e.index.getSystemSetting():{bluetoothEnabled:!1,locationEnabled:!1,wifiEnabled:!1}}catch(t){return console.warn("获取系统设置失败:",t),{bluetoothEnabled:!1,locationEnabled:!1,wifiEnabled:!1}}})(),d={brand:i.brand,model:i.model,platform:i.platform,system:i.system,version:s.version,language:s.language,theme:s.theme,windowWidth:a.windowWidth,windowHeight:a.windowHeight,screenWidth:a.screenWidth,screenHeight:a.screenHeight,pixelRatio:a.pixelRatio,statusBarHeight:a.statusBarHeight,safeArea:a.safeArea,safeAreaInsets:a.safeAreaInsets,bluetoothEnabled:h.bluetoothEnabled,locationEnabled:h.locationEnabled,wifiEnabled:h.wifiEnabled,_timestamp:r};return t=d,n=r,d}catch(i){console.error("获取系统信息失败，使用降级方案:",i);try{console.warn("使用最后的降级方案：getSystemInfoSync");const o=e.index.getSystemInfoSync();return t=o,n=r,o}catch(s){console.error("所有方案都失败，使用硬编码默认值:",s);const e={brand:"unknown",model:"unknown",platform:"unknown",system:"unknown",version:"unknown",language:"zh_CN",theme:"light",windowWidth:375,windowHeight:667,screenWidth:375,screenHeight:667,pixelRatio:2,statusBarHeight:20,safeArea:{top:0,left:0,right:375,bottom:667,width:375,height:667},safeAreaInsets:{top:0,left:0,right:0,bottom:0},bluetoothEnabled:!1,locationEnabled:!1,wifiEnabled:!1,_timestamp:r};return t=e,n=r,e}}},exports.getWindowInfo=o,exports.pxToRpx=e=>{const t=o();return 750*Number.parseInt(e)/t.windowWidth};
//# sourceMappingURL=systemInfo.js.map
