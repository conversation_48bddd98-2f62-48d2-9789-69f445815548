"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js");require("../../../store/index.js");const r=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-textarea")+e.resolveComponent("u-button"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js"))();const s={__name:"returnGood",setup(s){const o=e.ref({order_id:"",msg:""});e.onLoad((e=>{o.value.order_id=e.order_id}));const u=async()=>{if(o.value.msg){const s=await t.goodsshenqing_tuikuan(o.value);"ok"===s.status?r.back({tip:"提交成功，即将返回",time:2e3}):e.index.$u.toast(s.msg)}};return(t,r)=>({a:e.o((e=>o.value.msg=e)),b:e.p({placeholder:"请输入退款理由~",count:!0,maxlength:"200",modelValue:o.value.msg}),c:e.o(u),d:e.p({shape:"circle",color:"linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)",text:"提交",customStyle:{margin:"50rpx auto 0",color:"#333"}})})},__runtimeHooks:1};wx.createPage(s);
//# sourceMappingURL=returnGood.js.map
