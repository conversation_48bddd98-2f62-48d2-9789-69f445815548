"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),a=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||(r+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const r=()=>"../../../../components/customNavbar.js",l={__name:"search",setup(r){const l=e.ref(""),u=e.ref([]),n=e.ref(!1),s=e.ref(!1),t=e.ref(!0),i=e.ref(1),c=getCurrentPages(),d="select"===c[c.length-1].options.type,v=async(r=!1)=>{if(!(n.value||r&&s.value)){r?s.value=!0:(n.value=!0,i.value=1,u.value=[],t.value=!0);try{const n=await o.searchSources({uid:a.store.userInfo.uid,token:a.store.userInfo.token,keyword:l.value,page:i.value,page_size:20});if("ok"===n.status){const e=n.data.sources||[];u.value=r?[...u.value,...e]:e,t.value=20===e.length,t.value&&i.value++}else e.index.showToast({title:n.msg||"搜索失败",icon:"none"})}catch(c){console.error("搜索出处失败:",c),e.index.showToast({title:"搜索失败，请稍后重试",icon:"none"})}finally{n.value=!1,s.value=!1}}},p=o=>{d?(e.index.$emit("sourceSelected",o),e.index.navigateBack()):e.index.navigateTo({url:`/pages/bundle/world/source/detail?id=${o.id}`})},m=()=>{d?e.index.navigateTo({url:`/pages/bundle/world/source/create?type=select&keyword=${encodeURIComponent(l.value)}`}):e.index.navigateTo({url:"/pages/bundle/world/source/create"})};let g=null;const f=()=>{clearTimeout(g),g=setTimeout((()=>{v()}),500)},h=()=>{t.value&&!s.value&&v(!0)};return e.onMounted((()=>{v()})),e.index.$on("sourceCreated",(e=>{d?p(e):v()})),e.onUnmounted((()=>{e.index.$off("sourceCreated")})),(o,a)=>e.e({a:e.p({title:d?"选择出处":"搜索出处",backIcon:"arrow-left"}),b:e.p({name:"search",size:"20",color:"#999"}),c:e.o([e=>l.value=e.detail.value,f]),d:e.o(v),e:l.value,f:l.value},l.value?{g:e.p({name:"close-circle-fill",size:"18",color:"#ccc"}),h:e.o((e=>{l.value="",v()}))}:{},{i:n.value&&0===u.value.length},n.value&&0===u.value.length?{j:e.p({mode:"spinner",color:"#6AC086",size:"40"})}:0!==u.value.length||n.value?e.e({o:e.f(u.value,((o,a,r)=>e.e({a:o.cover_image},o.cover_image?{b:o.cover_image}:{c:"60959967-6-"+r,d:e.p({name:"bookmark",size:"32",color:"#6AC086"})},{e:e.t(o.name),f:o.publisher},o.publisher?{g:e.t(o.publisher)}:o.category?{i:e.t(o.category)}:{},{h:o.category,j:o.description},o.description?{k:e.t(o.description)}:{},{l:e.t(o.quote_count||0),m:"60959967-7-"+r,n:o.id,o:e.o((e=>p(o)),o.id)}))),p:e.p({name:"arrow-right",size:"16",color:"#ccc"}),q:t.value},t.value?e.e({r:s.value},s.value?{s:e.p({mode:"spinner",color:"#6AC086",size:"20"})}:{},{t:e.t(s.value?"加载中...":"加载更多"),v:e.o(h)}):{},{w:e.p({name:"plus",size:"16",color:"#6AC086"}),x:e.o(m)}):{l:e.p({name:"bookmark",size:"80",color:"#ddd"}),m:e.p({name:"plus",size:"16",color:"#6AC086"}),n:e.o(m)},{k:0===u.value.length&&!n.value})}},u=e._export_sfc(l,[["__scopeId","data-v-60959967"]]);wx.createPage(u);
//# sourceMappingURL=search.js.map
