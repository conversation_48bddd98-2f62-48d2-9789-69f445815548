"use strict";const t=require("../../../../common/vendor.js"),i={name:"u-icon",data:()=>({}),emits:["click"],mixins:[t.mpMixin,t.mixin,t.props$12],computed:{uClasses(){let i=[];return i.push(this.customPrefix+"-"+this.name),"uicon"==this.customPrefix?i.push("u-iconfont"):i.push(this.customPrefix),this.color&&t.config.type.includes(this.color)&&i.push("u-icon__icon--"+this.color),i},iconStyle(){let i={};return i={fontSize:t.addUnit(this.size),lineHeight:t.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:t.addUnit(this.top)},this.color&&!t.config.type.includes(this.color)&&(i.color=this.color),i},isImg(){return-1!==this.name.indexOf("/")},imgStyle(){let i={};return i.width=this.width?t.addUnit(this.width):t.addUnit(this.size),i.height=this.height?t.addUnit(this.height):t.addUnit(this.size),i},icon(){return"uicon"!==this.customPrefix?"":t.icons["uicon-"+this.name]||this.name}},methods:{addStyle:t.addStyle,addUnit:t.addUnit,clickHandler(t){this.$emit("click",this.index),this.stop&&this.preventEvent(t)}}};const e=t._export_sfc(i,[["render",function(i,e,s,o,n,l){return t.e({a:l.isImg},l.isImg?{b:i.name,c:i.imgMode,d:t.s(l.imgStyle),e:t.s(l.addStyle(i.customStyle))}:{f:t.t(l.icon),g:t.n(l.uClasses),h:t.s(l.iconStyle),i:t.s(l.addStyle(i.customStyle)),j:i.hoverClass},{k:""!==i.label},""!==i.label?{l:t.t(i.label),m:i.labelColor,n:l.addUnit(i.labelSize),o:"right"==i.labelPos?l.addUnit(i.space):0,p:"bottom"==i.labelPos?l.addUnit(i.space):0,q:"left"==i.labelPos?l.addUnit(i.space):0,r:"top"==i.labelPos?l.addUnit(i.space):0}:{},{s:t.o(((...t)=>l.clickHandler&&l.clickHandler(...t))),t:t.n("u-icon--"+i.labelPos)})}],["__scopeId","data-v-b9c4af5d"]]);wx.createComponent(e);
//# sourceMappingURL=u-icon.js.map
