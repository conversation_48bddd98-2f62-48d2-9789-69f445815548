{"version": 3, "file": "detail.js", "sources": ["../../../../../../../src/pages/bundle/world/diary/detail.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGRpYXJ5XGRldGFpbC52dWU"], "sourcesContent": ["<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\nimport { getDiaryDetail, deleteDiary, editDiary } from '@/api/index.js';\nimport { store } from '@/store';\nimport { navto } from '@/utils';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// 🔧 移除点赞和评论相关导入\n\n// {{ AURA-X: Fix - 添加缺失的变量定义. Confirmed via 寸止 }}\n// 状态管理\nconst diary = ref(null);\nconst loading = ref(true);\nconst diaryId = ref('');\nconst isAuthor = ref(false);\n\n// 基础状态管理\nconst isTransitioning = ref(false);\n\n// 添加缺失的变量定义\nconst contentHistory = ref([]);\nconst currentIndex = ref(0);\nconst isMember = ref(false);\n\n// 关闭页面方法\nconst closePage = () => {\n  uni.navigateBack();\n};\n\n// 检查用户会员状态\nconst checkMemberStatus = () => {\n  const userInfo = store().$state.userInfo;\n  if (!userInfo) return false;\n\n  if (userInfo.is_huiyuan == 1) {\n    if (!userInfo.huiyuan_end_time || new Date(userInfo.huiyuan_end_time) > new Date()) {\n      return true;\n    }\n  }\n  return false;\n};\n\n// {{ AURA-X: Fix - 添加页面加载调试信息. Confirmed via 寸止 }}\n// 获取URL参数\nonLoad((options) => {\n  console.log('🚀 日卡详情页面加载，参数:', options);\n  console.log('📱 触摸事件已绑定到容器元素');\n\n  if (options.id) {\n    diaryId.value = options.id;\n    loadDiaryDetail();\n  } else {\n    uni.showToast({ title: '参数错误', icon: 'none' });\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }\n});\n\n// {{ AURA-X: Add - 监听日记更新事件. Confirmed via 寸止. }}\n// 监听日记更新事件\nuni.$on('diary-updated', (data) => {\n  if (data.id === diaryId.value) {\n    // 重新加载日记详情\n    loadDiaryDetail();\n  }\n});\n\n// 页面卸载时移除事件监听\nonUnmounted(() => {\n  uni.$off('diary-updated');\n});\n\n// 加载日记详情\nconst loadDiaryDetail = async () => {\n  try {\n    loading.value = true;\n    \n    const res = await getDiaryDetail({\n      id: diaryId.value,\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || ''\n    });\n\n    if (res.status === 'ok') {\n      diary.value = res.data;\n      // 检查是否是作者\n      isAuthor.value = diary.value.user_id === store().$state.userInfo?.uid;\n\n      // {{ AURA-X: Add - 将原始日卡添加到历史栈. Confirmed via 寸止 }}\n      // 将原始日卡添加到历史栈\n      const diaryWithType = {\n        ...diary.value,\n        _type: 'diary'\n      };\n      contentHistory.value = [diaryWithType];\n      currentIndex.value = 0;\n\n      // 检查会员状态\n      isMember.value = checkMemberStatus();\n\n    } else {\n      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n    }\n  } catch (error) {\n    console.error('加载日记详情失败:', error);\n    uni.showToast({ title: '加载失败', icon: 'none' });\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n\n  const formattedTimeStr = timeStr.replace(/-/g, '/');\n  const time = new Date(formattedTimeStr);\n  const year = time.getFullYear();\n  const month = String(time.getMonth() + 1).padStart(2, '0');\n  const day = String(time.getDate()).padStart(2, '0');\n  const hours = String(time.getHours()).padStart(2, '0');\n  const minutes = String(time.getMinutes()).padStart(2, '0');\n\n  return `${year}-${month}-${day} ${hours}:${minutes}`;\n};\n\n// {{ AURA-X: Modify - 修复位置信息JSON解析错误，添加HTML实体解码处理. Confirmed via 寸止. }}\n// 安全解析位置信息\nconst getLocationName = (locationStr) => {\n  if (!locationStr) return '';\n\n  try {\n    // 如果是JSON字符串，尝试解析\n    if (typeof locationStr === 'string' && locationStr.startsWith('{')) {\n      // 先进行HTML实体解码处理\n      let decodedStr = locationStr\n        .replace(/&quot;/g, '\"')\n        .replace(/&amp;/g, '&')\n        .replace(/&lt;/g, '<')\n        .replace(/&gt;/g, '>')\n        .replace(/&#39;/g, \"'\");\n\n      const locationObj = JSON.parse(decodedStr);\n      return locationObj.name || locationObj.address || '未知位置';\n    }\n    // 如果是普通字符串，直接返回\n    return locationStr;\n  } catch (error) {\n    console.warn('位置信息解析失败:', error);\n    return locationStr || '未知位置';\n  }\n};\n\n// 编辑日记\nconst editDiaryAction = () => {\n  // {{ AURA-X: Add - 实现日记编辑跳转功能. Confirmed via 寸止. }}\n  navto(`/pages/bundle/world/diary/edit?id=${diaryId.value}`);\n};\n\n// 删除日记\nconst deleteDiaryAction = () => {\n  uni.showModal({\n    title: '确认删除',\n    content: '删除后无法恢复，确定要删除这篇日记吗？',\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          const result = await deleteDiary({\n            id: diaryId.value,\n            uid: store().$state.userInfo.uid,\n            token: store().$state.userInfo.token\n          });\n          \n          if (result.status === 'ok') {\n            uni.showToast({ title: '删除成功', icon: 'success' });\n            setTimeout(() => {\n              uni.navigateBack();\n            }, 1000);\n          } else {\n            uni.showToast({ title: result.msg || '删除失败', icon: 'none' });\n          }\n        } catch (error) {\n          console.error('删除日记失败:', error);\n          uni.showToast({ title: '删除失败', icon: 'none' });\n        }\n      }\n    }\n  });\n};\n\n// 预览图片\nconst previewImage = (current, images) => {\n  uni.previewImage({\n    current: current,\n    urls: images\n  });\n};\n\n// 移除了摘录相关功能\n\n// 移除了上滑切换功能\n\n// 移除了下拉回看功能\n\n// 移除了触摸事件处理\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n</script>\n\n<template>\n  <!-- {{ AURA-X: Fix - 恢复日记详情页面为简单布局. Confirmed via 寸止 }} -->\n  <view class=\"diary-detail-container\">\n    <!-- 顶部导航栏 -->\n    <view class=\"diary-header\">\n      <view class=\"back-button\" @click=\"closePage\">\n        <u-icon name=\"arrow-left\" color=\"#333\" size=\"20\"></u-icon>\n      </view>\n      <text class=\"header-title\">日记详情</text>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-state\">\n      <u-loading-icon mode=\"circle\" size=\"30\" color=\"#ffffff\"></u-loading-icon>\n    </view>\n\n    <!-- {{ AURA-X: Modify - 修改为全屏背景图片布局，去除卡片容器. Confirmed via 寸止 }} -->\n    <!-- 背景图片 -->\n    <view v-if=\"diary\" class=\"background-image\">\n      <image\n        v-if=\"diary.images && diary.images.length > 0\"\n        :src=\"diary.images[0]\"\n        class=\"full-background-image\"\n        mode=\"aspectFill\"\n      ></image>\n      <view v-else class=\"white-background\"></view>\n    </view>\n\n    <!-- {{ AURA-X: Modify - 重新布局用户信息和内容，文本移到用户信息下方. Confirmed via 寸止 }} -->\n    <!-- 内容区域 - 垂直布局 -->\n    <view v-if=\"diary\" class=\"content-overlay\">\n      <!-- 用户信息 - 顶部 -->\n      <view class=\"user-info-top\">\n        <image class=\"user-avatar-small\" :src=\"diary.user?.avatar_url || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\n        <view class=\"user-meta-overlay\">\n          <text class=\"user-nickname-overlay\">{{ diary.user?.nickname || '用户' }}</text>\n          <text class=\"post-time-overlay\">{{ formatTime(diary.created_at) }}</text>\n        </view>\n        <!-- 编辑和删除按钮，仅在当前用户是内容创建者时显示 -->\n        <view v-if=\"isAuthor\" class=\"action-buttons-overlay\">\n          <view class=\"edit-btn-overlay\" @click=\"editDiaryAction\">\n            <u-icon name=\"edit\" color=\"#6AC086\" size=\"16\"></u-icon>\n          </view>\n          <view class=\"delete-btn-overlay\" @click=\"deleteDiaryAction\">\n            <u-icon name=\"trash\" color=\"#666\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 内容文字 - 用户信息下方 -->\n      <view class=\"content-text-below\" :class=\"{ 'has-background': diary.images && diary.images.length > 0 }\">\n        <text class=\"diary-text-overlay\">{{ diary.content }}</text>\n\n        <!-- 位置信息 - 移到文本内容蒙版内的右下方 -->\n        <view v-if=\"diary.location\" class=\"location-info-inline\">\n          <u-icon name=\"map\" size=\"14\" :color=\"diary.images && diary.images.length > 0 ? '#ffffff' : '#666'\"></u-icon>\n          <text class=\"location-text-inline\" :class=\"{ 'white-text': diary.images && diary.images.length > 0 }\">{{ getLocationName(diary.location) }}</text>\n        </view>\n      </view>\n\n      <!-- 标签 -->\n      <view v-if=\"diary.tags\" class=\"tags-container-overlay\">\n        <view\n          v-for=\"tag in diary.tags.split(',')\"\n          :key=\"tag\"\n          class=\"tag-item-overlay\"\n          :class=\"{ 'has-background': diary.images && diary.images.length > 0 }\"\n        >\n          <text class=\"tag-text-overlay\" :class=\"{ 'white-text': diary.images && diary.images.length > 0 }\">#{{ tag.trim() }}</text>\n        </view>\n      </view>\n\n      <!-- 图片列表 - 横排显示所有图片 -->\n      <view v-if=\"diary.images && diary.images.length > 0\" class=\"images-horizontal\">\n        <view\n          v-for=\"(img, index) in diary.images\"\n          :key=\"index\"\n          class=\"image-item-horizontal\"\n          @click=\"previewImage(img, diary.images)\"\n        >\n          <image :src=\"img\" mode=\"aspectFill\" class=\"diary-image-horizontal\"></image>\n        </view>\n      </view>\n    </view>\n\n    <!-- 错误状态 -->\n    <view v-else class=\"error-state\">\n      <u-icon name=\"warning\" size=\"40\" color=\"#999\"></u-icon>\n      <text class=\"error-text\">加载失败，请重试</text>\n      <view class=\"retry-button\" @click=\"loadDiaryDetail\">\n        <text class=\"retry-text\">重新加载</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n/* {{ AURA-X: Modify - 修改日记详情页面样式，与日卡详情页面保持一致. Confirmed via 寸止 }} */\n@import '@/style/judu-theme.scss';\n\n/* {{ AURA-X: Modify - 修改为全屏背景图片布局样式. Confirmed via 寸止 }} */\n.diary-detail-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  width: 100vw;\n  position: relative;\n  background: #ffffff;\n  overflow: hidden;\n  animation: fadeIn 0.4s ease-out forwards;\n}\n\n/* 背景图片样式 */\n.background-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.full-background-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.white-background {\n  width: 100%;\n  height: 100%;\n  background: #ffffff;\n}\n\n.diary-header {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 60rpx 32rpx 20rpx 32rpx;\n  background: transparent;\n  z-index: 100;\n}\n\n.back-button {\n  width: 80rpx;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10rpx);\n  transition: background-color 0.3s ease;\n\n  &:active {\n    background: rgba(255, 255, 255, 0.3);\n  }\n}\n\n.header-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #ffffff;\n  position: absolute;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n/* {{ AURA-X: Modify - 重新设计内容覆盖层布局. Confirmed via 寸止 }} */\n/* 内容覆盖层 - 垂直布局 */\n.content-overlay {\n  position: absolute;\n  top: 140rpx;\n  left: 40rpx;\n  right: 40rpx;\n  z-index: 10;\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n/* 用户信息 - 顶部 */\n.user-info-top {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 16rpx 24rpx;\n  border-radius: 50rpx;\n  backdrop-filter: blur(10rpx);\n  align-self: flex-start;\n  max-width: 500rpx;\n}\n\n.user-avatar-small {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  margin-right: 16rpx;\n}\n\n.user-meta-overlay {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n}\n\n.user-nickname-overlay {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #333333;\n  line-height: 1.2;\n}\n\n.post-time-overlay {\n  font-size: 22rpx;\n  color: #666666;\n  margin-top: 4rpx;\n}\n\n/* {{ AURA-X: Add - 添加操作按钮容器和编辑按钮样式. Confirmed via 寸止. }} */\n.action-buttons-overlay {\n  display: flex;\n  align-items: center;\n  margin-left: 16rpx;\n}\n\n.edit-btn-overlay {\n  margin-right: 8rpx;\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.8);\n  transition: background-color 0.3s ease;\n\n  &:active {\n    background: rgba(255, 255, 255, 1);\n  }\n}\n\n.delete-btn-overlay {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.8);\n  transition: background-color 0.3s ease;\n\n  &:active {\n    background: rgba(255, 255, 255, 1);\n  }\n}\n\n.loading-state {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 10;\n}\n\n/* 内容文字样式 */\n.content-text-below {\n  padding: 32rpx 40rpx 50rpx 40rpx; /* 底部增加更多内边距为地址信息留空间 */\n  border-radius: 20rpx;\n  position: relative; /* 为内联地址信息提供定位基准 */\n  min-height: 120rpx; /* 设置最小高度，确保蒙版有足够高度 */\n  /* 移除固定高度，让内容自适应 */\n  width: 100%;\n  box-sizing: border-box;\n\n  /* 无背景图片时的样式 */\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10rpx);\n\n  /* 有背景图片时的样式 */\n  &.has-background {\n    background: rgba(0, 0, 0, 0.4);\n    backdrop-filter: blur(10rpx);\n  }\n}\n\n/* 日记文本样式 */\n.diary-text-overlay {\n  font-size: 28rpx;\n  line-height: 1.6;\n  color: #333;\n  word-wrap: break-word;\n  word-break: break-all;\n  white-space: pre-wrap; /* 保持换行符 */\n  display: block;\n  width: 100%;\n}\n\n/* 有背景图片时的文本颜色 */\n.content-text-below.has-background .diary-text-overlay {\n  color: #ffffff;\n}\n\n.diary-text-overlay {\n  font-size: 32rpx;\n  line-height: 1.6;\n  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;\n  letter-spacing: 1rpx;\n  font-weight: 400;\n\n  /* 默认深色文字 */\n  color: #333333;\n\n  /* 有背景图片时的白色文字 */\n  .has-background & {\n    color: #ffffff;\n    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\n  }\n}\n\n/* 内联位置信息样式 - 位于文本内容右下方 */\n.location-info-inline {\n  position: absolute;\n  bottom: 16rpx;\n  right: 16rpx;\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  /* 去除背景容器，直接显示在文本蒙版内 */\n}\n\n.location-text-inline {\n  font-size: 22rpx;\n  color: #666666;\n  opacity: 0.8;\n\n  &.white-text {\n    color: #ffffff;\n    text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);\n  }\n}\n\n/* 保留原有位置信息样式作为备用 */\n.location-info-overlay {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n  padding: 12rpx 20rpx;\n  border-radius: 50rpx;\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(10rpx);\n  align-self: flex-start;\n}\n\n.location-text-overlay {\n  font-size: 24rpx;\n  color: #666666;\n\n  &.white-text {\n    color: #ffffff;\n  }\n}\n\n/* 标签样式 */\n.tags-container-overlay {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.tag-item-overlay {\n  padding: 8rpx 16rpx;\n  border-radius: 50rpx;\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(10rpx);\n\n  &.has-background {\n    background: rgba(0, 0, 0, 0.3);\n  }\n}\n\n.tag-text-overlay {\n  font-size: 22rpx;\n  color: #666666;\n  font-weight: 500;\n\n  &.white-text {\n    color: #ffffff;\n  }\n}\n\n/* 横排图片样式 */\n.images-horizontal {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-top: 8rpx;\n}\n\n.image-item-horizontal {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(5rpx);\n}\n\n.diary-image-horizontal {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* {{ AURA-X: Delete - 删除旧的图片网格、位置信息和标签样式. Confirmed via 寸止 }} */\n\n.error-state {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 32rpx;\n}\n\n.error-text {\n  font-size: 28rpx;\n  color: #999999;\n}\n\n.retry-button {\n  background-color: #6AC086;\n  border-radius: 50rpx;\n  padding: 24rpx 48rpx;\n\n  &:active {\n    background-color: #5a9c73;\n  }\n}\n\n.retry-text {\n  font-size: 28rpx;\n  color: #ffffff;\n}\n\n// {{ AURA-X: Add - 添加双向滑动功能的样式. Confirmed via 寸止 }}\n.content-indicator {\n  display: flex;\n  justify-content: center;\n  gap: 32rpx;\n  margin-top: 40rpx;\n  padding: 20rpx 0;\n}\n\n.indicator-item {\n  padding: 12rpx 24rpx;\n  border-radius: 20rpx;\n  background: #f5f5f5;\n  transition: all 0.3s ease;\n\n  &.active {\n    background: #6AC086;\n\n    .indicator-text {\n      color: #ffffff;\n    }\n  }\n}\n\n.indicator-text {\n  font-size: 26rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n.swipe-hints {\n  margin-top: 32rpx;\n  padding: 24rpx;\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 16rpx;\n  text-align: center;\n}\n\n.hint-item {\n  margin-bottom: 16rpx;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  &.limit {\n    .hint-text {\n      color: #ff6b6b;\n    }\n  }\n}\n\n// 移除了摘录相关样式\n\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/diary/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["diary", "ref", "loading", "diaryId", "is<PERSON><PERSON><PERSON>", "contentHistory", "currentIndex", "isMember", "closePage", "uni", "index", "navigateBack", "common_vendor", "onLoad", "options", "console", "log", "id", "value", "showToast", "title", "icon", "setTimeout", "$on", "data", "onUnmounted", "$off", "loadDiaryDetail", "async", "res", "getDiaryDetail", "uid", "store", "$state", "userInfo", "token", "status", "user_id", "_c", "diaryWithType", "_type", "is_huiyuan", "huiyuan_end_time", "Date", "checkMemberStatus", "msg", "error", "formatTime", "timeStr", "formattedTimeStr", "replace", "time", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getLocationName", "locationStr", "startsWith", "decodedStr", "locationObj", "JSON", "parse", "name", "address", "warn", "editDiaryAction", "navto", "deleteDiaryAction", "showModal", "content", "success", "confirm", "result", "deleteDiary", "current", "images", "previewImage", "urls", "wx", "createPage", "MiniProgramPage"], "mappings": "qsBAYM,MAAAA,EAAQC,EAAAA,IAAI,MACZC,EAAUD,EAAAA,KAAI,GACdE,EAAUF,EAAAA,IAAI,IACdG,EAAWH,EAAAA,KAAI,GAGGA,EAAGA,KAAC,GAG5B,MAAMI,EAAiBJ,EAAAA,IAAI,IACrBK,EAAeL,EAAAA,IAAI,GACnBM,EAAWN,EAAAA,KAAI,GAGfO,EAAY,KAChBC,EAAGC,MAACC,cAAY,EAkBZC,EAAAC,QAAEC,IACEC,QAAAC,IAAI,kBAAmBF,GAC/BC,QAAQC,IAAI,mBAERF,EAAQG,IACVd,EAAQe,MAAQJ,EAAQG,SAGxBR,EAAGC,MAACS,UAAU,CAAEC,MAAO,OAAQC,KAAM,SACrCC,YAAW,KACTb,EAAGC,MAACC,cAAY,GACf,MACL,IAKFF,EAAAA,MAAIc,IAAI,iBAAkBC,IACpBA,EAAKP,KAAOd,EAAQe,UAGxB,IAIFO,EAAAA,aAAY,aACNC,KAAK,gBAAe,IAI1B,MAAMC,EAAkBC,oBAClB,IACF1B,EAAQgB,OAAQ,EAEV,MAAAW,QAAYC,iBAAe,CAC/Bb,GAAId,EAAQe,MACZa,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,KAGvC,GAAe,OAAfN,EAAIO,OAAiB,CACvBpC,EAAMkB,MAAQW,EAAIL,KAETpB,EAAAc,MAAQlB,EAAMkB,MAAMmB,WAAYL,OAAAA,IAAAA,QAAQC,OAAOC,eAAU,EAAAI,EAAAP,KAIlE,MAAMQ,EAAgB,IACjBvC,EAAMkB,MACTsB,MAAO,SAEMnC,EAAAa,MAAQ,CAACqB,GACxBjC,EAAaY,MAAQ,EAGrBX,EAASW,MArEW,MACxB,MAAMgB,EAAWF,EAAAA,QAAQC,OAAOC,SAChC,QAAKA,GAEsB,GAAvBA,EAASO,cACNP,EAASQ,kBAAoB,IAAIC,KAAKT,EAASQ,kBAAoB,IAAIC,KAIvE,EA4DcC,EAEvB,cACUzB,UAAU,CAAEC,MAAOS,EAAIgB,KAAO,OAAQxB,KAAM,SAChDC,YAAW,KACTb,EAAGC,MAACC,cAAY,GACf,KAEN,OAAQmC,GACC/B,QAAA+B,MAAM,YAAaA,GAC3BrC,EAAGC,MAACS,UAAU,CAAEC,MAAO,OAAQC,KAAM,QACzC,CAAY,QACRnB,EAAQgB,OAAQ,CAClB,GAII6B,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAErB,MAAMC,EAAmBD,EAAQE,QAAQ,KAAM,KACzCC,EAAO,IAAIR,KAAKM,GAOtB,MAAO,GANME,EAAKC,iBACJC,OAAOF,EAAKG,WAAa,GAAGC,SAAS,EAAG,QAC1CF,OAAOF,EAAKK,WAAWD,SAAS,EAAG,QACjCF,OAAOF,EAAKM,YAAYF,SAAS,EAAG,QAClCF,OAAOF,EAAKO,cAAcH,SAAS,EAAG,MAEX,EAKvCI,EAAmBC,IACvB,IAAKA,EAAoB,MAAA,GAErB,IAEF,GAA2B,iBAAhBA,GAA4BA,EAAYC,WAAW,KAAM,CAE9D,IAAAC,EAAaF,EACdV,QAAQ,UAAW,KACnBA,QAAQ,SAAU,KAClBA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KACjBA,QAAQ,SAAU,KAEf,MAAAa,EAAcC,KAAKC,MAAMH,GACxB,OAAAC,EAAYG,MAAQH,EAAYI,SAAW,MACpD,CAEO,OAAAP,CACR,OAAQd,GAEP,OADQ/B,QAAAqD,KAAK,YAAatB,GACnBc,GAAe,MACxB,GAIIS,EAAkB,KAEtBC,EAAAA,MAAM,qCAAqCnE,EAAQe,QAAO,EAItDqD,EAAoB,KACxB9D,EAAAA,MAAI+D,UAAU,CACZpD,MAAO,OACPqD,QAAS,sBACTC,QAAS9C,MAAOC,IACd,GAAIA,EAAI8C,QACF,IACI,MAAAC,QAAeC,cAAY,CAC/B5D,GAAId,EAAQe,MACZa,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,QAGX,OAAlByC,EAAOxC,QACT3B,EAAGC,MAACS,UAAU,CAAEC,MAAO,OAAQC,KAAM,YACrCC,YAAW,KACTb,EAAGC,MAACC,cAAY,GACf,cAECQ,UAAU,CAAEC,MAAOwD,EAAO/B,KAAO,OAAQxB,KAAM,QAEtD,OAAQyB,GACC/B,QAAA+B,MAAM,UAAWA,GACzBrC,EAAGC,MAACS,UAAU,CAAEC,MAAO,OAAQC,KAAM,QACvC,CACF,GAEH,6tCAImByD,IAASC,sBAC7BtE,EAAAA,MAAIuE,aAAa,CACfF,UACAG,KAAMF,IAHW,IAACD,EAASC,mICnM/BG,GAAGC,WAAWC"}