{"version": 3, "file": "node.js", "sources": ["../../../../../../../../node_modules/uview-plus/components/u-parse/node/node.vue", "../../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBhcnNlL25vZGUvbm9kZS52dWU"], "sourcesContent": ["<template>\n  <view :id=\"attrs.id\" :class=\"'_block _'+name+' '+attrs.class\" :style=\"attrs.style\">\n    <block v-for=\"(n, i) in childs\" v-bind:key=\"i\">\n      <!-- 图片 -->\n      <!-- 占位图 -->\n      <image v-if=\"n.name==='img'&&!n.t&&((opts[1]&&!ctrl[i])||ctrl[i]<0)\" class=\"_img\" :style=\"n.attrs.style\" :src=\"ctrl[i]<0?opts[2]:opts[1]\" mode=\"widthFix\" />\n      <!-- 显示图片 -->\n      <!-- #ifdef H5 || (APP-PLUS && VUE2) -->\n      <img v-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\n      <!-- #endif -->\n      <!-- #ifndef H5 || (APP-PLUS && VUE2) -->\n      <!-- 表格中的图片，使用 rich-text 防止大小不正确 -->\n      <rich-text v-if=\"n.name==='img'&&n.t\" :style=\"'display:'+n.t\" :nodes=\"'<img class=\\'_img\\' style=\\''+n.attrs.style+'\\' src=\\''+n.attrs.src+'\\'>'\" :data-i=\"i\" @tap.stop=\"imgTap\" />\n      <!-- #endif -->\n      <!-- #ifndef H5 || APP-PLUS -->\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;height:1px;'+n.attrs.style\" :src=\"n.attrs.src\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':'')\" :lazy-load=\"opts[0]\" :webp=\"n.webp\" :show-menu-by-longpress=\"opts[3]&&!n.attrs.ignore\" :image-menu-prevent=\"!opts[3]||n.attrs.ignore\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\n      <!-- #endif -->\n      <!-- #ifdef APP-PLUS && VUE3 -->\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;'+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':'')\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\n      <!-- #endif -->\n      <!-- 文本 -->\n      <!-- #ifdef MP-WEIXIN -->\n      <text v-else-if=\"n.text\" :user-select=\"opts[4]=='force'&&isiOS\" decode>{{n.text}}</text>\n      <!-- #endif -->\n      <!-- #ifndef MP-WEIXIN || MP-BAIDU || MP-ALIPAY || MP-TOUTIAO -->\n      <text v-else-if=\"n.text\" decode>{{n.text}}</text>\n      <!-- #endif -->\n      <text v-else-if=\"n.name==='br'\">\\n</text>\n      <!-- 链接 -->\n      <view v-else-if=\"n.name==='a'\" :id=\"n.attrs.id\" :class=\"(n.attrs.href?'_a ':'')+n.attrs.class\" hover-class=\"_hover\" :style=\"'display:inline;'+n.attrs.style\" :data-i=\"i\" @tap.stop=\"linkTap\">\n        <node name=\"span\" :childs=\"n.children\" :opts=\"opts\" style=\"display:inherit\" />\n      </view>\n      <!-- 视频 -->\n      <!-- #ifdef APP-PLUS -->\n      <view v-else-if=\"n.html\" :id=\"n.attrs.id\" :class=\"'_video '+n.attrs.class\" :style=\"n.attrs.style\" v-html=\"n.html\" @vplay.stop=\"play\" />\n      <!-- #endif -->\n      <!-- #ifndef APP-PLUS -->\n      <video v-else-if=\"n.name==='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :object-fit=\"n.attrs['object-fit']\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\n      <!-- #endif -->\n      <!-- #ifdef H5 || APP-PLUS -->\n      <iframe v-else-if=\"n.name==='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\" :src=\"n.attrs.src\" />\n      <embed v-else-if=\"n.name==='embed'\" :style=\"n.attrs.style\" :src=\"n.attrs.src\" />\n      <!-- #endif -->\n      <!-- #ifndef MP-TOUTIAO || ((H5 || APP-PLUS) && VUE3) -->\n      <!-- 音频 -->\n      <audio v-else-if=\"n.name==='audio'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\n      <!-- #endif -->\n      <view v-else-if=\"(n.name==='table'&&n.c)||n.name==='li'\" :id=\"n.attrs.id\" :class=\"'_'+n.name+' '+n.attrs.class\" :style=\"n.attrs.style\">\n        <node v-if=\"n.name==='li'\" :childs=\"n.children\" :opts=\"opts\" />\n        <view v-else v-for=\"(tbody, x) in n.children\" v-bind:key=\"x\" :class=\"'_'+tbody.name+' '+tbody.attrs.class\" :style=\"tbody.attrs.style\">\n          <node v-if=\"tbody.name==='td'||tbody.name==='th'\" :childs=\"tbody.children\" :opts=\"opts\" />\n          <block v-else v-for=\"(tr, y) in tbody.children\" v-bind:key=\"y\">\n            <view v-if=\"tr.name==='td'||tr.name==='th'\" :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\n              <node :childs=\"tr.children\" :opts=\"opts\" />\n            </view>\n            <view v-else :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\n              <view v-for=\"(td, z) in tr.children\" v-bind:key=\"z\" :class=\"'_'+td.name+' '+td.attrs.class\" :style=\"td.attrs.style\">\n                <node :childs=\"td.children\" :opts=\"opts\" />\n              </view>\n            </view>\n          </block>\n        </view>\n      </view>\n\n      <!-- 富文本 -->\n      <!-- #ifdef H5 || ((MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE2) -->\n      <rich-text v-else-if=\"!n.c&&!handler.isInline(n.name, n.attrs.style)\" :id=\"n.attrs.id\" :style=\"n.f\" :user-select=\"opts[4]\" :nodes=\"[n]\" />\n      <!-- #endif -->\n      <!-- #ifndef H5 || ((MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE2) -->\n      <rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :style=\"n.f+';display:inline'\" :preview=\"false\" :selectable=\"opts[4]\" :user-select=\"opts[4]\" :nodes=\"[n]\" />\n      <!-- #endif -->\n      <!-- 继续递归 -->\n      <view v-else-if=\"n.c===2\" :id=\"n.attrs.id\" :class=\"'_block _'+n.name+' '+n.attrs.class\" :style=\"n.f+';'+n.attrs.style\">\n        <node v-for=\"(n2, j) in n.children\" v-bind:key=\"j\" :style=\"n2.f\" :name=\"n2.name\" :attrs=\"n2.attrs\" :childs=\"n2.children\" :opts=\"opts\" />\n      </view>\n      <node v-else :style=\"n.f\" :name=\"n.name\" :attrs=\"n.attrs\" :childs=\"n.children\" :opts=\"opts\" />\n    </block>\n  </view>\n</template>\n<script module=\"handler\" lang=\"wxs\">\n// 行内标签列表\nvar inlineTags = {\n  abbr: true,\n  b: true,\n  big: true,\n  code: true,\n  del: true,\n  em: true,\n  i: true,\n  ins: true,\n  label: true,\n  q: true,\n  small: true,\n  span: true,\n  strong: true,\n  sub: true,\n  sup: true\n}\n/**\n * @description 判断是否为行内标签\n */\nmodule.exports = {\n  isInline: function (tagName, style) {\n    return inlineTags[tagName] || (style || '').indexOf('display:inline') !== -1\n  }\n}\n</script>\n<script>\n\nimport node from './node'\nexport default {\n  name: 'node',\n  options: {\n    // #ifdef MP-WEIXIN\n    virtualHost: true,\n    // #endif\n    // #ifdef MP-TOUTIAO\n    addGlobalClass: false\n    // #endif\n  },\n  data () {\n    return {\n      ctrl: {},\n      // #ifdef MP-WEIXIN\n      isiOS: uni.getSystemInfoSync().system.includes('iOS')\n      // #endif\n    }\n  },\n  props: {\n    name: String,\n    attrs: {\n      type: Object,\n      default () {\n        return {}\n      }\n    },\n    childs: Array,\n    opts: Array\n  },\n  components: {\n\n    // #ifndef (H5 || APP-PLUS) && VUE3\n    node\n    // #endif\n  },\n  mounted () {\n    this.$nextTick(() => {\n      for (this.root = this.$parent; this.root.$options.name !== 'u-parse'; this.root = this.root.$parent);\n    })\n    // #ifdef H5 || APP-PLUS\n    if (this.opts[0]) {\n      let i\n      for (i = this.childs.length; i--;) {\n        if (this.childs[i].name === 'img') break\n      }\n      if (i !== -1) {\n        this.observer = uni.createIntersectionObserver(this).relativeToViewport({\n          top: 500,\n          bottom: 500\n        })\n        this.observer.observe('._img', res => {\n          if (res.intersectionRatio) {\n            this.$set(this.ctrl, 'load', 1)\n            this.observer.disconnect()\n          }\n        })\n      }\n    }\n    // #endif\n  },\n  beforeUnmount () {\n    // #ifdef H5 || APP-PLUS\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n    // #endif\n  },\n  methods:{\n    // #ifdef MP-WEIXIN\n    toJSON () { return this },\n    // #endif\n    /**\n     * @description 播放视频事件\n     * @param {Event} e\n     */\n    play (e) {\n      this.root.$emit('play')\n      // #ifndef APP-PLUS\n      if (this.root.pauseVideo) {\n        let flag = false\n        const id = e.target.id\n        for (let i = this.root._videos.length; i--;) {\n          if (this.root._videos[i].id === id) {\n            flag = true\n          } else {\n            this.root._videos[i].pause() // 自动暂停其他视频\n          }\n        }\n        // 将自己加入列表\n        if (!flag) {\n          const ctx = uni.createVideoContext(id\n            // #ifndef MP-BAIDU\n            , this\n            // #endif\n          )\n          ctx.id = id\n          if (this.root.playbackRate) {\n            ctx.playbackRate(this.root.playbackRate)\n          }\n          this.root._videos.push(ctx)\n        }\n      }\n      // #endif\n    },\n\n    /**\n     * @description 图片点击事件\n     * @param {Event} e\n     */\n    imgTap (e) {\n      const node = this.childs[e.currentTarget.dataset.i]\n      if (node.a) {\n        this.linkTap(node.a)\n        return\n      }\n      if (node.attrs.ignore) return\n      // #ifdef H5 || APP-PLUS\n      node.attrs.src = node.attrs.src || node.attrs['data-src']\n      // #endif\n      this.root.$emit('imgTap', node.attrs)\n      // 自动预览图片\n      if (this.root.previewImg) {\n        uni.previewImage({\n          // #ifdef MP-WEIXIN\n          showmenu: this.root.showImgMenu,\n          // #endif\n          // #ifdef MP-ALIPAY\n          enablesavephoto: this.root.showImgMenu,\n          enableShowPhotoDownload: this.root.showImgMenu,\n          // #endif\n          current: parseInt(node.attrs.i),\n          urls: this.root.imgList\n        })\n      }\n    },\n\n    /**\n     * @description 图片长按\n     */\n    imgLongTap (e) {\n      // #ifdef APP-PLUS\n      const attrs = this.childs[e.currentTarget.dataset.i].attrs\n      if (this.opts[3] && !attrs.ignore) {\n        uni.showActionSheet({\n          itemList: ['保存图片'],\n          success: () => {\n            const save = path => {\n              uni.saveImageToPhotosAlbum({\n                filePath: path,\n                success () {\n                  uni.showToast({\n                    title: '保存成功'\n                  })\n                }\n              })\n            }\n            if (this.root.imgList[attrs.i].startsWith('http')) {\n              uni.downloadFile({\n                url: this.root.imgList[attrs.i],\n                success: res => save(res.tempFilePath)\n              })\n            } else {\n              save(this.root.imgList[attrs.i])\n            }\n          }\n        })\n      }\n      // #endif\n    },\n\n    /**\n     * @description 图片加载完成事件\n     * @param {Event} e\n     */\n    imgLoad (e) {\n      const i = e.currentTarget.dataset.i\n      /* #ifndef H5 || (APP-PLUS && VUE2) */\n      if (!this.childs[i].w) {\n        // 设置原宽度\n        this.$set(this.ctrl, i, e.detail.width)\n      } else /* #endif */ if ((this.opts[1] && !this.ctrl[i]) || this.ctrl[i] === -1) {\n        // 加载完毕，取消加载中占位图\n        this.$set(this.ctrl, i, 1)\n      }\n      this.checkReady()\n    },\n\n    /**\n     * @description 检查是否所有图片加载完毕\n     */\n    checkReady () {\n      if (!this.root.lazyLoad) {\n        this.root._unloadimgs -= 1\n        if (!this.root._unloadimgs) {\n          setTimeout(() => {\n            this.root.getRect().then(rect => {\n              this.root.$emit('ready', rect)\n            }).catch(() => {\n              this.root.$emit('ready', {})\n            })\n          }, 350)\n        }\n      }\n    },\n\n    /**\n     * @description 链接点击事件\n     * @param {Event} e\n     */\n    linkTap (e) {\n      const node = e.currentTarget ? this.childs[e.currentTarget.dataset.i] : {}\n      const attrs = node.attrs || e\n      const href = attrs.href\n      this.root.$emit('linkTap', Object.assign({\n        innerText: this.root.getText(node.children || []) // 链接内的文本内容\n      }, attrs))\n      if (href) {\n        if (href[0] === '#') {\n          // 跳转锚点\n          this.root.navigateTo(href.substring(1)).catch(() => { })\n        } else if (href.split('?')[0].includes('://')) {\n          // 复制外部链接\n          if (this.root.copyLink) {\n            // #ifdef H5\n            window.open(href)\n            // #endif\n            // #ifdef MP\n            uni.setClipboardData({\n              data: href,\n              success: () =>\n                uni.showToast({\n                  title: '链接已复制'\n                })\n            })\n            // #endif\n            // #ifdef APP-PLUS\n            plus.runtime.openWeb(href)\n            // #endif\n          }\n        } else {\n          // 跳转页面\n          uni.navigateTo({\n            url: href,\n            fail () {\n              uni.switchTab({\n                url: href,\n                fail () { }\n              })\n            }\n          })\n        }\n      }\n    },\n\n    /**\n     * @description 错误事件\n     * @param {Event} e\n     */\n    mediaError (e) {\n      const i = e.currentTarget.dataset.i\n      const node = this.childs[i]\n      // 加载其他源\n      if (node.name === 'video' || node.name === 'audio') {\n        let index = (this.ctrl[i] || 0) + 1\n        if (index > node.src.length) {\n          index = 0\n        }\n        if (index < node.src.length) {\n          this.$set(this.ctrl, i, index)\n          return\n        }\n      } else if (node.name === 'img') {\n        // #ifdef H5 && VUE3\n        if (this.opts[0] && !this.ctrl.load) return\n        // #endif\n        // 显示错误占位图\n        if (this.opts[2]) {\n          this.$set(this.ctrl, i, -1)\n        }\n        this.checkReady()\n      }\n      if (this.root) {\n        this.root.$emit('error', {\n          source: node.name,\n          attrs: node.attrs,\n          // #ifndef H5 && VUE3\n          errMsg: e.detail.errMsg\n          // #endif\n        })\n      }\n    }\n  }\n}\n</script>\n<style>\n/* a 标签默认效果 */\n._a {\n  padding: 1.5px 0 1.5px 0;\n  color: #366092;\n  /* #ifndef APP-NVUE */\n  word-break: break-all;\n  /* #endif */\n}\n\n/* a 标签点击态效果 */\n._hover {\n  text-decoration: underline;\n  opacity: 0.7;\n}\n\n/* 图片默认效果 */\n._img {\n  max-width: 100%;\n  -webkit-touch-callout: none;\n}\n\n/* 内部样式 */\n\n._block {\n  /* #ifndef APP-NVUE */\n  display: block;\n  /* #endif */\n}\n\n._b,\n._strong {\n  font-weight: bold;\n}\n\n._code {\n  font-family: monospace;\n}\n\n._del {\n  text-decoration: line-through;\n}\n\n._em,\n._i {\n  font-style: italic;\n}\n\n._h1 {\n  font-size: 2em;\n}\n\n._h2 {\n  font-size: 1.5em;\n}\n\n._h3 {\n  font-size: 1.17em;\n}\n\n._h5 {\n  font-size: 0.83em;\n}\n\n._h6 {\n  font-size: 0.67em;\n}\n\n._h1,\n._h2,\n._h3,\n._h4,\n._h5,\n._h6 {\n  /* #ifndef APP-NVUE */\n  display: block;\n  /* #endif */\n  font-weight: bold;\n}\n\n._image {\n  height: 1px;\n}\n\n._ins {\n  text-decoration: underline;\n}\n\n._li {\n  display: list-item;\n}\n\n._ol {\n  list-style-type: decimal;\n}\n\n._ol,\n._ul {\n  /* #ifndef APP-NVUE */\n  display: block;\n  /* #endif */\n  padding-left: 40px;\n  margin: 1em 0;\n}\n\n._q::before {\n  content: '\"';\n}\n\n._q::after {\n  content: '\"';\n}\n\n._sub {\n  font-size: smaller;\n  vertical-align: sub;\n}\n\n._sup {\n  font-size: smaller;\n  vertical-align: super;\n}\n\n._thead,\n._tbody,\n._tfoot {\n  display: table-row-group;\n}\n\n._tr {\n  display: table-row;\n}\n\n._td,\n._th {\n  display: table-cell;\n  vertical-align: middle;\n}\n\n._th {\n  font-weight: bold;\n  text-align: center;\n}\n\n._ul {\n  list-style-type: disc;\n}\n\n._ul ._ul {\n  margin: 0;\n  list-style-type: circle;\n}\n\n._ul ._ul ._ul {\n  list-style-type: square;\n}\n\n._abbr,\n._b,\n._code,\n._del,\n._em,\n._i,\n._ins,\n._label,\n._q,\n._span,\n._strong,\n._sub,\n._sup {\n  display: inline;\n}\n\n/* #ifdef APP-PLUS */\n._video {\n  width: 300px;\n  height: 225px;\n}\n/* #endif */\n</style>", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-parse/node/node.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "options", "virtualHost", "data", "ctrl", "isiOS", "uni", "index", "getSystemInfoSync", "system", "includes", "props", "String", "attrs", "type", "Object", "default", "childs", "Array", "opts", "components", "node", "Promise", "resolve", "then", "RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBhcnNlL25vZGUvbm9kZS52dWU", "mounted", "this", "$nextTick", "root", "$parent", "$options", "beforeUnmount", "methods", "toJSON", "play", "e", "$emit", "pauseVideo", "flag", "id", "target", "i", "_videos", "length", "pause", "ctx", "createVideoContext", "playbackRate", "push", "imgTap", "currentTarget", "dataset", "a", "linkTap", "ignore", "previewImg", "previewImage", "showmenu", "showImgMenu", "current", "parseInt", "urls", "imgList", "imgLongTap", "imgLoad", "w", "$set", "detail", "width", "checkReady", "lazyLoad", "_unloadimgs", "setTimeout", "getRect", "rect", "catch", "href", "assign", "innerText", "getText", "children", "navigateTo", "substring", "split", "copyLink", "setClipboardData", "success", "showToast", "title", "url", "fail", "switchTab", "mediaError", "src", "source", "errMsg", "wx", "createComponent", "Component"], "mappings": "qEA8GKA,EAAU,CACbC,KAAM,OACNC,QAAS,CAEPC,aAAa,GAMfC,KAAQ,KACC,CACLC,KAAM,CAAE,EAERC,MAAOC,EAAGC,MAACC,oBAAoBC,OAAOC,SAAS,SAInDC,MAAO,CACLX,KAAMY,OACNC,MAAO,CACLC,KAAMC,OACNC,QAAW,KACF,CAAC,IAGZC,OAAQC,MACRC,KAAMD,OAERE,WAAY,CAGVC,KAjCS,IAAIC,QAAAC,UAAAC,MAAA,IAAAC,KAoCf,OAAAC,GACEC,KAAKC,WAAU,KACb,IAAKD,KAAKE,KAAOF,KAAKG,QAAqC,YAA5BH,KAAKE,KAAKE,SAAS/B,KAAoB2B,KAAKE,KAAOF,KAAKE,KAAKC,SAAQ,GAsBvG,EACD,aAAAE,GAMC,EACDC,QAAQ,CAEN,MAAAC,GAAmB,OAAAP,IAAM,EAMzB,IAAAQ,CAAMC,GAGA,GAFCT,KAAAE,KAAKQ,MAAM,QAEZV,KAAKE,KAAKS,WAAY,CACxB,IAAIC,GAAO,EACL,MAAAC,EAAKJ,EAAEK,OAAOD,GACpB,IAAA,IAASE,EAAIf,KAAKE,KAAKc,QAAQC,OAAQF,KACjCf,KAAKE,KAAKc,QAAQD,GAAGF,KAAOA,EACvBD,GAAA,EAEPZ,KAAKE,KAAKc,QAAQD,GAAGG,QAIzB,IAAKN,EAAM,CACH,MAAAO,EAAMxC,QAAIyC,mBAAmBP,EAE/Bb,MAGJmB,EAAIN,GAAKA,EACLb,KAAKE,KAAKmB,cACRF,EAAAE,aAAarB,KAAKE,KAAKmB,cAExBrB,KAAAE,KAAKc,QAAQM,KAAKH,EACzB,CACF,CAED,EAMD,MAAAI,CAAQd,GACN,MAAMf,EAAOM,KAAKV,OAAOmB,EAAEe,cAAcC,QAAQV,GAC7CrB,EAAKgC,EACF1B,KAAA2B,QAAQjC,EAAKgC,GAGhBhC,EAAKR,MAAM0C,SAIf5B,KAAKE,KAAKQ,MAAM,SAAUhB,EAAKR,OAE3Bc,KAAKE,KAAK2B,YACZlD,EAAAA,MAAImD,aAAa,CAEfC,SAAU/B,KAAKE,KAAK8B,YAMpBC,QAASC,SAASxC,EAAKR,MAAM6B,GAC7BoB,KAAMnC,KAAKE,KAAKkC,UAGrB,EAKD,UAAAC,CAAY5B,GA6BX,EAMD,OAAA6B,CAAS7B,GACD,MAAAM,EAAIN,EAAEe,cAAcC,QAAQV,EAE7Bf,KAAKV,OAAOyB,GAAGwB,GAGRvC,KAAKR,KAAK,KAAOQ,KAAKvB,KAAKsC,KAA4B,IAArBf,KAAKvB,KAAKsC,KAEtDf,KAAKwC,KAAKxC,KAAKvB,KAAMsC,EAAG,GAHxBf,KAAKwC,KAAKxC,KAAKvB,KAAMsC,EAAGN,EAAEgC,OAAOC,OAKnC1C,KAAK2C,YACN,EAKD,UAAAA,GACO3C,KAAKE,KAAK0C,WACb5C,KAAKE,KAAK2C,aAAe,EACpB7C,KAAKE,KAAK2C,aACbC,YAAW,KACT9C,KAAKE,KAAK6C,UAAUlD,MAAamD,IAC1BhD,KAAAE,KAAKQ,MAAM,QAASsC,EAAI,IAC5BC,OAAM,KACPjD,KAAKE,KAAKQ,MAAM,QAAS,CAAA,EAAE,GAC5B,GACA,KAGR,EAMD,OAAAiB,CAASlB,GACDf,MAAAA,EAAOe,EAAEe,cAAgBxB,KAAKV,OAAOmB,EAAEe,cAAcC,QAAQV,GAAK,CAAC,EACnE7B,EAAQQ,EAAKR,OAASuB,EACtByC,EAAOhE,EAAMgE,KACnBlD,KAAKE,KAAKQ,MAAM,UAAWtB,OAAO+D,OAAO,CACvCC,UAAWpD,KAAKE,KAAKmD,QAAQ3D,EAAK4D,UAAY,KAC7CpE,IACCgE,IACc,MAAZA,EAAK,GAEFlD,KAAAE,KAAKqD,WAAWL,EAAKM,UAAU,IAAIP,OAAM,SACrCC,EAAKO,MAAM,KAAK,GAAG1E,SAAS,OAEjCiB,KAAKE,KAAKwD,UAKZ/E,EAAAA,MAAIgF,iBAAiB,CACnBnF,KAAM0E,EACNU,QAAS,IACPjF,EAAAA,MAAIkF,UAAU,CACZC,MAAO,YAUfnF,EAAAA,MAAI4E,WAAW,CACbQ,IAAKb,EACL,IAAAc,GACErF,EAAAA,MAAIsF,UAAU,CACZF,IAAKb,EACL,IAAAc,GAAU,GAEd,IAIP,EAMD,UAAAE,CAAYzD,GACJ,MAAAM,EAAIN,EAAEe,cAAcC,QAAQV,EAC5BrB,EAAOM,KAAKV,OAAOyB,GAEzB,GAAkB,UAAdrB,EAAKrB,MAAkC,UAAdqB,EAAKrB,KAAkB,CAClD,IAAIO,GAASoB,KAAKvB,KAAKsC,IAAM,GAAK,EAI9B,GAHAnC,EAAQc,EAAKyE,IAAIlD,SACXrC,EAAA,GAENA,EAAQc,EAAKyE,IAAIlD,OAEnB,YADAjB,KAAKwC,KAAKxC,KAAKvB,KAAMsC,EAAGnC,OAGH,QAAdc,EAAKrB,OAKV2B,KAAKR,KAAK,IACZQ,KAAKwC,KAAKxC,KAAKvB,KAAMsC,GAAK,GAE5Bf,KAAK2C,cAEH3C,KAAKE,MACFF,KAAAE,KAAKQ,MAAM,QAAS,CACvB0D,OAAQ1E,EAAKrB,KACba,MAAOQ,EAAKR,MAEZmF,OAAQ5D,EAAEgC,OAAO4B,QAIvB,44GC/YJC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}