"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),a=require("../../../store/index.js"),n=require("../../../utils/index.js"),i=require("../../../utils/auth.js"),t=require("../../../utils/permissions.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("u-text")+e.resolveComponent("u-icon")+e.resolveComponent("u-input")+e.resolveComponent("u-form-item")+e.resolveComponent("u-radio")+e.resolveComponent("u-radio-group")+e.resolveComponent("u-form")+e.resolveComponent("u-checkbox")+e.resolveComponent("u-checkbox-group")+e.resolveComponent("u-button"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../node-modules/uview-plus/components/u-form-item/u-form-item.js")+(()=>"../../../node-modules/uview-plus/components/u-radio/u-radio.js")+(()=>"../../../node-modules/uview-plus/components/u-radio-group/u-radio-group.js")+(()=>"../../../node-modules/uview-plus/components/u-form/u-form.js")+(()=>"../../../node-modules/uview-plus/components/u-checkbox/u-checkbox.js")+(()=>"../../../node-modules/uview-plus/components/u-checkbox-group/u-checkbox-group.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js"))();const r={__name:"withDraw",setup(r){const l=e.ref(),u=e.ref({money:"",username:"",bank_num:"",bank_name:""}),s=e.ref({money:[{type:"number",required:!0,message:`请填写不小于${a.store().$state.config.config.min_tixian_money.val}的金额`,trigger:["blur","change"]},{validator:(e,o,n)=>Number(o)>=+a.store().$state.config.config.min_tixian_money.val,message:`请填写不小于${a.store().$state.config.config.min_tixian_money.val}的金额`,trigger:["change","blur"]}],username:{type:"string",required:!0,message:"请填写正确的姓名",trigger:["blur","change"]},bank_num:{type:"number",max:19,required:!0,message:"请填写正确的银行卡号",trigger:["blur","change"]},bank_name:{type:"string",required:!0,message:"请填写正确的银行名称",trigger:["blur","change"]}}),m=e.ref([{name:"银行卡",icon:"coupon-fill"}]),c=e.ref("银行卡"),p=e.ref(),d=e.ref(!1),v=e.ref({daijiesuan:0}),b=e.ref("commission"),g=e.ref({available_income:"0.00",pending_income:"0.00",withdrawing_income:"0.00",total_income:"0.00"});e.onLoad((async()=>{if(i.isLoggedIn())try{const e=await i.safeApiCall((()=>o.userget_daijiesuan_status(i.getAuthParams())),"获取待结算状态");if(e&&e.data){v.value={daijiesuan:0,...e.data};for(let o in e.data)e.data[o]=100*e.data[o]/100,"yue"!==o&&(v.value.daijiesuan+=100*e.data[o]/100)}else v.value={daijiesuan:0};const a=await i.safeApiCall((()=>o.get_activity_income_status(i.getAuthParams())),"获取活动收入状态");a&&a.data&&(g.value=a.data)}catch(e){console.error("获取状态失败:",e),v.value={daijiesuan:0},g.value={available_income:"0.00",pending_income:"0.00",withdrawing_income:"0.00",total_income:"0.00"}}else v.value={daijiesuan:0},g.value={available_income:"0.00",pending_income:"0.00",withdrawing_income:"0.00",total_income:"0.00"}}));const f=e=>{b.value=e,u.value.money="",d.value=!1},x=()=>"commission"===b.value?v.value.yue||"0.00":g.value.available_income||"0.00",_=()=>"commission"===b.value?"佣金":"活动收入",y=()=>{if(!t.hasActivityIncomePermission())return!1;return parseFloat(g.value.total_income||0)>0},h=e=>{"银行卡"===e?(u.value.bank_name="",delete s.value.zfbCard,s.value={...s.value,bank_num:{type:"number",max:19,required:!0,message:"请填写正确的银行卡号",trigger:["blur","change"]},bank_name:{type:"string",required:!0,message:"请填写正确的银行名称",trigger:["blur","change"]}}):(u.value.bank_name="支付宝",s.value.bank_num.max=100,s.value.bank_num.message="请填写正确的支付宝账号")},w=async()=>{const a=parseFloat(x());if(u.value.money>a)return e.index.$u.toast(`提现金额不能大于可提现${_()}金额`);e.index.$u.test.isEmpty(p.value)?e.index.$u.toast("请先阅读并同意《用户隐私协议》"):l.value.validate().then((async a=>{const n=await o.userbank_add(u.value);if("ok"===n.status){let a;a="commission"===b.value?await o.apply_commission_withdraw({bank_id:n.data,money:u.value.money}):await o.apply_activity_income_withdraw({bank_id:n.data,money:u.value.money}),"ok"===a.status?(e.index.$u.toast(`${_()}提现申请成功`),setTimeout((()=>{e.index.navigateBack()}),1500)):e.index.$u.toast(a.msg)}})).catch((o=>e.index.$u.toast(o[0].message)))};return(o,i)=>{var r,j,z;return e.e({a:y()},y()?{b:e.p({color:"commission"===b.value?"#6AC086":"#666",size:"32rpx",bold:"commission"===b.value,text:"佣金提现"}),c:"commission"===b.value?1:"",d:e.o((e=>f("commission"))),e:e.p({color:"activity"===b.value?"#6AC086":"#666",size:"32rpx",bold:"activity"===b.value,text:"活动收入提现"}),f:"activity"===b.value?1:"",g:e.o((e=>f("activity")))}:{},{h:e.unref(t.hasActivityIncomePermission)()&&!y()},e.unref(t.hasActivityIncomePermission)()&&!y()?{i:e.p({size:"28rpx",color:"#999",text:"暂无活动收入记录，发布收费活动后可查看活动收入"})}:{},{j:e.p({size:"28rpx",color:"#666",text:`可提现${_()}(元)`}),k:e.p({name:"info-circle-fill",color:"#6AC086",size:"32rpx"}),l:e.p({mode:"price",color:"#6AC086",size:"48rpx",bold:!0,text:x()}),m:d.value},d.value?e.e({n:"commission"===b.value},"commission"===b.value?{o:e.p({mode:"price",size:"32rpx",bold:!0,color:"#fff",text:v.value.invite_yongjin_daijiesuan||"0.00"}),p:e.o((o=>e.unref(n.navto)("jiesuanList?id=1"))),q:e.p({mode:"price",size:"32rpx",bold:!0,color:"#fff",text:v.value.operation_yongjin_daijiesuan||"0.00"}),r:e.o((o=>e.unref(n.navto)("jiesuanList?id=5")))}:{s:e.p({mode:"price",size:"32rpx",bold:!0,color:"#fff",text:g.value.pending_income||"0.00"}),t:e.p({mode:"price",size:"32rpx",bold:!0,color:"#fff",text:g.value.withdrawing_income||"0.00"}),v:e.p({mode:"price",size:"32rpx",bold:!0,color:"#fff",text:g.value.total_income||"0.00"})}):{},{w:e.o((e=>d.value=!d.value)),x:e.p({size:"28rpx",color:"#666",text:"可提现(元)"}),y:e.p({mode:"price",color:"#6AC086",size:"48rpx",bold:!0,text:v.value.yue||"0.00"}),z:e.o((e=>u.value.money=e)),A:e.p({type:"number","input-align":"right","placeholder-style":"font-size: 32rpx;color: #999;",placeholder:`单次最少提现${null==(z=null==(j=null==(r=e.unref(a.store)().$state.config)?void 0:r.config)?void 0:j.min_tixian_money)?void 0:z.val}元`,border:"none",customStyle:{fontSize:"32rpx",color:"#333"},modelValue:u.value.money}),B:e.p({label:"提现金额",prop:"money",borderBottom:!0}),C:e.f(m.value,((o,a,n)=>({a:"e39b61d4-18-"+n+",e39b61d4-17",b:e.p({name:o.icon,color:"#6AC086",size:"40rpx"}),c:"e39b61d4-19-"+n+",e39b61d4-17",d:e.p({color:"#333",size:"32rpx",text:o.name}),e:"e39b61d4-20-"+n+",e39b61d4-17",f:e.p({name:o.name}),g:a}))),D:e.o(h),E:e.o((e=>c.value=e)),F:e.p({placement:"row",activeColor:"#6AC086",modelValue:c.value}),G:e.p({label:"提现至",prop:"type",borderBottom:!0}),H:e.o((e=>u.value.username=e)),I:e.p({"input-align":"right","placeholder-style":"font-size: 32rpx;color: #999;",placeholder:"请填写您的真实姓名",border:"none",customStyle:{fontSize:"32rpx",color:"#333"},modelValue:u.value.username}),J:e.p({label:"姓名",prop:"username",borderBottom:!0}),K:e.o((e=>u.value.bank_num=e)),L:e.p({"input-align":"right","placeholder-style":"font-size: 32rpx;color: #999;",placeholder:"请填写"+("银行卡"===c.value?"银行卡号":"支付宝账号"),border:"none",customStyle:{fontSize:"32rpx",color:"#333"},modelValue:u.value.bank_num}),M:e.p({label:"银行卡"===c.value?"银行卡号":"支付宝账号",prop:"bank_num",borderBottom:!0}),N:"银行卡"===c.value},"银行卡"===c.value?{O:e.o((e=>u.value.bank_name=e)),P:e.p({"input-align":"right","placeholder-style":"font-size: 32rpx;color: #999;",placeholder:"请填写银行名称",border:"none",customStyle:{fontSize:"32rpx",color:"#333"},modelValue:u.value.bank_name}),Q:e.p({label:"银行名称",prop:"bank_name",borderBottom:!0})}:{},{R:e.sr(l,"e39b61d4-13",{k:"formRef"}),S:e.p({labelPosition:"left","label-width":"200rpx","label-style":{fontSize:"32rpx",fontWeight:"500",color:"#333"},model:u.value,rules:s.value,errorType:"toast"}),T:e.p({color:"#999",size:"28rpx",text:"请确认您提现的真实信息以及真实账号"}),U:e.p({name:"a"}),V:e.p({color:"#666",size:"28rpx",text:"同意"}),W:e.p({color:"#6AC086",size:"28rpx",text:"《用户隐私协议》"}),X:e.o((o=>e.unref(n.navto)("/pages/bundle/common/xieyi?type=4"))),Y:e.o((e=>p.value=e)),Z:e.p({placement:"column",shape:"circle",activeColor:"#6AC086",modelValue:p.value}),aa:e.o(w),ab:e.p({shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",text:"申请提现",customStyle:{width:"100%",height:"96rpx",color:"#fff",fontSize:"36rpx",fontWeight:"bold",boxShadow:"0 8rpx 24rpx rgba(106, 192, 134, 0.3)"}}),ac:e.p({color:"#6AC086",size:"32rpx",text:"查看提现记录"}),ad:e.p({name:"arrow-right",color:"#6AC086",size:"24rpx"}),ae:e.o((o=>e.unref(n.navto)("/pages/bundle/user/withDrawList"))),af:e.o((e=>d.value=!1))})}}},l=e._export_sfc(r,[["__scopeId","data-v-e39b61d4"]]);r.__runtimeHooks=1,wx.createPage(l);
//# sourceMappingURL=withDraw.js.map
