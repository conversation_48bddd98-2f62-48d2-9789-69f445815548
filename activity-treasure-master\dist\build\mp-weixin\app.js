"use strict";const e=require("./common/vendor.js"),r=require("./api/index.js"),n=require("./store/index.js"),o=require("./utils/auth.js"),t=require("./utils/systemInfo.js"),s=require("./utils/errorHandler.js");require("./utils/request.js"),require("./utils/BaseUrl.js"),require("./utils/index.js"),require("./utils/cacheManager.js"),require("./store/counter.js"),Math;const i={__name:"App",setup(s){const i=e.index.getSystemInfoSync;if(e.index.getSystemInfoSync=()=>{try{return t.getSystemInfo()}catch(e){return console.warn("新API获取系统信息失败，使用原始API:",e),i()}},e.index.$u&&e.index.$u.sys){const r=e.index.$u.sys;e.index.$u.sys=()=>{try{return t.getSystemInfo()}catch(e){return console.warn("$u.sys()新API失败，使用原始方法:",e),r()}}}return e.onLaunch((async()=>{try{console.log("应用启动，尝试恢复登录状态...");o.restoreLoginState()?console.log("登录状态恢复成功"):console.log("无有效的登录状态需要恢复");const e=await r.configapp();"ok"===(null==e?void 0:e.status)&&(null==e?void 0:e.data)&&n.store().changeConfig(e.data);const t=await r.configpop();"ok"==(null==t?void 0:t.status)&&n.store().setPopContent(t.data)}catch(e){console.error("获取App配置出错:",e)}})),e.onShow((()=>{})),e.onHide((()=>{})),(e,r)=>({})}},a=e.createApp(i);a.config.errorHandler=(e,r,n)=>{console.error("全局捕获的错误:",e),console.error("错误组件:",r),console.error("错误详情:",n),s.handleError(e,s.ERROR_TYPES.SYSTEM,s.ERROR_LEVELS.ERROR,{component:(null==r?void 0:r.$options.name)||"Unknown",errorInfo:n})},a.config.warnHandler=(e,r,n)=>{s.handleError(e,s.ERROR_TYPES.SYSTEM,s.ERROR_LEVELS.WARNING,{component:(null==r?void 0:r.$options.name)||"Unknown",trace:n})};const u=e.createPinia();u.use(e.createUnistorage()),a.use(u).use(e.uviewPlus).mount("#app"),"undefined"!=typeof window&&window.addEventListener?window.addEventListener("unhandledrejection",(e=>{console.error("未捕获的Promise错误:",e.reason),s.handleError(e.reason,s.ERROR_TYPES.SYSTEM,s.ERROR_LEVELS.ERROR,{type:"unhandledrejection"}),e.preventDefault()})):e.index.onError&&e.index.onError((e=>{console.error("小程序错误:",e),s.handleError(e,s.ERROR_TYPES.SYSTEM,s.ERROR_LEVELS.ERROR,{type:"miniprogram_error"})}));
//# sourceMappingURL=app.js.map
