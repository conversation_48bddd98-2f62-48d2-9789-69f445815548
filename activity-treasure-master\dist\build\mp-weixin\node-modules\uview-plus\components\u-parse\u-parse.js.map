{"version": 3, "file": "u-parse.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-parse/u-parse.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBhcnNlL3UtcGFyc2UudnVl"], "sourcesContent": ["<template>\n\t<view id=\"_root\" :class=\"(selectable ? '_select ' : '') + '_root'\" :style=\"containerStyle\">\n\t\t<slot v-if=\"!nodes[0]\" />\n\t\t<!-- #ifndef APP-PLUS-NVUE -->\n\t\t<node v-else :childs=\"nodes\" :opts=\"[lazyLoad, loadingImg, errorImg, showImgMenu, selectable]\" name=\"span\" />\n\t\t<!-- #endif -->\n\t\t<!-- #ifdef APP-PLUS-NVUE -->\n\t\t<web-view ref=\"web\" src=\"/static/app-plus/mp-html/local.html\" :style=\"'margin-top:-2px;height:' + height + 'px'\" @onPostMessage=\"_onMessage\" />\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<script>\n/**\n * mp-html v2.4.1\n * @description 富文本组件\n * @tutorial https://github.com/jin-yufeng/mp-html\n * @property {String} container-style 容器的样式\n * @property {String} content 用于渲染的 html 字符串\n * @property {Boolean} copy-link 是否允许外部链接被点击时自动复制\n * @property {String} domain 主域名，用于拼接链接\n * @property {String} error-img 图片出错时的占位图链接\n * @property {Boolean} lazy-load 是否开启图片懒加载\n * @property {string} loading-img 图片加载过程中的占位图链接\n * @property {Boolean} pause-video 是否在播放一个视频时自动暂停其他视频\n * @property {Boolean} preview-img 是否允许图片被点击时自动预览\n * @property {Boolean} scroll-table 是否给每个表格添加一个滚动层使其能单独横向滚动\n * @property {Boolean | String} selectable 是否开启长按复制\n * @property {Boolean} set-title 是否将 title 标签的内容设置到页面标题\n * @property {Boolean} show-img-menu 是否允许图片被长按时显示菜单\n * @property {Object} tag-style 标签的默认样式\n * @property {Boolean | Number} use-anchor 是否使用锚点链接\n * @event {Function} load dom 结构加载完毕时触发\n * @event {Function} ready 所有图片加载完毕时触发\n * @event {Function} imgTap 图片被点击时触发\n * @event {Function} linkTap 链接被点击时触发\n * @event {Function} play 音视频播放时触发\n * @event {Function} error 媒体加载出错时触发\n */\n// #ifndef APP-PLUS-NVUE\nimport node from './node/node'\n// #endif\nimport Parser from './parser'\nconst plugins = []\n// #ifdef APP-PLUS-NVUE\nconst dom = weex.requireModule('dom')\n// #endif\nexport default {\n\tname: 'u-parse',\n\tdata() {\n\t\treturn {\n\t\t\tnodes: [],\n\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\theight: 3\n\t\t\t// #endif\n\t\t}\n\t},\n\tprops: {\n\t\tcontainerStyle: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tcontent: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tcopyLink: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: true\n\t\t},\n\t\tdomain: String,\n\t\terrorImg: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tlazyLoad: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: false\n\t\t},\n\t\tloadingImg: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tpauseVideo: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: true\n\t\t},\n\t\tpreviewImg: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: true\n\t\t},\n\t\tscrollTable: [Boolean, String],\n\t\tselectable: [Boolean, String],\n\t\tsetTitle: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: true\n\t\t},\n\t\tshowImgMenu: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: true\n\t\t},\n\t\ttagStyle: Object,\n\t\tuseAnchor: [Boolean, Number]\n\t},\n\t// #ifdef VUE3\n\temits: ['load', 'ready', 'imgTap', 'linkTap', 'play', 'error'],\n\t// #endif\n\t// #ifndef APP-PLUS-NVUE\n\tcomponents: {\n\t\tnode\n\t},\n\t// #endif\n\twatch: {\n\t\tcontent(content) {\n\t\t\tthis.setContent(content)\n\t\t}\n\t},\n\tcreated() {\n\t\tthis.plugins = []\n\t\tfor (let i = plugins.length; i--;) {\n\t\t\tthis.plugins.push(new plugins[i](this))\n\t\t}\n\t},\n\tmounted() {\n\t\tif (this.content && !this.nodes.length) {\n\t\t\tthis.setContent(this.content)\n\t\t}\n\t},\n\tbeforeUnmount() {\n\t\tthis._hook('onDetached')\n\t},\n\tmethods: {\n\t\t/**\n\t\t * @description 将锚点跳转的范围限定在一个 scroll-view 内\n\t\t * @param {Object} page scroll-view 所在页面的示例\n\t\t * @param {String} selector scroll-view 的选择器\n\t\t * @param {String} scrollTop scroll-view scroll-top 属性绑定的变量名\n\t\t */\n\t\tin(page, selector, scrollTop) {\n\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\tif (page && selector && scrollTop) {\n\t\t\t\tthis._in = {\n\t\t\t\t\tpage,\n\t\t\t\t\tselector,\n\t\t\t\t\tscrollTop\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\n\t\t/**\n\t\t * @description 锚点跳转\n\t\t * @param {String} id 要跳转的锚点 id\n\t\t * @param {Number} offset 跳转位置的偏移量\n\t\t * @returns {Promise}\n\t\t */\n\t\tnavigateTo(id, offset) {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tif (!this.useAnchor) {\n\t\t\t\t\treject(Error('Anchor is disabled'))\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\toffset = offset || parseInt(this.useAnchor) || 0\n\t\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\t\tif (!id) {\n\t\t\t\t\tdom.scrollToElement(this.$refs.web, {\n\t\t\t\t\t\toffset\n\t\t\t\t\t})\n\t\t\t\t\tresolve()\n\t\t\t\t} else {\n\t\t\t\t\tthis._navigateTo = {\n\t\t\t\t\t\tresolve,\n\t\t\t\t\t\treject,\n\t\t\t\t\t\toffset\n\t\t\t\t\t}\n\t\t\t\t\tthis.$refs.web.evalJs('uni.postMessage({data:{action:\"getOffset\",offset:(document.getElementById(' + id + ')||{}).offsetTop}})')\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\t\tlet deep = ' '\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\n\t\t\t\tdeep = '>>>'\n\t\t\t\t// #endif\n\t\t\t\tconst selector = uni.createSelectorQuery()\n\t\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\t\t.in(this._in ? this._in.page : this)\n\t\t\t\t\t// #endif\n\t\t\t\t\t.select((this._in ? this._in.selector : '._root') + (id ? `${deep}#${id}` : '')).boundingClientRect()\n\t\t\t\tif (this._in) {\n\t\t\t\t\tselector.select(this._in.selector).scrollOffset()\n\t\t\t\t\t\t.select(this._in.selector).boundingClientRect()\n\t\t\t\t} else {\n\t\t\t\t\t// 获取 scroll-view 的位置和滚动距离\n\t\t\t\t\tselector.selectViewport().scrollOffset() // 获取窗口的滚动距离\n\t\t\t\t}\n\t\t\t\tselector.exec(res => {\n\t\t\t\t\tif (!res[0]) {\n\t\t\t\t\t\treject(Error('Label not found'))\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tconst scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + offset\n\t\t\t\t\tif (this._in) {\n\t\t\t\t\t\t// scroll-view 跳转\n\t\t\t\t\t\tthis._in.page[this._in.scrollTop] = scrollTop\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 页面跳转\n\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\tscrollTop,\n\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\tresolve()\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t})\n\t\t},\n\n\t\t/**\n\t\t * @description 获取文本内容\n\t\t * @return {String}\n\t\t */\n\t\tgetText(nodes) {\n\t\t\tlet text = '';\n\t\t\t(function traversal(nodes) {\n\t\t\t\tfor (let i = 0; i < nodes.length; i++) {\n\t\t\t\t\tconst node = nodes[i]\n\t\t\t\t\tif (node.type === 'text') {\n\t\t\t\t\t\ttext += node.text.replace(/&amp;/g, '&')\n\t\t\t\t\t} else if (node.name === 'br') {\n\t\t\t\t\t\ttext += '\\n'\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 块级标签前后加换行\n\t\t\t\t\t\tconst isBlock = node.name === 'p' || node.name === 'div' || node.name === 'tr' || node.name === 'li' || (node.name[0] === 'h' && node.name[1] > '0' && node.name[1] < '7')\n\t\t\t\t\t\tif (isBlock && text && text[text.length - 1] !== '\\n') {\n\t\t\t\t\t\t\ttext += '\\n'\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 递归获取子节点的文本\n\t\t\t\t\t\tif (node.children) {\n\t\t\t\t\t\t\ttraversal(node.children)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (isBlock && text[text.length - 1] !== '\\n') {\n\t\t\t\t\t\t\ttext += '\\n'\n\t\t\t\t\t\t} else if (node.name === 'td' || node.name === 'th') {\n\t\t\t\t\t\t\ttext += '\\t'\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})(nodes || this.nodes)\n\t\t\treturn text\n\t\t},\n\n\t\t/**\n\t\t * @description 获取内容大小和位置\n\t\t * @return {Promise}\n\t\t */\n\t\tgetRect() {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\t\t.in(this)\n\t\t\t\t\t// #endif\n\t\t\t\t\t.select('#_root').boundingClientRect().exec(res => res[0] ? resolve(res[0]) : reject(Error('Root label not found')))\n\t\t\t})\n\t\t},\n\n\t\t/**\n\t\t * @description 暂停播放媒体\n\t\t */\n\t\tpauseMedia() {\n\t\t\tfor (let i = (this._videos || []).length; i--;) {\n\t\t\t\tthis._videos[i].pause()\n\t\t\t}\n\t\t\t// #ifdef APP-PLUS\n\t\t\tconst command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].pause()'\n\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\tlet page = this.$parent\n\t\t\twhile (!page.$scope) page = page.$parent\n\t\t\tpage.$scope.$getAppWebview().evalJS(command)\n\t\t\t// #endif\n\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\tthis.$refs.web.evalJs(command)\n\t\t\t// #endif\n\t\t\t// #endif\n\t\t},\n\n\t\t/**\n\t\t * @description 设置媒体播放速率\n\t\t * @param {Number} rate 播放速率\n\t\t */\n\t\tsetPlaybackRate(rate) {\n\t\t\tthis.playbackRate = rate\n\t\t\tfor (let i = (this._videos || []).length; i--;) {\n\t\t\t\tthis._videos[i].playbackRate(rate)\n\t\t\t}\n\t\t\t// #ifdef APP-PLUS\n\t\t\tconst command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].playbackRate=' + rate\n\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\tlet page = this.$parent\n\t\t\twhile (!page.$scope) page = page.$parent\n\t\t\tpage.$scope.$getAppWebview().evalJS(command)\n\t\t\t// #endif\n\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\tthis.$refs.web.evalJs(command)\n\t\t\t// #endif\n\t\t\t// #endif\n\t\t},\n\n\t\t/**\n\t\t * @description 设置内容\n\t\t * @param {String} content html 内容\n\t\t * @param {Boolean} append 是否在尾部追加\n\t\t */\n\t\tsetContent(content, append) {\n\t\t\tif (!append || !this.imgList) {\n\t\t\t\tthis.imgList = []\n\t\t\t}\n\t\t\tconst nodes = new Parser(this).parse(content)\n\t\t\t// #ifdef APP-PLUS-NVUE\n\t\t\tif (this._ready) {\n\t\t\t\tthis._set(nodes, append)\n\t\t\t}\n\t\t\t// #endif\n\t\t\tthis.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes)\n\n\t\t\t// #ifndef APP-PLUS-NVUE\n\t\t\tthis._videos = []\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis._hook('onLoad')\n\t\t\t\tthis.$emit('load')\n\t\t\t})\n\n\t\t\tif (this.lazyLoad || this.imgList._unloadimgs < this.imgList.length / 2) {\n\t\t\t\t// 设置懒加载，每 350ms 获取高度，不变则认为加载完毕\n\t\t\t\tlet height = 0\n\t\t\t\tconst callback = rect => {\n\t\t\t\t\tif (!rect || !rect.height) rect = {}\n\t\t\t\t\t// 350ms 总高度无变化就触发 ready 事件\n\t\t\t\t\tif (rect.height === height) {\n\t\t\t\t\t\tthis.$emit('ready', rect)\n\t\t\t\t\t} else {\n\t\t\t\t\t\theight = rect.height\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.getRect().then(callback).catch(callback)\n\t\t\t\t\t\t}, 350)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.getRect().then(callback).catch(callback)\n\t\t\t} else {\n\t\t\t\t// 未设置懒加载，等待所有图片加载完毕\n\t\t\t\tif (!this.imgList._unloadimgs) {\n\t\t\t\t\tthis.getRect().then(rect => {\n\t\t\t\t\t\tthis.$emit('ready', rect)\n\t\t\t\t\t}).catch(() => {\n\t\t\t\t\t\tthis.$emit('ready', {})\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\n\t\t/**\n\t\t * @description 调用插件钩子函数\n\t\t */\n\t\t_hook(name) {\n\t\t\tfor (let i = plugins.length; i--;) {\n\t\t\t\tif (this.plugins[i][name]) {\n\t\t\t\t\tthis.plugins[i][name]()\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// #ifdef APP-PLUS-NVUE\n\t\t/**\n\t\t * @description 设置内容\n\t\t */\n\t\t_set(nodes, append) {\n\t\t\tthis.$refs.web.evalJs('setContent(' + JSON.stringify(nodes).replace(/%22/g, '') + ',' + JSON.stringify([this.containerStyle.replace(/(?:margin|padding)[^;]+/g, ''), this.errorImg, this.loadingImg, this.pauseVideo, this.scrollTable, this.selectable]) + ',' + append + ')')\n\t\t},\n\n\t\t/**\n\t\t * @description 接收到 web-view 消息\n\t\t */\n\t\t_onMessage(e) {\n\t\t\tconst message = e.detail.data[0]\n\t\t\tswitch (message.action) {\n\t\t\t\t// web-view 初始化完毕\n\t\t\t\tcase 'onJSBridgeReady':\n\t\t\t\t\tthis._ready = true\n\t\t\t\t\tif (this.nodes) {\n\t\t\t\t\t\tthis._set(this.nodes)\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\t\t\t\t// 内容 dom 加载完毕\n\t\t\t\tcase 'onLoad':\n\t\t\t\t\tthis.height = message.height\n\t\t\t\t\tthis._hook('onLoad')\n\t\t\t\t\tthis.$emit('load')\n\t\t\t\t\tbreak\n\t\t\t\t// 所有图片加载完毕\n\t\t\t\tcase 'onReady':\n\t\t\t\t\tthis.getRect().then(res => {\n\t\t\t\t\t\tthis.$emit('ready', res)\n\t\t\t\t\t}).catch(() => {\n\t\t\t\t\t\tthis.$emit('ready', {})\n\t\t\t\t\t})\n\t\t\t\t\tbreak\n\t\t\t\t// 总高度发生变化\n\t\t\t\tcase 'onHeightChange':\n\t\t\t\t\tthis.height = message.height\n\t\t\t\t\tbreak\n\t\t\t\t// 图片点击\n\t\t\t\tcase 'onImgTap':\n\t\t\t\t\tthis.$emit('imgTap', message.attrs)\n\t\t\t\t\tif (this.previewImg) {\n\t\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\t\tcurrent: parseInt(message.attrs.i),\n\t\t\t\t\t\t\turls: this.imgList\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\t\t\t\t// 链接点击\n\t\t\t\tcase 'onLinkTap': {\n\t\t\t\t\tconst href = message.attrs.href\n\t\t\t\t\tthis.$emit('linkTap', message.attrs)\n\t\t\t\t\tif (href) {\n\t\t\t\t\t\t// 锚点跳转\n\t\t\t\t\t\tif (href[0] === '#') {\n\t\t\t\t\t\t\tif (this.useAnchor) {\n\t\t\t\t\t\t\t\tdom.scrollToElement(this.$refs.web, {\n\t\t\t\t\t\t\t\t\toffset: message.offset\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (href.includes('://')) {\n\t\t\t\t\t\t\t// 打开外链\n\t\t\t\t\t\t\tif (this.copyLink) {\n\t\t\t\t\t\t\t\tplus.runtime.openWeb(href)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: href,\n\t\t\t\t\t\t\t\tfail() {\n\t\t\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\t\t\turl: href\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t\tcase 'onPlay':\n\t\t\t\t\tthis.$emit('play')\n\t\t\t\t\tbreak\n\t\t\t\t// 获取到锚点的偏移量\n\t\t\t\tcase 'getOffset':\n\t\t\t\t\tif (typeof message.offset === 'number') {\n\t\t\t\t\t\tdom.scrollToElement(this.$refs.web, {\n\t\t\t\t\t\t\toffset: message.offset + this._navigateTo.offset\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis._navigateTo.resolve()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis._navigateTo.reject(Error('Label not found'))\n\t\t\t\t\t}\n\t\t\t\t\tbreak\n\t\t\t\t// 点击\n\t\t\t\tcase 'onClick':\n\t\t\t\t\tthis.$emit('tap')\n\t\t\t\t\tthis.$emit('click')\n\t\t\t\t\tbreak\n\t\t\t\t// 出错\n\t\t\t\tcase 'onError':\n\t\t\t\t\tthis.$emit('error', {\n\t\t\t\t\t\tsource: message.source,\n\t\t\t\t\t\tattrs: message.attrs\n\t\t\t\t\t})\n\t\t\t}\n\t\t}\n\t\t// #endif\n\t}\n}\n</script>\n\n<style>\n/* #ifndef APP-PLUS-NVUE */\n/* 根节点样式 */\n._root {\n\tpadding: 1px 0;\n\toverflow-x: auto;\n\toverflow-y: hidden;\n\t-webkit-overflow-scrolling: touch;\n}\n\n/* 长按复制 */\n._select {\n\tuser-select: text;\n}\n\n/* #endif */\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-parse/u-parse.vue'\nwx.createComponent(Component)"], "names": ["plugins", "_sfc_main", "name", "data", "nodes", "props", "containerStyle", "type", "String", "default", "content", "copyLink", "Boolean", "domain", "errorImg", "lazyLoad", "loadingImg", "pauseVideo", "previewImg", "scrollTable", "selectable", "setTitle", "showImgMenu", "tagStyle", "Object", "useAnchor", "Number", "emits", "components", "node", "watch", "this", "<PERSON><PERSON><PERSON><PERSON>", "created", "i", "length", "push", "mounted", "beforeUnmount", "_hook", "methods", "page", "selector", "scrollTop", "_in", "navigateTo", "id", "offset", "Promise", "resolve", "reject", "Error", "parseInt", "deep", "uni", "index", "createSelectorQuery", "in", "select", "boundingClientRect", "scrollOffset", "selectViewport", "exec", "res", "top", "pageScrollTo", "duration", "getText", "text", "traversal", "replace", "isBlock", "children", "getRect", "pauseMedia", "_videos", "pause", "setPlaybackRate", "rate", "playbackRate", "append", "imgList", "<PERSON><PERSON><PERSON>", "parse", "$set", "concat", "$nextTick", "$emit", "_unloadimgs", "height", "callback", "rect", "setTimeout", "then", "catch", "wx", "createComponent", "Component"], "mappings": "6DA2CMA,EAAU,GAIXC,EAAU,CACdC,KAAM,UACNC,KAAO,KACC,CACNC,MAAO,KAMTC,MAAO,CACNC,eAAgB,CACfC,KAAMC,OACNC,QAAS,IAEVC,QAAS,CACRH,KAAMC,OACNC,QAAS,IAEVE,SAAU,CACTJ,KAAM,CAACK,QAASJ,QAChBC,SAAS,GAEVI,OAAQL,OACRM,SAAU,CACTP,KAAMC,OACNC,QAAS,IAEVM,SAAU,CACTR,KAAM,CAACK,QAASJ,QAChBC,SAAS,GAEVO,WAAY,CACXT,KAAMC,OACNC,QAAS,IAEVQ,WAAY,CACXV,KAAM,CAACK,QAASJ,QAChBC,SAAS,GAEVS,WAAY,CACXX,KAAM,CAACK,QAASJ,QAChBC,SAAS,GAEVU,YAAa,CAACP,QAASJ,QACvBY,WAAY,CAACR,QAASJ,QACtBa,SAAU,CACTd,KAAM,CAACK,QAASJ,QAChBC,SAAS,GAEVa,YAAa,CACZf,KAAM,CAACK,QAASJ,QAChBC,SAAS,GAEVc,SAAUC,OACVC,UAAW,CAACb,QAASc,SAGtBC,MAAO,CAAC,OAAQ,QAAS,SAAU,UAAW,OAAQ,SAGtDC,WAAY,CACXC,KArEW,IAAW,kBAwEvBC,MAAO,CACN,OAAApB,CAAQA,GACPqB,KAAKC,WAAWtB,EACjB,GAED,OAAAuB,GACCF,KAAK/B,QAAU,GACN,IAAA,IAAAkC,EAAIlC,EAAQmC,OAAQD,KAC5BH,KAAK/B,QAAQoC,KAAK,IAAIpC,EAAQkC,GAAGH,MAElC,EACD,OAAAM,GACKN,KAAKrB,UAAYqB,KAAK3B,MAAM+B,QAC1BJ,KAAAC,WAAWD,KAAKrB,QAEtB,EACD,aAAA4B,GACCP,KAAKQ,MAAM,aACX,EACDC,QAAS,CAOR,GAAGC,EAAMC,EAAUC,GAEdF,GAAQC,GAAYC,IACvBZ,KAAKa,IAAM,CACVH,OACAC,WACAC,aAIF,EAQD,UAAAE,CAAWC,EAAIC,GACd,OAAO,IAAIC,SAAQ,CAACC,EAASC,KACxB,IAACnB,KAAKN,UAET,YADOyB,EAAAC,MAAM,uBAGdJ,EAASA,GAAUK,SAASrB,KAAKN,YAAc,EAiB/C,IAAI4B,EAAO,IAEJA,EAAA,MAED,MAAAX,EAAWY,EAAGC,MAACC,sBAEnBC,GAAG1B,KAAKa,IAAMb,KAAKa,IAAIH,KAAOV,MAE9B2B,QAAQ3B,KAAKa,IAAMb,KAAKa,IAAIF,SAAW,WAAaI,EAAK,OAAWA,IAAO,KAAKa,qBAC9E5B,KAAKa,IACRF,EAASgB,OAAO3B,KAAKa,IAAIF,UAAUkB,eACjCF,OAAO3B,KAAKa,IAAIF,UAAUiB,qBAGnBjB,EAAAmB,iBAAiBD,eAE3BlB,EAASoB,MAAYC,IAChB,IAACA,EAAI,GAER,YADOb,EAAAC,MAAM,oBAGd,MAAMR,EAAYoB,EAAI,GAAGpB,UAAYoB,EAAI,GAAGC,KAAOD,EAAI,GAAKA,EAAI,GAAGC,IAAM,GAAKjB,EAC1EhB,KAAKa,IAERb,KAAKa,IAAIH,KAAKV,KAAKa,IAAID,WAAaA,EAGpCW,EAAAA,MAAIW,aAAa,CAChBtB,YACAuB,SAAU,MAGJjB,GAAA,GACR,GAGF,EAMD,OAAAkB,CAAQ/D,GACP,IAAIgE,EAAO,GA0BJ,OAzBN,SAASC,EAAUjE,GACnB,IAAA,IAAS8B,EAAI,EAAGA,EAAI9B,EAAM+B,OAAQD,IAAK,CAChCL,MAAAA,EAAOzB,EAAM8B,GACfL,GAAc,SAAdA,EAAKtB,KACR6D,GAAQvC,EAAKuC,KAAKE,QAAQ,SAAU,aACZ,OAAdzC,EAAK3B,KACPkE,GAAA,SACF,CAEA,MAAAG,EAAwB,MAAd1C,EAAK3B,MAA8B,QAAd2B,EAAK3B,MAAgC,OAAd2B,EAAK3B,MAA+B,OAAd2B,EAAK3B,MAAmC,MAAjB2B,EAAK3B,KAAK,IAAc2B,EAAK3B,KAAK,GAAK,KAAO2B,EAAK3B,KAAK,GAAK,IAClKqE,GAAWH,GAAkC,OAA1BA,EAAKA,EAAKjC,OAAS,KACjCiC,GAAA,MAGLvC,EAAK2C,UACRH,EAAUxC,EAAK2C,UAEZD,GAAqC,OAA1BH,EAAKA,EAAKjC,OAAS,GACzBiC,GAAA,KACgB,OAAdvC,EAAK3B,MAA+B,OAAd2B,EAAK3B,OAC7BkE,GAAA,KAEV,CACD,CACD,CAxBC,CAwBEhE,GAAS2B,KAAK3B,OACVgE,CACP,EAMD,OAAAK,GACC,OAAO,IAAIzB,SAAQ,CAACC,EAASC,KAC5BI,EAAAA,MAAIE,sBAEFC,GAAG1B,MAEH2B,OAAO,UAAUC,qBAAqBG,SAAYC,EAAI,GAAKd,EAAQc,EAAI,IAAMb,EAAOC,MAAM,0BAAwB,GAErH,EAKD,UAAAuB,GACC,IAAA,IAASxC,GAAKH,KAAK4C,SAAW,IAAIxC,OAAQD,KACpCH,KAAA4C,QAAQzC,GAAG0C,OAajB,EAMD,eAAAC,CAAgBC,GACf/C,KAAKgD,aAAeD,EACpB,IAAA,IAAS5C,GAAKH,KAAK4C,SAAW,IAAIxC,OAAQD,KACzCH,KAAK4C,QAAQzC,GAAG6C,aAAaD,EAa9B,EAOD,UAAA9C,CAAWtB,EAASsE,GACdA,GAAWjD,KAAKkD,UACpBlD,KAAKkD,QAAU,IAEhB,MAAM7E,EAAQ,IAAI8E,EAAMA,OAACnD,MAAMoD,MAAMzE,GAejC,GATCqB,KAAAqD,KAAKrD,KAAM,QAASiD,GAAUjD,KAAK3B,OAAS,IAAIiF,OAAOjF,GAASA,GAGrE2B,KAAK4C,QAAU,GACf5C,KAAKuD,WAAU,KACdvD,KAAKQ,MAAM,UACXR,KAAKwD,MAAM,OAAM,IAGdxD,KAAKhB,UAAYgB,KAAKkD,QAAQO,YAAczD,KAAKkD,QAAQ9C,OAAS,EAAG,CAExE,IAAIsD,EAAS,EACb,MAAMC,EAAmBC,IACnBA,GAASA,EAAKF,SAAQE,EAAO,CAAC,GAE/BA,EAAKF,SAAWA,EACd1D,KAAAwD,MAAM,QAASI,IAEpBF,EAASE,EAAKF,OACdG,YAAW,KACV7D,KAAK0C,UAAUoB,KAAKH,GAAUI,MAAMJ,EAAQ,GAC1C,KACJ,EAED3D,KAAK0C,UAAUoB,KAAKH,GAAUI,MAAMJ,QAG/B3D,KAAKkD,QAAQO,aACZzD,KAAA0C,UAAUoB,MAAaF,IACtB5D,KAAAwD,MAAM,QAASI,EAAI,IACtBG,OAAM,KACH/D,KAAAwD,MAAM,QAAS,GAAE,GAKzB,EAKD,KAAAhD,CAAMrC,GACI,IAAA,IAAAgC,EAAIlC,EAAQmC,OAAQD,KACxBH,KAAK/B,QAAQkC,GAAGhC,IACnB6B,KAAK/B,QAAQkC,GAAGhC,IAGlB,oUChXH6F,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}