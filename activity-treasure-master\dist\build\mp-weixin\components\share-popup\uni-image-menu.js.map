{"version": 3, "file": "uni-image-menu.js", "sources": ["../../../../../src/components/share-popup/uni-image-menu.js"], "sourcesContent": ["var nvMask, nvImageMenu;\r\nclass NvImageMenu {\r\n\tconstructor(arg) {\r\n\t\tthis.isShow = false\r\n\t}\r\n\tshow({\r\n\t\tlist,\r\n\t\tcancelText\r\n\t}, callback) {\r\n\t\tif (!list) {\r\n\t\t\tlist = [{\r\n\t\t\t\t\"img\": \"/static/sharemenu/wechatfriend.png\",\r\n\t\t\t\t\"text\": \"图标文字\"\r\n\t\t\t}]\r\n\t\t}\r\n\t\t//以下为计算菜单的nview绘制布局，为固定算法，使用者无关关心\r\n\t\tvar screenWidth = plus.screen.resolutionWidth\r\n\t\t//以360px宽度屏幕为例，上下左右边距及2排按钮边距留25像素，图标宽度55像素，同行图标间的间距在360宽的屏幕是30px，但需要动态计算，以此原则计算4列图标分别的left位置\r\n\t\t//图标下的按钮文字距离图标5像素，文字大小12像素\r\n\t\t//底部取消按钮高度固定为44px\r\n\t\t//TODO 未处理横屏和pad，这些情况6个图标应该一排即可\r\n\t\tvar margin = 20,\r\n\t\t\ticonWidth = 60,\r\n\t\t\ticontextSpace = 5,\r\n\t\t\ttextHeight = 12\r\n\t\tvar left1 = margin / 360 * screenWidth\r\n\t\tvar iconSpace = (screenWidth - (left1 * 2) - (iconWidth * 4)) / 3 //屏幕宽度减去左右留白间距，再减去4个图标的宽度，就是3个同行图标的间距\r\n\t\tif (iconSpace <= 5) { //屏幕过窄时，缩小边距和图标大小，再算一次\r\n\t\t\tmargin = 15\r\n\t\t\ticonWidth = 40\r\n\t\t\tleft1 = margin / 360 * screenWidth\r\n\t\t\ticonSpace = (screenWidth - (left1 * 2) - (iconWidth * 4)) / 3 //屏幕宽度减去左右留白间距，再减去4个图标的宽度，就是3个同行图标的间距\r\n\t\t}\r\n\t\tvar left2 = left1 + iconWidth + iconSpace\r\n\t\tvar left3 = left1 + (iconWidth + iconSpace) * 2\r\n\t\tvar left4 = left1 + (iconWidth + iconSpace) * 3\r\n\t\tvar top1 = left1\r\n\t\tvar top2 = top1 + iconWidth + icontextSpace + textHeight + left1\r\n\r\n\t\tconst TOP = {\r\n\t\t\t\ttop1,\r\n\t\t\t\ttop2\r\n\t\t\t},\r\n\t\t\tLEFT = {\r\n\t\t\t\tleft1,\r\n\t\t\t\tleft2,\r\n\t\t\t\tleft3,\r\n\t\t\t\tleft4\r\n\t\t\t};\r\n\r\n\t\tnvMask = new plus.nativeObj.View(\"nvMask\", { //先创建遮罩层\r\n\t\t\ttop: '0px',\r\n\t\t\tleft: '0px',\r\n\t\t\theight: '100%',\r\n\t\t\twidth: '100%',\r\n\t\t\tbackgroundColor: 'rgba(0,0,0,0.2)'\r\n\t\t});\r\n\t\tnvImageMenu = new plus.nativeObj.View(\"nvImageMenu\", { //创建底部图标菜单\r\n\t\t\tbottom: '0px',\r\n\t\t\tleft: '0px',\r\n\t\t\theight: (iconWidth + textHeight + 2 * margin) * Math.ceil(list.length / 4) + 44 +\r\n\t\t\t\t'px', //'264px',\r\n\t\t\twidth: '100%',\r\n\t\t\tbackgroundColor: 'rgb(255,255,255)'\r\n\t\t});\r\n\t\tnvMask.addEventListener(\"click\", () => { //处理遮罩层点击\r\n\t\t\t// console.log('处理遮罩层点击');\r\n\t\t\tthis.hide()\r\n\t\t\tcallback({\r\n\t\t\t\tevent: \"clickMask\"\r\n\t\t\t})\r\n\t\t})\r\n\t\tlet myList = []\r\n\t\tlist.forEach((item, i) => {\r\n\t\t\tmyList.push({\r\n\t\t\t\ttag: 'img',\r\n\t\t\t\tsrc: item.img,\r\n\t\t\t\tposition: {\r\n\t\t\t\t\ttop: TOP['top' + (parseInt(i / 4) + 1)],\r\n\t\t\t\t\tleft: LEFT['left' + (1 + i % 4)],\r\n\t\t\t\t\twidth: iconWidth,\r\n\t\t\t\t\theight: iconWidth\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tmyList.push({\r\n\t\t\t\ttag: 'font',\r\n\t\t\t\ttext: item.text,\r\n\t\t\t\ttextStyles: {\r\n\t\t\t\t\tsize: textHeight\r\n\t\t\t\t},\r\n\t\t\t\tposition: {\r\n\t\t\t\t\ttop: TOP['top' + (parseInt(i / 4) + 1)] + iconWidth + icontextSpace,\r\n\t\t\t\t\tleft: LEFT['left' + (1 + i % 4)],\r\n\t\t\t\t\twidth: iconWidth,\r\n\t\t\t\t\theight: textHeight\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\r\n\t\t//绘制底部图标菜单的内容\r\n\t\tnvImageMenu.draw([{\r\n\t\t\t\ttag: 'rect', //菜单顶部的分割灰线\r\n\t\t\t\tcolor: '#e7e7e7',\r\n\t\t\t\tposition: {\r\n\t\t\t\t\ttop: '0px',\r\n\t\t\t\t\theight: '1px'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\ttag: 'font',\r\n\t\t\t\ttext: cancelText, //底部取消按钮的文字\r\n\t\t\t\ttextStyles: {\r\n\t\t\t\t\tsize: '14px'\r\n\t\t\t\t},\r\n\t\t\t\tposition: {\r\n\t\t\t\t\tbottom: '0px',\r\n\t\t\t\t\theight: '44px'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\ttag: 'rect', //底部取消按钮的顶部边线\r\n\t\t\t\tcolor: '#e7e7e7',\r\n\t\t\t\tposition: {\r\n\t\t\t\t\tbottom: '45px',\r\n\t\t\t\t\theight: '1px'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t...myList\r\n\t\t])\r\n\t\tnvMask.show()\r\n\t\tnvImageMenu.show()\r\n\t\t// 开始动画\r\n\t\t/*\r\n\t\t\tplus.nativeObj.View.startAnimation({\r\n\t\t\t\ttype: 'slide-in-bottom',\r\n\t\t\t\tduration: 300\r\n\t\t\t}, nvImageMenu, {}, function() {\r\n\t\t\t\tconsole.log('plus.nativeObj.View.startAnimation动画结束');\r\n\t\t\t\t// 关闭原生动画\r\n\t\t\t\tplus.nativeObj.View.clearAnimation();\r\n\t\t\t\tnvImageMenu.show()\r\n\t\t\t});\r\n\t\t*/\r\n\r\n\r\n\t\tthis.isShow = true\r\n\t\tnvImageMenu.addEventListener(\"click\", e => { //处理底部图标菜单的点击事件，根据点击位置触发不同的逻辑\r\n\t\t\t// console.log(\"click menu\"+JSON.stringify(e));\r\n\t\t\tif (e.screenY > plus.screen.resolutionHeight - 44) { //点击了底部取消按钮\r\n\t\t\t\t// callback({event:\"clickCancelButton\"})\r\n\t\t\t\tthis.hide()\r\n\t\t\t} else if (e.clientX < 5 || e.clientX > screenWidth - 5 || e.clientY < 5) {\r\n\t\t\t\t//屏幕左右边缘5像素及菜单顶部5像素不处理点击\r\n\t\t\t} else { //点击了图标按钮\r\n\t\t\t\tvar iClickIndex = -1 //点击的图标按钮序号，第一个图标按钮的index为0\r\n\t\t\t\tvar iRow = e.clientY < (top2 - (left1 / 2)) ? 0 : 1\r\n\t\t\t\tvar iCol = -1\r\n\t\t\t\tif (e.clientX < (left2 - (iconSpace / 2))) {\r\n\t\t\t\t\tiCol = 0\r\n\t\t\t\t} else if (e.clientX < (left3 - (iconSpace / 2))) {\r\n\t\t\t\t\tiCol = 1\r\n\t\t\t\t} else if (e.clientX < (left4 - (iconSpace / 2))) {\r\n\t\t\t\t\tiCol = 2\r\n\t\t\t\t} else {\r\n\t\t\t\t\tiCol = 3\r\n\t\t\t\t}\r\n\t\t\t\tif (iRow == 0) {\r\n\t\t\t\t\tiClickIndex = iCol\r\n\t\t\t\t} else {\r\n\t\t\t\t\tiClickIndex = iCol + 4\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(\"点击按钮的序号: \" + iClickIndex);\r\n\t\t\t\t// if (iClickIndex >= 0 && iClickIndex <= 5) { //处理具体的点击逻辑，此处也可以自行定义逻辑。如果增减了按钮，此处也需要跟着修改\r\n\t\t\t\t// }\r\n\t\t\t\tcallback({\r\n\t\t\t\t\tevent: \"clickMenu\",\r\n\t\t\t\t\tindex: iClickIndex\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t})\r\n\t\t/* nvImageMenu.addEventListener(\"touchstart\", function(e) {\r\n\t\t\tif (e.screenY > (plus.screen.resolutionHeight - 44)) {\r\n\t\t\t\t//TODO 这里可以处理按下背景变灰的效果\r\n\t\t\t}\r\n\t\t})\r\n\t\tnvImageMenu.addEventListener(\"touchmove\", function(e) {\r\n\t\t\t//TODO 这里可以处理按下背景变灰的效果\r\n\t\t\tif (e.screenY > plus.screen.resolutionHeight - 44) {}\r\n\t\t})\r\n\t\tnvImageMenu.addEventListener(\"touchend\", function(e) {\r\n\t\t\t//TODO 这里可以处理释放背景恢复的效果\r\n\t\t})\r\n\t\t*/\r\n\t}\r\n\r\n\thide() {\r\n\t\tif (this.isShow) {\r\n\t\t\tnvMask.hide()\r\n\t\t\tnvImageMenu.hide()\r\n\t\t\tthis.isShow = false\r\n\t\t}\r\n\t}\r\n}\r\nexport default NvImageMenu"], "names": ["nvMask", "nvImageMenu", "constructor", "arg", "this", "isShow", "show", "list", "cancelText", "callback", "img", "text", "screenWidth", "plus", "screen", "resolutionWidth", "margin", "iconWidth", "left1", "iconSpace", "left2", "left3", "left4", "top2", "TOP", "top1", "LEFT", "nativeObj", "View", "top", "left", "height", "width", "backgroundColor", "bottom", "Math", "ceil", "length", "addEventListener", "hide", "event", "myList", "for<PERSON>ach", "item", "i", "push", "tag", "src", "position", "parseInt", "textStyles", "size", "draw", "color", "e", "screenY", "resolutionHeight", "clientX", "clientY", "iRow", "iCol", "index"], "mappings": "aAAA,IAAIA,EAAQC,sBACZ,MACC,WAAAC,CAAYC,GACXC,KAAKC,QAAS,CACd,CACD,IAAAC,EAAKC,KACJA,EAAAC,WACAA,GACEC,GACGF,IACJA,EAAO,CAAC,CACPG,IAAO,qCACPC,KAAQ,UAIN,IAAAC,EAAcC,KAAKC,OAAOC,gBAK1BC,EAAS,GACZC,EAAY,GAGTC,EAAQF,EAAS,IAAMJ,EACvBO,GAAaP,EAAuB,EAARM,EAA0B,EAAZD,GAAkB,EAC5DE,GAAa,IAIhBA,GAAaP,EAAuB,GADpCM,GAFSF,EAAA,IAEQ,IAAMJ,GAC+B,GAF1CK,EAAA,KAEgD,GAEzD,IAAAG,EAAQF,EAAQD,EAAYE,EAC5BE,EAAQH,EAAkC,GAAzBD,EAAYE,GAC7BG,EAAQJ,EAAkC,GAAzBD,EAAYE,GAE7BI,EADOL,EACOD,EAdD,EACH,GAa6CC,EAE3D,MAAMM,EAAM,CACVC,KAJSP,EAKTK,QAEDG,EAAO,CACNR,QACAE,QACAC,QACAC,SAGFtB,EAAS,IAAIa,KAAKc,UAAUC,KAAK,SAAU,CAC1CC,IAAK,MACLC,KAAM,MACNC,OAAQ,OACRC,MAAO,OACPC,gBAAiB,oBAElBhC,EAAc,IAAIY,KAAKc,UAAUC,KAAK,cAAe,CACpDM,OAAQ,MACRJ,KAAM,MACNC,QAASd,EApCI,GAoCqB,EAAID,GAAUmB,KAAKC,KAAK7B,EAAK8B,OAAS,GAAK,GAC5E,KACDL,MAAO,OACPC,gBAAiB,qBAEXjC,EAAAsC,iBAAiB,SAAS,KAEhClC,KAAKmC,OACI9B,EAAA,CACR+B,MAAO,aACP,IAEF,IAAIC,EAAS,GACRlC,EAAAmC,SAAQ,CAACC,EAAMC,KACnBH,EAAOI,KAAK,CACXC,IAAK,MACLC,IAAKJ,EAAKjC,IACVsC,SAAU,CACTnB,IAAKL,EAAI,OAASyB,SAASL,EAAI,GAAK,IACpCd,KAAMJ,EAAK,QAAU,EAAIkB,EAAI,IAC7BZ,MAAOf,EACPc,OAAQd,KAGVwB,EAAOI,KAAK,CACXC,IAAK,OACLnC,KAAMgC,EAAKhC,KACXuC,WAAY,CACXC,KAhEW,IAkEZH,SAAU,CACTnB,IAAKL,EAAI,OAASyB,SAASL,EAAI,GAAK,IAAM3B,EApE5B,EAqEda,KAAMJ,EAAK,QAAU,EAAIkB,EAAI,IAC7BZ,MAAOf,EACPc,OAtEW,KAwEZ,IAIF9B,EAAYmD,KAAK,CAAC,CAChBN,IAAK,OACLO,MAAO,UACPL,SAAU,CACTnB,IAAK,MACLE,OAAQ,QAGV,CACCe,IAAK,OACLnC,KAAMH,EACN0C,WAAY,CACXC,KAAM,QAEPH,SAAU,CACTd,OAAQ,MACRH,OAAQ,SAGV,CACCe,IAAK,OACLO,MAAO,UACPL,SAAU,CACTd,OAAQ,OACRH,OAAQ,WAGPU,IAEJzC,EAAOM,OACPL,EAAYK,OAeZF,KAAKC,QAAS,EACFJ,EAAAqC,iBAAiB,SAAcgB,IAE1C,GAAIA,EAAEC,QAAU1C,KAAKC,OAAO0C,iBAAmB,GAE9CpD,KAAKmC,YACL,GAAUe,EAAEG,QAAU,GAAKH,EAAEG,QAAU7C,EAAc,GAAK0C,EAAEI,QAAU,OAEhE,CACN,IACIC,EAAOL,EAAEI,QAAWnC,EAAQL,EAAQ,EAAM,EAAI,EAC9C0C,GAAO,EAEHA,EADJN,EAAEG,QAAWrC,EAASD,EAAY,EAC9B,EACGmC,EAAEG,QAAWpC,EAASF,EAAY,EACrC,EACGmC,EAAEG,QAAWnC,EAASH,EAAY,EACrC,EAEA,EAUCV,EAAA,CACR+B,MAAO,YACPqB,MAVW,GAARF,EACWC,EAEAA,EAAO,GAStB,IAeF,CAED,IAAArB,GACKnC,KAAKC,SACRL,EAAOuC,OACPtC,EAAYsC,OACZnC,KAAKC,QAAS,EAEf"}