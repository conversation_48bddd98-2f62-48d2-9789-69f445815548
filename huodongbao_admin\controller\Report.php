<?php
namespace controller;
use core\Controller;
use core\Db;

/**
 * {{ AURA-X: Add - 创建举报管理控制器. Confirmed via 寸止. }}
 * @className 举报管理
 */
class Report extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}

	/**
	 * @name 举报列表
	 * @method index
	 */			
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		
		// 状态筛选
		if(isset($_REQUEST['status']) && in_array($_REQUEST['status'], ['pending','processed','rejected'])){
			$status = $_REQUEST['status'];
			$where .= " AND r.status=:status";
			$prepareParam[":status"] = $status;
			$pageParams['status'] = $status;
			assign("status", $status);
		}
		
		// 举报人筛选
		if(isset($_REQUEST['reporter_uid']) && check($_REQUEST['reporter_uid'],"intgt0")){
			$reporter_uid = (int)$_REQUEST['reporter_uid'];
			$where .= " AND r.reporter_uid=:reporter_uid";
			$prepareParam[":reporter_uid"] = $reporter_uid;
			$pageParams['reporter_uid'] = $reporter_uid;
			assign("reporter_uid", $reporter_uid);
		}
		
		// 被举报人筛选
		if(isset($_REQUEST['reported_uid']) && check($_REQUEST['reported_uid'],"intgt0")){
			$reported_uid = (int)$_REQUEST['reported_uid'];
			$where .= " AND r.reported_uid=:reported_uid";
			$prepareParam[":reported_uid"] = $reported_uid;
			$pageParams['reported_uid'] = $reported_uid;
			assign("reported_uid", $reported_uid);
		}
		
		// 活动筛选
		if(isset($_REQUEST['activity_id']) && check($_REQUEST['activity_id'],"intgt0")){
			$activity_id = (int)$_REQUEST['activity_id'];
			$where .= " AND r.activity_id=:activity_id";
			$prepareParam[":activity_id"] = $activity_id;
			$pageParams['activity_id'] = $activity_id;
			assign("activity_id", $activity_id);
		}
		
		$sql = "SELECT r.*, 
				   reporter.nickname as reporter_nickname,
				   reported.nickname as reported_nickname,
				   h.name as activity_name,
				   ro.title as report_reason,
				   ro.penalty_type,
				   ro.penalty_value
			FROM user_reports r
			LEFT JOIN user reporter ON r.reporter_uid = reporter.uid
			LEFT JOIN user reported ON r.reported_uid = reported.uid
			LEFT JOIN huodong h ON r.activity_id = h.id
			LEFT JOIN report_options ro ON r.report_option_id = ro.id
			WHERE {$where}
			ORDER BY r.created_at DESC";
		
		$data = Db()->table("")->pageQuery($sql, $page, $page_size, $prepareParam);
		assign("data", $data);
		assign("pageParams", $pageParams);
		
		// 获取状态统计
		$stats = [
			'pending' => Db()->table("user_reports")->where("status='pending'")->count(),
			'processed' => Db()->table("user_reports")->where("status='processed'")->count(),
			'rejected' => Db()->table("user_reports")->where("status='rejected'")->count()
		];
		assign("stats", $stats);
		
		display("report/index.html");
	}

	/**
	 * @name 查看举报详情
	 * @method view
	 */
	public function view(){
		if(!isset($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			$this->error("参数错误");
		}
		
		$id = (int)$_REQUEST['id'];
		dbConn();
		
		$sql = "SELECT r.*, 
				   reporter.nickname as reporter_nickname,
				   reporter.mobile as reporter_mobile,
				   reported.nickname as reported_nickname,
				   reported.mobile as reported_mobile,
				   h.name as activity_name,
				   h.start_time as activity_start_time,
				   ro.title as report_reason,
				   ro.description as report_description,
				   ro.penalty_type,
				   ro.penalty_value
			FROM user_reports r
			LEFT JOIN user reporter ON r.reporter_uid = reporter.uid
			LEFT JOIN user reported ON r.reported_uid = reported.uid
			LEFT JOIN huodong h ON r.activity_id = h.id
			LEFT JOIN report_options ro ON r.report_option_id = ro.id
			WHERE r.id = :id";
		
		$report = Db()->_fetch($sql, [":id" => $id]);
		
		if(empty($report)){
			$this->error("举报记录不存在");
		}
		
		// 解析证据图片
		if(!empty($report['evidence_images'])){
			$report['evidence_images'] = json_decode($report['evidence_images'], true);
		}
		
		assign("report", $report);
		display("report/view.html");
	}

	/**
	 * @name 处理举报
	 * @method process
	 */
	public function process(){
		if(!isset($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			$this->error("参数错误");
		}
		
		if(!isset($_REQUEST['action']) || !in_array($_REQUEST['action'], ['approve','reject'])){
			$this->error("操作类型错误");
		}
		
		$id = (int)$_REQUEST['id'];
		$action = $_REQUEST['action'];
		$admin_note = isset($_REQUEST['admin_note']) ? trim($_REQUEST['admin_note']) : '';
		
		dbConn();
		
		try {
			Db::begin();
			
			// 获取举报信息
			$sql = "SELECT r.*, ro.penalty_type, ro.penalty_value, ro.title
				FROM user_reports r
				LEFT JOIN report_options ro ON r.report_option_id = ro.id
				WHERE r.id = :id AND r.status = 'pending'";
			
			$report = Db()->_fetch($sql, [":id" => $id]);
			
			if(empty($report)){
				throw new \Exception("举报记录不存在或已处理");
			}
			
			if($action === 'approve'){
				// 批准举报，执行惩罚
				$this->apply_penalty($report['reported_uid'], $report);
				$status = 'processed';
			} else {
				// 拒绝举报
				$status = 'rejected';
			}
			
			// 更新举报状态
			$sql = "UPDATE user_reports SET 
					status = :status,
					admin_note = :admin_note,
					processed_at = NOW()
				WHERE id = :id";
			
			Db()->_exec($sql, [
				":status" => $status,
				":admin_note" => $admin_note,
				":id" => $id
			]);
			
			Db::commit();
			
			$this->success("处理成功", "?c=Report&m=index");
			
		} catch (\Throwable $e) {
			Db::rollback();
			$this->error("处理失败：" . $e->getMessage());
		}
	}

	/**
	 * 执行惩罚
	 */
	private function apply_penalty($reported_uid, $report) {
		// {{ AURA-X: Modify - 直接使用数据库操作处理惩罚. Confirmed via 寸止. }}
		if ($report['penalty_type'] == 'points') {
			// 扣除积分
			$points_change = -abs($report['penalty_value']);
			$description = "被举报：{$report['title']}";

			// 直接更新用户积分
			Db()->table("user")
				->where("uid=:uid")
				->prepareParam([":uid" => $reported_uid])
				->update(["points" => "points + ({$points_change})"]);

			// 使用User类的add_points方法记录积分变动
			\controller\User::add_points($reported_uid, $points_change, 'report_penalty', null, $description);

		} elseif ($report['penalty_type'] == 'freeze') {
			// 冻结账号
			$freeze_days = $report['penalty_value'];
			$freeze_until = date('Y-m-d H:i:s', strtotime("+{$freeze_days} days"));

			Db()->table("user")
				->where("uid=:uid")
				->prepareParam([":uid" => $reported_uid])
				->update(["is_dongjie" => 1, "dongjie_until" => $freeze_until]);
		}
	}
}
