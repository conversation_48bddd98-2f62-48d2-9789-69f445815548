{"version": 3, "file": "u-icon.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-icon/u-icon.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWljb24vdS1pY29uLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-icon\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"['u-icon--' + labelPos]\"\n\t>\n\t\t<image\n\t\t    class=\"u-icon__img\"\n\t\t    v-if=\"isImg\"\n\t\t    :src=\"name\"\n\t\t    :mode=\"imgMode\"\n\t\t    :style=\"[imgStyle, addStyle(customStyle)]\"\n\t\t></image>\n\t\t<text\n\t\t    v-else\n\t\t    class=\"u-icon__icon\"\n\t\t    :class=\"uClasses\"\n\t\t    :style=\"[iconStyle, addStyle(customStyle)]\"\n\t\t    :hover-class=\"hoverClass\"\n\t\t>{{icon}}</text>\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\n\t\t<text\n\t\t    v-if=\"label !== ''\" \n\t\t    class=\"u-icon__label\"\n\t\t    :style=\"{\n\t\t\tcolor: labelColor,\n\t\t\tfontSize: addUnit(labelSize),\n\t\t\tmarginLeft: labelPos == 'right' ? addUnit(space) : 0,\n\t\t\tmarginTop: labelPos == 'bottom' ? addUnit(space) : 0,\n\t\t\tmarginRight: labelPos == 'left' ? addUnit(space) : 0,\n\t\t\tmarginBottom: labelPos == 'top' ? addUnit(space) : 0,\n\t\t}\"\n\t\t>{{ label }}</text>\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\t// nvue通过weex的dom模块引入字体，相关文档地址如下：\n\t// https://weex.apache.org/zh/docs/modules/dom.html#addrule\n\tconst fontUrl = 'https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf'\n\tconst domModule = weex.requireModule('dom')\n\tdomModule.addRule('fontFace', {\n\t\t'fontFamily': \"uicon-iconfont\",\n\t\t'src': `url('${fontUrl}')`\n\t})\n\t// #endif\n\n\t// 引入图标名称，已经对应的unicode\n\timport icons from './icons'\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle } from '../../libs/function/index';\n\timport config from '../../libs/config/config';\n\t/**\n\t * icon 图标\n\t * @description 基于字体的图标集，包含了大多数常见场景的图标。\n\t * @tutorial https://ijry.github.io/uview-plus/components/icon.html\n\t * @property {String}\t\t\tname\t\t\t图标名称，见示例图标集\n\t * @property {String}\t\t\tcolor\t\t\t图标颜色,可接受主题色 （默认 color['u-content-color'] ）\n\t * @property {String | Number}\tsize\t\t\t图标字体大小，单位px （默认 '16px' ）\n\t * @property {Boolean}\t\t\tbold\t\t\t是否显示粗体 （默认 false ）\n\t * @property {String | Number}\tindex\t\t\t点击图标的时候传递事件出去的index（用于区分点击了哪一个）\n\t * @property {String}\t\t\thoverClass\t\t图标按下去的样式类，用法同uni的view组件的hoverClass参数，详情见官网\n\t * @property {String}\t\t\tcustomPrefix\t自定义扩展前缀，方便用户扩展自己的图标库 （默认 'uicon' ）\n\t * @property {String | Number}\tlabel\t\t\t图标右侧的label文字\n\t * @property {String}\t\t\tlabelPos\t\tlabel相对于图标的位置，只能right或bottom （默认 'right' ）\n\t * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px （默认 '15px' ）\n\t * @property {String}\t\t\tlabelColor\t\t图标右侧的label文字颜色 （ 默认 color['u-content-color'] ）\n\t * @property {String | Number}\tspace\t\t\tlabel与图标的距离，单位px （默认 '3px' ）\n\t * @property {String}\t\t\timgMode\t\t\t图片的mode\n\t * @property {String | Number}\twidth\t\t\t显示图片小图标时的宽度\n\t * @property {String | Number}\theight\t\t\t显示图片小图标时的高度\n\t * @property {String | Number}\ttop\t\t\t\t图标在垂直方向上的定位 用于解决某些情况下，让图标垂直居中的用途  （默认 0 ）\n\t * @property {Boolean}\t\t\tstop\t\t\t是否阻止事件传播 （默认 false ）\n\t * @property {Object}\t\t\tcustomStyle\t\ticon的样式，对象形式\n\t * @event {Function} click 点击图标时触发\n\t * @event {Function} touchstart 事件触摸时触发\n\t * @example <u-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></u-icon>\n\t */\n\texport default {\n\t\tname: 'u-icon',\n\t\tdata() {\n\t\t\treturn {\n\n\t\t\t}\n\t\t},\n\t\temits: ['click'],\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tuClasses() {\n\t\t\t\tlet classes = []\n\t\t\t\tclasses.push(this.customPrefix + '-' + this.name)\n\t\t\t\t// uView的自定义图标类名为u-iconfont\n\t\t\t\tif (this.customPrefix == 'uicon') {\n\t\t\t\t\tclasses.push('u-iconfont')\n\t\t\t\t} else {\n\t\t\t\t\t// 不能缺少这一步，否则自定义图标会无效\n\t\t\t\t\tclasses.push(this.customPrefix)\n\t\t\t\t}\n\t\t\t\t// 主题色，通过类配置\n\t\t\t\tif (this.color && config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\n\t\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\n\t\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\n\t\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\n\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t//#endif\n\t\t\t\treturn classes\n\t\t\t},\n\t\t\ticonStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle = {\n\t\t\t\t\tfontSize: addUnit(this.size),\n\t\t\t\t\tlineHeight: addUnit(this.size),\n\t\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\n\t\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\n\t\t\t\t\ttop: addUnit(this.top)\n\t\t\t\t}\n\t\t\t\t// 非主题色值时，才当作颜色值\n\t\t\t\tif (this.color && !config.type.includes(this.color)) style.color = this.color\n\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\t\tisImg() {\n\t\t\t\treturn this.name.indexOf('/') !== -1\n\t\t\t},\n\t\t\timgStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\n\t\t\t\tstyle.width = this.width ? addUnit(this.width) : addUnit(this.size)\n\t\t\t\tstyle.height = this.height ? addUnit(this.height) : addUnit(this.size)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 通过图标名，查找对应的图标\n\t\t\ticon() {\n\t\t\t\t// 使用自定义图标的时候页面上会把name属性也展示出来，所以在这里处理一下\n\t\t\t\tif (this.customPrefix !== \"uicon\") return \"\";\n\t\t\t\t// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码\n\t\t\t\treturn icons['uicon-' + this.name] || this.name\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tclickHandler(e) {\n\t\t\t\tthis.$emit('click', this.index)\n\t\t\t\t// 是否阻止事件冒泡\n\t\t\t\tthis.stop && this.preventEvent(e)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t// 变量定义\n\t$u-icon-primary: $u-primary !default;\n\t$u-icon-success: $u-success !default;\n\t$u-icon-info: $u-info !default;\n\t$u-icon-warning: $u-warning !default;\n\t$u-icon-error: $u-error !default;\n\t$u-icon-label-line-height:1 !default;\n\n\t/* #ifndef APP-NVUE */\n\t// 非nvue下加载字体\n\t@font-face {\n\t\tfont-family: 'uicon-iconfont';\n\t\tsrc: url('https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf') format('truetype');\n\t}\n\n\t/* #endif */\n\n\t.u-icon {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\talign-items: center;\n\n\t\t&--left {\n\t\t\tflex-direction: row-reverse;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--right {\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--top {\n\t\t\tflex-direction: column-reverse;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&--bottom {\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&__icon {\n\t\t\tfont-family: uicon-iconfont;\n\t\t\tposition: relative;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\n\t\t\t&--primary {\n\t\t\t\tcolor: $u-icon-primary;\n\t\t\t}\n\n\t\t\t&--success {\n\t\t\t\tcolor: $u-icon-success;\n\t\t\t}\n\n\t\t\t&--error {\n\t\t\t\tcolor: $u-icon-error;\n\t\t\t}\n\n\t\t\t&--warning {\n\t\t\t\tcolor: $u-icon-warning;\n\t\t\t}\n\n\t\t\t&--info {\n\t\t\t\tcolor: $u-icon-info;\n\t\t\t}\n\t\t}\n\n\t\t&__img {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\theight: auto;\n\t\t\twill-change: transform;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&__label {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tline-height: $u-icon-label-line-height;\n\t\t\t/* #endif */\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-icon/u-icon.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "data", "emits", "mixins", "mpMixin", "mixin", "props", "computed", "uClasses", "classes", "push", "this", "customPrefix", "color", "config", "type", "includes", "iconStyle", "style", "fontSize", "addUnit", "size", "lineHeight", "fontWeight", "bold", "top", "isImg", "indexOf", "imgStyle", "width", "height", "icon", "icons", "methods", "addStyle", "clickHandler", "e", "$emit", "index", "stop", "preventEvent", "wx", "createComponent", "Component"], "mappings": "6DAiFMA,EAAU,CACdC,KAAM,SACNC,KAAO,KACC,CAEP,GAEDC,MAAO,CAAC,SACRC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,SAAU,CACT,QAAAC,GACC,IAAIC,EAAU,GAgBP,OAfPA,EAAQC,KAAKC,KAAKC,aAAe,IAAMD,KAAKX,MAEnB,SAArBW,KAAKC,aACRH,EAAQC,KAAK,cAGLD,EAAAC,KAAKC,KAAKC,cAGfD,KAAKE,OAASC,SAAOC,KAAKC,SAASL,KAAKE,QAAgBJ,EAAAC,KAAK,iBAAmBC,KAAKE,OAMlFJ,CACP,EACD,SAAAQ,GACC,IAAIC,EAAQ,CAAC,EAWN,OAVCA,EAAA,CACPC,SAAUC,EAAAA,QAAQT,KAAKU,MACvBC,WAAYF,EAAAA,QAAQT,KAAKU,MACzBE,WAAYZ,KAAKa,KAAO,OAAS,SAEjCC,IAAKL,EAAAA,QAAQT,KAAKc,MAGfd,KAAKE,QAAUC,EAAAA,OAAOC,KAAKC,SAASL,KAAKE,SAAQK,EAAML,MAAQF,KAAKE,OAEjEK,CACP,EAED,KAAAQ,GACC,OAAkC,IAA3Bf,KAAKX,KAAK2B,QAAQ,IACzB,EACD,QAAAC,GACC,IAAIV,EAAQ,CAAC,EAIN,OAFDA,EAAAW,MAAQlB,KAAKkB,MAAQT,EAAOA,QAACT,KAAKkB,OAAST,EAAAA,QAAQT,KAAKU,MACxDH,EAAAY,OAASnB,KAAKmB,OAASV,EAAOA,QAACT,KAAKmB,QAAUV,EAAAA,QAAQT,KAAKU,MAC1DH,CACP,EAED,IAAAa,GAEC,MAA0B,UAAtBpB,KAAKC,aAAiC,GAEnCoB,EAAAA,MAAM,SAAWrB,KAAKX,OAASW,KAAKX,IAC5C,GAEDiC,QAAS,CACRC,SAAAA,EAAQA,SACRd,QAAAA,EAAOA,QACP,YAAAe,CAAaC,GACPzB,KAAA0B,MAAM,QAAS1B,KAAK2B,OAEpB3B,KAAA4B,MAAQ5B,KAAK6B,aAAaJ,EAChC,yoBCrJHK,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}