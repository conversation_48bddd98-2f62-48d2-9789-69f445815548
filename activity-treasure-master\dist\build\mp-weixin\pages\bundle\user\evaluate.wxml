<view class="page px30"><view wx:for="{{a}}" wx:for-item="val" wx:key="r" class="pt20 borderBottom"><my-line wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></my-line><u-gap wx:if="{{b}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{b}}"></u-gap><u-rate wx:if="{{val.f}}" u-i="{{val.d}}" bind:__l="__l" bindupdateModelValue="{{val.e}}" u-p="{{val.f}}"></u-rate><u-gap wx:if="{{c}}" u-i="{{val.g}}" bind:__l="__l" u-p="{{c}}"></u-gap><u-textarea wx:if="{{val.j}}" u-i="{{val.h}}" bind:__l="__l" bindupdateModelValue="{{val.i}}" u-p="{{val.j}}"></u-textarea><u-gap wx:if="{{d}}" u-i="{{val.k}}" bind:__l="__l" u-p="{{d}}"></u-gap><u-upload wx:if="{{val.p}}" u-s="{{['d']}}" bindafterRead="{{val.m}}" binddelete="{{val.n}}" u-i="{{val.o}}" bind:__l="__l" u-p="{{val.p}}"><view class="df aic jcc b6e" style="width:215rpx;height:215rpx"><u-icon wx:if="{{e}}" u-i="{{val.l}}" bind:__l="__l" u-p="{{e}}"></u-icon></view></u-upload><u-gap wx:if="{{f}}" u-i="{{val.q}}" bind:__l="__l" u-p="{{f}}"></u-gap></view><u-gap wx:if="{{g}}" u-i="6776ca4a-9" bind:__l="__l" u-p="{{g}}"></u-gap><view class="pfx w690 bottom0 tl50 left50 bottomBox"><u-button wx:if="{{i}}" bindclick="{{h}}" u-i="6776ca4a-10" bind:__l="__l" u-p="{{i}}"></u-button><u-safe-bottom u-i="6776ca4a-11" bind:__l="__l"></u-safe-bottom></view></view>