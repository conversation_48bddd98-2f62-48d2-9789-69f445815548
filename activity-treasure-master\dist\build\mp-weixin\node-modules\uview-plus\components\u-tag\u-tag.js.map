{"version": 3, "file": "u-tag.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-tag/u-tag.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXRhZy91LXRhZy52dWU"], "sourcesContent": ["<template>\n\t<u-transition\n\t\tmode=\"fade\"\n\t\t:show=\"show\"\n\t\tstyle=\"display: inline-flex;\"\n\t>\n\t\t<view class=\"u-tag-wrapper cursor-pointer\">\n\t\t\t<view\n\t\t\t\tclass=\"u-tag\"\n\t\t\t\t:class=\"[`u-tag--${shape}`, !plain && `u-tag--${type}`, plain && `u-tag--${type}--plain`, `u-tag--${size}`, plain && plainFill && `u-tag--${type}--plain--fill`]\"\n\t\t\t\**********=\"clickHandler\"\n\t\t\t\t:style=\"[{\n\t\t\t\t\tmarginRight: closable ? '10px' : 0,\n\t\t\t\t\tmarginTop: closable ? '10px' : 0,\n\t\t\t\t}, style]\"\n\t\t\t>\n\t\t\t\t<slot name=\"icon\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-tag__icon\"\n\t\t\t\t\t\tv-if=\"icon\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tv-if=\"testImage(icon)\"\n\t\t\t\t\t\t\t:src=\"icon\"\n\t\t\t\t\t\t\t:style=\"[imgStyle]\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t\t:color=\"elIconColor\"\n\t\t\t\t\t\t\t:name=\"icon\"\n\t\t\t\t\t\t\t:size=\"iconSize\"\n\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</slot>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-tag__text\"\n\t\t\t\t\t:style=\"[textColor]\"\n\t\t\t\t\t:class=\"[`u-tag__text--${type}`, plain && `u-tag__text--${type}--plain`, `u-tag__text--${size}`]\"\n\t\t\t\t>\n\t\t\t\t\t<slot>\n\t\t\t\t\t\t{{ text }}\n\t\t\t\t\t</slot>\n\t\t\t\t</text>\n\t\t\t</view>\n\t\t\t<view\n\t\t\t\tclass=\"u-tag__close\"\n\t\t\t\t:class=\"[`u-tag__close--${size}`]\"\n\t\t\t\tv-if=\"closable\"\n\t\t\t\**********=\"closeHandler\"\n\t\t\t\t:style=\"{backgroundColor: closeColor}\"\n\t\t\t>\n\t\t\t\t<u-icon\n\t\t\t\t\tname=\"close\"\n\t\t\t\t\t:size=\"closeSize\"\n\t\t\t\t\tcolor=\"#ffffff\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t</u-transition>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport test from '../../libs/function/test';\n\t/**\n\t * Tag 标签\n\t * @description tag组件一般用于标记和选择，我们提供了更加丰富的表现形式，能够较全面的涵盖您的使用场景\n\t * @tutorial https://ijry.github.io/uview-plus/components/tag.html\n\t * @property {String}\t\t\ttype\t\t标签类型info、primary、success、warning、error （默认 'primary' ）\n\t * @property {Boolean | String}\tdisabled\t不可用（默认 false ）\n\t * @property {String}\t\t\tsize\t\t标签的大小，large，medium，mini （默认 'medium' ）\n\t * @property {String}\t\t\tshape\t\ttag的形状，circle（两边半圆形）, square（方形，带圆角）（默认 'square' ）\n\t * @property {String | Number}\ttext\t\t标签的文字内容 \n\t * @property {String}\t\t\tbgColor\t\t背景颜色，默认为空字符串，即不处理\n\t * @property {String}\t\t\tcolor\t\t标签字体颜色，默认为空字符串，即不处理\n\t * @property {String}\t\t\tborderColor\t镂空形式标签的边框颜色\n\t * @property {String}\t\t\tcloseColor\t关闭按钮图标的颜色（默认 #C6C7CB）\n\t * @property {String | Number}\tname\t\t点击时返回的索引值，用于区分例遍的数组哪个元素被点击了\n\t * @property {Boolean}\t\t\tplainFill\t镂空时是否填充背景色（默认 false ）\n\t * @property {Boolean}\t\t\tplain\t\t是否镂空（默认 false ）\n\t * @property {Boolean}\t\t\tclosable\t是否可关闭，设置为true，文字右边会出现一个关闭图标（默认 false ）\n\t * @property {Boolean}\t\t\tshow\t\t标签显示与否（默认 true ）\n\t * @property {String}\t\t\ticon\t\t内置图标，或绝对路径的图片\n\t * @event {Function(index)} click 点击标签时触发 index: 传递的index参数值\n\t * @event {Function(index)} close closable为true时，点击标签关闭按钮触发 index: 传递的index参数值\t\n\t * @example <u-tag text=\"标签\" type=\"error\" plain plainFill></u-tag>\n\t */\n\texport default {\n\t\tname: 'u-tag',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tstyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tif (this.bgColor) {\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\t}\n\t\t\t\tif (this.color) {\n\t\t\t\t\tstyle.color = this.color\n\t\t\t\t}\n\t\t\t\tif(this.borderColor) {\n\t\t\t\t\tstyle.borderColor = this.borderColor\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// nvue下，文本颜色无法继承父元素\n\t\t\ttextColor() {\n\t\t\t\tconst style = {}\n\t\t\t\tif (this.color) {\n\t\t\t\t\tstyle.color = this.color\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\timgStyle() {\n\t\t\t\tconst width = this.size === 'large' ? '17px' : this.size === 'medium' ? '15px' : '13px'\n\t\t\t\treturn {\n\t\t\t\t\twidth,\n\t\t\t\t\theight: width\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 文本的样式\n\t\t\tcloseSize() {\n\t\t\t\tconst size = this.size === 'large' ? 15 : this.size === 'medium' ? 13 : 12\n\t\t\t\treturn size\n\t\t\t},\n\t\t\t// 图标大小\n\t\t\ticonSize() {\n\t\t\t\tconst size = this.size === 'large' ? 21 : this.size === 'medium' ? 19 : 16\n\t\t\t\treturn size\n\t\t\t},\n\t\t\t// 图标颜色\n\t\t\telIconColor() {\n\t\t\t\treturn this.iconColor ? this.iconColor : this.plain ? this.type : '#ffffff'\n\t\t\t}\n\t\t},\n\t\temits: [\"click\", \"close\"],\n\t\tmethods: {\n\t\t\ttestImage: test.image,\n\t\t\t// 点击关闭按钮\n\t\t\tcloseHandler() {\n\t\t\t\tthis.$emit('close', this.name)\n\t\t\t},\n\t\t\t// 点击标签\n\t\t\tclickHandler() {\n\t\t\t\tthis.$emit('click', this.name)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style\n\tlang=\"scss\"\n\tscoped\n>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-tag-wrapper {\n\t\tposition: relative;\n\t}\n\n\t.u-tag {\n\t\t@include flex;\n\t\talign-items: center;\n\t\tborder-style: solid;\n\n\t\t&--circle {\n\t\t\tborder-radius: 100px;\n\t\t}\n\n\t\t&--square {\n\t\t\tborder-radius: 3px;\n\t\t}\n\n\t\t&__icon {\n\t\t\tmargin-right: 4px;\n\t\t}\n\n\t\t&__text {\n\t\t\t&--mini {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tline-height: 12px;\n\t\t\t}\n\n\t\t\t&--medium {\n\t\t\t\tfont-size: 13px;\n\t\t\t\tline-height: 13px;\n\t\t\t}\n\n\t\t\t&--large {\n\t\t\t\tfont-size: 15px;\n\t\t\t\tline-height: 15px;\n\t\t\t}\n\t\t}\n\n\t\t&--mini {\n\t\t\theight: 22px;\n\t\t\tline-height: 22px;\n\t\t\tpadding: 0 5px;\n\t\t}\n\n\t\t&--medium {\n\t\t\theight: 26px;\n\t\t\tline-height: 22px;\n\t\t\tpadding: 0 10px;\n\t\t}\n\n\t\t&--large {\n\t\t\theight: 32px;\n\t\t\tline-height: 32px;\n\t\t\tpadding: 0 15px;\n\t\t}\n\n\t\t&--primary {\n\t\t\tbackground-color: $u-primary;\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-primary;\n\t\t}\n\n\t\t&--primary--plain {\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-primary;\n\t\t}\n\n\t\t&--primary--plain--fill {\n\t\t\tbackground-color: #ecf5ff;\n\t\t}\n\n\t\t&__text--primary {\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\n\t\t&__text--primary--plain {\n\t\t\tcolor: $u-primary;\n\t\t}\n\n\t\t&--error {\n\t\t\tbackground-color: $u-error;\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-error;\n\t\t}\n\n\t\t&--error--plain {\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-error;\n\t\t}\n\n\t\t&--error--plain--fill {\n\t\t\tbackground-color: #fef0f0;\n\t\t}\n\n\t\t&__text--error {\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\n\t\t&__text--error--plain {\n\t\t\tcolor: $u-error;\n\t\t}\n\n\t\t&--warning {\n\t\t\tbackground-color: $u-warning;\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-warning;\n\t\t}\n\n\t\t&--warning--plain {\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-warning;\n\t\t}\n\n\t\t&--warning--plain--fill {\n\t\t\tbackground-color: #fdf6ec;\n\t\t}\n\n\t\t&__text--warning {\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\n\t\t&__text--warning--plain {\n\t\t\tcolor: $u-warning;\n\t\t}\n\n\t\t&--success {\n\t\t\tbackground-color: $u-success;\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-success;\n\t\t}\n\n\t\t&--success--plain {\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-success;\n\t\t}\n\n\t\t&--success--plain--fill {\n\t\t\tbackground-color: #f5fff0;\n\t\t}\n\n\t\t&__text--success {\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\n\t\t&__text--success--plain {\n\t\t\tcolor: $u-success;\n\t\t}\n\n\t\t&--info {\n\t\t\tbackground-color: $u-info;\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-info;\n\t\t}\n\n\t\t&--info--plain {\n\t\t\tborder-width: 1px;\n\t\t\tborder-color: $u-info;\n\t\t}\n\n\t\t&--info--plain--fill {\n\t\t\tbackground-color: #f4f4f5;\n\t\t}\n\n\t\t&__text--info {\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\n\t\t&__text--info--plain {\n\t\t\tcolor: $u-info;\n\t\t}\n\n\t\t&__close {\n\t\t\tposition: absolute;\n\t\t\tz-index: 999;\n\t\t\ttop: 10px;\n\t\t\tright: 10px;\n\t\t\tborder-radius: 100px;\n\t\t\tbackground-color: #C6C7CB;\n\t\t\t@include flex(row);\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\ttransform: scale(0.6) translate(80%, -80%);\n\t\t\t/* #endif */\n\t\t\t/* #ifdef APP-NVUE */\n\t\t\ttransform: scale(0.6) translate(50%, -50%);\n\t\t\t/* #endif */\n\n\t\t\t&--mini {\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t}\n\n\t\t\t&--medium {\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t}\n\n\t\t\t&--large {\n\t\t\t\twidth: 25px;\n\t\t\t\theight: 25px;\n\t\t\t}\n\t\t}\n\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-tag/u-tag.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "computed", "style", "this", "bgColor", "backgroundColor", "color", "borderColor", "textColor", "imgStyle", "width", "size", "height", "closeSize", "iconSize", "elIconColor", "iconColor", "plain", "type", "emits", "methods", "testImage", "test", "image", "<PERSON><PERSON><PERSON><PERSON>", "$emit", "clickHandler", "wx", "createComponent", "Component"], "mappings": "6DAyFMA,EAAU,CACdC,KAAM,QACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,KAAO,KACC,CAEP,GAEDC,SAAU,CACT,KAAAC,GACC,MAAMA,EAAQ,CAAC,EAUR,OATHC,KAAKC,UACRF,EAAMG,gBAAkBF,KAAKC,SAE1BD,KAAKG,QACRJ,EAAMI,MAAQH,KAAKG,OAEjBH,KAAKI,cACPL,EAAMK,YAAcJ,KAAKI,aAEnBL,CACP,EAED,SAAAM,GACC,MAAMN,EAAQ,CAAC,EAIR,OAHHC,KAAKG,QACRJ,EAAMI,MAAQH,KAAKG,OAEbJ,CACP,EACD,QAAAO,GACO,MAAAC,EAAsB,UAAdP,KAAKQ,KAAmB,OAAuB,WAAdR,KAAKQ,KAAoB,OAAS,OAC1E,MAAA,CACND,QACAE,OAAQF,EAET,EAED,SAAAG,GAEQ,MADoB,UAAdV,KAAKQ,KAAmB,GAAmB,WAAdR,KAAKQ,KAAoB,GAAK,EAExE,EAED,QAAAG,GAEQ,MADoB,UAAdX,KAAKQ,KAAmB,GAAmB,WAAdR,KAAKQ,KAAoB,GAAK,EAExE,EAED,WAAAI,GACC,OAAOZ,KAAKa,UAAYb,KAAKa,UAAYb,KAAKc,MAAQd,KAAKe,KAAO,SACnE,GAEDC,MAAO,CAAC,QAAS,SACjBC,QAAS,CACRC,UAAWC,EAAIA,KAACC,MAEhB,YAAAC,GACMrB,KAAAsB,MAAM,QAAStB,KAAKR,KACzB,EAED,YAAA+B,GACMvB,KAAAsB,MAAM,QAAStB,KAAKR,KAC1B,omCCtJHgC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}