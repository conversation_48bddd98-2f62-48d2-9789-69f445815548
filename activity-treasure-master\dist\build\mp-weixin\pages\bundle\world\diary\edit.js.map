{"version": 3, "file": "edit.js", "sources": ["../../../../../../../src/pages/bundle/world/diary/edit.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGRpYXJ5XGVkaXQudnVl"], "sourcesContent": ["<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { getDiaryDetail, editDiary, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport { onLoad } from '@dcloudio/uni-app';\nimport { requireLogin } from '@/utils/auth';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 实现日记编辑页面. Confirmed via 寸止. }}\n\n// 状态管理\nconst content = ref(''); // 日记内容\nconst images = ref([]); // 图片列表\nconst location = ref(null); // 位置信息\nconst privacy = ref('public'); // 隐私设置\nconst isPrivate = ref(false); // 是否设为私密\nconst isSubmitting = ref(false); // 提交状态\nconst isLoading = ref(true); // 加载状态\nconst diaryId = ref(null); // 日记ID\n\n// 隐私选项\nconst privacyOptions = [\n  { label: '公开', value: 'public' },\n  { label: '私密', value: 'private' }\n];\n\n// 计算最大上传图片数量（会员4张，非会员1张）\nconst maxImageCount = computed(() => {\n    const userInfo = store().$state.userInfo;\n    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员\n    return isVip ? 4 : 1;\n});\n\n// 页面加载时获取日记数据\nonLoad(async (options) => {\n    if (!options.id) {\n        uni.showToast({ title: '参数错误', icon: 'none' });\n        uni.navigateBack();\n        return;\n    }\n    \n    diaryId.value = options.id;\n    await loadDiaryData();\n});\n\n// 加载日记数据\nconst loadDiaryData = async () => {\n    try {\n        isLoading.value = true;\n        const userInfo = store().$state.userInfo;\n        \n        const response = await getDiaryDetail({\n            id: diaryId.value,\n            uid: userInfo.uid,\n            token: userInfo.token\n        });\n        \n        if (response.status === 'ok' && response.data) {\n            const diary = response.data;\n            \n            // 检查权限\n            if (diary.user_id !== userInfo.uid) {\n                uni.showToast({ title: '您无权编辑此日记', icon: 'none' });\n                uni.navigateBack();\n                return;\n            }\n            \n            // 填充数据\n            content.value = diary.content || '';\n            privacy.value = diary.privacy || 'public';\n            isPrivate.value = diary.privacy === 'private';\n            \n            // 处理位置信息\n            if (diary.location) {\n                location.value = { name: diary.location };\n            }\n            \n            // 处理图片\n            if (diary.images && diary.images.length > 0) {\n                images.value = diary.images.map((img, index) => ({\n                    url: img,\n                    status: 'success',\n                    message: '',\n                    uid: Date.now() + index\n                }));\n            }\n        } else {\n            uni.showToast({ title: response.msg || '加载失败', icon: 'none' });\n            uni.navigateBack();\n        }\n    } catch (error) {\n        console.error('加载日记数据失败:', error);\n        uni.showToast({ title: '加载失败', icon: 'none' });\n        uni.navigateBack();\n    } finally {\n        isLoading.value = false;\n    }\n};\n\n// 处理图片上传\nconst handleAfterRead = async (event) => {\n    let lists = [].concat(event.file);\n    let fileListLen = images.value.length;\n\n    lists.map((item) => {\n        images.value.push({\n            ...item,\n            status: 'uploading',\n            message: '上传中'\n        });\n    });\n\n    for (let i = 0; i < lists.length; i++) {\n        const result = await uploadFilePromise(lists[i].file);\n        let item = images.value[fileListLen];\n        images.value.splice(fileListLen, 1, Object.assign(item, {\n            status: result.success ? 'success' : 'failed',\n            message: result.success ? '' : result.message,\n            url: result.success ? result.data : ''\n        }));\n        fileListLen++;\n    }\n};\n\n// 上传文件Promise\nconst uploadFilePromise = (file) => {\n    return new Promise((resolve, reject) => {\n        let formData = new FormData();\n        formData.append('file', file);\n        \n        upload_img(formData).then(res => {\n            if (res.status === 'ok') {\n                resolve({ success: true, data: res.data.url });\n            } else {\n                resolve({ success: false, message: res.msg || '上传失败' });\n            }\n        }).catch(err => {\n            resolve({ success: false, message: '上传失败' });\n        });\n    });\n};\n\n// 删除图片\nconst handleDelete = (event) => {\n    images.value.splice(event.index, 1);\n};\n\n// 隐私设置切换\nconst handlePrivacyChange = (value) => {\n    privacy.value = value ? 'private' : 'public';\n};\n\n// 选择位置\nconst chooseLocation = () => {\n    uni.chooseLocation({\n        success: (res) => {\n            location.value = {\n                name: res.name,\n                address: res.address,\n                latitude: res.latitude,\n                longitude: res.longitude\n            };\n        },\n        fail: (err) => {\n            console.log('选择位置失败:', err);\n        }\n    });\n};\n\n// 提交编辑\nconst handleSubmit = async () => {\n    if (!requireLogin()) return;\n    \n    if (!content.value.trim()) {\n        uni.showToast({ title: '请输入日记内容', icon: 'none' });\n        return;\n    }\n    \n    if (isSubmitting.value) return;\n    \n    try {\n        isSubmitting.value = true;\n        uni.showLoading({ title: '保存中...' });\n        \n        const userInfo = store().$state.userInfo;\n        const imageUrls = images.value\n            .filter(img => img.status === 'success' && img.url)\n            .map(img => img.url);\n        \n        const params = {\n            uid: userInfo.uid,\n            token: userInfo.token,\n            diary_id: diaryId.value,\n            content: content.value.trim(),\n            images: imageUrls,\n            location: location.value ? location.value.name : '',\n            privacy: privacy.value\n        };\n        \n        const response = await editDiary(params);\n        \n        if (response.status === 'ok') {\n            uni.hideLoading();\n            uni.showToast({ title: '保存成功', icon: 'success' });\n\n            // {{ AURA-X: Add - 通知详情页刷新数据. Confirmed via 寸止. }}\n            // 通知详情页刷新数据\n            uni.$emit('diary-updated', { id: diaryId.value });\n\n            // 延迟返回，让用户看到成功提示\n            setTimeout(() => {\n                uni.navigateBack();\n            }, 1500);\n        } else {\n            uni.hideLoading();\n            uni.showToast({ title: response.msg || '保存失败', icon: 'none' });\n        }\n    } catch (error) {\n        console.error('保存日记失败:', error);\n        uni.hideLoading();\n        uni.showToast({ title: '保存失败，请重试', icon: 'none' });\n    } finally {\n        isSubmitting.value = false;\n    }\n};\n\n// 返回\nconst handleClose = () => {\n    uni.navigateBack();\n};\n</script>\n\n<template>\n  <view class=\"diary-edit-page\">\n    <!-- 自定义导航栏 -->\n    <customNavbar \n      title=\"编辑日记\" \n      :showBack=\"true\" \n      @back=\"handleClose\"\n    />\n    \n    <!-- 加载状态 -->\n    <view v-if=\"isLoading\" class=\"loading-container\">\n      <u-loading-icon mode=\"spinner\" color=\"#6AC086\" size=\"40\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n    \n    <!-- 编辑表单 -->\n    <view v-else class=\"edit-form\">\n      <!-- 内容输入 -->\n      <view class=\"content-section\">\n        <u-textarea\n          v-model=\"content\"\n          placeholder=\"记录今天的美好时光...\"\n          :maxlength=\"5000\"\n          :showWordLimit=\"true\"\n          :autoHeight=\"true\"\n          :minHeight=\"200\"\n          class=\"content-textarea\"\n        />\n      </view>\n      \n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <view class=\"section-title\">添加图片</view>\n        <u-upload\n          :fileList=\"images\"\n          @afterRead=\"handleAfterRead\"\n          @delete=\"handleDelete\"\n          :maxCount=\"maxImageCount\"\n          :multiple=\"true\"\n          :previewFullImage=\"true\"\n          accept=\"image\"\n          :maxSize=\"400 * 1024\"\n          @oversize=\"() => uni.showToast({ title: '图片大小不能超过400KB', icon: 'none' })\"\n        />\n        <view class=\"upload-tip\">\n          最多上传{{ maxImageCount }}张图片，单张不超过400KB\n        </view>\n      </view>\n      \n      <!-- 位置信息 -->\n      <view class=\"location-section\">\n        <view class=\"section-title\">位置</view>\n        <view class=\"location-selector\" @click=\"chooseLocation\">\n          <u-icon name=\"map\" color=\"#6AC086\" size=\"20\"></u-icon>\n          <text class=\"location-text\">{{ location ? location.name : '添加位置' }}</text>\n          <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n        </view>\n      </view>\n      \n      <!-- 隐私设置 -->\n      <view class=\"privacy-section\">\n        <view class=\"section-title\">隐私设置</view>\n        <view class=\"privacy-toggle\">\n          <text class=\"privacy-label\">设为私密</text>\n          <u-switch \n            v-model=\"isPrivate\" \n            @change=\"handlePrivacyChange\"\n            activeColor=\"#6AC086\"\n          />\n        </view>\n        <view class=\"privacy-tip\">私密日记仅自己可见</view>\n      </view>\n    </view>\n    \n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <u-button\n        type=\"primary\"\n        :loading=\"isSubmitting\"\n        :disabled=\"!content.trim() || isSubmitting\"\n        @click=\"handleSubmit\"\n        class=\"submit-btn\"\n        color=\"#6AC086\"\n      >\n        {{ isSubmitting ? '保存中...' : '保存' }}\n      </u-button>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.diary-edit-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  padding-bottom: 120rpx;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400rpx;\n}\n\n.loading-text {\n  margin-top: 20rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.edit-form {\n  padding: 32rpx;\n}\n\n.content-section {\n  margin-bottom: 40rpx;\n}\n\n.content-textarea {\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.image-section,\n.location-section,\n.privacy-section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 24rpx;\n}\n\n.upload-tip {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 16rpx;\n}\n\n.location-selector {\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.location-text {\n  flex: 1;\n  margin-left: 16rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.privacy-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background: white;\n  border-radius: 20rpx;\n  padding: 24rpx;\n}\n\n.privacy-label {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.privacy-tip {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 16rpx;\n}\n\n.submit-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: white;\n  padding: 24rpx 32rpx;\n  border-top: 1rpx solid #eee;\n}\n\n.submit-btn {\n  width: 100%;\n  height: 88rpx;\n  border-radius: 50rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/diary/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "content", "ref", "images", "location", "privacy", "isPrivate", "isSubmitting", "isLoading", "diaryId", "maxImageCount", "computed", "userInfo", "store", "$state", "role_type", "common_vendor", "onLoad", "async", "options", "id", "uni", "index", "showToast", "title", "icon", "navigateBack", "value", "loadDiaryData", "response", "getDiaryDetail", "uid", "token", "status", "data", "diary", "user_id", "name", "length", "map", "img", "url", "message", "Date", "now", "msg", "error", "console", "handleAfterRead", "event", "lists", "concat", "file", "fileListLen", "item", "push", "i", "result", "uploadFilePromise", "splice", "Object", "assign", "success", "Promise", "resolve", "reject", "formData", "FormData", "append", "upload_img", "then", "res", "catch", "err", "handleDelete", "handlePrivacyChange", "chooseLocation", "address", "latitude", "longitude", "fail", "log", "handleSubmit", "requireLogin", "trim", "showLoading", "imageUrls", "filter", "params", "diary_id", "editDiary", "hideLoading", "$emit", "setTimeout", "handleClose", "wx", "createPage", "MiniProgramPage"], "mappings": "ulCAMA,MAAMA,EAAe,IAAW,mEAK1B,MAAAC,EAAUC,EAAAA,IAAI,IACdC,EAASD,EAAAA,IAAI,IACbE,EAAWF,EAAAA,IAAI,MACfG,EAAUH,EAAAA,IAAI,UACdI,EAAYJ,EAAAA,KAAI,GAChBK,EAAeL,EAAAA,KAAI,GACnBM,EAAYN,EAAAA,KAAI,GAChBO,EAAUP,EAAAA,IAAI,MASdQ,EAAgBC,EAAQA,UAAC,KAC3B,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAEhC,OADsC,KAAxB,MAAAA,OAAA,EAAAA,EAAUG,YAA2C,WAAxBH,WAAUG,WACtC,EAAI,CAAA,IAIjBC,EAAAC,QAACC,MAAOC,IACN,IAACA,EAAQC,GAGT,OAFAC,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,cACrCJ,EAAGC,MAACI,eAIRjB,EAAQkB,MAAQR,EAAQC,SAClBQ,GAAa,IAIvB,MAAMA,EAAgBV,UACd,IACAV,EAAUmB,OAAQ,EAClB,MAAMf,EAAWC,EAAAA,QAAQC,OAAOF,SAE1BiB,QAAiBC,iBAAe,CAClCV,GAAIX,EAAQkB,MACZI,IAAKnB,EAASmB,IACdC,MAAOpB,EAASoB,QAGpB,GAAwB,OAApBH,EAASI,QAAmBJ,EAASK,KAAM,CAC3C,MAAMC,EAAQN,EAASK,KAGnB,GAAAC,EAAMC,UAAYxB,EAASmB,IAG3B,OAFAV,EAAGC,MAACC,UAAU,CAAEC,MAAO,WAAYC,KAAM,cACzCJ,EAAGC,MAACI,eAKAzB,EAAA0B,MAAQQ,EAAMlC,SAAW,GACzBI,EAAAsB,MAAQQ,EAAM9B,SAAW,SACvBC,EAAAqB,MAA0B,YAAlBQ,EAAM9B,QAGpB8B,EAAM/B,WACNA,EAASuB,MAAQ,CAAEU,KAAMF,EAAM/B,WAI/B+B,EAAMhC,QAAUgC,EAAMhC,OAAOmC,OAAS,IACtCnC,EAAOwB,MAAQQ,EAAMhC,OAAOoC,KAAI,CAACC,EAAKlB,KAAW,CAC7CmB,IAAKD,EACLP,OAAQ,UACRS,QAAS,GACTX,IAAKY,KAAKC,MAAQtB,MAGtC,cACgBC,UAAU,CAAEC,MAAOK,EAASgB,KAAO,OAAQpB,KAAM,SACrDJ,EAAGC,MAACI,cAEX,OAAQoB,GACGC,QAAAD,MAAM,YAAaA,GAC3BzB,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,SACrCJ,EAAGC,MAACI,cACZ,CAAc,QACNlB,EAAUmB,OAAQ,CACtB,GAIEqB,EAAkB9B,MAAO+B,IAC3B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MACxBC,EAAclD,EAAOwB,MAAMW,OAEzBY,EAAAX,KAAKe,IACPnD,EAAOwB,MAAM4B,KAAK,IACXD,EACHrB,OAAQ,YACRS,QAAS,OACZ,IAGL,IAAA,IAASc,EAAI,EAAGA,EAAIN,EAAMZ,OAAQkB,IAAK,CACnC,MAAMC,QAAeC,EAAkBR,EAAMM,GAAGJ,MAC5C,IAAAE,EAAOnD,EAAOwB,MAAM0B,GACxBlD,EAAOwB,MAAMgC,OAAON,EAAa,EAAGO,OAAOC,OAAOP,EAAM,CACpDrB,OAAQwB,EAAOK,QAAU,UAAY,SACrCpB,QAASe,EAAOK,QAAU,GAAKL,EAAOf,QACtCD,IAAKgB,EAAOK,QAAUL,EAAOvB,KAAO,MAExCmB,GACJ,GAIEK,EAAqBN,GAChB,IAAIW,SAAQ,CAACC,EAASC,KACrB,IAAAC,EAAW,IAAIC,SACVD,EAAAE,OAAO,OAAQhB,GAExBiB,EAAAA,WAAWH,GAAUI,MAAYC,IACV,OAAfA,EAAItC,OACJ+B,EAAQ,CAAEF,SAAS,EAAM5B,KAAMqC,EAAIrC,KAAKO,MAExCuB,EAAQ,CAAEF,SAAS,EAAOpB,QAAS6B,EAAI1B,KAAO,QAClD,IACD2B,OAAaC,IACZT,EAAQ,CAAEF,SAAS,EAAOpB,QAAS,QAAQ,GAC9C,IAKHgC,EAAgBzB,IAClB9C,EAAOwB,MAAMgC,OAAOV,EAAM3B,MAAO,EAAC,EAIhCqD,EAAuBhD,IACjBtB,EAAAsB,MAAQA,EAAQ,UAAY,QAAA,EAIlCiD,EAAiB,KACnBvD,EAAAA,MAAIuD,eAAe,CACfd,QAAUS,IACNnE,EAASuB,MAAQ,CACbU,KAAMkC,EAAIlC,KACVwC,QAASN,EAAIM,QACbC,SAAUP,EAAIO,SACdC,UAAWR,EAAIQ,UAC/B,EAEQC,KAAOP,IACK1B,QAAAkC,IAAI,UAAWR,EAAG,GAEjC,EAICS,EAAehE,UACb,GAACiE,EAAYA,eAEjB,GAAKlF,EAAQ0B,MAAMyD,QAKnB,IAAI7E,EAAaoB,MAEb,IACApB,EAAaoB,OAAQ,EACrBN,EAAAA,MAAIgE,YAAY,CAAE7D,MAAO,WAEzB,MAAMZ,EAAWC,EAAAA,QAAQC,OAAOF,SAC1B0E,EAAYnF,EAAOwB,MACpB4D,WAA6B,YAAf/C,EAAIP,QAAwBO,EAAIC,MAC9CF,KAAIC,GAAOA,EAAIC,MAEd+C,EAAS,CACXzD,IAAKnB,EAASmB,IACdC,MAAOpB,EAASoB,MAChByD,SAAUhF,EAAQkB,MAClB1B,QAASA,EAAQ0B,MAAMyD,OACvBjF,OAAQmF,EACRlF,SAAUA,EAASuB,MAAQvB,EAASuB,MAAMU,KAAO,GACjDhC,QAASA,EAAQsB,OAGfE,QAAiB6D,YAAUF,GAET,OAApB3D,EAASI,QACTZ,EAAGC,MAACqE,cACJtE,EAAGC,MAACC,UAAU,CAAEC,MAAO,OAAQC,KAAM,YAIrCJ,EAAGC,MAACsE,MAAM,gBAAiB,CAAExE,GAAIX,EAAQkB,QAGzCkE,YAAW,KACPxE,EAAGC,MAACI,cAAY,GACjB,QAEHL,EAAGC,MAACqE,sBACApE,UAAU,CAAEC,MAAOK,EAASgB,KAAO,OAAQpB,KAAM,SAE5D,OAAQqB,GACGC,QAAAD,MAAM,UAAWA,GACzBzB,EAAGC,MAACqE,cACJtE,EAAGC,MAACC,UAAU,CAAEC,MAAO,WAAYC,KAAM,QACjD,CAAc,QACNlB,EAAaoB,OAAQ,CACzB,OAjDIN,EAAGC,MAACC,UAAU,CAAEC,MAAO,UAAWC,KAAM,QAiD5C,EAIEqE,EAAc,KAChBzE,EAAGC,MAACI,cAAY,03BCnOpBqE,GAAGC,WAAWC"}