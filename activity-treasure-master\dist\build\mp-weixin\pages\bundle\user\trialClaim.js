"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),a=require("../../../store/index.js"),r=require("../../../utils/index.js"),n=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const t={__name:"trialClaim",setup(t){const s=e.ref(""),l=e.ref(null),u=e.ref(!1),i=e.ref(!1),c=e.ref(!1);e.onLoad((o=>{o.code?(s.value=o.code,v()):(e.index.$u.toast("分享链接无效"),setTimeout((()=>{e.index.navigateBack()}),2e3))}));const v=async()=>{u.value=!0;try{console.log("开始获取分享信息，分享码:",s.value);const a=await o.userget_trial_info({share_code:s.value});console.log("获取分享信息响应:",a),"ok"===(null==a?void 0:a.status)?(l.value=a.data,l.value.sharer_nickname&&(l.value.sharer_nickname=m(l.value.sharer_nickname)),console.log("分享信息获取成功:",l.value)):(console.error("获取分享信息失败:",a),e.index.$u.toast((null==a?void 0:a.msg)||"获取分享信息失败"),setTimeout((()=>{e.index.navigateBack()}),2e3))}catch(a){console.error("获取分享信息异常:",a),e.index.$u.toast("获取分享信息失败"),setTimeout((()=>{e.index.navigateBack()}),2e3)}finally{u.value=!1}},d=async()=>{var t;if(n.requireLogin("","请先登录后再领取体验会员")&&!i.value){i.value=!0;try{console.log("开始领取体验会员，参数:",{uid:a.store().$state.userInfo.uid,token:(null==(t=a.store().$state.userInfo.token)?void 0:t.substring(0,8))+"...",share_code:s.value});const n=await o.userclaim_trial_member({uid:a.store().$state.userInfo.uid,token:a.store().$state.userInfo.token,share_code:s.value});console.log("领取体验会员响应:",n),"ok"===(null==n?void 0:n.status)?(c.value=!0,e.index.$u.toast("领取成功！"),r.getUserInfo(),setTimeout((()=>{e.index.navigateTo({url:"/pages/index"})}),3e3)):"relogin"===(null==n?void 0:n.status)?(console.error("登录信息验证失败，需要重新登录"),e.index.$u.toast("登录信息已过期，请重新登录"),a.store().$patch({userInfo:{}}),setTimeout((()=>{r.login({fun:()=>{r.getUserInfo(),setTimeout((()=>{d()}),1e3)}})}),1500)):(console.error("领取失败:",n),e.index.$u.toast((null==n?void 0:n.msg)||"领取失败"))}catch(l){console.error("领取异常:",l),e.index.$u.toast("领取失败，请重试")}finally{i.value=!1}}},m=e=>e?e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&nbsp;/g," "):e,g=e=>{if(!e)return"";const o=e.replace(/\s/,"T"),a=new Date(o);return isNaN(a.getTime())?(console.warn("无效的日期格式:",e),e):a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},p=()=>{e.index.navigateTo({url:"/pages/index"})};return(o,n)=>{var t,s;return e.e({a:e.p({name:"arrow-left",color:"#333",size:"20"}),b:e.o(p),c:u.value},u.value?{d:e.p({mode:"circle",color:"#333",size:"40"})}:c.value?{f:e.p({name:"checkmark-circle",color:"#4CAF50",size:"60"}),g:e.t(null==(t=l.value)?void 0:t.trial_days),h:e.o(p)}:l.value?{j:e.t(l.value.sharer_nickname||"好友"),k:e.t(l.value.trial_days),l:e.t(g(l.value.expire_time)),m:e.p({name:"checkmark",color:"#333",size:"16"}),n:e.p({name:"checkmark",color:"#333",size:"16"}),o:e.p({name:"checkmark",color:"#333",size:"16"}),p:e.p({name:"checkmark",color:"#333",size:"16"})}:{},{e:c.value,i:l.value,q:l.value},l.value?e.e({r:i.value},i.value?{s:e.p({mode:"circle",color:"#fff",size:"16"})}:{},{t:e.t(i.value?"领取中...":(null==(s=e.unref(a.store)().$state.userInfo)?void 0:s.uid)?"立即领取":"登录后领取"),v:e.o((o=>{var n;return(null==(n=e.unref(a.store)().$state.userInfo)?void 0:n.uid)?d():void r.navto("/pages/bundle/common/login")})),w:e.o(p)}):{},{x:l.value},(l.value,{}))}}};wx.createPage(t);
//# sourceMappingURL=trialClaim.js.map
