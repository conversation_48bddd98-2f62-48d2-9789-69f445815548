{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages/bundle/world/feed/index.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL3BhZ2VzL2J1bmRsZS93b3JsZC9mZWVkL2luZGV4LnZ1ZQ"], "sourcesContent": ["<script setup>\nimport { ref, onMounted, reactive, nextTick } from 'vue';\nimport { getFeeds, likeFeed, favoriteFeed } from '@/api/index.js';\nimport { store } from '@/store';\nimport { navto } from '@/utils';\nimport { requireLogin } from '@/utils/auth';\nimport { onReachBottom, onLoad, onShareAppMessage } from \"@dcloudio/uni-app\"; // Import for infinite scroll & onLoad for waterfall\nimport SharePopup from '@/components/share-popup/share-popup.vue';\n\n// --- Refs & Reactive State ---\nconst feeds = ref([]); // 直接使用这个数组渲染列表\nconst isLoading = ref(false);\nconst refreshing = ref(false); // {{ AURA-X: Add - 下拉刷新状态. Confirmed via 寸止 }}\nconst isError = ref(false);\nconst errorMessage = ref('');\nconst hasMore = ref(true);\n\n// 数据缓存机制 - 解决Tab切换数据丢失问题\nconst feedsCache = ref(new Map()); // 缓存不同分类的数据\nconst pageCache = ref(new Map()); // 缓存分页信息\nconst hasMoreCache = ref(new Map()); // 缓存是否还有更多数据\nconst form = reactive({\n  page: 1,\n  page_size: 10,\n  uid: 0, // Initialize, will be set from ref\n  token: '', // Initialize, will be set from ref\n  user_id: 0,\n  category: 'latest', // 初始化分类参数\n  type: 'feed' // 初始化类型参数\n});\nconst categories = ref([\n    { name: '最新', id: 'latest' },\n    { name: '热门', id: 'hot' },\n]);\nconst currentCategory = ref('latest');\n\n// --- Functions ---\n\n/**\n * 格式化时间戳为易读格式\n * @param {string} timestamp - 时间戳字符串 (e.g., \"YYYY-MM-DD HH:MM:SS\")\n * @returns {string} 格式化后的时间字符串\n */\nconst formatTimestamp = (timestamp) => {\n  if (!timestamp) return '';\n\n  // 修复iOS日期格式问题\n  const formattedTimeStr = timestamp.replace(/-/g, '/');\n  const past = new Date(formattedTimeStr);\n  const now = new Date();\n  const diff = now - past;\n\n  // 1小时内显示xx分钟前\n  if (diff < 3600000) { // 1小时 = 3600000毫秒\n    const minutes = Math.floor(diff / 60000);\n    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;\n  }\n\n  // 1天内显示xx小时前\n  if (diff < 86400000) { // 1天 = 86400000毫秒\n    const hours = Math.floor(diff / 3600000);\n    return `${hours}小时前`;\n  }\n\n  // 超过1天显示具体日期\n  const year = past.getFullYear();\n  const month = String(past.getMonth() + 1).padStart(2, '0');\n  const day = String(past.getDate()).padStart(2, '0');\n  const hours = String(past.getHours()).padStart(2, '0');\n  const minutes = String(past.getMinutes()).padStart(2, '0');\n\n  // 判断是否是今年\n  if (year === now.getFullYear()) {\n    return `${month}-${day} ${hours}:${minutes}`;\n  } else {\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\n  }\n};\n\n// 修复：生成包含用户ID的缓存键，确保不同用户数据隔离\nconst generateFeedCacheKey = (category, userId = null) => {\n    // 优先使用传入的userId，然后是form.uid，最后是store中的uid\n    const userInfo = store().$state.userInfo;\n    const uid = userId || form.uid || userInfo?.uid || 'anonymous';\n    return `feed_${category}_user_${uid}`;\n};\n\nconst handleCategoryChange = (id) => {\n    if (currentCategory.value === id) {\n        console.log(`分类 ${id} 已经是当前选中状态，跳过切换`);\n        return;\n    }\n\n    console.log(`分类切换: ${currentCategory.value} -> ${id}`);\n\n    // 保存当前分类的数据到缓存 - 修复：使用包含用户ID的缓存键\n    if (feeds.value.length > 0) {\n        const currentCacheKey = generateFeedCacheKey(currentCategory.value, form.uid);\n        feedsCache.value.set(currentCacheKey, [...feeds.value]);\n        pageCache.value.set(currentCacheKey, form.page);\n        hasMoreCache.value.set(currentCacheKey, hasMore.value);\n        console.log(`缓存分类 ${currentCategory.value} 的数据，用户 ${form.uid}，共 ${feeds.value.length} 条`);\n    }\n\n    // 更新分类\n    currentCategory.value = id;\n\n    // 检查是否有缓存数据 - 修复：使用包含用户ID的缓存键\n    const newCacheKey = generateFeedCacheKey(id, form.uid);\n    if (feedsCache.value.has(newCacheKey)) {\n        // 从缓存恢复数据\n        feeds.value = [...feedsCache.value.get(newCacheKey)];\n        form.page = pageCache.value.get(newCacheKey) || 1;\n        hasMore.value = hasMoreCache.value.get(newCacheKey) || true;\n        isLoading.value = false;\n        isError.value = false;\n        errorMessage.value = '';\n        console.log(`从缓存恢复分类 ${id} 的数据，用户 ${form.uid}，共 ${feeds.value.length} 条`);\n    } else {\n        // 没有缓存，重置状态并获取新数据\n        resetFeedList();\n        nextTick(() => {\n            console.log(`开始获取分类 ${id} 的新数据，用户 ${form.uid}`);\n            fetchFeeds();\n        });\n    }\n};\n\n/**\n * 获取动态列表数据\n * @param {boolean} [loadMore=false] - 是否为加载更多操作\n * @param {boolean} [isRefresh=false] - 是否为下拉刷新操作\n */\nconst fetchFeeds = async (loadMore = false, isRefresh = false) => {\n  // 直接从store获取用户信息，避免使用可能未定义的ref变量\n  const userInfo = store().$state.userInfo;\n  const currentUid = userInfo?.uid || 0;\n  const currentToken = userInfo?.token || '';\n\n  console.log(`fetchFeeds called - loadMore: ${loadMore}, isRefresh: ${isRefresh}, uid: ${currentUid}, token present: ${!!currentToken}, category: ${currentCategory.value}`);\n\n  // 防止重复请求\n  if (isLoading.value && !isRefresh) {\n    console.log('正在加载中，跳过重复请求');\n    return;\n  }\n\n  // {{ AURA-X: Add - 下拉刷新状态处理. Confirmed via 寸止 }}\n  if (isRefresh) {\n    refreshing.value = true;\n    form.page = 1;\n    hasMore.value = true;\n    console.log('下拉刷新，重置分页状态');\n  } else {\n    isLoading.value = true;\n  }\n\n  isError.value = false;\n  errorMessage.value = '';\n\n  if (!loadMore && !isRefresh) {\n    form.page = 1;\n    hasMore.value = true;\n    console.log('首次加载，重置分页状态');\n  } else if (loadMore) {\n    console.log(`加载更多，当前页码: ${form.page}`);\n  }\n\n  try {\n     // 更新表单中的用户信息\n     form.uid = currentUid;\n     form.token = currentToken;\n\n     // 添加分类参数 - 后端排序逻辑使用$_REQUEST['category']\n     form.category = currentCategory.value;\n\n     // 确保只获取动态类型的内容\n     form.type = 'feed';\n\n    console.log('Executing API call: getFeeds with params:', JSON.parse(JSON.stringify(form)));\n    const res = await getFeeds(form);\n    console.log('API getFeeds response:', res);\n\n    if (res.status === 'ok' && res.data?.list) {\n      const newFeeds = res.data.list.map(feed => {\n        // 从全局状态获取点赞和收藏状态\n        const globalLikeState = store().getFeedLikeState(feed.id);\n        const globalFavoriteState = store().getFeedFavoriteState(feed.id);\n\n        return {\n          ...feed,\n          // 优先使用全局状态，如果没有则使用API返回的状态\n          isLiked: globalLikeState ? globalLikeState.isLiked : feed.isLiked,\n          likeCount: globalLikeState ? globalLikeState.likeCount : feed.likeCount,\n          isFavorited: globalFavoriteState !== null ? globalFavoriteState : feed.isFavorited,\n          isExpanded: false // 保留以防意外访问\n        };\n      });\n      if (loadMore) {\n        feeds.value.push(...newFeeds); // 直接追加到数组\n      } else {\n        feeds.value = newFeeds; // 直接替换数组\n      }\n      hasMore.value = feeds.value.length < (res.data.total || 0); // 基于 feeds.value 判断\n      form.page++;\n\n      // 更新缓存 - 修复：使用包含用户ID的缓存键\n      const cacheKey = generateFeedCacheKey(currentCategory.value, form.uid);\n      feedsCache.value.set(cacheKey, [...feeds.value]);\n      pageCache.value.set(cacheKey, form.page);\n      hasMoreCache.value.set(cacheKey, hasMore.value);\n      console.log(`更新分类 ${currentCategory.value} 的缓存，用户 ${form.uid}，共 ${feeds.value.length} 条数据`);\n    } else if (res.status === 'empty') {\n       if (!loadMore) {\n         feeds.value = []; // 清空数组\n      }\n      hasMore.value = false;\n    } else {\n      isError.value = true;\n      errorMessage.value = res.msg || '获取动态失败';\n       if (!loadMore) {\n          feeds.value = []; // 清空数组\n       }\n       hasMore.value = false;\n    }\n  } catch (error) {\n    console.error('Error caught in fetchFeeds:', error);\n    console.error('Error fetching feeds:', error);\n    isError.value = true;\n    errorMessage.value = '加载失败，请稍后重试';\n     if (!loadMore) {\n         feeds.value = []; // 清空数组\n     }\n     hasMore.value = false;\n  } finally {\n    isLoading.value = false;\n    refreshing.value = false; // {{ AURA-X: Add - 重置下拉刷新状态. Confirmed via 寸止 }}\n  }\n};\n\n// {{ AURA-X: Add - 下拉刷新方法. Confirmed via 寸止 }}\n// 下拉刷新\nconst onRefresh = () => {\n  console.log('触发下拉刷新');\n  fetchFeeds(false, true);\n};\n\nconst resetFeedList = () => {\n    console.log('重置动态列表状态');\n\n    // 清空数据\n    feeds.value = [];\n\n    // 重置分页状态\n    form.page = 1;\n    hasMore.value = true;\n\n    // 重置加载状态\n    isLoading.value = false;\n    isError.value = false;\n    errorMessage.value = '';\n\n    // 强制触发响应式更新\n    nextTick(() => {\n        console.log('动态列表状态重置完成，数据长度:', feeds.value.length);\n    });\n}\n\n// --- Event Handlers ---\n/**\n * 处理动态卡片点击事件，导航到详情页\n * @param {number} feedId - 动态 ID\n */\nconst handleFeedClick = (feedId) => {\n  try {\n    console.log(`handleFeedClick triggered for feedId: ${feedId}`);\n\n    // 即使未登录也允许查看详情，但会在详情页面限制某些操作\n    navto(`/pages/bundle/world/feed/detail?feedId=${feedId}&showComments=true`);\n  } catch (error) {\n    console.error('导航到详情页失败:', error);\n    uni.showToast({\n      title: '页面加载失败，请重试',\n      icon: 'none',\n      duration: 2000\n    });\n  }\n};\n\n/**\n * 处理点赞/取消点赞\n * @param {object} feed - 动态对象\n */\nconst handleLike = async (feed) => {\n    // 直接从store获取用户信息，避免使用可能未定义的ref变量\n    const userInfo = store().$state.userInfo;\n    const currentToken = userInfo?.token;\n    const currentUserId = userInfo?.uid;\n\n    console.log(`handleLike triggered - Token exists: ${!!currentToken}, UserID: ${currentUserId}`);\n\n    // 使用统一的登录校验\n    if (!requireLogin('', '请先登录后再点赞')) {\n        return;\n    }\n\n    const originalLikedState = feed.isLiked;\n    const originalLikeCount = feed.likeCount;\n\n    // 乐观更新UI\n    feed.isLiked = !feed.isLiked;\n    feed.likeCount += feed.isLiked ? 1 : -1;\n\n    try {\n        const res = await likeFeed({\n            id: feed.id,\n            uid: currentUserId,\n            token: currentToken\n        });\n\n        if (res.status !== 'ok') {\n            // 操作失败，恢复原状态\n            feed.isLiked = originalLikedState;\n            feed.likeCount = originalLikeCount;\n            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n        } else {\n            // 操作成功，更新全局状态\n            store().updateFeedLike(feed.id, feed.isLiked, feed.likeCount);\n            console.log('Like/Unlike success', res);\n        }\n    } catch (error) {\n        // 发生错误，恢复原状态\n        feed.isLiked = originalLikedState;\n        feed.likeCount = originalLikeCount;\n        uni.showToast({ title: '操作失败，请重试', icon: 'none' });\n        console.error('Like feed error:', error);\n    }\n}\n\n/**\n * 处理收藏/取消收藏\n * @param {object} feed - 动态对象\n */\nconst handleFavorite = async (feed) => {\n    const userInfo = store().$state.userInfo;\n    const currentToken = userInfo?.token;\n    const currentUserId = userInfo?.uid;\n\n    if (!currentToken) {\n        uni.showToast({ title: '请先登录', icon: 'none' });\n        return;\n    }\n\n    const originalFavoritedState = feed.isFavorited || false;\n\n    // 乐观更新UI\n    feed.isFavorited = !feed.isFavorited;\n\n    try {\n        const res = await favoriteFeed({\n            id: feed.id,\n            uid: currentUserId,\n            token: currentToken\n        });\n\n        if (res.status !== 'ok') {\n            // 操作失败，恢复原状态\n            feed.isFavorited = originalFavoritedState;\n            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n        } else {\n            // 操作成功，更新全局状态\n            store().updateFeedFavorite(feed.id, feed.isFavorited);\n            uni.showToast({ title: res.msg || '操作成功', icon: 'success' });\n        }\n    } catch (error) {\n        // 发生错误，恢复原状态\n        feed.isFavorited = originalFavoritedState;\n        uni.showToast({ title: '操作失败，请重试', icon: 'none' });\n        console.error('Favorite feed error:', error);\n    }\n}\n\n/**\n * 处理用户头像点击，跳转到用户个人信息页面\n * @param {number} userId - 用户ID\n */\nconst handleUserAvatarClick = (userId) => {\n    if (!userId) {\n        console.warn('用户ID为空，无法跳转');\n        return;\n    }\n\n    // 跳转到用户个人信息页面\n    navto(`/pages/bundle/user/userInfo?uid=${userId}`);\n};\n\n/**\n * 图片加载成功处理\n */\nconst onImageLoad = () => {\n    // 图片加载成功，可以进行一些优化操作\n};\n\n/**\n * 图片加载失败处理\n */\nconst onImageError = () => {\n    // 可以设置默认图片或进行其他处理\n};\n\n/**\n * 处理评论点击事件\n * @param {number} feedId - 动态 ID\n */\nconst handleCommentClick = (feedId) => {\n    console.log('Comment clicked for feed:', feedId);\n    // 导航到评论页面，使用新的日记详情页面\n    navto(`/pages/bundle/world/feed/detail?feedId=${feedId}&showComments=true`);\n};\n\n// 分享相关状态\nconst showSharePopup = ref(false);\nconst currentShareFeed = ref(null);\n\n// 分享配置\nonShareAppMessage(() => {\n  try {\n    if (!currentShareFeed.value) {\n      console.warn('动态信息未设置，使用默认分享信息');\n      return {\n        title: '分享一条精彩动态',\n        path: '/pages/bundle/world/feed/index',\n        imageUrl: store().$state.config?.img_config?.app_logo?.val || ''\n      };\n    }\n\n    return {\n      title: currentShareFeed.value.content ?\n        (currentShareFeed.value.content.length > 30 ?\n          currentShareFeed.value.content.substring(0, 30) + '...' :\n          currentShareFeed.value.content) :\n        '分享一条精彩动态',\n      path: `/pages/bundle/world/feed/detail?feedId=${currentShareFeed.value.id}`,\n      imageUrl: currentShareFeed.value.images?.[0] || store().$state.config?.img_config?.app_logo?.val || ''\n    };\n  } catch (error) {\n    console.error('动态分享配置失败:', error);\n    return {\n      title: '分享一条精彩动态',\n      path: '/pages/bundle/world/feed/index',\n      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''\n    };\n  }\n});\n\n/**\n * 处理分享点击事件\n * @param {number} feedId - 动态 ID\n * @param {Event} event - 事件对象\n */\nconst handleShareClick = (feedId, event) => {\n    if (event) {\n        event.stopPropagation();\n        event.preventDefault();\n    }\n\n    // 获取当前动态数据\n    const currentFeed = feeds.value.find(feed => feed.id === feedId);\n    if (!currentFeed) {\n        uni.showToast({\n            title: '分享内容不存在',\n            icon: 'none',\n            duration: 2000\n        });\n        return;\n    }\n\n    // 设置当前分享的动态数据\n    currentShareFeed.value = currentFeed;\n\n    // 显示分享弹窗\n    showSharePopup.value = true;\n};\n\n// 处理分享成功\nconst handleShareSuccess = (result) => {\n    console.log('分享成功:', result);\n    uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n    });\n};\n\n// 处理分享错误\nconst handleShareError = (error) => {\n    console.error('分享失败:', error);\n    uni.showToast({\n        title: '分享失败',\n        icon: 'none'\n    });\n};\n\n/**\n * 处理图片点击事件，预览图片\n * @param {string[]} images - 图片 URL 数组\n * @param {number} index - 被点击图片的索引\n */\nconst handleImageClick = (images, index) => {\n    uni.previewImage({\n        urls: images,\n        current: index\n    });\n}\n\n// --- Lifecycle Hooks ---\n/**\n * 页面加载时执行 (早于 onMounted)\n */\nonLoad(() => {\n    console.log('FeedIndex onLoad - 开始初始化');\n\n    // 确保用户信息已加载\n    const userInfo = store().$state.userInfo;\n    if (userInfo?.uid) {\n        form.uid = userInfo.uid;\n        form.token = userInfo.token || '';\n        console.log('用户信息已设置:', { uid: form.uid, hasToken: !!form.token });\n    }\n\n    // {{ AURA-X: Modify - 移除自动数据加载，改为按需加载. Confirmed via 寸止. }}\n    // 不在onLoad时自动加载数据，等待父组件调用\n    console.log('FeedIndex onLoad - 初始化完成，等待按需加载');\n\n    // 加载本地存储的世界状态\n    store().loadWorldStateFromLocal();\n\n    // 立即设置默认分类\n    currentCategory.value = 'latest';\n    console.log('设置默认分类为:', currentCategory.value);\n\n    // 立即重置列表状态\n    resetFeedList();\n});\n\n/**\n * 组件挂载后执行\n */\nonMounted(() => {\n  console.log('FeedIndex onMounted - 组件已挂载');\n  // 不在这里加载数据，等待父组件调用\n});\n\n// {{ AURA-X: Add - 添加外部调用接口，供父组件按需加载数据. Confirmed via 寸止. }}\n/**\n * 外部调用接口：加载动态数据\n * 供父组件在切换到动态tab时调用\n */\nconst loadFeedData = () => {\n  console.log('FeedIndex loadFeedData - 被父组件调用');\n  fetchFeeds();\n};\n\n// 暴露给父组件的方法\ndefineExpose({\n  loadFeedData\n});\n\n// 防抖定时器\nlet reachBottomTimer = null;\n\n/**\n * 页面滚动到底部时触发 (用于无限滚动)\n */\nonReachBottom(() => {\n  if (reachBottomTimer) {\n    clearTimeout(reachBottomTimer);\n  }\n\n  reachBottomTimer = setTimeout(() => {\n    if (hasMore.value && !isLoading.value) {\n      console.log('Reached bottom, loading more feeds...');\n      fetchFeeds(true); // Pass true for loadMore\n    } else if (!hasMore.value) {\n        console.log('No more feeds to load.');\n        // Optionally show a \"no more data\" indicator\n    }\n  }, 300); // 300ms防抖\n});\n\n</script>\n\n<template>\n  <view class=\"feed-page-container\">\n     <!-- Category Tabs (Scrollable) -->\n     <u-sticky offset-top=\"0\" customNavHeight=\"0\"> <!-- Adjust customNavHeight if needed -->\n        <scroll-view scroll-x class=\"category-tabs-scroll\" enable-flex>\n            <view class=\"category-tabs-inner\">\n                <view\n                    v-for=\"category in categories\"\n                    :key=\"category.id\"\n                    class=\"category-tab-item\"\n                    :class=\"{ 'active': currentCategory === category.id }\"\n                    @click=\"handleCategoryChange(category.id)\"\n                >\n                    <text class=\"tab-text\">{{ category.name }}</text>\n                    <view v-if=\"currentCategory === category.id\" class=\"tab-indicator\"></view>\n                </view>\n            </view>\n        </scroll-view>\n     </u-sticky>\n\n    <!-- Feed List (Simple v-for) -->\n    <scroll-view\n      class=\"feed-list\"\n      scroll-y\n      refresher-enabled\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMoreFeeds\"\n    >\n\n      <!-- Simple List Rendering -->\n      <view v-for=\"item in feeds\" :key=\"item.id\">\n         <view class=\"feed-card\">\n           <!-- Card Content Start -->\n            <!-- {{ AURA-X: Modify - 修复头像类名，使其与CSS样式匹配. Confirmed via 寸止. }} -->\n            <view class=\"user-info\">\n                <image\n                    class=\"user-avatar\"\n                    :src=\"item.user.avatar_url || '/static/default-avatar.png'\"\n                    mode=\"aspectFill\"\n                    @click.stop=\"handleUserAvatarClick(item.user.uid)\"\n                ></image>\n                <view class=\"user-meta\" @click.stop=\"handleUserAvatarClick(item.user.uid)\">\n                    <text class=\"nickname\">{{ item.user.nickname }}</text>\n                    <text class=\"timestamp\">{{ formatTimestamp(item.created_at) }}</text>\n                </view>\n            </view>\n\n            <view class=\"feed-content\" @click=\"handleFeedClick(item.id)\">\n                <text>{{ item.content }}</text>\n            </view>\n\n           <!-- Image Grid / Single Image -->\n            <view v-if=\"item.images && item.images.length > 0\" class=\"image-display-area\">\n                <!-- Single Image -->\n                <image\n                    v-if=\"item.images.length === 1\"\n                    class=\"single-image\"\n                    :src=\"item.images[0]\"\n                    mode=\"aspectFill\"\n                    lazy-load\n                    :fade-show=\"true\"\n                    :webp=\"true\"\n                    @click.stop=\"handleImageClick(item.images, 0)\"\n                    @load=\"onImageLoad\"\n                    @error=\"onImageError\"\n                ></image>\n                <!-- Image Grid for multiple images -->\n                <view v-else class=\"image-grid\">\n                     <view\n                        v-for=\"(imgUrl, imgIndex) in item.images.slice(0, 9)\"\n                        :key=\"imgIndex\"\n                        class=\"image-grid-item\"\n                        @click.stop=\"handleImageClick(item.images, imgIndex)\"\n                     >\n                        <image\n                            :src=\"imgUrl\"\n                            mode=\"aspectFill\"\n                            lazy-load\n                            :fade-show=\"true\"\n                            :webp=\"true\"\n                            @load=\"onImageLoad\"\n                            @error=\"onImageError\"\n                        ></image>\n                     </view>\n                </view>\n            </view>\n\n            <!-- Action Bar -->\n            <view class=\"action-bar\">\n                 <!-- Left Actions: Like, Comment, Favorite, Share -->\n                 <view class=\"left-actions\">\n                    <view class=\"action-item\" @click.stop=\"handleLike(item)\">\n                        <image :src=\"item.isLiked ? '/static/dianzanqianhou.svg' : '/static/dianzanqian.svg'\"\n                               style=\"width: 44rpx; height: 44rpx\"\n                               :style=\"{ filter: item.isLiked ? 'none' : 'opacity(0.7)' }\"></image>\n                    </view>\n                    <view class=\"action-item\" @click.stop=\"handleCommentClick(item.id)\">\n                        <image src=\"/static/pinglun.svg\"\n                               style=\"width: 44rpx; height: 44rpx; opacity: 0.7\"></image>\n                    </view>\n                    <view class=\"action-item\" @click.stop=\"handleFavorite(item)\">\n                        <image :src=\"item.isFavorited ? '/static/shoucanghou.svg' : '/static/shoucangqian.svg'\" \n                               style=\"width: 44rpx; height: 44rpx\"\n                               :style=\"{ filter: item.isFavorited ? 'none' : 'opacity(0.7)' }\"></image>\n                        <text class=\"action-text\">收藏</text>\n                    </view>\n                    <!-- 分享按钮已注释，使用微信右上角分享 -->\n                    <!-- <view class=\"action-item\" @click.stop=\"handleShareClick(item.id, $event)\">\n                        <image src=\"/static/fenxiang.svg\"\n                               style=\"width: 44rpx; height: 44rpx; opacity: 0.7\"></image>\n                        <text class=\"action-text\">分享</text>\n                    </view> -->\n                 </view>\n                 <!-- Right Action: Resonance Count - 移除以符合需求 -->\n                 <!-- <view class=\"action-item resonance-count\" v-if=\"item.likeCount > 0\">\n                    <text>{{ item.likeCount }}人共鸣</text>\n                 </view> -->\n            </view>\n           <!-- Card Content End -->\n         </view>\n      </view>\n\n       <!-- Loading More / No More Data Indicators -->\n      <view v-if=\"!isLoading && feeds.length > 0\"> <!-- Based on feeds.value -->\n        <u-loadmore :status=\"hasMore ? (isLoading ? 'loading' : 'loadmore') : 'nomore'\"\n                    loadingText=\"努力加载中\"\n                    loadmoreText=\"轻轻上拉\"\n                    nomoreText=\"到底啦\" />\n      </view>\n       <!-- Initial Empty State -->\n        <view v-if=\"!isLoading && !isError && feeds.length === 0\" class=\"empty-initial\">\n            <u-empty mode=\"list\" text=\"还没有人发布动态哦\"></u-empty>\n        </view>\n         <view v-if=\"isError && feeds.length === 0\" class=\"error-initial\">\n           <u-empty mode=\"network\" :text=\"errorMessage\"></u-empty>\n      </view>\n\n    </scroll-view>\n\n    <!-- 隐藏的Canvas元素用于生成分享图片 -->\n    <canvas\n      canvas-id=\"share-canvas\"\n      id=\"share-canvas\"\n      style=\"position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;\"\n    ></canvas>\n\n    <!-- 分享弹窗 -->\n    <share-popup\n      :show=\"showSharePopup\"\n      title=\"分享动态\"\n      :share-data=\"{\n        image: currentShareFeed?.images?.[0],\n        content: currentShareFeed?.content,\n        author: currentShareFeed?.user?.nickname,\n        date: currentShareFeed?.created_at,\n        template: 'dynamic'\n      }\"\n      :show-member-invite=\"store().$state.userInfo?.role_type === 0 || store().$state.userInfo?.role_type === 1\"\n      @close=\"showSharePopup = false\"\n      @share-success=\"handleShareSuccess\"\n      @share-error=\"handleShareError\"\n    />\n\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.action-list {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-around;\n  align-items: center;\n  padding: 20rpx 0;\n  background-color: #fff;\n}\n\n.share-popup {\n  .share-options {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-around;\n    align-items: center;\n    padding: 30rpx 20rpx;\n\n    .share-option {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n\n      .share-icon {\n        width: 80rpx;\n        height: 80rpx;\n        border-radius: 50%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-bottom: 10rpx;\n        background-color: #f5f5f5;\n      }\n\n      .share-text {\n        font-size: 24rpx;\n        color: #333;\n      }\n    }\n  }\n}\n\n.feed-page-container {\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - var(--window-top) - var(--window-bottom)); // Full height minus system bars\n  background-color: #f8f9fa; // 统一背景色\n  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n}\n\n// --- Category Tabs --- (Height 48dp ~ 96rpx)\n.category-tabs-scroll {\n  width: 100%;\n  height: 100rpx; // 稍微增加高度\n  background-color: #FFFFFF;\n  white-space: nowrap; // Keep tabs in one line\n  border-bottom: 1px solid #f0f0f0;\n  box-sizing: border-box;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05); // 添加轻微阴影\n  position: relative;\n  z-index: 10;\n\n  .category-tabs-inner {\n      display: flex;\n      align-items: center;\n      height: 100%;\n      padding: 0 20rpx; // 增加两侧内边距\n  }\n}\n\n.category-tab-item {\n  display: inline-flex; // Use inline-flex for better alignment control\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 0 30rpx; // 增加内边距\n  margin: 0 10rpx;\n  font-size: 30rpx; // 增大字体\n  color: #666666;\n  position: relative;\n  transition: all 0.3s ease;\n\n  .tab-text {\n      line-height: 100rpx; // 垂直居中文本\n  }\n\n  &.active {\n    color: #576b95; // 微信蓝色\n    font-weight: 600;\n\n    .tab-indicator {\n        position: absolute;\n        bottom: 12rpx;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 48rpx; // 增加指示器宽度\n        height: 6rpx; // 保持厚度\n        background-color: #576b95; // 微信蓝色\n        border-radius: 3rpx;\n    }\n  }\n}\n\n// --- Feed List & Cards (Adjusted for Waterfall) ---\n.feed-list {\n  flex: 1;\n  padding: 8rpx; // Reduce padding slightly for waterfall columns\n  overflow-y: auto;\n  padding-top: 20rpx; // 减少顶部间距，因为已有sticky定位的tab\n}\n\n.feed-card {\n  background-color: #ffffff;\n  margin: 0 24rpx 30rpx 24rpx; // 左右24rpx边距，底部30rpx\n  padding: 28rpx;             // 增加内边距\n  border-radius: 16rpx;        // 圆角\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); // 更明显的阴影\n  overflow: hidden; // 隐藏溢出内容\n  transition: all 0.2s ease; // 平滑过渡效果\n  border: 1px solid #f5f5f5; // 添加细边框\n\n  &:active {\n      transform: scale(0.98); // 点击反馈\n      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); // 点击时减小阴影\n  }\n\n  .user-info {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx; // 增加间距\n    /* {{ AURA-X: Modify - 调整头像大小为合适尺寸，确保圆形显示. Confirmed via 寸止. }} */\n    .user-avatar {\n      width: 60rpx; // 调整为合适的头像大小\n      height: 60rpx;\n      border-radius: 50%; // 确保圆形\n      margin-right: 16rpx;\n      flex-shrink: 0;\n      border: 2rpx solid #f0f0f0; // 添加边框\n      object-fit: cover; // 确保图片正确填充\n    }\n    .user-meta {\n      flex: 1; // 允许元数据占据剩余空间\n      display: flex;\n      flex-direction: column; // 改为纵向排列\n      justify-content: center; // 垂直居中\n\n      .nickname {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #333; // 更深的昵称颜色\n        margin-bottom: 6rpx; // 昵称和时间戳之间的间距\n      }\n\n      .timestamp {\n        font-size: 22rpx;\n        color: #999; // 时间戳颜色\n      }\n    }\n  }\n\n  .feed-content {\n    font-size: 30rpx;\n    color: #333; // 主要内容颜色\n    line-height: 1.8; // 增加行高\n    margin-bottom: 24rpx;\n    letter-spacing: 1rpx; // 增加字间距\n    word-break: break-all; // 允许在任意字符间断行\n  }\n\n  .image-display-area {\n    margin-top: 20rpx;   // 增加上边距\n    margin-bottom: 20rpx; // 增加下边距\n    border-radius: 12rpx; // 增加圆角\n    overflow: hidden;    // 裁剪图像到圆角\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); // 轻微阴影\n\n    .single-image {\n      display: block; // 确保图像表现为块元素\n      width: 100%;\n      aspect-ratio: 3 / 2; // 3:2宽高比\n      object-fit: cover;   // 覆盖区域，必要时裁剪\n      border-radius: 12rpx; // 匹配区域圆角\n    }\n\n    .image-grid {\n      display: grid;\n      grid-template-columns: repeat(3, 1fr); // 3等分列\n      gap: 8rpx; // 增加网格间隙\n\n      .image-grid-item {\n        position: relative;\n        width: 100%; // 占满列宽\n        aspect-ratio: 3 / 2; // 移除正方形容器，改为3:2比例\n        overflow: hidden;    // 隐藏内部图像溢出\n        border-radius: 8rpx; // 每个项目的圆角\n        background-color: #f5f5f5; // 占位背景色\n\n        .grid-item-image {\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          object-fit: cover; // 覆盖网格项\n        }\n      }\n    }\n  }\n\n  .action-bar {\n    display: flex;\n    justify-content: space-between; // 左右动作间隔\n    align-items: center;\n    margin-top: 24rpx;\n    padding-top: 20rpx;\n    border-top: 1rpx solid #f5f5f5; // 更浅的分隔线\n  }\n\n  .left-actions {\n    display: flex;\n    align-items: center;\n  }\n\n  .action-item {\n    display: flex;\n    align-items: center;\n    margin-right: 36rpx; // 增加动作项之间的间距\n    padding: 8rpx 12rpx; // 增加可点击区域\n    border-radius: 30rpx; // 圆角\n    transition: background-color 0.3s;\n\n    &:active {\n      background-color: #f5f5f5; // 点击时背景色变化\n    }\n\n    &:last-child {\n        margin-right: 0;\n    }\n\n    .action-text {\n      margin-left: 8rpx;\n      font-size: 26rpx; // 增大字体\n      color: #666; // 更深的文本颜色\n    }\n  }\n}\n\n.empty-initial, .error-initial {\n    margin-top: 100rpx; /* Add some margin for empty/error states */\n}\n\n/* 移除空规则集 */\n\n</style>", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/feed/index.vue'\nwx.createComponent(Component)"], "names": ["SharePopup", "feeds", "ref", "isLoading", "refreshing", "isError", "errorMessage", "hasMore", "feedsCache", "Map", "pageCache", "hasMore<PERSON>ache", "form", "reactive", "page", "page_size", "uid", "token", "user_id", "category", "type", "categories", "name", "id", "currentCategory", "formatTimestamp", "timestamp", "formattedTimeStr", "replace", "past", "Date", "now", "diff", "minutes", "Math", "floor", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "getMinutes", "generateFeedCacheKey", "userId", "userInfo", "store", "$state", "fetchFeeds", "async", "loadMore", "isRefresh", "currentUid", "currentToken", "console", "log", "value", "JSON", "parse", "stringify", "res", "getFeeds", "status", "_a", "data", "list", "newFeeds", "map", "feed", "globalLikeState", "getFeedLikeState", "globalFavoriteState", "getFeedFavoriteState", "isLiked", "likeCount", "isFavorited", "isExpanded", "push", "length", "total", "cache<PERSON>ey", "set", "msg", "error", "onRefresh", "resetFeedList", "nextTick", "handleUserAvatarClick", "navto", "warn", "onImageLoad", "onImageError", "showSharePopup", "currentShareFeed", "onShareAppMessage", "title", "content", "substring", "path", "imageUrl", "_d", "images", "config", "_e", "img_config", "_f", "app_logo", "val", "_b", "store_index", "_i", "_h", "handleShareSuccess", "result", "uni", "showToast", "icon", "handleShareError", "handleImageClick", "index", "previewImage", "urls", "current", "onLoad", "hasToken", "loadWorldStateFromLocal", "onMounted", "expose", "loadFeedData", "reachBottomTimer", "onReachBottom", "clearTimeout", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newCache<PERSON>ey", "has", "get", "feedId", "duration", "currentUserId", "requireLogin", "originalLikedState", "originalLikeCount", "likeFeed", "updateFeedLike", "originalFavoritedState", "favoriteFeed", "updateFeedFavorite", "wx", "createComponent", "Component"], "mappings": "ywBAOA,MAAMA,EAAa,IAAW,0FAG9B,MAAMC,EAAQC,EAAAA,IAAI,IACZC,EAAYD,EAAAA,KAAI,GAChBE,EAAaF,EAAAA,KAAI,GACjBG,EAAUH,EAAAA,KAAI,GACdI,EAAeJ,EAAAA,IAAI,IACnBK,EAAUL,EAAAA,KAAI,GAGdM,EAAaN,EAAGA,IAAC,IAAIO,KACrBC,EAAYR,EAAGA,IAAC,IAAIO,KACpBE,EAAeT,EAAGA,IAAC,IAAIO,KACvBG,EAAOC,EAAAA,SAAS,CACpBC,KAAM,EACNC,UAAW,GACXC,IAAK,EACLC,MAAO,GACPC,QAAS,EACTC,SAAU,SACVC,KAAM,SAEFC,EAAanB,EAAAA,IAAI,CACnB,CAAEoB,KAAM,KAAMC,GAAI,UAClB,CAAED,KAAM,KAAMC,GAAI,SAEhBC,EAAkBtB,EAAAA,IAAI,UAStBuB,EAAmBC,IACvB,IAAKA,EAAkB,MAAA,GAGvB,MAAMC,EAAmBD,EAAUE,QAAQ,KAAM,KAC3CC,EAAO,IAAIC,KAAKH,GAChBI,EAAM,IAAID,KACVE,EAAOD,EAAMF,EAGnB,GAAIG,EAAO,KAAS,CAClB,MAAMC,EAAUC,KAAKC,MAAMH,EAAO,KAC3BC,OAAAA,GAAW,EAAI,KAAO,GAAGA,MAClC,CAGA,GAAID,EAAO,MAAU,CAEnB,MAAO,GADOE,KAAKC,MAAMH,EAAO,UAElC,CAGM,MAAAI,EAAOP,EAAKQ,cACZC,EAAQC,OAAOV,EAAKW,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOV,EAAKc,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOV,EAAKgB,YAAYJ,SAAS,EAAG,KAC5CR,EAAUM,OAAOV,EAAKiB,cAAcL,SAAS,EAAG,KAGlD,OAAAL,IAASL,EAAIM,cACR,GAAGC,KAASI,KAAOE,KAASX,IAE5B,GAAGG,KAAQE,KAASI,KAAOE,KAASX,GAC7C,EAIIc,EAAuB,CAAC5B,EAAU6B,EAAS,QAE7C,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAEhC,MAAO,QAAQ9B,UADH6B,GAAUpC,EAAKI,YAAOiC,WAAUjC,MAAO,aACnB,EAiD9BoC,EAAaC,MAAOC,GAAW,EAAOC,GAAY,WAEtD,MAAMN,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BO,SAAaP,WAAUjC,MAAO,EAC9ByC,SAAeR,WAAUhC,QAAS,GAKpC,GAHIyC,QAAAC,IAAI,iCAAiCL,iBAAwBC,WAAmBC,uBAAgCC,gBAA2BjC,EAAgBoC,UAG/JzD,EAAUyD,OAAUL,EAApB,CAMAA,GACFnD,EAAWwD,OAAQ,EACnBhD,EAAKE,KAAO,EACZP,EAAQqD,OAAQ,EAChBF,QAAQC,IAAI,gBAEZxD,EAAUyD,OAAQ,EAGpBvD,EAAQuD,OAAQ,EAChBtD,EAAasD,MAAQ,GAEhBN,GAAaC,EAIPD,GACDI,QAAAC,IAAI,cAAc/C,EAAKE,SAJ/BF,EAAKE,KAAO,EACZP,EAAQqD,OAAQ,EAChBF,QAAQC,IAAI,gBAKV,IAED/C,EAAKI,IAAMwC,EACX5C,EAAKK,MAAQwC,EAGb7C,EAAKO,SAAWK,EAAgBoC,MAGhChD,EAAKQ,KAAO,OAELsC,QAAAC,IAAI,4CAA6CE,KAAKC,MAAMD,KAAKE,UAAUnD,KACnF,MAAMoD,QAAYC,WAASrD,GAG3B,GAFQ8C,QAAAC,IAAI,yBAA0BK,GAEnB,OAAfA,EAAIE,SAAmB,OAAAC,EAAIH,EAAAI,eAAMC,MAAM,CACzC,MAAMC,EAAWN,EAAII,KAAKC,KAAKE,KAAYC,IAEzC,MAAMC,EAAkBvB,EAAAA,QAAQwB,iBAAiBF,EAAKjD,IAChDoD,EAAsBzB,EAAAA,QAAQ0B,qBAAqBJ,EAAKjD,IAEvD,MAAA,IACFiD,EAEHK,QAASJ,EAAkBA,EAAgBI,QAAUL,EAAKK,QAC1DC,UAAWL,EAAkBA,EAAgBK,UAAYN,EAAKM,UAC9DC,YAAqC,OAAxBJ,EAA+BA,EAAsBH,EAAKO,YACvEC,YAAY,EACtB,IAEU1B,EACIrD,EAAA2D,MAAMqB,QAAQX,GAEpBrE,EAAM2D,MAAQU,EAEhB/D,EAAQqD,MAAQ3D,EAAM2D,MAAMsB,QAAUlB,EAAII,KAAKe,OAAS,GACnDvE,EAAAE,OAGL,MAAMsE,EAAWrC,EAAqBvB,EAAgBoC,MAAOhD,EAAKI,KAClER,EAAWoD,MAAMyB,IAAID,EAAU,IAAInF,EAAM2D,QACzClD,EAAUkD,MAAMyB,IAAID,EAAUxE,EAAKE,MACnCH,EAAaiD,MAAMyB,IAAID,EAAU7E,EAAQqD,OACjCF,QAAAC,IAAI,QAAQnC,EAAgBoC,gBAAgBhD,EAAKI,SAASf,EAAM2D,MAAMsB,aACpF,KAA8B,UAAflB,EAAIE,QACPZ,IACHrD,EAAM2D,MAAQ,IAEjBrD,EAAQqD,OAAQ,IAEhBvD,EAAQuD,OAAQ,EACHtD,EAAAsD,MAAQI,EAAIsB,KAAO,SAC1BhC,IACFrD,EAAM2D,MAAQ,IAEjBrD,EAAQqD,OAAQ,EAEpB,OAAQ2B,GACC7B,QAAA6B,MAAM,8BAA+BA,GACrC7B,QAAA6B,MAAM,wBAAyBA,GACvClF,EAAQuD,OAAQ,EAChBtD,EAAasD,MAAQ,aACfN,IACDrD,EAAM2D,MAAQ,IAElBrD,EAAQqD,OAAQ,CACrB,CAAY,QACRzD,EAAUyD,OAAQ,EAClBxD,EAAWwD,OAAQ,CACrB,CA5FA,MAFEF,QAAQC,IAAI,eA8Fd,EAKI6B,EAAY,KAChB9B,QAAQC,IAAI,UACZP,GAAW,GAAO,EAAI,EAGlBqC,EAAgB,KAClB/B,QAAQC,IAAI,YAGZ1D,EAAM2D,MAAQ,GAGdhD,EAAKE,KAAO,EACZP,EAAQqD,OAAQ,EAGhBzD,EAAUyD,OAAQ,EAClBvD,EAAQuD,OAAQ,EAChBtD,EAAasD,MAAQ,GAGrB8B,EAAAA,YAAS,KACLhC,QAAQC,IAAI,mBAAoB1D,EAAM2D,MAAMsB,OAAM,GACrD,EAyHCS,EAAyB3C,IACtBA,EAML4C,EAAAA,MAAM,mCAAmC5C,KALrCU,QAAQmC,KAAK,cAKgC,EAM/CC,EAAc,OAOdC,EAAe,OAefC,EAAiB9F,EAAAA,KAAI,GACrB+F,EAAmB/F,EAAAA,IAAI,MAG7BgG,EAAAA,mBAAkB,6BACZ,IACE,OAACD,EAAiBrC,MASf,CACLuC,MAAOF,EAAiBrC,MAAMwC,QAC3BH,EAAiBrC,MAAMwC,QAAQlB,OAAS,GACvCe,EAAiBrC,MAAMwC,QAAQC,UAAU,EAAG,IAAM,MAClDJ,EAAiBrC,MAAMwC,QACzB,WACFE,KAAM,0CAA0CL,EAAiBrC,MAAMrC,KACvEgF,UAAU,OAAAC,EAAiBP,EAAArC,MAAM6C,aAAvB,EAAAD,EAAgC,MAAMtD,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,EAAAA,QAAQC,OAAOuD,aAAQ,EAAAC,EAAAC,iBAAY,EAAAC,EAAAC,mBAAUC,MAAO,KAfpGrD,QAAQmC,KAAK,oBACN,CACLM,MAAO,WACPG,KAAM,iCACNC,UAAUrD,OAAAA,EAAAA,OAAK8D,EAAL9D,OAAKiB,EAAA8C,EAAA/D,QAAGC,OAAOuD,aAAfxD,EAAAA,EAAuB0D,iBAAvB1D,EAAAA,EAAmC4D,eAAnC5D,EAAAA,EAA6C6D,MAAO,IAanE,OAAQxB,GAEA,OADC7B,QAAA6B,MAAM,YAAaA,GACpB,CACLY,MAAO,WACPG,KAAM,iCACNC,UAAUrD,OAAAA,EAAAA,OAAKgE,EAALhE,OAAKiE,EAAAF,EAAA/D,QAAGC,OAAOuD,aAAfxD,EAAAA,EAAuB0D,iBAAvB1D,EAAAA,EAAmC4D,eAAnC5D,EAAAA,EAA6C6D,MAAO,GAElE,KAiCI,MAAAK,EAAsBC,IAChB3D,QAAAC,IAAI,QAAS0D,GACrBC,EAAAA,MAAIC,UAAU,CACVpB,MAAO,OACPqB,KAAM,WACT,EAICC,EAAoBlC,IACd7B,QAAA6B,MAAM,QAASA,GACvB+B,EAAAA,MAAIC,UAAU,CACVpB,MAAO,OACPqB,KAAM,QACT,EAQCE,EAAmB,CAACjB,EAAQkB,KAC9BL,EAAAA,MAAIM,aAAa,CACbC,KAAMpB,EACNqB,QAASH,GACZ,EAOLI,EAAAA,QAAO,KACHrE,QAAQC,IAAI,4BAGZ,MAAMV,EAAWC,EAAAA,QAAQC,OAAOF,gBAC5BA,WAAUjC,OACVJ,EAAKI,IAAMiC,EAASjC,IACfJ,EAAAK,MAAQgC,EAAShC,OAAS,GACvByC,QAAAC,IAAI,WAAY,CAAE3C,IAAKJ,EAAKI,IAAKgH,WAAYpH,EAAKK,SAK9DyC,QAAQC,IAAI,mCAGPsD,EAAA/D,QAAG+E,0BAGRzG,EAAgBoC,MAAQ,SAChBF,QAAAC,IAAI,WAAYnC,EAAgBoC,cAS5CsE,EAAAA,WAAU,KACRxE,QAAQC,IAAI,8BAA6B,IAe9BwE,EAAA,CACXC,aAPmB,KACnB1E,QAAQC,IAAI,0CAUd,IAAI0E,EAAmB,YAKvBC,EAAAA,eAAc,KACRD,GACFE,aAAaF,GAGfA,EAAmBG,YAAW,KACxBjI,EAAQqD,QAAUzD,EAAUyD,OAC9BF,QAAQC,IAAI,yCACZP,GAAW,IACD7C,EAAQqD,OAChBF,QAAQC,IAAI,yBAEhB,GACC,IAAG,mKAnfqB,CAACpC,IACtB,GAAAC,EAAgBoC,QAAUrC,EAE1B,YADQmC,QAAAC,IAAI,MAAMpC,oBAOlB,GAHJmC,QAAQC,IAAI,SAASnC,EAAgBoC,YAAYrC,KAG7CtB,EAAM2D,MAAMsB,OAAS,EAAG,CACxB,MAAMuD,EAAkB1F,EAAqBvB,EAAgBoC,MAAOhD,EAAKI,KACzER,EAAWoD,MAAMyB,IAAIoD,EAAiB,IAAIxI,EAAM2D,QAChDlD,EAAUkD,MAAMyB,IAAIoD,EAAiB7H,EAAKE,MAC1CH,EAAaiD,MAAMyB,IAAIoD,EAAiBlI,EAAQqD,OACxCF,QAAAC,IAAI,QAAQnC,EAAgBoC,gBAAgBhD,EAAKI,SAASf,EAAM2D,MAAMsB,WAClF,CAGA1D,EAAgBoC,MAAQrC,EAGxB,MAAMmH,EAAc3F,EAAqBxB,EAAIX,EAAKI,KAC9CR,EAAWoD,MAAM+E,IAAID,IAErBzI,EAAM2D,MAAQ,IAAIpD,EAAWoD,MAAMgF,IAAIF,IACvC9H,EAAKE,KAAOJ,EAAUkD,MAAMgF,IAAIF,IAAgB,EAChDnI,EAAQqD,MAAQjD,EAAaiD,MAAMgF,IAAIF,KAAgB,EACvDvI,EAAUyD,OAAQ,EAClBvD,EAAQuD,OAAQ,EAChBtD,EAAasD,MAAQ,GACrBF,QAAQC,IAAI,WAAWpC,YAAaX,EAAKI,SAASf,EAAM2D,MAAMsB,kBAI9DQ,EAAAA,YAAS,KACLhC,QAAQC,IAAI,UAAUpC,aAAcX,EAAKI,cAGjD,wRAoJoB,CAAC6H,IACnB,IACMnF,QAAAC,IAAI,yCAAyCkF,KAGrDjD,EAAAA,MAAM,0CAA0CiD,sBACjD,OAAQtD,GACC7B,QAAA6B,MAAM,YAAaA,GAC3B+B,EAAAA,MAAIC,UAAU,CACZpB,MAAO,aACPqB,KAAM,OACNsB,SAAU,KAEd,iaAOiBzF,OAAOmB,IAEtB,MAAMvB,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BQ,EAAyB,MAAVR,OAAU,EAAAA,EAAAhC,MACzB8H,EAA0B,MAAV9F,OAAU,EAAAA,EAAAjC,IAKhC,GAHA0C,QAAQC,IAAI,0CAA0CF,cAAyBsF,MAG1EC,EAAYA,aAAC,GAAI,YAClB,OAGJ,MAAMC,EAAqBzE,EAAKK,QAC1BqE,EAAoB1E,EAAKM,UAG1BN,EAAAK,SAAWL,EAAKK,QAChBL,EAAAM,WAAaN,EAAKK,QAAU,GAAI,EAEjC,IACM,MAAAb,QAAYmF,WAAS,CACvB5H,GAAIiD,EAAKjD,GACTP,IAAK+H,EACL9H,MAAOwC,IAGQ,OAAfO,EAAIE,QAEJM,EAAKK,QAAUoE,EACfzE,EAAKM,UAAYoE,UACb3B,UAAU,CAAEpB,MAAOnC,EAAIsB,KAAO,OAAQkC,KAAM,qBAGxC4B,eAAe5E,EAAKjD,GAAIiD,EAAKK,QAASL,EAAKM,WAC3CpB,QAAAC,IAAI,sBAAuBK,GAE1C,OAAQuB,GAELf,EAAKK,QAAUoE,EACfzE,EAAKM,UAAYoE,EACjB5B,EAAGK,MAACJ,UAAU,CAAEpB,MAAO,WAAYqB,KAAM,SACjC9D,QAAA6B,MAAM,mBAAoBA,EACtC,gCA8EwBsD,OAChBnF,QAAAC,IAAI,4BAA6BkF,QAEzCjD,EAAAA,MAAM,0CAA0CiD,uBAHzB,IAACA,+HAvELxF,OAAOmB,IAC1B,MAAMvB,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BQ,EAAyB,MAAVR,OAAU,EAAAA,EAAAhC,MACzB8H,EAA0B,MAAV9F,OAAU,EAAAA,EAAAjC,IAEhC,IAAKyC,EAED,YADA6D,EAAGK,MAACJ,UAAU,CAAEpB,MAAO,OAAQqB,KAAM,SAInC,MAAA6B,EAAyB7E,EAAKO,cAAe,EAG9CP,EAAAO,aAAeP,EAAKO,YAErB,IACM,MAAAf,QAAYsF,eAAa,CAC3B/H,GAAIiD,EAAKjD,GACTP,IAAK+H,EACL9H,MAAOwC,IAGQ,OAAfO,EAAIE,QAEJM,EAAKO,YAAcsE,UACf9B,UAAU,CAAEpB,MAAOnC,EAAIsB,KAAO,OAAQkC,KAAM,WAGhDtE,EAAKA,QAAGqG,mBAAmB/E,EAAKjD,GAAIiD,EAAKO,qBACrCwC,UAAU,CAAEpB,MAAOnC,EAAIsB,KAAO,OAAQkC,KAAM,YAEvD,OAAQjC,GAELf,EAAKO,YAAcsE,EACnB/B,EAAGK,MAACJ,UAAU,CAAEpB,MAAO,WAAYqB,KAAM,SACjC9D,QAAA6B,MAAM,uBAAwBA,EAC1C,4hCC1XJiE,GAAGC,gBAAgBC"}