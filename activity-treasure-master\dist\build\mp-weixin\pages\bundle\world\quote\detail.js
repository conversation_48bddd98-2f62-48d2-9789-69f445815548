"use strict";const e=require("../../../../common/vendor.js"),t=require("../../../../api/index.js"),o=require("../../../../store/index.js"),a=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/cacheManager.js"),require("../../../../store/counter.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const i={__name:"detail",setup(i){const s=e.ref(null),n=e.ref(!0),u=e.ref(!1),l=e.ref([]),r=e.ref(!1),v=e.ref(""),c=e.ref(0),d=async()=>{var a,i;try{r.value=!0;const s=await t.getQuoteComments({quote_id:c.value,page:1,page_size:50,uid:(null==(a=o.store().$state.userInfo)?void 0:a.uid)||0,token:(null==(i=o.store().$state.userInfo)?void 0:i.token)||""});"ok"===s.status?l.value=s.data.list||[]:"empty"===s.status?l.value=[]:e.index.showToast({title:s.msg||"加载评论失败",icon:"none"})}catch(s){console.error("加载评论失败:",s),e.index.showToast({title:"加载评论失败",icon:"none"})}finally{r.value=!1}},f=async()=>{if(a.requireLogin("","请先登录后再点赞")&&s.value)try{const a=await t.likeQuote({id:s.value.id,uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token});"ok"===a.status?(s.value.is_liked=!s.value.is_liked,s.value.like_count=s.value.is_liked?(s.value.like_count||0)+1:(s.value.like_count||1)-1,e.index.showToast({title:s.value.is_liked?"点赞成功":"取消点赞",icon:"success"})):e.index.showToast({title:a.msg||"操作失败",icon:"none"})}catch(i){console.error("点赞失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}},m=async()=>{if(a.requireLogin("","请先登录后再收藏")&&s.value)try{const a=await t.favoriteQuote({id:s.value.id,uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token});"ok"===a.status?(s.value.is_favorited=!s.value.is_favorited,s.value.favorite_count=s.value.is_favorited?(s.value.favorite_count||0)+1:(s.value.favorite_count||1)-1,e.index.showToast({title:s.value.is_favorited?"收藏成功":"取消收藏",icon:"success"})):e.index.showToast({title:a.msg||"操作失败",icon:"none"})}catch(i){console.error("收藏失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}},h=async()=>{if(a.requireLogin("","请先登录后再评论"))if(v.value.trim())try{const a=await t.postQuoteComment({quote_id:c.value,content:v.value.trim(),uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token});"ok"===a.status?(v.value="",e.index.showToast({title:"评论成功",icon:"success"}),d()):e.index.showToast({title:a.msg||"评论失败",icon:"none"})}catch(i){console.error("评论失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}else e.index.showToast({title:"请输入评论内容",icon:"none"})},g=()=>{u.value=!u.value,u.value&&0===l.value.length&&d()};e.onLoad((a=>{if(c.value=parseInt(a.id),!c.value)return e.index.showToast({title:"摘录ID无效",icon:"none"}),void e.index.navigateBack();(async()=>{var a,i;try{n.value=!0;const u=await t.getQuoteDetail({quote_id:c.value,uid:(null==(a=o.store().$state.userInfo)?void 0:a.uid)||0,token:(null==(i=o.store().$state.userInfo)?void 0:i.token)||""});"ok"===u.status?s.value=u.data:(e.index.showToast({title:u.msg||"加载失败",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500))}catch(u){console.error("加载摘录详情失败:",u),e.index.showToast({title:"加载失败，请重试",icon:"none"})}finally{n.value=!1}})(),"true"===a.showComments&&(u.value=!0,d())}));const _=e=>{if(!e)return"";const t=new Date(e.replace(/-/g,"/")),o=new Date-t;return o<6e4?"刚刚":o<36e5?Math.floor(o/6e4)+"分钟前":o<864e5?Math.floor(o/36e5)+"小时前":o<2592e6?Math.floor(o/864e5)+"天前":e.split(" ")[0]},k=()=>{e.index.navigateBack()};return(t,o)=>{var a,i;return e.e({a:e.p({name:"arrow-left",color:"#333",size:"20"}),b:e.o(k),c:n.value},n.value?{d:e.p({mode:"circle",size:"30",color:"#6AC086"})}:s.value?e.e({f:e.t(s.value.content),g:e.t(s.value.author),h:s.value.source},s.value.source?{i:e.t(s.value.source)}:{},{j:s.value.images&&s.value.images.length>0},s.value.images&&s.value.images.length>0?{k:e.f(s.value.images,((o,a,i)=>({a:a,b:o,c:e.o((e=>t.previewImage(o)),a)})))}:{},{l:(null==(a=s.value.user)?void 0:a.avatar_url)||"/static/default-avatar.png",m:e.t((null==(i=s.value.user)?void 0:i.nickname)||"匿名用户"),n:e.t(_(s.value.created_at)),o:e.p({name:s.value.is_liked?"heart-fill":"heart",color:s.value.is_liked?"#ff4757":"#999",size:"20"}),p:e.t(s.value.like_count||0),q:e.o(f),r:e.p({name:s.value.is_favorited?"star-fill":"star",color:s.value.is_favorited?"#ffa502":"#999",size:"20"}),s:e.t(s.value.favorite_count||0),t:e.o(m),v:e.p({name:"chat",color:"#999",size:"20"}),w:e.t(l.value.length),x:e.o(g),y:u.value},u.value?e.e({z:e.t(l.value.length),A:0===l.value.length&&!r.value},(0!==l.value.length||r.value,{}),{B:e.f(l.value,((t,o,a)=>{var i,s;return{a:(null==(i=t.user)?void 0:i.avatar)||"/static/default-avatar.png",b:e.t((null==(s=t.user)?void 0:s.nickname)||"用户"),c:e.t(_(t.created_at)),d:e.t(t.content),e:t.id}})),C:r.value},r.value?{D:e.p({mode:"circle",size:"24",color:"#999"})}:{}):{}):{},{e:s.value,E:u.value},u.value?{F:e.o(h),G:v.value,H:e.o((e=>v.value=e.detail.value)),I:e.o(h)}:{})}}},s=e._export_sfc(i,[["__scopeId","data-v-a06348f5"]]);wx.createPage(s);
//# sourceMappingURL=detail.js.map
