"use strict";const e=require("../../../../common/vendor.js"),o={name:"u-search",mixins:[e.mpMixin,e.mixin,e.props$36],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword(e){this.$emit("update:modelValue",e),this.$emit("change",e)},modelValue:{immediate:!0,handler(e){this.keyword=e}}},computed:{showActionBtn(){return!this.animation&&this.showAction}},emits:["clear","search","custom","focus","blur","click","clickIcon","update:modelValue","change"],methods:{addStyle:e.addStyle,addUnit:e.addUnit,inputChange(e){this.keyword=e.detail.value},clear(){this.keyword="",this.$nextTick((()=>{this.$emit("clear")}))},search(o){this.$emit("search",o.detail.value);try{e.index.hideKeyboard()}catch(t){}},custom(){this.$emit("custom",this.keyword);try{e.index.hideKeyboard()}catch(o){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout((()=>{this.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(o){this.$emit("clickIcon",this.keyword);try{e.index.hideKeyboard()}catch(t){}}}};if(!Array){e.resolveComponent("u-icon")()}Math;const t=e._export_sfc(o,[["render",function(o,t,i,c,s,a){return e.e({a:o.$slots.label||null!==o.label},o.$slots.label||null!==o.label?{b:e.t(o.label)}:{},{c:e.o(a.clickIcon),d:e.p({size:o.searchIconSize,name:o.searchIcon,color:o.searchIconColor?o.searchIconColor:o.color}),e:e.o(((...e)=>a.blur&&a.blur(...e))),f:s.keyword,g:e.o(((...e)=>a.search&&a.search(...e))),h:e.o(((...e)=>a.inputChange&&a.inputChange(...e))),i:o.disabled,j:e.o(((...e)=>a.getFocus&&a.getFocus(...e))),k:o.focus,l:o.maxlength,m:o.adjustPosition,n:o.autoBlur,o:o.placeholder,p:`color: ${o.placeholderColor}`,q:e.s({textAlign:o.inputAlign,color:o.color,backgroundColor:o.bgColor,height:a.addUnit(o.height)}),r:e.s(o.inputStyle),s:s.keyword&&o.clearabled&&s.focused},s.keyword&&o.clearabled&&s.focused?{t:e.p({name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}),v:e.o(((...e)=>a.clear&&a.clear(...e)))}:{},{w:o.bgColor,x:"round"==o.shape?"100px":"4px",y:o.borderColor,z:e.t(o.actionText),A:e.s(o.actionStyle),B:e.n((a.showActionBtn||s.show)&&"u-search__action--active"),C:e.o(((...e)=>a.custom&&a.custom(...e))),D:e.o(((...e)=>a.clickHandler&&a.clickHandler(...e))),E:e.s({margin:o.margin}),F:e.s(a.addStyle(o.customStyle))})}],["__scopeId","data-v-9c758f09"]]);wx.createComponent(t);
//# sourceMappingURL=u-search.js.map
