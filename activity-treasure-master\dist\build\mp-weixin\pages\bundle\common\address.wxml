<view class="page"><u-radio-group wx:if="{{f}}" u-s="{{['d']}}" u-i="17a316c2-0" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><view wx:for="{{a}}" wx:for-item="val" wx:key="o" class="mb20 w b6f" bindtap="{{val.p}}"><view class="item py30"><view class="df aie mb20 px30 fb"><view class="mr20 x32">{{val.a}}</view><view class="x28">{{val.b}}</view></view><view class="px30 x28">{{val.c}}-{{val.d}}-{{val.e}}-{{val.f}}</view><u-line wx:if="{{b}}" u-i="{{val.g}}" bind:__l="__l" u-p="{{b}}"></u-line><view class="df aic jcsb px30"><u-radio wx:if="{{val.j}}" bindchange="{{val.h}}" u-i="{{val.i}}" bind:__l="__l" u-p="{{val.j}}"></u-radio><view class="df aic"><view class="mr30" catchtap="{{val.l}}"><u-icon wx:if="{{c}}" u-i="{{val.k}}" bind:__l="__l" u-p="{{c}}"></u-icon></view><u-icon wx:if="{{d}}" bindclick="{{val.m}}" u-i="{{val.n}}" bind:__l="__l" u-p="{{d}}"></u-icon></view></view></view></view></u-radio-group><u-gap wx:if="{{g}}" u-i="17a316c2-5" bind:__l="__l" u-p="{{g}}"></u-gap><view class="pfx bottom0 bottomBox b6f"><u-button wx:if="{{i}}" bindclick="{{h}}" u-i="17a316c2-6" bind:__l="__l" u-p="{{i}}"></u-button><u-safe-bottom u-i="17a316c2-7" bind:__l="__l"></u-safe-bottom></view></view>