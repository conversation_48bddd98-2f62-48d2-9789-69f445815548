{"version": 3, "file": "u-switch.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-switch/u-switch.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN3aXRjaC91LXN3aXRjaC52dWU"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-switch cursor-pointer\"\n\t    :class=\"[disabled && 'u-switch--disabled']\"\n\t    :style=\"[switchStyle, addStyle(customStyle)]\"\n\t    @tap=\"clickHandler\"\n\t>\n\t\t<view\n\t\t    class=\"u-switch__bg\"\n\t\t    :style=\"[bgStyle]\"\n\t\t>\n\t\t</view>\n\t\t<view\n\t\t    class=\"u-switch__node\"\n\t\t    <!-- #ifdef VUE3 -->\n\t\t\t:class=\"[modelValue && 'u-switch__node--on']\"\n\t\t\t<!-- #endif -->\n\t\t\t<!-- #ifdef VUE2 -->\n\t\t\t:class=\"[value && 'u-switch__node--on']\"\n\t\t\t<!-- #endif -->\n\t\t    :style=\"[nodeStyle]\"\n\t\t    ref=\"u-switch__node\"\n\t\t>\n\t\t\t<u-loading-icon\n\t\t\t    :show=\"loading\"\n\t\t\t    mode=\"circle\"\n\t\t\t    timingFunction='linear'\n\t\t\t    <!-- #ifdef VUE3 -->\n\t\t\t\t:color=\"modelValue ? activeColor : '#AAABAD'\"\n\t\t\t\t<!-- #endif -->\n\t\t\t\t<!-- #ifdef VUE2 -->\n\t\t\t\t:color=\"value ? activeColor : '#AAABAD'\"\n\t\t\t\t<!-- #endif -->\n\t\t\t    :size=\"size * 0.6\"\n\t\t\t/>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, addUnit, error } from '../../libs/function/index';\n\t/**\n\t * switch 开关选择器\n\t * @description 选择开关一般用于只有两个选择，且只能选其一的场景。\n\t * @tutorial https://ijry.github.io/uview-plus/components/switch.html\n\t * @property {Boolean}\t\t\t\t\t\tloading\t\t\t是否处于加载中（默认 false ）\n\t * @property {Boolean}\t\t\t\t\t\tdisabled\t\t是否禁用（默认 false ）\n\t * @property {String | Number}\t\t\t\tsize\t\t\t开关尺寸，单位px （默认 25 ）\n\t * @property {String}\t\t\t\t\t\tactiveColor\t\t打开时的背景色 （默认 '#2979ff' ）\n\t * @property {String} \t\t\t\t\t\tinactiveColor\t关闭时的背景色 （默认 '#ffffff' ）\n\t * @property {Boolean | String | Number}\tvalue\t\t\t通过v-model双向绑定的值 （默认 false ）\n\t * @property {Boolean | String | Number}\tactiveValue\t\t打开选择器时通过change事件发出的值 （默认 true ）\n\t * @property {Boolean | String | Number}\tinactiveValue\t关闭选择器时通过change事件发出的值 （默认 false ）\n\t * @property {Boolean}\t\t\t\t\t\tasyncChange\t\t是否开启异步变更，开启后需要手动控制输入值 （默认 false ）\n\t * @property {String | Number}\t\t\t\tspace\t\t\t圆点与外边框的距离 （默认 0 ）\n\t * @property {Object}\t\t\t\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t *\n\t * @event {Function} change 在switch打开或关闭时触发\n\t * @example <u-switch v-model=\"checked\" active-color=\"red\" inactive-color=\"#eee\"></u-switch>\n\t */\n\texport default {\n\t\tname: \"u-switch\",\n\t\tmixins: [mpMixin, mixin,props],\n\t\twatch: {\n\t\t\t// #ifdef VUE3\n\t\t\tmodelValue: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tif(n !== this.inactiveValue && n !== this.activeValue) {\n\t\t\t\t\t\terror('v-model绑定的值必须为inactiveValue、activeValue二者之一')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// #endif\n        \t// #ifdef VUE2\n\t\t\tvalue: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tif(n !== this.inactiveValue && n !== this.activeValue) {\n\t\t\t\t\t\terror('v-model绑定的值必须为inactiveValue、activeValue二者之一')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tbgColor: '#ffffff'\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tisActive(){\n\t\t\t\t// #ifdef VUE3\n\t\t\t\treturn this.modelValue === this.activeValue;\n\t\t\t\t// #endif\n        \t\t// #ifdef VUE2\n\t\t\t\treturn this.value === this.activeValue;\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tswitchStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 这里需要加2，是为了腾出边框的距离，否则圆点node会和外边框紧贴在一起\n\t\t\t\tstyle.width = addUnit(this.size * 2 + 2)\n\t\t\t\tstyle.height = addUnit(Number(this.size) + 2)\n\t\t\t\t// style.borderColor = this.value ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.12)'\n\t\t\t\t// 如果自定义了“非激活”演示，name边框颜色设置为透明(跟非激活颜色一致)\n\t\t\t\t// 这里不能简单的设置为非激活的颜色，否则打开状态时，会有边框，所以需要透明\n\t\t\t\tif(this.customInactiveColor) {\n\t\t\t\t\tstyle.borderColor = 'rgba(0, 0, 0, 0)'\n\t\t\t\t}\n\t\t\t\tstyle.backgroundColor = this.isActive ? this.activeColor : this.inactiveColor\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\tnodeStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 如果自定义非激活颜色，将node圆点的尺寸减少两个像素，让其与外边框距离更大一点\n\t\t\t\tstyle.width = addUnit(this.size - this.space)\n\t\t\t\tstyle.height = addUnit(this.size - this.space)\n\t\t\t\tconst translateX = this.isActive ? addUnit(this.space) : addUnit(this.size);\n\t\t\t\tstyle.transform = `translateX(-${translateX})`\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tbgStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 这里配置一个多余的元素在HTML中，是为了让switch切换时，有更良好的背景色扩充体验(见实际效果)\n\t\t\t\tstyle.width = addUnit(Number(this.size) * 2 - this.size / 2)\n\t\t\t\tstyle.height = addUnit(this.size)\n\t\t\t\tstyle.backgroundColor = this.inactiveColor\n\t\t\t\t// 打开时，让此元素收缩，否则反之\n\t\t\t\tstyle.transform = `scale(${this.isActive ? 0 : 1})`\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tcustomInactiveColor() {\n\t\t\t\t// 之所以需要判断是否自定义了“非激活”颜色，是为了让node圆点离外边框更宽一点的距离\n\t\t\t\treturn this.inactiveColor !== '#fff' && this.inactiveColor !== '#ffffff'\n\t\t\t}\n\t\t},\n\t\t// #ifdef VUE3\n\t\temits: ['update:modelValue', 'change'],\n    \t// #endif\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\tclickHandler() {\n\t\t\t\tif (!this.disabled && !this.loading) {\n\t\t\t\t\tconst oldValue = this.isActive ? this.inactiveValue : this.activeValue\n\t\t\t\t\tif(!this.asyncChange) {\n\t\t\t\t\t\t// #ifdef VUE3\n\t\t\t\t\t\tthis.$emit(\"update:modelValue\", oldValue);\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifdef VUE2\n\t\t\t\t\t\tthis.$emit(\"input\", oldValue);\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t}\n\t\t\t\t\t// 放到下一个生命周期，因为双向绑定的value修改父组件状态需要时间，且是异步的\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$emit('change', oldValue)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-switch {\n\t\t@include flex(row);\n\t\t/* #ifndef APP-NVUE */\n\t\tbox-sizing: border-box;\n\t\t/* #endif */\n\t\tposition: relative;\n\t\tbackground-color: #fff;\n\t\tborder-width: 1px;\n\t\tborder-radius: 100px;\n\t\ttransition: background-color 0.4s;\n\t\tborder-color: rgba(0, 0, 0, 0.12);\n\t\tborder-style: solid;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\t// 由于weex为阿里逗着玩的KPI项目，导致bug奇多，这必须要写这一行，\n\t\t// 否则在iOS上，点击页面任意地方，都会触发switch的点击事件\n\t\toverflow: hidden;\n\n\t\t&__node {\n\t\t\t@include flex(row);\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tborder-radius: 100px;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 100px;\n\t\t\tbox-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.25);\n\t\t\ttransition-property: transform;\n\t\t\ttransition-duration: 0.4s;\n\t\t\ttransition-timing-function: cubic-bezier(0.3, 1.05, 0.4, 1.05);\n\t\t}\n\n\t\t&__bg {\n\t\t\tposition: absolute;\n\t\t\tborder-radius: 100px;\n\t\t\tbackground-color: #FFFFFF;\n\t\t\ttransition-property: transform;\n\t\t\ttransition-duration: 0.4s;\n\t\t\tborder-top-left-radius: 0;\n\t\t\tborder-bottom-left-radius: 0;\n\t\t\ttransition-timing-function: ease;\n\t\t}\n\n\t\t&--disabled {\n\t\t\topacity: 0.6;\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-switch/u-switch.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "watch", "modelValue", "immediate", "handler", "n", "this", "inactiveValue", "activeValue", "data", "bgColor", "computed", "isActive", "switchStyle", "style", "width", "addUnit", "size", "height", "Number", "customInactiveColor", "borderColor", "backgroundColor", "activeColor", "inactiveColor", "nodeStyle", "space", "translateX", "transform", "bgStyle", "emits", "methods", "addStyle", "clickHandler", "disabled", "loading", "oldValue", "asyncChange", "$emit", "$nextTick", "wx", "createComponent", "Component"], "mappings": "6DA+DMA,EAAU,CACdC,KAAM,WACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAACC,YACxBC,MAAO,CAENC,WAAY,CACXC,WAAW,EACX,OAAAC,CAAQC,GACJA,IAAMC,KAAKC,eAAuBD,KAAKE,WAG3C,IAcFC,KAAO,KACC,CACNC,QAAS,YAGXC,SAAU,CACT,QAAAC,GAEQ,OAAAN,KAAKJ,aAAeI,KAAKE,WAKhC,EACD,WAAAK,GACC,IAAIC,EAAQ,CAAC,EAWN,OATPA,EAAMC,MAAQC,EAAOA,QAAa,EAAZV,KAAKW,KAAW,GACtCH,EAAMI,OAASF,UAAQG,OAAOb,KAAKW,MAAQ,GAIxCX,KAAKc,sBACPN,EAAMO,YAAc,oBAErBP,EAAMQ,gBAAkBhB,KAAKM,SAAWN,KAAKiB,YAAcjB,KAAKkB,cACzDV,CACP,EACD,SAAAW,GACC,IAAIX,EAAQ,CAAC,EAEbA,EAAMC,MAAQC,EAAOA,QAACV,KAAKW,KAAOX,KAAKoB,OACvCZ,EAAMI,OAASF,EAAOA,QAACV,KAAKW,KAAOX,KAAKoB,OAClC,MAAAC,EAAarB,KAAKM,SAAWI,UAAQV,KAAKoB,OAASV,EAAOA,QAACV,KAAKW,MAE/D,OADPH,EAAMc,UAAY,eAAeD,KAC1Bb,CACP,EACD,OAAAe,GACC,IAAIf,EAAQ,CAAC,EAON,OALDA,EAAAC,MAAQC,UAA4B,EAApBG,OAAOb,KAAKW,MAAYX,KAAKW,KAAO,GAC1DH,EAAMI,OAASF,UAAQV,KAAKW,MAC5BH,EAAMQ,gBAAkBhB,KAAKkB,cAE7BV,EAAMc,UAAY,SAAStB,KAAKM,SAAW,EAAI,KACxCE,CACP,EACD,mBAAAM,GAEC,MAA8B,SAAvBd,KAAKkB,eAAmD,YAAvBlB,KAAKkB,aAC9C,GAGDM,MAAO,CAAC,oBAAqB,UAE7BC,QAAS,CACRC,SAAAA,EAAQA,SACR,YAAAC,GACC,IAAK3B,KAAK4B,WAAa5B,KAAK6B,QAAS,CACpC,MAAMC,EAAW9B,KAAKM,SAAWN,KAAKC,cAAgBD,KAAKE,YACvDF,KAAK+B,aAEH/B,KAAAgC,MAAM,oBAAqBF,GAOjC9B,KAAKiC,WAAU,KACTjC,KAAAgC,MAAM,SAAUF,EAAQ,GAE/B,CACD,0fChKHI,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}