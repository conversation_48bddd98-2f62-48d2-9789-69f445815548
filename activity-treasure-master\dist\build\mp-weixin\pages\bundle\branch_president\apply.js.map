{"version": 3, "file": "apply.js", "sources": ["../../../../../../src/pages/bundle/branch_president/apply.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGJyYW5jaF9wcmVzaWRlbnRcYXBwbHkudnVl"], "sourcesContent": ["<template>\n  <view class=\"page\">\n    <myTitle\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      title=\"申请成为分会长\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <view class=\"form-container\">\n        <u-form\n          :model=\"formData\"\n          ref=\"formRef\"\n          :rules=\"rules\"\n          labelPosition=\"top\"\n          labelWidth=\"auto\"\n        >\n          <u-form-item label=\"分会名称\" prop=\"branch_name\" required>\n            <u-input\n              v-model=\"formData.branch_name\"\n              placeholder=\"请输入分会名称（2-100字符）\"\n              maxlength=\"100\"\n              :clearable=\"true\"\n            />\n          </u-form-item>\n          \n          <u-form-item label=\"分会地区\" prop=\"branch_location\" required>\n            <u-input\n              v-model=\"formData.branch_location\"\n              placeholder=\"请输入分会所在地区\"\n              maxlength=\"200\"\n              :clearable=\"true\"\n            />\n          </u-form-item>\n\n          <u-form-item label=\"申请人昵称\" prop=\"nickname\" required>\n            <u-input\n              v-model=\"formData.nickname\"\n              placeholder=\"请输入您的昵称（2-20个字符）\"\n              maxlength=\"20\"\n              :clearable=\"true\"\n            />\n          </u-form-item>\n\n          <u-form-item label=\"微信二维码\" prop=\"wechat_qr_image\" required>\n            <view class=\"upload-container\">\n              <view v-if=\"!formData.wechat_qr_image\" class=\"upload-placeholder\" @click=\"chooseWechatQrImage\">\n                <u-icon name=\"camera\" size=\"60rpx\" color=\"#6AC086\"></u-icon>\n                <view class=\"upload-hint\">点击上传</view>\n              </view>\n              <view v-else class=\"upload-preview\" @click=\"previewWechatQrImage\">\n                <image :src=\"formData.wechat_qr_image\" mode=\"aspectFit\" class=\"preview-image\"></image>\n                <view class=\"upload-actions\">\n                  <u-icon name=\"eye\" size=\"40rpx\" color=\"#fff\" @click.stop=\"previewWechatQrImage\"></u-icon>\n                  <u-icon name=\"trash\" size=\"40rpx\" color=\"#fff\" @click.stop=\"removeWechatQrImage\"></u-icon>\n                </view>\n              </view>\n            </view>\n          </u-form-item>\n\n          <u-form-item label=\"分会描述\" prop=\"branch_description\">\n            <u-textarea\n              v-model=\"formData.branch_description\"\n              placeholder=\"请简要描述分会的特色和定位\"\n              maxlength=\"500\"\n              count\n              height=\"120rpx\"\n            />\n          </u-form-item>\n          \n          <u-form-item label=\"申请理由\" prop=\"application_reason\" required>\n            <u-textarea\n              v-model=\"formData.application_reason\"\n              placeholder=\"请详细说明您申请成为分会长的理由（至少50字）\"\n              maxlength=\"500\"\n              count\n              height=\"200rpx\"\n            />\n          </u-form-item>\n        </u-form>\n        \n        <view class=\"submit-container\">\n          <u-button\n            type=\"primary\"\n            :loading=\"submitting\"\n            :disabled=\"submitting\"\n            @click=\"submitApplication\"\n            customStyle=\"background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%); border: none; border-radius: 50rpx; height: 88rpx; font-size: 32rpx;\"\n          >\n            {{ submitting ? '提交中...' : '提交申请' }}\n          </u-button>\n        </view>\n        \n        <view class=\"tips-container\">\n          <view class=\"tips-title\">申请须知：</view>\n          <view class=\"tips-item\">• 申请提交后，管理员将在3个工作日内审核</view>\n          <view class=\"tips-item\">• 审核通过后，您将成为该分会的分会长</view>\n          <view class=\"tips-item\">• 分会长可以审核本分会的活动并获得运营佣金</view>\n          <view class=\"tips-item\">• 请确保分会名称的唯一性和合规性</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive } from \"vue\";\nimport { onLoad } from \"@dcloudio/uni-app\";\nimport { navto } from \"@/utils\";\nimport { requireLogin } from \"@/utils/auth\";\nimport { branch_presidentapply } from \"@/api\";\nimport { store } from \"@/store\";\n\n// 表单数据\nconst formData = reactive({\n  branch_name: '',\n  branch_location: '',\n  nickname: '',\n  wechat_qr_image: '',\n  branch_description: '',\n  application_reason: ''\n});\n\n// 表单验证规则\nconst rules = reactive({\n  branch_name: [\n    {\n      required: true,\n      message: '请输入分会名称',\n      trigger: ['blur', 'change']\n    },\n    {\n      min: 2,\n      max: 100,\n      message: '分会名称长度应在2-100字符之间',\n      trigger: ['blur', 'change']\n    }\n  ],\n  branch_location: [\n    {\n      required: true,\n      message: '请输入分会地区',\n      trigger: ['blur', 'change']\n    },\n    {\n      max: 200,\n      message: '地区名称不能超过200字符',\n      trigger: ['blur', 'change']\n    }\n  ],\n  nickname: [\n    {\n      required: true,\n      message: '请输入申请人昵称',\n      trigger: ['blur', 'change']\n    },\n    {\n      min: 2,\n      max: 20,\n      message: '昵称长度应在2-20个字符之间',\n      trigger: ['blur', 'change']\n    }\n  ],\n  wechat_qr_image: [\n    {\n      required: true,\n      message: '请上传微信二维码',\n      trigger: ['blur', 'change']\n    }\n  ],\n  application_reason: [\n    {\n      required: true,\n      message: '请输入申请理由',\n      trigger: ['blur', 'change']\n    },\n    {\n      min: 50,\n      max: 500,\n      message: '申请理由应在50-500字符之间',\n      trigger: ['blur', 'change']\n    }\n  ]\n});\n\n// 表单引用\nconst formRef = ref();\nconst submitting = ref(false);\n\n// 页面加载\nonLoad(() => {\n  // 检查登录状态\n  if (!requireLogin()) {\n    return;\n  }\n  \n  // 检查用户是否已经是分会长\n  const userInfo = store().$state.userInfo;\n  if (userInfo?.role_type === '1') {\n    uni.showModal({\n      title: '提示',\n      content: '您已经是分会长，无需重复申请',\n      showCancel: false,\n      success: () => {\n        uni.navigateBack();\n      }\n    });\n    return;\n  }\n  \n  // 🔧 修复：移除会员身份验证，根据业务需求不需要会员身份即可申请分会长\n  // 原验证逻辑已移除\n});\n\n// 提交申请\nconst submitApplication = async () => {\n  try {\n    // 表单验证\n    const valid = await formRef.value.validate();\n    if (!valid) {\n      return;\n    }\n    \n    submitting.value = true;\n    \n    const userInfo = store().$state.userInfo;\n    const params = {\n      uid: userInfo.uid,\n      token: userInfo.token,\n      ...formData\n    };\n    \n    const res = await branch_presidentapply(params);\n    \n    if (res.status === 'ok') {\n      uni.showToast({\n        title: '申请提交成功',\n        icon: 'success'\n      });\n      \n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n    } else if (res.status === 'relogin') {\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n      // 跳转到登录页面\n      navto('/pages/bundle/common/login');\n    } else {\n      uni.showToast({\n        title: res.msg || '申请失败',\n        icon: 'none'\n      });\n    }\n  } catch (error) {\n    console.error('申请失败:', error);\n    uni.showToast({\n      title: '网络错误，请稍后重试',\n      icon: 'none'\n    });\n  } finally {\n    submitting.value = false;\n  }\n};\n\n// 选择微信二维码图片\nconst chooseWechatQrImage = () => {\n  uni.chooseImage({\n    count: 1,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      const tempFilePath = res.tempFilePaths[0];\n\n      // 验证图片格式\n      const fileExtension = tempFilePath.split('.').pop().toLowerCase();\n      if (!['jpg', 'jpeg', 'png'].includes(fileExtension)) {\n        uni.showToast({\n          title: '请选择jpg或png格式的图片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 上传图片\n      uploadWechatQrImage(tempFilePath);\n    },\n    fail: (error) => {\n      console.error('选择图片失败:', error);\n      uni.showToast({\n        title: '选择图片失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 上传微信二维码图片\nconst uploadWechatQrImage = (filePath) => {\n  uni.showLoading({\n    title: '上传中...'\n  });\n\n  const userInfo = store().$state.userInfo;\n\n  uni.uploadFile({\n    url: `${store().$state.url}config/upload_img`,\n    filePath: filePath,\n    name: 'img',\n    formData: {\n      uid: userInfo.uid,\n      token: userInfo.token,\n      type: 'wechat_qr'\n    },\n    success: (uploadRes) => {\n      try {\n        const result = JSON.parse(uploadRes.data);\n        if (result.status === 'ok') {\n          // 🔧 修复：根据实际API返回结构获取图片URL\n          formData.wechat_qr_image = result.data || result.url;\n          uni.showToast({\n            title: '上传成功',\n            icon: 'success'\n          });\n        } else {\n          uni.showToast({\n            title: result.msg || '上传失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('解析上传结果失败:', error);\n        console.error('上传响应数据:', uploadRes.data);\n        uni.showToast({\n          title: '上传失败',\n          icon: 'none'\n        });\n      }\n    },\n    fail: (error) => {\n      console.error('上传失败:', error);\n      uni.showToast({\n        title: '上传失败，请重试',\n        icon: 'none'\n      });\n    },\n    complete: () => {\n      uni.hideLoading();\n    }\n  });\n};\n\n// 预览微信二维码图片\nconst previewWechatQrImage = () => {\n  uni.previewImage({\n    urls: [formData.wechat_qr_image],\n    current: formData.wechat_qr_image\n  });\n};\n\n// 移除微信二维码图片\nconst removeWechatQrImage = () => {\n  uni.showModal({\n    title: '确认删除',\n    content: '确定要删除这张微信二维码图片吗？',\n    success: (res) => {\n      if (res.confirm) {\n        formData.wechat_qr_image = '';\n        uni.showToast({\n          title: '已删除',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.content-container {\n  padding: 30rpx;\n}\n\n.form-container {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.submit-container {\n  margin-top: 60rpx;\n  margin-bottom: 40rpx;\n}\n\n.tips-container {\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  border-left: 6rpx solid #6AC086;\n}\n\n.tips-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.tips-item {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.6;\n  margin-bottom: 12rpx;\n}\n\n.tips-item:last-child {\n  margin-bottom: 0;\n}\n\n// uView组件样式覆盖\n:deep(.u-form-item__label) {\n  font-size: 28rpx !important;\n  font-weight: bold !important;\n  color: #333333 !important;\n}\n\n:deep(.u-input__content__field-wrapper__field) {\n  font-size: 28rpx !important;\n}\n\n:deep(.u-textarea__content__field) {\n  font-size: 28rpx !important;\n}\n\n// 🆕 微信二维码上传组件样式\n.upload-container {\n  width: 100%;\n}\n\n.upload-placeholder {\n  width: 200rpx;\n  height: 200rpx;\n  border: 2rpx dashed #6AC086;\n  border-radius: 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(106, 192, 134, 0.05);\n  transition: all 0.3s ease;\n\n  &:active {\n    background: rgba(106, 192, 134, 0.1);\n    border-color: #88D7A0;\n    transform: scale(0.98);\n  }\n}\n\n.upload-hint {\n  font-size: 24rpx;\n  color: #6AC086;\n  margin-top: 10rpx;\n  font-weight: 500;\n}\n\n.upload-preview {\n  position: relative;\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n}\n\n.preview-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 20rpx;\n}\n\n.upload-actions {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 30rpx;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  border-radius: 20rpx;\n}\n\n.upload-preview:active .upload-actions {\n  opacity: 1;\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/branch_president/apply.vue'\nwx.createPage(MiniProgramPage)"], "names": ["formData", "reactive", "branch_name", "branch_location", "nickname", "wechat_qr_image", "branch_description", "application_reason", "rules", "required", "message", "trigger", "min", "max", "formRef", "ref", "submitting", "onLoad", "requireLogin", "userInfo", "store", "$state", "role_type", "uni", "showModal", "title", "content", "showCancel", "success", "index", "navigateBack", "submitApplication", "async", "value", "validate", "params", "uid", "token", "res", "branch_presidentapply", "status", "showToast", "icon", "setTimeout", "navto", "msg", "error", "console", "chooseWechatQrImage", "chooseImage", "count", "sizeType", "sourceType", "tempFile<PERSON>ath", "tempFilePaths", "fileExtension", "split", "pop", "toLowerCase", "includes", "uploadWechatQrImage", "fail", "filePath", "showLoading", "uploadFile", "url", "name", "type", "uploadRes", "result", "JSON", "parse", "data", "complete", "hideLoading", "previewWechatQrImage", "previewImage", "urls", "current", "removeWechatQrImage", "confirm", "wx", "createPage", "MiniProgramPage"], "mappings": "2nCAoHM,MAAAA,EAAWC,EAAAA,SAAS,CACxBC,YAAa,GACbC,gBAAiB,GACjBC,SAAU,GACVC,gBAAiB,GACjBC,mBAAoB,GACpBC,mBAAoB,KAIhBC,EAAQP,EAAAA,SAAS,CACrBC,YAAa,CACX,CACEO,UAAU,EACVC,QAAS,UACTC,QAAS,CAAC,OAAQ,WAEpB,CACEC,IAAK,EACLC,IAAK,IACLH,QAAS,oBACTC,QAAS,CAAC,OAAQ,YAGtBR,gBAAiB,CACf,CACEM,UAAU,EACVC,QAAS,UACTC,QAAS,CAAC,OAAQ,WAEpB,CACEE,IAAK,IACLH,QAAS,gBACTC,QAAS,CAAC,OAAQ,YAGtBP,SAAU,CACR,CACEK,UAAU,EACVC,QAAS,WACTC,QAAS,CAAC,OAAQ,WAEpB,CACEC,IAAK,EACLC,IAAK,GACLH,QAAS,kBACTC,QAAS,CAAC,OAAQ,YAGtBN,gBAAiB,CACf,CACEI,UAAU,EACVC,QAAS,WACTC,QAAS,CAAC,OAAQ,YAGtBJ,mBAAoB,CAClB,CACEE,UAAU,EACVC,QAAS,UACTC,QAAS,CAAC,OAAQ,WAEpB,CACEC,IAAK,GACLC,IAAK,IACLH,QAAS,mBACTC,QAAS,CAAC,OAAQ,cAMlBG,EAAUC,EAAGA,MACbC,EAAaD,EAAAA,KAAI,GAGvBE,EAAAA,QAAO,KAED,IAACC,EAAYA,eACf,OAIF,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SACJ,OAAxB,MAAAA,OAAA,EAAAA,EAAUG,YACZC,EAAAA,MAAIC,UAAU,CACZC,MAAO,KACPC,QAAS,iBACTC,YAAY,EACZC,QAAS,KACPL,EAAGM,MAACC,cAAY,GAItB,IAOF,MAAMC,EAAoBC,UACpB,IAGF,WADoBlB,EAAQmB,MAAMC,YAEhC,OAGFlB,EAAWiB,OAAQ,EAEnB,MAAMd,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BgB,EAAS,CACbC,IAAKjB,EAASiB,IACdC,MAAOlB,EAASkB,SACbrC,GAGCsC,QAAYC,wBAAsBJ,GAErB,OAAfG,EAAIE,QACNjB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,SACPiB,KAAM,YAGRC,YAAW,KACTpB,EAAGM,MAACC,cAAY,GACf,OACqB,YAAfQ,EAAIE,QACbjB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,cACPiB,KAAM,SAGRE,EAAKA,MAAC,+BAENrB,EAAAA,MAAIkB,UAAU,CACZhB,MAAOa,EAAIO,KAAO,OAClBH,KAAM,QAGX,OAAQI,GACCC,QAAAD,MAAM,QAASA,GACvBvB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,aACPiB,KAAM,QAEZ,CAAY,QACR1B,EAAWiB,OAAQ,CACrB,GAIIe,EAAsB,KAC1BzB,EAAAA,MAAI0B,YAAY,CACdC,MAAO,EACPC,SAAU,CAAC,cACXC,WAAY,CAAC,QAAS,UACtBxB,QAAUU,IACF,MAAAe,EAAef,EAAIgB,cAAc,GAGjCC,EAAgBF,EAAaG,MAAM,KAAKC,MAAMC,cAC/C,CAAC,MAAO,OAAQ,OAAOC,SAASJ,GASrCK,EAAoBP,GARlB9B,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,kBACPiB,KAAM,QAMsB,EAElCmB,KAAOf,IACGC,QAAAD,MAAM,UAAWA,GACzBvB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,SACPiB,KAAM,QACP,GAEJ,EAIGkB,EAAuBE,IAC3BvC,EAAAA,MAAIwC,YAAY,CACdtC,MAAO,WAGT,MAAMN,EAAWC,EAAAA,QAAQC,OAAOF,SAEhCI,EAAAA,MAAIyC,WAAW,CACbC,IAAK,GAAG7C,EAAKA,QAAGC,OAAO4C,uBACvBH,WACAI,KAAM,MACNlE,SAAU,CACRoC,IAAKjB,EAASiB,IACdC,MAAOlB,EAASkB,MAChB8B,KAAM,aAERvC,QAAUwC,IACJ,IACF,MAAMC,EAASC,KAAKC,MAAMH,EAAUI,MACd,OAAlBH,EAAO7B,QAEAxC,EAAAK,gBAAkBgE,EAAOG,MAAQH,EAAOJ,IACjD1C,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,OACPiB,KAAM,aAGRnB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO4C,EAAOxB,KAAO,OACrBH,KAAM,QAGX,OAAQI,GACCC,QAAAD,MAAM,YAAaA,GACnBC,QAAAD,MAAM,UAAWsB,EAAUI,MACnCjD,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,OACPiB,KAAM,QAEV,GAEFmB,KAAOf,IACGC,QAAAD,MAAM,QAASA,GACvBvB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,WACPiB,KAAM,QACP,EAEH+B,SAAU,KACRlD,EAAGM,MAAC6C,aAAW,GAElB,EAIGC,EAAuB,KAC3BpD,EAAAA,MAAIqD,aAAa,CACfC,KAAM,CAAC7E,EAASK,iBAChByE,QAAS9E,EAASK,iBACnB,EAIG0E,EAAsB,KAC1BxD,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,QAAS,mBACTE,QAAUU,IACJA,EAAI0C,UACNhF,EAASK,gBAAkB,GAC3BkB,EAAAA,MAAIkB,UAAU,CACZhB,MAAO,MACPiB,KAAM,YAEV,GAEH,mvDCzXHuC,GAAGC,WAAWC"}