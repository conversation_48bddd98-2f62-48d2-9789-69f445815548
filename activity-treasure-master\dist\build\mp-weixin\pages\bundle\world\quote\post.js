"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),l=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u--textarea")+e.resolveComponent("u-upload")+e.resolveComponent("u-icon")+e.resolveComponent("u-radio")+e.resolveComponent("u-radio-group")+e.resolveComponent("u-switch")+e.resolveComponent("u-loading-icon"))()}Math||(t+(()=>"../../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+s+a+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-radio/u-radio.js")+(()=>"../../../../node-modules/uview-plus/components/u-radio-group/u-radio-group.js")+(()=>"../../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const t=()=>"../../../../components/customNavbar.js",s=()=>"../../../../components/AuthorSelector.js",a=()=>"../../../../components/SourceSelector.js",n={__name:"post",setup(t){const s=e.ref(""),a=e.ref(null),n=e.ref(null),u=e.ref(""),i=e.ref("public"),r=e.ref(!1),c=e.ref(!1),d=e.ref([]),p=()=>{e.index.navigateBack()},v=async l=>{let t=[].concat(l.file);d.value.length>0&&(e.index.showToast({title:"摘录只能上传一张图片，将替换现有图片",icon:"none"}),d.value=[]),t.length>1&&(e.index.showToast({title:"摘录只能上传一张图片，已自动选择第一张",icon:"none"}),t=[t[0]]);let s=d.value.length;t.map((e=>{d.value.push({...e,status:"uploading",message:"上传中"})}));for(let n=0;n<t.length;n++){const l=s+n;try{const s=await o.upload_img(t[n].url);if("ok"===s.status&&s.data){let e=d.value[l];e&&d.value.splice(l,1,{...e,status:"success",message:"",url:s.data})}else d.value[l]&&(d.value[l].status="failed",d.value[l].message=s.msg||"上传失败"),e.index.showToast({title:s.msg||"图片上传失败",icon:"none"})}catch(a){d.value[l]&&(d.value[l].status="failed",d.value[l].message="上传失败"),e.index.showToast({title:"图片上传失败，请重试",icon:"none"})}}},m=e=>{d.value.splice(e.index,1)},f=async()=>{var t,p,v,m;if(!s.value.trim())return void e.index.showToast({title:"摘录内容不能为空",icon:"none"});if(!(null==(t=l.store().$state.userInfo)?void 0:t.uid)||!(null==(p=l.store().$state.userInfo)?void 0:p.token))return void e.index.showToast({title:"请先登录",icon:"none"});const f=d.value.filter((e=>"success"===e.status&&e.url)).map((e=>e.url));if(0===f.length)return void e.index.showToast({title:"摘录必须上传一张图片",icon:"none"});if(c.value)return;c.value=!0;const h={uid:l.store().$state.userInfo.uid,token:l.store().$state.userInfo.token,content:s.value.trim(),author_id:(null==(v=a.value)?void 0:v.id)||null,source_id:(null==(m=n.value)?void 0:m.id)||null,tags:u.value.trim(),privacy:i.value,allow_official:r.value?1:0,images:f};try{const l=await o.publishQuote(h);"ok"===l.status?(e.index.showToast({title:"摘录成功",icon:"success"}),e.index.$emit("refreshQuoteList"),setTimeout((()=>{e.index.navigateBack()}),1e3)):"relogin"===l.status?e.index.showToast({title:"请先登录",icon:"none"}):e.index.showToast({title:l.msg||"摘录失败",icon:"none"})}catch(g){e.index.showToast({title:"摘录失败，请重试",icon:"none"})}finally{c.value=!1}};return(o,l)=>e.e({a:e.o(p),b:e.p({title:"摘录",backIcon:"close"}),c:e.o((e=>s.value=e)),d:e.p({placeholder:"记录书摘格言，名家语录",height:"300",maxlength:"-1",border:"none",customStyle:{padding:"32rpx",lineHeight:"1.7",fontSize:"32rpx",color:"#333333",fontFamily:"-apple-system, BlinkMacSystemFont, PingFang SC, Hiragino Sans GB, sans-serif",backgroundColor:"transparent"},modelValue:s.value}),e:e.o(v),f:e.o(m),g:e.p({fileList:d.value,name:"file",multiple:!0,maxCount:1,previewImage:!0,width:"200rpx",height:"200rpx",uploadIconColor:"#ccc"}),h:e.o((e=>a.value=e)),i:e.p({placeholder:"选择作者 (选填)",modelValue:a.value}),j:e.o((e=>n.value=e)),k:e.p({placeholder:"选择出处 (选填)",modelValue:n.value}),l:e.p({name:"tags-fill",size:"20",color:"#999"}),m:u.value,n:e.o((e=>u.value=e.detail.value)),o:e.p({name:"lock-fill",size:"20",color:"#999"}),p:e.p({label:"公开",name:"public",customStyle:{marginRight:"16rpx"}}),q:e.p({label:"私密",name:"private"}),r:e.o((e=>i.value=e)),s:e.p({placement:"row",modelValue:i.value}),t:e.p({name:"checkmark-circle-fill",size:"20",color:"#999"}),v:e.o((e=>r.value=e)),w:e.p({activeColor:"#6AC086",size:"24",modelValue:r.value}),x:!c.value},c.value?{}:{y:e.p({name:"checkmark",size:"44rpx",color:"#ffffff"})},{z:c.value},c.value?{A:e.p({color:"#ffffff",size:"40rpx"})}:{},{B:!c.value},(c.value,{}),{C:c.value||!s.value.trim()?1:"",D:e.o(f)})}},u=e._export_sfc(n,[["__scopeId","data-v-a592b3d3"]]);wx.createPage(u);
//# sourceMappingURL=post.js.map
