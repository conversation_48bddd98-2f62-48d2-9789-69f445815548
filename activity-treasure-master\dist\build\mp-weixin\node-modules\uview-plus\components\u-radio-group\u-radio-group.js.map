{"version": 3, "file": "u-radio-group.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-radio-group/u-radio-group.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXJhZGlvLWdyb3VwL3UtcmFkaW8tZ3JvdXAudnVl"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-radio-group\"\n\t    :class=\"bemClass\"\n\t\t\t:style=\"radioGroupStyle\"\n\t>\n\t\t<slot></slot>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\n\n\t/**\n\t * radioRroup 单选框父组件\n\t * @description 单选框用于有一个选择，用户只能选择其中一个的场景。搭配u-radio使用\n\t * @tutorial https://ijry.github.io/uview-plus/components/radio.html\n\t * @property {String | Number | Boolean}\tvalue \t\t\t绑定的值\n\t * @property {Boolean}\t\t\t\t\t\tdisabled\t\t是否禁用所有radio（默认 false ）\n\t * @property {String}\t\t\t\t\t\tshape\t\t\t外观形状，shape-方形，circle-圆形(默认 circle )\n\t * @property {String}\t\t\t\t\t\tactiveColor\t\t选中时的颜色，应用到所有子Radio组件（默认 '#2979ff' ）\n\t * @property {String}\t\t\t\t\t\tinactiveColor\t未选中的颜色 (默认 '#c8c9cc' )\n\t * @property {String}\t\t\t\t\t\tname\t\t\t标识符\n\t * @property {String | Number}\t\t\t\tsize\t\t\t组件整体的大小，单位px（默认 18 ）\n\t * @property {String}\t\t\t\t\t\tplacement\t\t布局方式，row-横向，column-纵向 （默认 'row' ）\n\t * @property {String}\t\t\t\t\t\tlabel\t\t\t文本\n\t * @property {String}\t\t\t\t\t\tlabelColor\t\tlabel的颜色 （默认 '#303133' ）\n\t * @property {String | Number}\t\t\t\tlabelSize\t\tlabel的字体大小，px单位 （默认 14 ）\n\t * @property {Boolean}\t\t\t\t\t\tlabelDisabled\t是否禁止点击文本操作checkbox(默认 false )\n\t * @property {String}\t\t\t\t\t\ticonColor\t\t图标颜色 （默认 '#ffffff' ）\n\t * @property {String | Number}\t\t\t\ticonSize\t\t图标的大小，单位px （默认 12 ）\n\t * @property {Boolean}\t\t\t\t\t\tborderBottom\tplacement为row时，是否显示下边框 （默认 false ）\n\t * @property {String}\t\t\t\t\t\ticonPlacement\t图标与文字的对齐方式 （默认 'left' ）\n\t * @property {Object}\t\t\t\t\t\tgap\t\t\t\titem 之间的间距\n   * @property {Object}\t\t\t\t\t\tcustomStyle\t\t组件的样式，对象形式\n\t * @event {Function} change 任一个radio状态发生变化时触发\n\t * @example <u-radio-group v-model=\"value\"></u-radio-group>\n\t */\n\texport default {\n\t\tname: 'u-radio-group',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\t// 这里computed的变量，都是子组件u-radio需要用到的，由于头条小程序的兼容性差异，子组件无法实时监听父组件参数的变化\n\t\t\t// 所以需要手动通知子组件，这里返回一个parentData变量，供watch监听，在其中去通知每一个子组件重新从父组件(u-radio-group)\n\t\t\t// 拉取父组件新的变化后的参数\n\t\t\tparentData() {\n\t\t\t\t// #ifdef VUE3\n                return [this.modelValue, this.disabled, this.inactiveColor, this.activeColor, this.size, this.labelDisabled, this.shape,\n\t\t\t\t\tthis.iconSize, this.borderBottom, this.placement\n\t\t\t\t]\n                // #endif\n                // #ifdef VUE2\n                return [this.value, this.disabled, this.inactiveColor, this.activeColor, this.size, this.labelDisabled, this.shape,\n\t\t\t\t\tthis.iconSize, this.borderBottom, this.placement\n\t\t\t\t]\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t},\n\t\t\tbemClass() {\n\t\t\t\t// this.bem为一个computed变量，在mixin中\n\t\t\t\treturn this.bem('radio-group', ['placement'])\n\t\t\t},\n\t\t\tradioGroupStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tgap: addUnit(this.gap)\n\t\t\t\t};\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle));\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件\n\t\t\tparentData() {\n\t\t\t\tif (this.children.length) {\n\t\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t\t// 判断子组件(u-radio)如果有init方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\n\t\t\t\t\t\ttypeof(child.init) === 'function' && child.init()\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.children = []\n\t\t},\n\t\t// #ifdef VUE3\n\t\temits: ['update:modelValue', 'change'],\n    \t// #endif\n\t\tmethods: {\n\t\t\t// 将其他的radio设置为未选中的状态\n\t\t\tunCheckedOther(childInstance) {\n\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t// 所有子radio中，被操作组件实例的checked的值无需修改\n\t\t\t\t\tif (childInstance !== child) {\n\t\t\t\t\t\tchild.checked = false\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tconst {\n\t\t\t\t\tname\n\t\t\t\t} = childInstance\n\t\t\t\t// 通过emit事件，设置父组件通过v-model双向绑定的值\n\t\t\t\t// #ifdef VUE3\n                this.$emit(\"update:modelValue\", name);\n                // #endif\n                // #ifdef VUE2\n                this.$emit(\"input\", name);\n\t\t\t\t// #endif\n\t\t\t\t// 发出事件\n\t\t\t\tthis.$emit('change', name)\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-radio-group {\n\t\tflex: 1;\n\n\t\t&--row {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tdisplay: flex;\n\t\t\t/* #endif */\n\t\t\tflex-flow: row wrap;\n\t\t}\n\n\t\t&--column {\n\t\t\t@include flex(column);\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-radio-group/u-radio-group.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "computed", "parentData", "this", "modelValue", "disabled", "inactiveColor", "activeColor", "size", "labelDisabled", "shape", "iconSize", "borderBottom", "placement", "bemClass", "bem", "radioGroupStyle", "style", "gap", "addUnit", "deepMerge", "addStyle", "customStyle", "watch", "children", "length", "map", "child", "init", "data", "created", "emits", "methods", "unCheckedOther", "childInstance", "checked", "$emit", "wx", "createComponent", "Component"], "mappings": "6DAyCMA,EAAU,CACdC,KAAM,gBACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,SAAU,CAIT,UAAAC,GAEoB,MAAA,CAACC,KAAKC,WAAYD,KAAKE,SAAUF,KAAKG,cAAeH,KAAKI,YAAaJ,KAAKK,KAAML,KAAKM,cAAeN,KAAKO,MAC7HP,KAAKQ,SAAUR,KAAKS,aAAcT,KAAKU,UASxC,EACD,QAAAC,GAEC,OAAOX,KAAKY,IAAI,cAAe,CAAC,aAChC,EACD,eAAAC,GACC,MAAMC,EAAQ,CACbC,IAAKC,EAAAA,QAAQhB,KAAKe,MAEnB,OAAOE,EAAAA,UAAUH,EAAOI,EAAAA,SAASlB,KAAKmB,aACvC,GAEDC,MAAO,CAEN,UAAArB,GACKC,KAAKqB,SAASC,QACZtB,KAAAqB,SAASE,KAAaC,IAEH,mBAAhBA,EAAMC,MAAwBD,EAAMC,MAAK,GAGlD,GAEFC,KAAO,KACC,CACP,GAED,OAAAC,GACC3B,KAAKqB,SAAW,EAChB,EAEDO,MAAO,CAAC,oBAAqB,UAE7BC,QAAS,CAER,cAAAC,CAAeC,GACT/B,KAAAqB,SAASE,KAAaC,IAEtBO,IAAkBP,IACrBA,EAAMQ,SAAU,EACjB,IAEK,MAAAvC,KACLA,GACGsC,EAGa/B,KAAAiC,MAAM,oBAAqBxC,GAMvCO,KAAAiC,MAAM,SAAUxC,EACrB,oJCjHJyC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}