{"version": 3, "file": "detail.js", "sources": ["../../../../../../../src/pages/bundle/world/source/detail.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXHNvdXJjZVxkZXRhaWwudnVl"], "sourcesContent": ["<script setup>\nimport { ref, onMounted } from 'vue';\nimport { getSourceDetail } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 出处详情页面. Confirmed via 寸止 }}\nconst sourceInfo = ref(null);\nconst loading = ref(true);\nconst error = ref('');\n\n// 获取页面参数\nconst pages = getCurrentPages();\nconst currentPageInstance = pages[pages.length - 1];\nconst sourceId = currentPageInstance.options.id;\n\n// 获取出处详情\nconst fetchSourceDetail = async () => {\n  if (!sourceId) {\n    error.value = '出处ID无效';\n    loading.value = false;\n    return;\n  }\n\n  try {\n    loading.value = true;\n    const params = {\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || '',\n      source_id: sourceId\n    };\n\n    const response = await getSourceDetail(params);\n    \n    if (response.status === 'ok') {\n      sourceInfo.value = response.data.source;\n    } else {\n      error.value = response.msg || '获取出处详情失败';\n    }\n  } catch (err) {\n    console.error('获取出处详情失败:', err);\n    error.value = '网络错误，请稍后重试';\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 打开网址\nconst openUrl = (url) => {\n  if (!url) return;\n  \n  // 确保URL有协议前缀\n  let fullUrl = url;\n  if (!url.startsWith('http://') && !url.startsWith('https://')) {\n    fullUrl = 'https://' + url;\n  }\n  \n  uni.showModal({\n    title: '打开链接',\n    content: `是否要打开链接：${fullUrl}`,\n    success: (res) => {\n      if (res.confirm) {\n        // 在小程序中复制链接到剪贴板\n        uni.setClipboardData({\n          data: fullUrl,\n          success: () => {\n            uni.showToast({ title: '链接已复制到剪贴板', icon: 'success' });\n          }\n        });\n      }\n    }\n  });\n};\n\n// 页面加载\nonMounted(() => {\n  fetchSourceDetail();\n});\n</script>\n\n<template>\n  <view class=\"source-detail-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar \n      :title=\"sourceInfo?.name || '出处详情'\" \n      backIcon=\"arrow-left\"\n    />\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <u-loading-icon mode=\"spinner\" color=\"#6AC086\" size=\"40\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"error\" class=\"error-container\">\n      <u-icon name=\"error-circle\" size=\"60\" color=\"#ff4757\"></u-icon>\n      <text class=\"error-text\">{{ error }}</text>\n      <view class=\"retry-btn\" @click=\"fetchSourceDetail\">\n        <text>重试</text>\n      </view>\n    </view>\n    \n    <!-- 出处详情内容 -->\n    <view v-else-if=\"sourceInfo\" class=\"detail-container\">\n      <!-- 出处基本信息 -->\n      <view class=\"source-header\">\n        <view class=\"cover-section\">\n          <image \n            v-if=\"sourceInfo.cover_image\" \n            :src=\"sourceInfo.cover_image\" \n            class=\"source-cover\"\n            mode=\"aspectFill\"\n          />\n          <view v-else class=\"default-cover\">\n            <u-icon name=\"bookmark-fill\" size=\"60\" color=\"#ccc\"></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"info-section\">\n          <view class=\"source-name\">{{ sourceInfo.name }}</view>\n          <view v-if=\"sourceInfo.category\" class=\"source-category\">{{ sourceInfo.category }}</view>\n          <view v-if=\"sourceInfo.publisher\" class=\"source-publisher\">{{ sourceInfo.publisher }}</view>\n          <view v-if=\"sourceInfo.publish_year\" class=\"source-year\">{{ sourceInfo.publish_year }}年</view>\n        </view>\n      </view>\n      \n      <!-- 统计信息 -->\n      <view class=\"stats-section\">\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ sourceInfo.quote_count || 0 }}</view>\n          <view class=\"stat-label\">摘录引用</view>\n        </view>\n      </view>\n      \n      <!-- 出处简介 -->\n      <view v-if=\"sourceInfo.description\" class=\"description-section\">\n        <view class=\"section-title\">简介</view>\n        <view class=\"description-content\">{{ sourceInfo.description }}</view>\n      </view>\n      \n      <!-- 详细信息 -->\n      <view class=\"details-section\">\n        <view class=\"section-title\">详细信息</view>\n        \n        <view v-if=\"sourceInfo.publisher\" class=\"detail-item\">\n          <view class=\"detail-label\">出版社</view>\n          <view class=\"detail-value\">{{ sourceInfo.publisher }}</view>\n        </view>\n        \n        <view v-if=\"sourceInfo.publish_year\" class=\"detail-item\">\n          <view class=\"detail-label\">出版年份</view>\n          <view class=\"detail-value\">{{ sourceInfo.publish_year }}</view>\n        </view>\n        \n        <view v-if=\"sourceInfo.isbn\" class=\"detail-item\">\n          <view class=\"detail-label\">ISBN</view>\n          <view class=\"detail-value\">{{ sourceInfo.isbn }}</view>\n        </view>\n        \n        <view v-if=\"sourceInfo.category\" class=\"detail-item\">\n          <view class=\"detail-label\">类别</view>\n          <view class=\"detail-value\">{{ sourceInfo.category }}</view>\n        </view>\n        \n        <view v-if=\"sourceInfo.url\" class=\"detail-item clickable\" @click=\"openUrl(sourceInfo.url)\">\n          <view class=\"detail-label\">网址</view>\n          <view class=\"detail-value url-value\">{{ sourceInfo.url }}</view>\n          <u-icon name=\"arrow-right\" size=\"16\" color=\"#6AC086\"></u-icon>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.source-detail-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  \n  .loading-container, .error-container {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 120rpx 32rpx;\n    \n    .loading-text, .error-text {\n      margin-top: 24rpx;\n      font-size: 28rpx;\n      color: #666;\n    }\n    \n    .retry-btn {\n      margin-top: 32rpx;\n      padding: 16rpx 32rpx;\n      background-color: #6AC086;\n      border-radius: 50rpx;\n      color: white;\n      font-size: 28rpx;\n    }\n  }\n  \n  .detail-container {\n    padding: 32rpx;\n    \n    .source-header {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 40rpx;\n      margin-bottom: 32rpx;\n      display: flex;\n      align-items: center;\n      \n      .cover-section {\n        margin-right: 32rpx;\n        \n        .source-cover {\n          width: 120rpx;\n          height: 160rpx;\n          border-radius: 12rpx;\n        }\n        \n        .default-cover {\n          width: 120rpx;\n          height: 160rpx;\n          border-radius: 12rpx;\n          background-color: #f5f5f5;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n      }\n      \n      .info-section {\n        flex: 1;\n        \n        .source-name {\n          font-size: 36rpx;\n          font-weight: 600;\n          color: #333;\n          margin-bottom: 12rpx;\n          line-height: 1.4;\n        }\n        \n        .source-category {\n          font-size: 28rpx;\n          color: #6AC086;\n          margin-bottom: 8rpx;\n        }\n        \n        .source-publisher, .source-year {\n          font-size: 26rpx;\n          color: #666;\n          margin-bottom: 6rpx;\n        }\n      }\n    }\n    \n    .stats-section {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 32rpx;\n      display: flex;\n      justify-content: center;\n      \n      .stat-item {\n        text-align: center;\n        \n        .stat-number {\n          font-size: 48rpx;\n          font-weight: 600;\n          color: #6AC086;\n          margin-bottom: 8rpx;\n        }\n        \n        .stat-label {\n          font-size: 26rpx;\n          color: #666;\n        }\n      }\n    }\n    \n    .description-section, .details-section {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 32rpx;\n      \n      .section-title {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 24rpx;\n      }\n      \n      .description-content {\n        font-size: 30rpx;\n        color: #666;\n        line-height: 1.6;\n      }\n      \n      .detail-item {\n        display: flex;\n        align-items: center;\n        padding: 20rpx 0;\n        border-bottom: 1rpx solid #f0f0f0;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        &.clickable {\n          cursor: pointer;\n          \n          &:active {\n            background-color: #f8f9fa;\n          }\n        }\n        \n        .detail-label {\n          width: 160rpx;\n          font-size: 30rpx;\n          color: #666;\n          flex-shrink: 0;\n        }\n        \n        .detail-value {\n          flex: 1;\n          font-size: 30rpx;\n          color: #333;\n          \n          &.url-value {\n            color: #6AC086;\n            text-decoration: underline;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/source/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "sourceInfo", "ref", "loading", "error", "pages", "getCurrentPages", "sourceId", "length", "options", "id", "fetchSourceDetail", "async", "value", "params", "uid", "store", "$state", "userInfo", "token", "source_id", "response", "getSourceDetail", "status", "data", "source", "msg", "err", "console", "onMounted", "url", "fullUrl", "startsWith", "uni", "showModal", "title", "content", "success", "res", "confirm", "setClipboardData", "index", "showToast", "icon", "wx", "createPage", "MiniProgramPage"], "mappings": "mqBAIA,MAAMA,EAAe,IAAW,qEAG1B,MAAAC,EAAaC,EAAAA,IAAI,MACjBC,EAAUD,EAAAA,KAAI,GACdE,EAAQF,EAAAA,IAAI,IAGZG,EAAQC,kBAERC,EADsBF,EAAMA,EAAMG,OAAS,GACZC,QAAQC,GAGvCC,EAAoBC,kBACxB,IAAKL,EAGH,OAFAH,EAAMS,MAAQ,cACdV,EAAQU,OAAQ,GAId,IACFV,EAAQU,OAAQ,EAChB,MAAMC,EAAS,CACbC,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,GACzCC,UAAWb,GAGPc,QAAiBC,kBAAgBR,GAEf,OAApBO,EAASE,OACAtB,EAAAY,MAAQQ,EAASG,KAAKC,OAE3BrB,EAAAS,MAAQQ,EAASK,KAAO,UAEjC,OAAQC,GACCC,QAAAxB,MAAM,YAAauB,GAC3BvB,EAAMS,MAAQ,YAClB,CAAY,QACRV,EAAQU,OAAQ,CAClB,UA+BFgB,EAAAA,WAAU,inCA3BM,CAACC,IACf,IAAKA,EAAK,OAGV,IAAIC,EAAUD,EACTA,EAAIE,WAAW,YAAeF,EAAIE,WAAW,cAChDD,EAAU,WAAaD,GAGzBG,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,QAAS,WAAWL,IACpBM,QAAUC,IACJA,EAAIC,SAENN,EAAAA,MAAIO,iBAAiB,CACnBhB,KAAMO,EACNM,QAAS,KACPJ,EAAGQ,MAACC,UAAU,CAAEP,MAAO,YAAaQ,KAAM,WAAW,GAG3D,GAEH,yGCtEHC,GAAGC,WAAWC"}