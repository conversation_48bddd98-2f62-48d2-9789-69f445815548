<view class="page"><u-tabs wx:if="{{b}}" bindclick="{{a}}" u-i="41f1b930-0" bind:__l="__l" u-p="{{b}}"></u-tabs><view class="px30"><mescroll-uni wx:if="{{i}}" u-s="{{['d']}}" class="list" bindinit="{{e}}" binddown="{{f}}" bindup="{{g}}" bindtopclick="{{h}}" u-i="41f1b930-1" bind:__l="__l" u-p="{{i}}"><view wx:for="{{c}}" wx:for-item="val" wx:key="N" class="mb10 py20 pl30 pr30 b6f r20"><view class="df aic jcsb pb10" bindtap="{{val.e}}"><u-text wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></u-text><u-text wx:if="{{val.d}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{val.d}}"></u-text></view><view wx:if="{{d}}" bindtap="{{val.p}}"><view class="df aic"><view><u-text wx:if="{{val.g}}" u-i="{{val.f}}" bind:__l="__l" u-p="{{val.g}}"></u-text></view><u-text wx:if="{{val.i}}" u-i="{{val.h}}" bind:__l="__l" u-p="{{val.i}}"></u-text></view><view class="df aic"><u-text wx:if="{{val.k}}" u-i="{{val.j}}" bind:__l="__l" u-p="{{val.k}}"></u-text><view class="df aic"><u-text wx:if="{{val.m}}" u-i="{{val.l}}" bind:__l="__l" u-p="{{val.m}}"></u-text><u-text wx:if="{{val.o}}" u-i="{{val.n}}" bind:__l="__l" u-p="{{val.o}}"></u-text></view></view></view><block wx:else><view wx:for="{{val.q}}" wx:for-item="value" wx:key="l" class="df pb10 mb10 borderBottom" bindtap="{{value.k}}"><u-image wx:if="{{value.b}}" u-i="{{value.a}}" bind:__l="__l" u-p="{{value.b}}"></u-image><view class="ml20 df fdc jcsb f1"><u-text wx:if="{{value.d}}" u-i="{{value.c}}" bind:__l="__l" u-p="{{value.d}}"></u-text><view class="df aic jcsb"><view class="df aic"><view><u-text wx:if="{{val.r}}" u-i="{{value.e}}" bind:__l="__l" u-p="{{val.r}}"></u-text></view><view wx:for="{{value.f}}" wx:for-item="item" wx:key="c"><u-text wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></u-text></view></view><u-text wx:if="{{value.h}}" u-i="{{value.g}}" bind:__l="__l" u-p="{{value.h}}"></u-text></view><u-text wx:if="{{value.j}}" u-i="{{value.i}}" bind:__l="__l" u-p="{{value.j}}"></u-text></view></view></block><view class="df aic jcr"><u-button wx:if="{{val.s}}" catchclick="{{val.t}}" u-i="{{val.v}}" bind:__l="__l" u-p="{{val.w}}"></u-button><u-button wx:if="{{val.x}}" catchclick="{{val.y}}" u-i="{{val.z}}" bind:__l="__l" u-p="{{val.A}}"></u-button><u-button wx:if="{{val.B}}" catchclick="{{val.C}}" u-i="{{val.D}}" bind:__l="__l" u-p="{{val.E}}"></u-button><u-button wx:if="{{val.F}}" catchclick="{{val.G}}" u-i="{{val.H}}" bind:__l="__l" u-p="{{val.I}}"></u-button><u-button wx:if="{{val.J}}" catchclick="{{val.K}}" u-i="{{val.L}}" bind:__l="__l" u-p="{{val.M}}"></u-button></view></view></mescroll-uni></view><u-action-sheet wx:if="{{l}}" bindclose="{{j}}" bindselect="{{k}}" u-i="41f1b930-20" bind:__l="__l" u-p="{{l}}"></u-action-sheet></view>