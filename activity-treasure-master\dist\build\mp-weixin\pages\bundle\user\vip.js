"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),n=require("../../../store/index.js"),t=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-icon")+e.resolveComponent("u-checkbox")+e.resolveComponent("u-checkbox-group")+e.resolveComponent("u-button")+e.resolveComponent("u-parse"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-checkbox/u-checkbox.js")+(()=>"../../../node-modules/uview-plus/components/u-checkbox-group/u-checkbox-group.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-parse/u-parse.js")+i)();const i=()=>"../../../components/iptInfo.js",s={__name:"vip",setup(i){var s,u;const a=e.ref(""),r=e.ref(!1),l=e.ref(""),d=e.ref("");e.ref(!1);const c=e.ref(!1),p=e.ref(!0);e.ref([{name:"微信支付",color:"#ffaa7f",fontSize:"18"}]);const v=e.ref({type:3,order_id:null,money:null==(u=null==(s=n.store().$state.config.config)?void 0:s.huiyuan_price)?void 0:u.val});e.onShareTimeline((async()=>{var e,t,i,s,u,a;return await o.userfenxiang_event({type:1,item_id:null==(e=n.store().$state.userInfo)?void 0:e.uid}),{title:null==(i=null==(t=n.store().$state.config.config)?void 0:t.app_name)?void 0:i.val,imageUrl:null==(u=null==(s=n.store().$state.config.img_config)?void 0:s.app_logo)?void 0:u.val,path:`/pages/bundle/user/vip?pid=${null==(a=n.store().$state.userInfo)?void 0:a.uid}`}})),e.onShareAppMessage((async()=>{var e,t,i,s,u,a;return await o.userfenxiang_event({type:1,item_id:null==(e=n.store().$state.userInfo)?void 0:e.uid}),{title:null==(i=null==(t=n.store().$state.config.config)?void 0:t.app_name)?void 0:i.val,imageUrl:null==(u=null==(s=n.store().$state.config.img_config)?void 0:s.app_logo)?void 0:u.val,path:`/pages/bundle/user/vip?pid=${null==(a=n.store().$state.userInfo)?void 0:a.uid}`}})),e.onLoad((async t=>{var i,s,u,r;(null==t?void 0:t.pid)&&n.store().changePid(t.pid);try{const e=await o.htmlindex({type:7});a.value="ok"===e.status?e.data.contents:"<p>会员权益加载失败，请稍后重试。</p>",d.value=(null==(s=null==(i=n.store().$state.config.config)?void 0:i.huiyuan_original_price)?void 0:s.val)||"",(null==(u=n.store().$state.userInfo)?void 0:u.is_huiyuan)&&(null==(r=n.store().$state.userInfo)?void 0:r.huiyuan_end_time)&&(l.value=n.store().$state.userInfo.huiyuan_end_time)}catch(c){console.error("加载页面数据失败:",c),e.index.$u.toast("加载信息失败，请稍后重试"),a.value="<p>会员权益加载失败，请稍后重试。</p>"}}));const f=async()=>{if(r.value){if(!n.store().$state.userInfo.is_huiyuan){e.index.showLoading({title:"处理中...",mask:!0});try{console.log("开始创建会员订单...");const n=await o.useradd_huiyuan_order();if(console.log("会员订单创建结果:",n),"ok"===n.status){v.value.order_id=n.order_id,v.value.money=n.money,v.value.type=3,console.log("准备调用微信支付，参数:",JSON.stringify(v.value));const s=await o.payweixin_pay(v.value);if(console.log("微信支付下单结果:",s),"ok"===s.status){console.log("获取到prepay_id:",s.prepay_id);try{const n=await o.payget_weixinpay_sign({prepay_id:s.prepay_id});console.log("获取支付签名结果:",n);"requestPayment:ok"===(await t.pay(n)).errMsg?(e.index.$u.toast("支付成功！"),await t.getUserInfo(),setTimeout((()=>{e.index.navigateBack()}),1500)):e.index.$u.toast("支付失败，请稍后重试"),e.index.hideLoading()}catch(i){e.index.hideLoading(),console.error("获取支付签名异常:",i),e.index.$u.toast("获取支付参数失败，请稍后重试")}}else e.index.hideLoading(),console.error("创建微信支付订单失败:",s),e.index.$u.toast(s.msg||"创建微信支付订单失败")}else e.index.hideLoading(),console.error("创建会员订单失败:",n),e.index.$u.toast(n.msg||"创建会员订单失败")}catch(s){e.index.hideLoading(),console.error("支付处理异常:",s),e.index.$u.toast("支付处理异常，请稍后重试")}}}else e.index.showToast({title:"请先阅读并同意会员服务协议",icon:"none"})},g=()=>{e.index.navigateTo({url:"/pages/bundle/user/membershipAgreement"})};return(o,t)=>{var i,s,u,v;return e.e({a:e.p({bgColor:"rgba(255, 255, 255, 1)",height:"176rpx",title:"我的会员",color:"#333",backColor:"#333"}),b:null==(i=e.unref(n.store)().$state.userInfo)?void 0:i.is_huiyuan},(null==(s=e.unref(n.store)().$state.userInfo)?void 0:s.is_huiyuan)?e.e({c:e.p({name:"checkmark-circle-fill",color:"#ffffff",size:"40rpx"}),d:l.value},l.value?{e:e.t(l.value)}:{}):e.e({f:d.value},d.value?{g:e.t(d.value)}:{},{h:e.t((null==(v=null==(u=e.unref(n.store)().$state.config.config)?void 0:u.huiyuan_price)?void 0:v.val)||"N/A")}),{i:e.o((e=>r.value=!r.value)),j:e.p({name:"agree",shape:"circle",checked:r.value}),k:e.o((e=>r.value=e)),l:e.p({modelValue:r.value}),m:e.o(g),n:e.o(f),o:e.p({shape:"circle",text:e.unref(n.store)().$state.userInfo.is_huiyuan?"您已是会员":"立即开通",disabled:e.unref(n.store)().$state.userInfo.is_huiyuan}),p:e.p({content:a.value}),q:e.o(o.submit),r:e.o((e=>c.value=e)),s:e.p({ipt:p.value,popupShow:c.value})})}}},u=e._export_sfc(s,[["__scopeId","data-v-6f00dee8"]]);s.__runtimeHooks=7,wx.createPage(u);
//# sourceMappingURL=vip.js.map
