<view class="diary-post-container data-v-999cefb4"><custom-navbar wx:if="{{b}}" class="data-v-999cefb4" bindback="{{a}}" u-i="999cefb4-0" bind:__l="__l" u-p="{{b}}"/><scroll-view scroll-y class="content-area data-v-999cefb4"><view class="content-section data-v-999cefb4"><block wx:if="{{r0}}"><textarea class="content-textarea data-v-999cefb4" placeholder="记录你的一天" auto-height maxlength="{{2000}}" show-confirm-bar value="{{c}}" bindinput="{{d}}"></textarea></block><view class="char-count data-v-999cefb4"><text class="count-text data-v-999cefb4">{{e}}/500</text></view></view><view class="image-section data-v-999cefb4"><u-upload wx:if="{{h}}" class="data-v-999cefb4" bindafterRead="{{f}}" binddelete="{{g}}" u-i="999cefb4-1" bind:__l="__l" u-p="{{h}}"></u-upload><view wx:if="{{i}}" class="upload-tip data-v-999cefb4"><text class="tip-text data-v-999cefb4">非会员最多上传1张图片，升级会员可上传4张</text></view></view><view class="location-section data-v-999cefb4"><view class="section-header data-v-999cefb4"><u-icon wx:if="{{j}}" class="data-v-999cefb4" u-i="999cefb4-2" bind:__l="__l" u-p="{{j}}"></u-icon><text class="section-title data-v-999cefb4">位置</text></view><view wx:if="{{k}}" class="location-info data-v-999cefb4"><view class="location-content data-v-999cefb4"><text class="location-name data-v-999cefb4">{{l}}</text><text class="location-address data-v-999cefb4">{{m}}</text></view><u-icon wx:if="{{o}}" class="data-v-999cefb4" bindclick="{{n}}" u-i="999cefb4-3" bind:__l="__l" u-p="{{o}}"></u-icon></view><view wx:else class="location-empty data-v-999cefb4" bindtap="{{q}}"><text class="location-placeholder data-v-999cefb4">添加位置</text><u-icon wx:if="{{p}}" class="data-v-999cefb4" u-i="999cefb4-4" bind:__l="__l" u-p="{{p}}"></u-icon></view></view><view class="privacy-section data-v-999cefb4"><view class="privacy-item data-v-999cefb4"><text class="privacy-label data-v-999cefb4">设为私密</text><u-switch wx:if="{{s}}" class="data-v-999cefb4" u-i="999cefb4-5" bind:__l="__l" bindupdateModelValue="{{r}}" u-p="{{s}}"></u-switch></view></view></scroll-view><view class="publish-button-container data-v-999cefb4"><view class="{{['publish-btn', 'data-v-999cefb4', z && 'disabled']}}" bindtap="{{A}}"><u-icon wx:if="{{t}}" class="data-v-999cefb4" u-i="999cefb4-6" bind:__l="__l" u-p="{{v}}"></u-icon><u-loading-icon wx:if="{{w}}" class="data-v-999cefb4" u-i="999cefb4-7" bind:__l="__l" u-p="{{x}}"></u-loading-icon><text wx:if="{{y}}" class="publish-text data-v-999cefb4">发布</text></view></view></view>