{"version": 3, "file": "u-link.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-link/u-link.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWxpbmsvdS1saW5rLnZ1ZQ"], "sourcesContent": ["<template>\n\t<text\n\t    class=\"u-link\"\n\t    @tap.stop=\"openLink\"\n\t    :style=\"[linkStyle, addStyle(customStyle)]\"\n\t>{{text}}</text>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, addUnit, getPx, toast } from '../../libs/function/index';\n\t/**\n\t * link 超链接\n\t * @description 该组件为超链接组件，在不同平台有不同表现形式：在APP平台会通过plus环境打开内置浏览器，在小程序中把链接复制到粘贴板，同时提示信息，在H5中通过window.open打开链接。\n\t * @tutorial https://ijry.github.io/uview-plus/components/link.html\n\t * @property {String}\t\t\tcolor\t\t文字颜色 （默认 color['u-primary'] ）\n\t * @property {String ｜ Number}\tfontSize\t字体大小，单位px （默认 15 ）\n\t * @property {Boolean}\t\t\tunderLine\t是否显示下划线 （默认 false ）\n\t * @property {String}\t\t\thref\t\t跳转的链接，要带上http(s)\n\t * @property {String}\t\t\tmpTips\t\t各个小程序平台把链接复制到粘贴板后的提示语（默认“链接已复制，请在浏览器打开”）\n\t * @property {String}\t\t\tlineColor\t下划线颜色，默认同color参数颜色 \n\t * @property {String}\t\t\ttext\t\t超链接的问题，不使用slot形式传入，是因为nvue下无法修改颜色 \n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * \n\t * @example <u-link href=\"http://www.uviewui.com\">蜀道难，难于上青天</u-link>\n\t */\n\texport default {\n\t\tname: \"u-link\",\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tlinkStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tcolor: this.color,\n\t\t\t\t\tfontSize: addUnit(this.fontSize),\n\t\t\t\t\t// line-height设置为比字体大小多2px\n\t\t\t\t\tlineHeight: addUnit(getPx(this.fontSize) + 2),\n\t\t\t\t\ttextDecoration: this.underLine ? 'underline' : 'none'\n\t\t\t\t}\n\t\t\t\t// if (this.underLine) {\n\t\t\t\t// \tstyle.borderBottomColor = this.lineColor || this.color\n\t\t\t\t// \tstyle.borderBottomWidth = '1px'\n\t\t\t\t// }\n\t\t\t\treturn style\n\t\t\t}\n\t\t},\n\t\temits: [\"click\"],\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\topenLink() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tplus.runtime.openURL(this.href)\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\twindow.open(this.href)\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: this.href,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.hideToast();\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\ttoast(this.mpTips);\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\tthis.$emit('click')\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\t$u-link-line-height:1 !default;\n\n\t.u-link {\n\t\t/* #ifndef APP-NVUE */\n\t\tline-height: $u-link-line-height;\n\t\t/* #endif */\n\t\t@include flex;\n\t\tflex-wrap: wrap;\n\t\tflex: 1;\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-link/u-link.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "computed", "linkStyle", "color", "this", "fontSize", "addUnit", "lineHeight", "getPx", "textDecoration", "underLine", "emits", "methods", "addStyle", "openLink", "uni", "setClipboardData", "data", "href", "success", "index", "hideToast", "$nextTick", "mpTips", "$emit", "wx", "createComponent", "Component"], "mappings": "6DA4BMA,EAAU,CACdC,KAAM,SACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,SAAU,CACT,SAAAC,GAYQ,MAXO,CACbC,MAAOC,KAAKD,MACZE,SAAUC,EAAAA,QAAQF,KAAKC,UAEvBE,WAAYD,EAAOA,QAACE,EAAKA,MAACJ,KAAKC,UAAY,GAC3CI,eAAgBL,KAAKM,UAAY,YAAc,OAOjD,GAEDC,MAAO,CAAC,SACRC,QAAS,CACRC,SAAAA,EAAQA,SACR,QAAAC,GAQCC,EAAAA,MAAIC,iBAAiB,CACpBC,KAAMb,KAAKc,KACXC,QAAS,KACRJ,EAAGK,MAACC,YACJjB,KAAKkB,WAAU,aACRlB,KAAKmB,OAAM,GACjB,IAIHnB,KAAKoB,MAAM,QACZ,yNCpEHC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}