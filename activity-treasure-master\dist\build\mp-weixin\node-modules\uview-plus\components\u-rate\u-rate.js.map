{"version": 3, "file": "u-rate.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-rate/u-rate.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXJhdGUvdS1yYXRlLnZ1ZQ"], "sourcesContent": ["<template>\n    <view\n        class=\"u-rate\"\n        :id=\"elId\"\n        ref=\"u-rate\"\n        :style=\"[addStyle(customStyle)]\"\n    >\n        <view\n            class=\"u-rate__content\"\n            @touchmove.stop=\"touchMove\"\n            @touchend.stop=\"touchEnd\"\n        >\n            <view\n                class=\"u-rate__content__item cursor-pointer\"\n                v-for=\"(item, index) in Number(count)\"\n                :key=\"index\"\n                :class=\"[elClass]\"\n            >\n                <view\n                    class=\"u-rate__content__item__icon-wrap\"\n                    ref=\"u-rate__content__item__icon-wrap\"\n                    @tap.stop=\"clickHandler($event, index + 1)\"\n                >\n                    <u-icon\n                        :name=\"\n                            Math.floor(activeIndex) > index\n                                ? activeIcon\n                                : inactiveIcon\n                        \"\n                        :color=\"\n                            disabled\n                                ? '#c8c9cc'\n                                : Math.floor(activeIndex) > index\n                                ? activeColor\n                                : inactiveColor\n                        \"\n                        :custom-style=\"{\n                            padding: `0 ${addUnit(gutter / 2)}`,\n                        }\"\n                        :size=\"size\"\n                    ></u-icon>\n                </view>\n                <view\n                    v-if=\"allowHalf\"\n                    @tap.stop=\"clickHandler($event, index + 1)\"\n                    class=\"u-rate__content__item__icon-wrap u-rate__content__item__icon-wrap--half\"\n                    :style=\"[{\n                        width: addUnit(rateWidth / 2),\n                    }]\"\n                    ref=\"u-rate__content__item__icon-wrap\"\n                >\n                    <u-icon\n                        :name=\"\n                            Math.ceil(activeIndex) > index\n                                ? activeIcon\n                                : inactiveIcon\n                        \"\n                        :color=\"\n                            disabled\n                                ? '#c8c9cc'\n                                : Math.ceil(activeIndex) > index\n                                ? activeColor\n                                : inactiveColor\n                        \"\n                        :custom-style=\"{\n                            padding: `0 ${addUnit(gutter / 2)}`\n                        }\"\n                        :size=\"size\"\n                    ></u-icon>\n                </view>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, guid, sleep, range, os } from '../../libs/function/index';\n\t// #ifdef APP-NVUE\n\tconst dom = weex.requireModule(\"dom\");\n\t// #endif\n\t/**\n\t * rate 评分\n\t * @description 该组件一般用于满意度调查，星型评分的场景\n\t * @tutorial https://ijry.github.io/uview-plus/components/rate.html\n\t * @property {String | Number}\tvalue\t\t\t用于v-model双向绑定选中的星星数量 (默认 1 )\n\t * @property {String | Number}\tcount\t\t\t最多可选的星星数量 （默认 5 ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁止用户操作 （默认 false ）\n\t * @property {Boolean}\t\t\treadonly\t\t是否只读 （默认 false ）\n\t * @property {String | Number}\tsize\t\t\t星星的大小，单位px （默认 18 ）\n\t * @property {String}\t\t\tinactiveColor\t未选中星星的颜色 （默认 '#b2b2b2' ）\n\t * @property {String}\t\t\tactiveColor\t\t选中的星星颜色 （默认 '#FA3534' ）\n\t * @property {String | Number}\tgutter\t\t\t星星之间的距离 （默认 4 ）\n\t * @property {String | Number}\tminCount\t\t最少选中星星的个数 （默认 1 ）\n\t * @property {Boolean}\t\t\tallowHalf\t\t是否允许半星选择 （默认 false ）\n\t * @property {String}\t\t\tactiveIcon\t\t选中时的图标名，只能为uView的内置图标 （默认 'star-fill' ）\n\t * @property {String}\t\t\tinactiveIcon\t未选中时的图标名，只能为uView的内置图标 （默认 'star' ）\n\t * @property {Boolean}\t\t\ttouchable\t\t是否可以通过滑动手势选择评分 （默认 'true' ）\n\t * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\n\t * @event {Function} change 选中的星星发生变化时触发\n\t * @example <u-rate :count=\"count\" :value=\"2\"></u-rate>\n\t */\n\texport default {\n\t\tname: \"u-rate\",\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 生成一个唯一id，否则一个页面多个评分组件，会造成冲突\n\t\t\t\telId: guid(),\n\t\t\t\telClass: guid(),\n\t\t\t\trateBoxLeft: 0, // 评分盒子左边到屏幕左边的距离，用于滑动选择时计算距离\n\t\t\t\t// #ifdef VUE3\n\t\t\t\tactiveIndex: this.modelValue,\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef VUE2\n\t\t\t\tactiveIndex: this.value,\n\t\t\t\t// #endif\n\t\t\t\trateWidth: 0, // 每个星星的宽度\n\t\t\t\t// 标识是否正在滑动，由于iOS事件上touch比click先触发，导致快速滑动结束后，接着触发click，导致事件混乱而出错\n\t\t\t\tmoving: false,\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\t// #ifdef VUE3\n\t\t\tmodelValue(val) {\n\t\t\t\tthis.activeIndex = val;\n\t\t\t},\n\t\t\t// #endif\n        \t// #ifdef VUE2\n\t\t\tvalue(val) {\n\t\t\t\tthis.activeIndex = val;\n\t\t\t},\n\t\t\t// #endif\n\t\t\tactiveIndex: 'emitEvent'\n\t\t},\n\t\t// #ifdef VUE3\n\t\temits: ['update:modelValue', 'change'],\n    \t// #endif\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tinit() {\n\t\t\t\tsleep().then(() => {\n\t\t\t\t\tthis.getRateItemRect();\n\t\t\t\t\tthis.getRateIconWrapRect();\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取评分组件盒子的布局信息\n\t\t\tasync getRateItemRect() {\n\t\t\t\tawait sleep();\n\t\t\t\t// uView封装的获取节点的方法，详见文档\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$uGetRect(\"#\" + this.elId).then((res) => {\n\t\t\t\t\tthis.rateBoxLeft = res.left;\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(this.$refs[\"u-rate\"], (res) => {\n\t\t\t\t\tthis.rateBoxLeft = res.size.left;\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 获取单个星星的尺寸\n\t\t\tgetRateIconWrapRect() {\n\t\t\t\t// uView封装的获取节点的方法，详见文档\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$uGetRect(\".\" + this.elClass).then((res) => {\n\t\t\t\t\tthis.rateWidth = res.width;\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(\n\t\t\t\t\tthis.$refs[\"u-rate__content__item__icon-wrap\"][0],\n\t\t\t\t\t(res) => {\n\t\t\t\t\t\tthis.rateWidth = res.size.width;\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 手指滑动\n\t\t\ttouchMove(e) {\n\t\t\t\t// 如果禁止通过手动滑动选择，返回\n\t\t\t\tif (!this.touchable) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.preventEvent(e);\n\t\t\t\tconst x = e.changedTouches[0].pageX;\n\t\t\t\tthis.getActiveIndex(x);\n\t\t\t},\n\t\t\t// 停止滑动\n\t\t\ttouchEnd(e) {\n\t\t\t\t// 如果禁止通过手动滑动选择，返回\n\t\t\t\tif (!this.touchable) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.preventEvent(e);\n\t\t\t\tconst x = e.changedTouches[0].pageX;\n\t\t\t\tthis.getActiveIndex(x);\n\t\t\t},\n\t\t\t// 通过点击，直接选中\n\t\t\tclickHandler(e, index) {\n\t\t\t\t// ios上，moving状态取消事件触发\n\t\t\t\tif (os() === \"ios\" && this.moving) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.preventEvent(e);\n\t\t\t\tlet x = 0;\n\t\t\t\t// 点击时，在nvue上，无法获得点击的坐标，所以无法实现点击半星选择\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tx = e.changedTouches[0].pageX;\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// nvue下，无法通过点击获得坐标信息，这里通过元素的位置尺寸值模拟坐标\n\t\t\t\tx = index * this.rateWidth + this.rateBoxLeft;\n\t\t\t\t// #endif\n\t\t\t\tthis.getActiveIndex(x,true);\n\t\t\t},\n\t\t\t// 发出事件\n\t\t\temitEvent() {\n\t\t\t\t// 发出change事件\n\t\t\t\tthis.$emit(\"change\", this.activeIndex);\n\t\t\t\t// 同时修改双向绑定的值\n\t\t\t\t// #ifdef VUE3\n                this.$emit(\"update:modelValue\", this.activeIndex);\n                // #endif\n                // #ifdef VUE2\n\t\t\t\tthis.$emit(\"input\", this.activeIndex);\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 获取当前激活的评分图标\n\t\t\tgetActiveIndex(x,isClick = false) {\n\t\t\t\tif (this.disabled || this.readonly) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 判断当前操作的点的x坐标值，是否在允许的边界范围内\n\t\t\t\tconst allRateWidth = this.rateWidth * this.count + this.rateBoxLeft;\n\t\t\t\t// 如果小于第一个图标的左边界，设置为最小值，如果大于所有图标的宽度，则设置为最大值\n\t\t\t\tx = range(this.rateBoxLeft, allRateWidth, x) - this.rateBoxLeft\n\t\t\t\t// 滑动点相对于评分盒子左边的距离\n\t\t\t\tconst distance = x;\n\t\t\t\t// 滑动的距离，相当于多少颗星星\n\t\t\t\tlet index;\n\t\t\t\t// 判断是否允许半星\n\t\t\t\tif (this.allowHalf) {\n\t\t\t\t\tindex = Math.floor(distance / this.rateWidth);\n\t\t\t\t\t// 取余，判断小数的区间范围\n\t\t\t\t\tconst decimal = distance % this.rateWidth;\n\t\t\t\t\tif (decimal <= this.rateWidth / 2 && decimal > 0) {\n\t\t\t\t\t\tindex += 0.5;\n\t\t\t\t\t} else if (decimal > this.rateWidth / 2) {\n\t\t\t\t\t\tindex++;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tindex = Math.floor(distance / this.rateWidth);\n\t\t\t\t\t// 取余，判断小数的区间范围\n\t\t\t\t\tconst decimal = distance % this.rateWidth;\n\t\t\t\t\t// 非半星时，只有超过了图标的一半距离，才认为是选择了这颗星\n\t\t\t\t\tif (isClick){\n\t\t\t\t\t\tif (decimal > 0) index++;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (decimal > this.rateWidth / 2) index++;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t\tthis.activeIndex = Math.min(index, this.count);\n\t\t\t\t// 对最少颗星星的限制\n\t\t\t\tif (this.activeIndex < this.minCount) {\n\t\t\t\t\tthis.activeIndex = this.minCount;\n\t\t\t\t}\n\n\t\t\t\t// 设置延时为了让click事件在touchmove之前触发\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.moving = true;\n\t\t\t\t}, 10);\n\t\t\t\t// 一定时间后，取消标识为移动中状态，是为了让click事件无效\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.moving = false;\n\t\t\t\t}, 10);\n\t\t\t},\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init();\n\t\t},\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n$u-rate-margin: 0 !default;\n$u-rate-padding: 0 !default;\n$u-rate-item-icon-wrap-half-top: 0 !default;\n$u-rate-item-icon-wrap-half-left: 0 !default;\n\n.u-rate {\n    @include flex;\n    align-items: center;\n    margin: $u-rate-margin;\n    padding: $u-rate-padding;\n    /* #ifndef APP-NVUE */\n    touch-action: none;\n    /* #endif */\n\n    &__content {\n        @include flex;\n\n\t\t&__item {\n\t\t    position: relative;\n\n\t\t    &__icon-wrap {\n\t\t        &--half {\n\t\t            position: absolute;\n\t\t            overflow: hidden;\n\t\t            top: $u-rate-item-icon-wrap-half-top;\n\t\t            left: $u-rate-item-icon-wrap-half-left;\n\t\t        }\n\t\t    }\n\t\t}\n    }\n}\n\n.u-icon {\n    /* #ifndef APP-NVUE */\n    box-sizing: border-box;\n    /* #endif */\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-rate/u-rate.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "elId", "guid", "elClass", "rateBoxLeft", "activeIndex", "this", "modelValue", "rateWidth", "moving", "watch", "val", "emits", "methods", "addStyle", "addUnit", "init", "common_vendor", "sleep", "then", "getRateItemRect", "getRateIconWrapRect", "$uGetRect", "res", "left", "width", "touchMove", "e", "touchable", "preventEvent", "x", "changedTouches", "pageX", "getActiveIndex", "touchEnd", "clickHandler", "index", "os", "emitEvent", "$emit", "isClick", "disabled", "readonly", "allRateWidth", "count", "distance", "range", "allowHalf", "Math", "floor", "decimal", "min", "minCount", "setTimeout", "mounted", "wx", "createComponent", "Component"], "mappings": "6DAwGMA,EAAU,CACdC,KAAM,SACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzB,IAAAC,GACQ,MAAA,CAENC,KAAMC,EAAAA,OACNC,QAASD,EAAAA,OACTE,YAAa,EAEbC,YAAaC,KAAKC,WAKlBC,UAAW,EAEXC,QAAQ,EAET,EACDC,MAAO,CAEN,UAAAH,CAAWI,GACVL,KAAKD,YAAcM,CACnB,EAODN,YAAa,aAGdO,MAAO,CAAC,oBAAqB,UAE7BC,QAAS,CACRC,SAAAA,EAAQA,SACRC,QAAAA,EAAOA,QACP,IAAAC,GACMC,EAAAC,QAAGC,MAAK,KACZb,KAAKc,kBACLd,KAAKe,qBAAmB,GAEzB,EAED,qBAAMD,SACCF,EAAKA,QAGXZ,KAAKgB,UAAU,IAAMhB,KAAKL,MAAMkB,MAAMI,IACrCjB,KAAKF,YAAcmB,EAAIC,IAAA,GAQxB,EAED,mBAAAH,GAGCf,KAAKgB,UAAU,IAAMhB,KAAKH,SAASgB,MAAMI,IACxCjB,KAAKE,UAAYe,EAAIE,KAAA,GAWtB,EAED,SAAAC,CAAUC,GAEL,IAACrB,KAAKsB,UACT,OAEDtB,KAAKuB,aAAaF,GAClB,MAAMG,EAAIH,EAAEI,eAAe,GAAGC,MAC9B1B,KAAK2B,eAAeH,EACpB,EAED,QAAAI,CAASP,GAEJ,IAACrB,KAAKsB,UACT,OAEDtB,KAAKuB,aAAaF,GAClB,MAAMG,EAAIH,EAAEI,eAAe,GAAGC,MAC9B1B,KAAK2B,eAAeH,EACpB,EAED,YAAAK,CAAaR,EAAGS,GAEf,GAAa,QAATC,QAAkB/B,KAAKG,OAC1B,OAEDH,KAAKuB,aAAaF,GAClB,IAAIG,EAAI,EAGJA,EAAAH,EAAEI,eAAe,GAAGC,MAMnB1B,KAAA2B,eAAeH,GAAE,EACtB,EAED,SAAAQ,GAEMhC,KAAAiC,MAAM,SAAUjC,KAAKD,aAGTC,KAAAiC,MAAM,oBAAqBjC,KAAKD,YAKjD,EAED,cAAA4B,CAAeH,EAAEU,GAAU,GACtB,GAAAlC,KAAKmC,UAAYnC,KAAKoC,SACzB,OAGD,MAAMC,EAAerC,KAAKE,UAAYF,KAAKsC,MAAQtC,KAAKF,YAIlDyC,EAFNf,EAAIgB,EAAKA,MAACxC,KAAKF,YAAauC,EAAcb,GAAKxB,KAAKF,YAIhD,IAAAgC,EAEJ,GAAI9B,KAAKyC,UAAW,CACnBX,EAAQY,KAAKC,MAAMJ,EAAWvC,KAAKE,WAE7B,MAAA0C,EAAUL,EAAWvC,KAAKE,UAC5B0C,GAAW5C,KAAKE,UAAY,GAAK0C,EAAU,EACrCd,GAAA,GACCc,EAAU5C,KAAKE,UAAY,GACrC4B,QAEK,CACNA,EAAQY,KAAKC,MAAMJ,EAAWvC,KAAKE,WAE7B,MAAA0C,EAAUL,EAAWvC,KAAKE,UAE5BgC,EACCU,EAAU,GAAGd,IAEbc,EAAU5C,KAAKE,UAAY,GAAG4B,GAGpC,CACA9B,KAAKD,YAAc2C,KAAKG,IAAIf,EAAO9B,KAAKsC,OAEpCtC,KAAKD,YAAcC,KAAK8C,WAC3B9C,KAAKD,YAAcC,KAAK8C,UAIzBC,YAAW,KACV/C,KAAKG,QAAS,CAAA,GACZ,IAEH4C,YAAW,KACV/C,KAAKG,QAAS,CAAA,GACZ,GACH,GAEF,OAAA6C,GACChD,KAAKU,MACL,08BC3RHuC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}