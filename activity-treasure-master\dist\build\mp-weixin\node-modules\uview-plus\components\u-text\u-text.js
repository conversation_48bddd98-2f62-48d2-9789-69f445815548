"use strict";const e=require("../../../../common/vendor.js"),t={name:"up-text",mixins:[e.mpMixin,e.mixin,e.value,e.buttonMixin,e.openType,e.props],emits:["click"],computed:{valueStyle(){const t={textDecoration:this.decoration,fontWeight:this.bold?"bold":"normal",wordWrap:this.wordWrap,fontSize:e.addUnit(this.size)};return!this.type&&(t.color=this.color),this.isNvue&&this.lines&&(t.lines=this.lines),this.lineHeight&&(t.lineHeight=e.addUnit(this.lineHeight)),!this.isNvue&&this.block&&(t.display="block"),e.deepMerge(t,e.addStyle(this.customStyle))},isNvue:()=>!1,isMp(){let e=!1;return e=!0,true}},data:()=>({}),methods:{addStyle:e.addStyle,click<PERSON><PERSON>ler(t){this.call&&"phone"===this.mode&&e.index.makePhoneCall({phoneNumber:this.text}),this.$emit("click",t)}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-link"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-link/u-link.js"))();const n=e._export_sfc(t,[["render",function(t,n,o,i,s,l){return e.e({a:t.show},t.show?e.e({b:"price"===t.mode},"price"===t.mode?{c:e.n(t.type&&`u-text__value--${t.type}`),d:e.s(l.valueStyle)}:{},{e:t.prefixIcon},t.prefixIcon?{f:e.p({name:t.prefixIcon,customStyle:l.addStyle(t.iconStyle)})}:{},{g:"link"===t.mode},"link"===t.mode?{h:l.valueStyle.fontWeight,i:l.valueStyle.wordWrap,j:l.valueStyle.fontSize,k:e.n(t.type&&`u-text__value--${t.type}`),l:e.n(t.lines&&`u-line-${t.lines}`),m:e.p({text:t.value,href:t.href,underLine:!0})}:t.openType&&l.isMp?{o:e.t(t.value),p:e.s(l.valueStyle),q:t.index,r:t.openType,s:e.o(((...e)=>t.onGetUserInfo&&t.onGetUserInfo(...e))),t:e.o(((...e)=>t.onContact&&t.onContact(...e))),v:e.o(((...e)=>t.onGetPhoneNumber&&t.onGetPhoneNumber(...e))),w:e.o(((...e)=>t.onError&&t.onError(...e))),x:e.o(((...e)=>t.onLaunchApp&&t.onLaunchApp(...e))),y:e.o(((...e)=>t.onOpenSetting&&t.onOpenSetting(...e))),z:t.lang,A:t.sessionFrom,B:t.sendMessageTitle,C:t.sendMessagePath,D:t.sendMessageImg,E:t.showMessageCard,F:t.appParameter}:{G:e.t(t.value),H:e.s(l.valueStyle),I:e.n(t.type&&`u-text__value--${t.type}`),J:e.n(t.lines&&`u-line-${t.lines}`)},{n:t.openType&&l.isMp,K:t.suffixIcon},t.suffixIcon?{L:e.p({name:t.suffixIcon,customStyle:l.addStyle(t.iconStyle)})}:{},{M:e.n(t.customClass),N:t.margin,O:"left"===t.align?"flex-start":"center"===t.align?"center":"flex-end",P:e.o(((...e)=>l.clickHandler&&l.clickHandler(...e)))}):{})}],["__scopeId","data-v-40b920e6"]]);wx.createComponent(n);
//# sourceMappingURL=u-text.js.map
