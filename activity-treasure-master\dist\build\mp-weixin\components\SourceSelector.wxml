<view class="source-selector data-v-017fc69b" bindtap="{{p}}"><view class="{{['selector-content', 'data-v-017fc69b', o && 'has-value']}}"><u-icon wx:if="{{a}}" class="icon data-v-017fc69b" u-i="017fc69b-0" bind:__l="__l" u-p="{{a}}"></u-icon><view wx:if="{{b}}" class="selected-item data-v-017fc69b"><image wx:if="{{c}}" src="{{d}}" class="cover data-v-017fc69b" mode="aspectFill"></image><view wx:else class="cover-placeholder data-v-017fc69b"><u-icon wx:if="{{e}}" class="data-v-017fc69b" u-i="017fc69b-1" bind:__l="__l" u-p="{{e}}"></u-icon></view><view class="source-info data-v-017fc69b"><text class="name data-v-017fc69b">{{f}}</text><text wx:if="{{g}}" class="publisher data-v-017fc69b">{{h}}</text><text wx:elif="{{i}}" class="category data-v-017fc69b">{{j}}</text></view><u-icon wx:if="{{l}}" bindclick="{{k}}" class="clear-btn data-v-017fc69b" u-i="017fc69b-2" bind:__l="__l" u-p="{{l}}"></u-icon></view><view wx:else class="placeholder data-v-017fc69b"><text class="placeholder-text data-v-017fc69b">{{m}}</text><u-icon wx:if="{{n}}" class="arrow data-v-017fc69b" u-i="017fc69b-3" bind:__l="__l" u-p="{{n}}"></u-icon></view></view></view>