{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages/bundle/world/card/index.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL3BhZ2VzL2J1bmRsZS93b3JsZC9jYXJkL2luZGV4LnZ1ZQ"], "sourcesContent": ["<script setup>\nimport { ref, computed, defineProps, watch, nextTick } from 'vue'; // 引入 nextTick\nimport { onShareAppMessage } from '@dcloudio/uni-app'; // 引入 onShareAppMessage\nimport { navto } from '@/utils';\nimport { store } from '@/store'; // 需要 store 获取 uid/token\nimport { likeCard, favoriteCard } from '@/api/index.js'; // 导入点赞和收藏API\nimport SharePopup from '@/components/share-popup/share-popup.vue';\nimport { requireLogin } from '@/utils/auth';\n\n// --- Props Definition ---\nconst props = defineProps({\n  cards: { // 从父组件接收已排序的卡片数组\n    type: Array,\n    default: () => []\n  },\n  loading: {\n    type: Boolean,\n    default: true\n  },\n  error: {\n    type: String,\n    default: ''\n  }\n});\n\n// --- State Refs ---\nconst dateList = ref([]); // 日期轴数据 [{date: 'YYYY-MM-DD', displayShort: 'MM-DD', displayDay: '周X', isToday: boolean, id: 'date-...'}]\nconst selectedDate = ref(''); // 当前选中的日期 YYYY-MM-DD\nconst currentSwiperIndex = ref(0); // 当前 Swiper 的索引\nconst dateScrollIntoView = ref(''); // 控制日期轴滚动\nconst showLoadingOverlay = ref(false); // 用于Swiper切换时的加载遮罩\n\n// 分享状态\nconst currentShareCard = ref(null);\nconst showSharePopup = ref(false);\n\n// 分享配置\nonShareAppMessage(() => {\n  try {\n    if (!currentShareCard.value) {\n      console.warn('日卡信息未设置，使用默认分享信息');\n      return {\n        title: '分享一张精美日卡',\n        path: '/pages/bundle/world/card/index',\n        imageUrl: store().$state.config?.img_config?.app_logo?.val || ''\n      };\n    }\n\n    return {\n      title: currentShareCard.value.description ?\n        (currentShareCard.value.description.length > 30 ?\n          currentShareCard.value.description.substring(0, 30) + '...' :\n          currentShareCard.value.description) :\n        '分享一张精美日卡',\n      path: `/pages/bundle/world/card/detail?cardId=${currentShareCard.value.id}`,\n      imageUrl: currentShareCard.value.background_image_url || store().$state.config?.img_config?.app_logo?.val || ''\n    };\n  } catch (error) {\n    console.error('日卡分享配置失败:', error);\n    return {\n      title: '分享一张精美日卡',\n      path: '/pages/bundle/world/card/index',\n      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''\n    };\n  }\n});\n\n// --- Computed Properties ---\nconst todayDateString = computed(() => {\n  return formatYYYYMMDD(new Date());\n});\n\n// --- Utility Functions ---\nconst formatYYYYMMDD = (date) => {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n}\n\nconst getWeekDay = (dateStr) => {\n  if (!dateStr) return '';\n  const date = new Date(dateStr);\n  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];\n  return weekDays[date.getDay()];\n};\n\n/**\n * 根据卡片数据生成日期轴列表\n */\nconst generateDateList = (cardsData) => {\n  const todayStr = todayDateString.value;\n  dateList.value = cardsData.map((card, index) => {\n    const dateParts = card.date.split('-');\n    return {\n      date: card.date,\n      displayShort: `${dateParts[2]}`, // 只显示日期，不显示月份\n      displayDay: getWeekDay(card.date),\n      isToday: card.date === todayStr,\n      id: `date-${index}` // 用于 scroll-into-view\n    };\n  });\n  console.log(\"Generated date list:\", dateList.value);\n};\n\n// --- Event Handlers ---\n/**\n * Swiper 切换完成时触发\n * @param {object} e - 事件对象\n */\nconst onSwiperChange = (e) => {\n  // --- Modify Safety Check to handle both event structures ---\n  let index = -1;\n  if (e && e.detail && typeof e.detail.current === 'number') {\n    index = e.detail.current;\n  } else if (e && typeof e.current === 'number') {\n    // Handle the case where current is directly on the event object\n    index = e.current;\n    console.warn(\"onSwiperChange received event with direct 'current' property:\", e);\n  } else {\n    console.error(\"onSwiperChange called with invalid or unexpected event object structure:\", e);\n    return; // Exit if we cannot determine the index\n  }\n  // --- End Modified Safety Check ---\n\n  console.log(\"Swiper changed to index:\", index);\n  if (props.cards && props.cards[index]) {\n      // currentSwiperIndex.value = index; // No longer needed to update if :current drives swiper\n      const newSelectedDate = props.cards[index].date;\n      if (selectedDate.value !== newSelectedDate) {\n          selectedDate.value = newSelectedDate;\n          // Scroll date axis\n          nextTick(() => {\n              dateScrollIntoView.value = `date-${index}`;\n              // 添加日志\n              console.log(`[${Date.now()}] Inside nextTick (SwiperChange) - Scrolling date axis to:`, dateScrollIntoView.value);\n          });\n      }\n  } else {\n       console.warn(`Swiper changed to invalid index ${index} or cards not ready.`);\n  }\n};\n\n/**\n * 点击日期轴日期时触发\n * @param {object} dateItem - 被点击的日期对象\n * @param {number} index - 被点击日期的索引\n */\nconst handleDateSelect = (dateItem, index) => {\n  console.log(\"Date selected:\", dateItem.date, \"at index:\", index);\n  if (selectedDate.value !== dateItem.date) {\n    selectedDate.value = dateItem.date;\n    // Find the index in cards array corresponding to the selected date\n    const targetIndex = props.cards.findIndex(card => card.date === dateItem.date);\n    if (targetIndex !== -1 && currentSwiperIndex.value !== targetIndex) {\n        console.log(\"Setting swiper index to:\", targetIndex);\n         showLoadingOverlay.value = true; // 显示遮罩\n         // 直接修改 currentSwiperIndex 驱动 Swiper\n         currentSwiperIndex.value = targetIndex;\n         // 短暂延时后隐藏遮罩，给 Swiper 切换一点时间\n         setTimeout(() => {\n            showLoadingOverlay.value = false;\n         }, 150); // 150ms 应该足够\n\n    } else if (targetIndex === -1) {\n        console.warn(\"Selected date not found in cards data:\", dateItem.date);\n        // Optionally handle error or do nothing\n    }\n     // Ensure the selected date is visible in the scroll view\n     // Use the original 'index' passed to handleDateSelect for scrolling date axis\n     nextTick(() => {\n         dateScrollIntoView.value = `date-${index}`; // 使用原始 index\n         // 添加日志\n         console.log(`[${Date.now()}] Inside nextTick (DateSelect) - Scrolling date axis to:`, dateScrollIntoView.value);\n     });\n  }\n};\n\n/**\n * 处理卡片单击事件\n * @param {number} cardId - 卡片ID\n */\nconst handleCardClick = (cardId) => {\n  navto(`/pages/bundle/world/card/detail?cardId=${cardId}`);\n};\n\n/**\n * 处理评论按钮点击事件\n * @param {number} cardId - 卡片ID\n */\nconst handleCommentClick = (cardId, event) => {\n    // 阻止事件冒泡\n    if (event) {\n        event.stopPropagation();\n    }\n    navto(`/pages/bundle/world/card/detail?cardId=${cardId}&showComments=true`);\n};\n\n/**\n * 处理点赞按钮点击事件\n * @param {number} cardId - 卡片ID\n * @param {Event} event - 事件对象\n */\nconst handleLikeClick = async (cardId, event) => {\n    // 使用统一的登录校验\n    if (!requireLogin('', '请先登录后再点赞')) {\n        return;\n    }\n\n    // 找到当前卡片\n    const currentCard = props.cards.find(card => card.id === cardId);\n    if (!currentCard) {\n        uni.showToast({ title: '卡片数据不存在', icon: 'none' });\n        return;\n    }\n\n    // 阻止事件冒泡\n    if (event) {\n        event.stopPropagation();\n    }\n\n    // 乐观更新UI\n    const originalLikedState = currentCard.isLiked;\n    const originalLikeCount = currentCard.likeCount;\n\n    currentCard.isLiked = !currentCard.isLiked;\n    currentCard.likeCount += currentCard.isLiked ? 1 : -1;\n\n    try {\n        console.log('发送点赞请求:', {\n            id: cardId,\n            uid: store().$state.userInfo.uid,\n            token: store().$state.userInfo.token\n        });\n\n        const res = await likeCard({\n            id: cardId,\n            uid: store().$state.userInfo.uid,\n            token: store().$state.userInfo.token\n        });\n\n        console.log('点赞响应:', res);\n\n        if (res.status !== 'ok') {\n            // 恢复原状态\n            currentCard.isLiked = originalLikedState;\n            currentCard.likeCount = originalLikeCount;\n            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n        } else {\n            uni.showToast({ title: res.msg || '操作成功', icon: 'success' });\n        }\n    } catch (error) {\n        // 恢复原状态\n        currentCard.isLiked = originalLikedState;\n        currentCard.likeCount = originalLikeCount;\n        console.error('点赞失败:', error);\n        uni.showToast({ title: '操作失败，请重试', icon: 'none' });\n    }\n};\n\n/**\n * 处理收藏按钮点击事件\n * @param {number} cardId - 卡片ID\n */\nconst handleFavoriteClick = async (cardId, event) => {\n    // 使用统一的登录校验\n    if (!requireLogin('', '请先登录后再收藏')) {\n        return;\n    }\n\n    // 阻止事件冒泡\n    if (event) {\n        event.stopPropagation();\n    }\n\n    // 找到当前卡片\n    const currentCard = props.cards.find(card => card.id === cardId);\n    if (!currentCard) {\n        uni.showToast({ title: '卡片数据不存在', icon: 'none' });\n        return;\n    }\n\n    // 乐观更新UI\n    const originalFavoritedState = currentCard.isFavorited || false;\n    currentCard.isFavorited = !currentCard.isFavorited;\n\n    try {\n        const res = await favoriteCard({\n            id: cardId,\n            uid: store().$state.userInfo.uid,\n            token: store().$state.userInfo.token\n        });\n\n        if (res.status !== 'ok') {\n            // 恢复原状态\n            currentCard.isFavorited = originalFavoritedState;\n            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });\n        } else {\n            uni.showToast({ title: res.msg || '操作成功', icon: 'success' });\n        }\n    } catch (error) {\n        // 恢复原状态\n        currentCard.isFavorited = originalFavoritedState;\n        console.error('收藏失败:', error);\n        uni.showToast({ title: '操作失败，请重试', icon: 'none' });\n    }\n};\n\n/**\n * 处理分享按钮点击事件\n * @param {number} cardId - 卡片ID\n * @param {Event} event - 事件对象\n */\nconst handleShareClick = (cardId, event) => {\n    if (event) {\n        event.stopPropagation();\n    }\n\n    // 获取当前卡片数据\n    const currentCard = props.cards.find(card => card.id === cardId);\n    if (!currentCard) {\n        uni.showToast({ title: '卡片数据不存在', icon: 'none' });\n        return;\n    }\n\n    // 设置当前分享的卡片数据\n    currentShareCard.value = currentCard;\n\n    // 显示分享弹窗\n    showSharePopup.value = true;\n};\n\n// 处理分享成功\nconst handleShareSuccess = (result) => {\n    console.log('分享成功:', result);\n    uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n    });\n};\n\n// 处理分享错误\nconst handleShareError = (error) => {\n    console.error('分享失败:', error);\n    uni.showToast({\n        title: '分享失败',\n        icon: 'none'\n    });\n};\n\n// 处理刷新按钮点击事件\nconst handleRefresh = () => {\n    uni.showToast({\n        title: '刷新中...',\n        icon: 'loading',\n        duration: 1000\n    });\n\n    // 这里可以触发父组件的刷新方法\n    // 或者发送事件给父组件\n    // emit('refresh');\n\n    // 暂时使用简单的提示\n    setTimeout(() => {\n        uni.showToast({\n            title: '刷新完成',\n            icon: 'success'\n        });\n    }, 1000);\n};\n\n\n\n// --- Watchers ---\nwatch(() => props.cards, (newCards) => {\n    console.log(\"Props cards updated in CardIndex watcher:\", newCards);\n    if (newCards && newCards.length > 0) {\n        generateDateList(newCards);\n        const todayIndex = newCards.findIndex(card => card.date === todayDateString.value);\n        const initialIndex = todayIndex !== -1 ? todayIndex : 0;\n\n        // 只有在首次加载或数据完全重置时才设置初始 index 和 date\n        // 避免在父组件更新(如点赞)时重置用户当前浏览的位置\n        if (currentSwiperIndex.value === 0 && selectedDate.value === '') {\n            currentSwiperIndex.value = initialIndex;\n            selectedDate.value = newCards[initialIndex].date;\n            console.log(\"CardIndex: Initial setup - Swiper index:\", initialIndex, \"Selected date:\", selectedDate.value);\n             // Scroll to initial date\n             nextTick(() => {\n                 dateScrollIntoView.value = `date-${initialIndex}`;\n                 // 添加日志\n                 console.log(`[${Date.now()}] Inside nextTick (Watch Initial) - Scrolling date axis to:`, dateScrollIntoView.value);\n             });\n        } else {\n             // Data might have updated (e.g., likes), find current card's new index if needed\n             // For now, assume the order is stable and index remains the same unless date changes\n             console.log(\"CardIndex: Cards updated, maintaining current index:\", currentSwiperIndex.value);\n        }\n    } else {\n        dateList.value = [];\n        selectedDate.value = '';\n        currentSwiperIndex.value = 0;\n    }\n}, { immediate: true, deep: true }); // deep: true may not be necessary if only array reference changes\n\n</script>\n\n<template>\n  <view class=\"card-page-container\">\n\n    <!-- Horizontal Date Axis -->\n    <view class=\"date-axis-container\">\n       <scroll-view\n            class=\"date-scroll-view\"\n            scroll-x\n            :scroll-into-view=\"dateScrollIntoView\"\n            scroll-with-animation\n       >\n         <view class=\"date-item-wrapper\" v-for=\"(item, index) in dateList\" :key=\"item.date\" :id=\"item.id\">\n             <view class=\"date-item\" :class=\"{ active: item.date === selectedDate, today: item.isToday }\" @click=\"handleDateSelect(item, index)\">\n               <text class=\"date-day\">{{ item.displayDay }}</text>\n               <text class=\"date-short\">{{ item.displayShort }}</text>\n             </view>\n         </view>\n       </scroll-view>\n       <view class=\"refresh-icon\" @click=\"handleRefresh\">\n            <u-icon name=\"reload\" size=\"20\" color=\"#999\"></u-icon>\n       </view>\n    </view>\n\n    <!-- Main Content Area -->\n    <view class=\"main-content-area\">\n        <!-- Loading State -->\n        <view v-if=\"loading && cards.length === 0\" class=\"loading-state\">\n          <u-loading-icon mode=\"circle\" size=\"24\"></u-loading-icon>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n\n        <!-- Error State -->\n        <view v-else-if=\"error\" class=\"error-state\">\n           <u-empty mode=\"list\" :text=\"error\"></u-empty>\n        </view>\n\n        <!-- Empty State -->\n        <view v-else-if=\"cards.length === 0\" class=\"empty-state\">\n            <u-empty mode=\"list\" text=\"暂无日卡数据\"></u-empty>\n        </view>\n\n        <!-- Native Swiper for Cards -->\n         <swiper\n            v-else\n            class=\"card-swiper\"\n            :current=\"currentSwiperIndex\"\n            @change=\"onSwiperChange\"\n            :circular=\"true\"\n            previous-margin=\"40rpx\"\n            next-margin=\"40rpx\"\n            :display-multiple-items=\"cards && cards.length > 0 ? 1 : 0\"\n         >\n           <!-- Loop through cards using swiper-item -->\n           <swiper-item v-for=\"(card, itemIndex) in cards\" :key=\"card?.id || itemIndex\" v-if=\"cards && cards.length > 0\">\n               <!-- Restore Original Content -->\n               <view v-if=\"card\" class=\"swiper-item-container\" @click=\"handleCardClick(card.id)\">\n                   <view class=\"card-content-wrapper\">\n                       <!-- 上半部分：图片 -->\n                       <view class=\"card-image-section\">\n                           <image\n                               class=\"card-image\"\n                               :src=\"card.background_image_url || '/static/default-card-bg.png'\"\n                               mode=\"aspectFill\"\n                               lazy-load\n                           />\n                       </view>\n\n                       <!-- 下半部分：文字和作者 -->\n                       <view class=\"card-text-section\">\n                           <text class=\"card-description\">{{ card.description }}</text>\n                           <text v-if=\"card.author\" class=\"card-author\">— {{ card.author }}</text>\n                       </view>\n\n                       <!-- 操作栏 -->\n                       <view class=\"card-action-bar\">\n                           <view class=\"action-item\" @click.stop=\"handleCommentClick(card.id, $event)\">\n                               <u-icon name=\"chat\" size=\"20\" color=\"#666\"></u-icon>\n                               <text class=\"action-count\">{{card.commentCount || 0}}</text>\n                           </view>\n                           <view class=\"action-item\" @click.stop=\"handleLikeClick(card.id, $event)\">\n                               <u-icon :name=\"card.isLiked ? 'heart-fill' : 'heart'\" size=\"20\" :color=\"card.isLiked ? '#ff6b81' : '#666'\"></u-icon>\n                               <text class=\"action-count\" :class=\"{ 'liked': card.isLiked }\">{{card.likeCount || 0}}</text>\n                           </view>\n                           <view class=\"action-item\" @click.stop=\"handleFavoriteClick(card.id, $event)\">\n                               <u-icon :name=\"card.isFavorited ? 'star-fill' : 'star'\" size=\"20\" :color=\"card.isFavorited ? '#FFD700' : '#666'\"></u-icon>\n                           </view>\n                           <!-- 分享按钮已注释，使用微信右上角分享 -->\n                           <!-- <view class=\"action-item\" @click.stop=\"handleShareClick(card.id, $event)\">\n                               <u-icon name=\"share\" size=\"20\" color=\"#666\"></u-icon>\n                           </view> -->\n                       </view>\n                   </view>\n               </view>\n           </swiper-item>\n        </swiper>\n\n         <!-- Loading Overlay during date selection jump -->\n        <view v-if=\"showLoadingOverlay\" class=\"loading-overlay\">\n            <u-loading-icon mode=\"circle\" size=\"30\" color=\"#FFFFFF\"></u-loading-icon>\n        </view>\n    </view>\n\n    <!-- 分享弹窗 -->\n    <share-popup\n      :show=\"showSharePopup\"\n      title=\"分享日卡\"\n      :share-data=\"{\n        image: currentShareCard?.background_image_url,\n        content: currentShareCard?.description,\n        author: currentShareCard?.author,\n        date: currentShareCard?.date,\n        template: 'card'\n      }\"\n      :show-member-invite=\"store().$state.userInfo?.role_type === 0 || store().$state.userInfo?.role_type === 1\"\n      @close=\"showSharePopup = false\"\n      @share-success=\"handleShareSuccess\"\n      @share-error=\"handleShareError\"\n    />\n\n    <!-- 隐藏的Canvas元素用于生成分享图片 -->\n    <canvas\n      canvas-id=\"share-canvas\"\n      id=\"share-canvas\"\n      style=\"position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;\"\n    ></canvas>\n\n  </view>\n</template>\n\n\n<style lang=\"scss\" scoped>\n.card-page-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f8f9fa; // 统一背景色\n  overflow: hidden;\n  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n}\n\n/* Date Axis */\n.date-axis-container {\n    display: flex;\n    align-items: center;\n    background-color: #fff;\n    padding: 20rpx 0 20rpx 30rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n    flex-shrink: 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    position: relative;\n    z-index: 10;\n}\n.date-scroll-view {\n    width: calc(100% - 80rpx); // Leave space for refresh icon\n    white-space: nowrap;\n    height: 110rpx; // 增加高度\n}\n.date-item-wrapper {\n    display: inline-block; // Important for scroll-view\n    margin-right: 24rpx;\n    text-align: center;\n}\n.date-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 12rpx 24rpx;\n    border-radius: 16rpx;\n    transition: all 0.3s ease;\n    height: 100%;\n    box-sizing: border-box;\n    min-width: 110rpx;\n\n    .date-day {\n        font-size: 24rpx;\n        color: #999;\n        margin-bottom: 8rpx;\n    }\n    .date-short {\n        font-size: 32rpx;\n        color: #333;\n        font-weight: 500;\n    }\n\n    &.active {\n        background-color: #f0f7ff; // 浅蓝色背景\n        .date-day {\n            color: #576b95; // 微信蓝色\n        }\n        .date-short {\n            color: #576b95; // 微信蓝色\n            font-weight: bold;\n        }\n    }\n    &.today .date-short::after { // Dot for today\n        content: '';\n        display: block;\n        width: 8rpx;\n        height: 8rpx;\n        border-radius: 50%;\n        background-color: #576b95; // 微信蓝色\n        margin: 6rpx auto 0;\n    }\n}\n.refresh-icon {\n    width: 80rpx;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    flex-shrink: 0;\n}\n\n/* Main Content Area */\n.main-content-area {\n    flex: 1;\n    position: relative; // For loading overlay\n    overflow: hidden; // Important for swiper height\n    display: flex; // Ensure children fill height\n    flex-direction: column;\n}\n\n/* Loading/Error/Empty States */\n.loading-state,\n.error-state,\n.empty-state {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  color: #999;\n  font-size: 28rpx;\n  .empty-text,\n  .loading-text {\n      margin-top: 10rpx;\n  }\n}\n\n/* Swiper */\n.card-swiper {\n  width: 100%;\n  flex: 1; /* Ensure swiper stretches in flex container */\n}\n\n/* {{ AURA-X: Modify - 修复HTML标签选择器，改为类选择器. Confirmed via 寸止. }} */\n/* Restore original commented styles */\n.swiper-item-container {\n  height: 100%;\n  display: flex;\n  align-items: stretch;\n}\n\n.swiper-item-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    box-sizing: border-box;\n    padding: 15rpx 0;\n    width: 100%;\n}\n\n.card-content-wrapper {\n  width: calc(100% - 20rpx);\n  height: 82%;\n  position: relative;\n  border-radius: 24rpx;\n  overflow: hidden;\n  background-color: #ffffff;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);\n  transform: scale(0.98);\n  transition: transform 0.3s ease;\n  display: flex;\n  flex-direction: column;\n\n  &:active {\n    transform: scale(0.96);\n  }\n}\n\n/* 新的卡片图片区域 */\n.card-image-section {\n  width: 100%;\n  height: 55%;\n  position: relative;\n  overflow: hidden;\n}\n\n.card-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 新的卡片文字区域 */\n.card-text-section {\n  flex: 1;\n  padding: 32rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 16rpx;\n}\n\n.card-description {\n  font-size: 30rpx;\n  line-height: 1.8;\n  color: #333333;\n  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n  letter-spacing: 1rpx;\n  text-align: left;\n}\n\n.card-author {\n  font-size: 26rpx;\n  color: #666666;\n  font-style: italic;\n  text-align: right;\n  margin-top: 8rpx;\n}\n\n.card-action-bar {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 80rpx;\n    display: flex;\n    justify-content: space-around;\n    align-items: center;\n    padding: 0 30rpx;\n    z-index: 4;\n    background-color: rgba(255, 255, 255, 0.95);\n    border-top: 1rpx solid #f0f0f0;\n    backdrop-filter: blur(10px);\n}\n\n.action-item {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    padding: 8rpx 12rpx;\n    border-radius: 20rpx;\n    transition: all 0.2s ease;\n\n    &:active {\n        background-color: rgba(0, 0, 0, 0.05);\n        transform: scale(0.95);\n    }\n}\n\n.action-count {\n    font-size: 22rpx;\n    color: #666;\n    margin-left: 8rpx;\n    min-width: 30rpx;\n    text-align: center;\n\n    &.liked {\n        color: #ff6b81;\n        font-weight: 500;\n    }\n}\n\n/* Loading Overlay */\n.loading-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.3);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 10;\n}\n\n</style>", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/card/index.vue'\nwx.createComponent(Component)"], "names": ["SharePopup", "dateList", "ref", "selectedDate", "currentSwiperIndex", "dateScrollIntoView", "showLoadingOverlay", "currentShareCard", "showSharePopup", "onShareAppMessage", "value", "title", "description", "length", "substring", "path", "id", "imageUrl", "background_image_url", "store", "$state", "config", "_d", "img_config", "_e", "app_logo", "val", "console", "warn", "_b", "_a", "store_index", "error", "_h", "_g", "todayDateString", "computed", "formatYYYYMMDD", "Date", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getWeekDay", "dateStr", "getDay", "onSwiperChange", "e", "index", "detail", "current", "log", "props", "cards", "newSelectedDate", "nextTick", "now", "handleShareSuccess", "result", "uni", "showToast", "icon", "handleShareError", "handleRefresh", "duration", "setTimeout", "watch", "newCards", "cardsData", "todayStr", "map", "card", "dateParts", "split", "displayShort", "displayDay", "isToday", "generateDateList", "todayIndex", "findIndex", "initialIndex", "immediate", "deep", "dateItem", "targetIndex", "cardId", "event", "stopPropagation", "navto", "async", "requireLogin", "currentCard", "find", "originalLikedState", "isLiked", "originalLikeCount", "likeCount", "uid", "userInfo", "token", "res", "likeCard", "status", "msg", "originalFavoritedState", "isFavorited", "favoriteCard", "wx", "createComponent", "Component"], "mappings": "+wBAMA,MAAMA,EAAa,IAAW,oMAoBxBC,EAAWC,EAAAA,IAAI,IACfC,EAAeD,EAAAA,IAAI,IACnBE,EAAqBF,EAAAA,IAAI,GACzBG,EAAqBH,EAAAA,IAAI,IACzBI,EAAqBJ,EAAAA,KAAI,GAGzBK,EAAmBL,EAAAA,IAAI,MACvBM,EAAiBN,EAAAA,KAAI,GAG3BO,EAAAA,mBAAkB,2BACZ,IACE,OAACF,EAAiBG,MASf,CACLC,MAAOJ,EAAiBG,MAAME,YAC3BL,EAAiBG,MAAME,YAAYC,OAAS,GAC3CN,EAAiBG,MAAME,YAAYE,UAAU,EAAG,IAAM,MACtDP,EAAiBG,MAAME,YACzB,WACFG,KAAM,0CAA0CR,EAAiBG,MAAMM,KACvEC,SAAUV,EAAiBG,MAAMQ,uBAAwBC,OAAAA,EAAAA,SAAAA,WAAKA,QAAGC,OAAOC,aAAQ,EAAAC,EAAAC,iBAAY,EAAAC,EAAAC,mBAAUC,MAAO,KAf7GC,QAAQC,KAAK,oBACN,CACLjB,MAAO,WACPI,KAAM,iCACNE,UAAUE,OAAAA,EAAAA,OAAKU,EAALV,OAAKW,EAAAC,EAAAZ,QAAGC,OAAOC,aAAfF,EAAAA,EAAuBI,iBAAvBJ,EAAAA,EAAmCM,eAAnCN,EAAAA,EAA6CO,MAAO,IAanE,OAAQM,GAEA,OADCL,QAAAK,MAAM,YAAaA,GACpB,CACLrB,MAAO,WACPI,KAAM,iCACNE,UAAUE,OAAAA,EAAAA,OAAKc,EAALd,OAAKe,EAAAH,EAAAZ,QAAGC,OAAOC,aAAfF,EAAAA,EAAuBI,iBAAvBJ,EAAAA,EAAmCM,eAAnCN,EAAAA,EAA6CO,MAAO,GAElE,KAII,MAAAS,EAAkBC,EAAQA,UAAC,IACxBC,EAAe,IAAIC,QAItBD,EAAkBE,GAIb,GAHMA,EAAKC,iBACJC,OAAOF,EAAKG,WAAa,GAAGC,SAAS,EAAG,QAC1CF,OAAOF,EAAKK,WAAWD,SAAS,EAAG,OAI7CE,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAGd,MADU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KADnC,IAAIR,KAAKQ,GAEDC,SAAQ,EA0BzBC,EAAkBC,IAEtB,IAAIC,GAAQ,EACZ,GAAID,GAAKA,EAAEE,QAAsC,iBAArBF,EAAEE,OAAOC,QACnCF,EAAQD,EAAEE,OAAOC,YACR,KAAAH,GAA0B,iBAAdA,EAAEG,QAMvB,YADQzB,QAAAK,MAAM,2EAA4EiB,GAH1FC,EAAQD,EAAEG,QACFzB,QAAAC,KAAK,gEAAiEqB,EAIhF,CAIA,GADQtB,QAAA0B,IAAI,2BAA4BH,GACpCI,EAAMC,OAASD,EAAMC,MAAML,GAAQ,CAEnC,MAAMM,EAAkBF,EAAMC,MAAML,GAAOX,KACvCpC,EAAaO,QAAU8C,IACvBrD,EAAaO,MAAQ8C,EAErBC,EAAAA,YAAS,KACLpD,EAAmBK,MAAQ,QAAQwC,IAEnCvB,QAAQ0B,IAAI,IAAIf,KAAKoB,kEAAmErD,EAAmBK,MAAK,IAG9H,MACeiB,QAAAC,KAAK,mCAAmCsB,wBACrD,EAiMIS,EAAsBC,IAChBjC,QAAA0B,IAAI,QAASO,GACrBC,EAAAA,MAAIC,UAAU,CACVnD,MAAO,OACPoD,KAAM,WACT,EAICC,EAAoBhC,IACdL,QAAAK,MAAM,QAASA,GACvB6B,EAAAA,MAAIC,UAAU,CACVnD,MAAO,OACPoD,KAAM,QACT,EAICE,EAAgB,KAClBJ,EAAAA,MAAIC,UAAU,CACVnD,MAAO,SACPoD,KAAM,UACNG,SAAU,MAQdC,YAAW,KACPN,EAAAA,MAAIC,UAAU,CACVnD,MAAO,OACPoD,KAAM,WACT,GACF,IAAI,SAMXK,EAAKA,OAAC,IAAMd,EAAMC,QAAQc,IAElB,GADI1C,QAAA0B,IAAI,4CAA6CgB,GACrDA,GAAYA,EAASxD,OAAS,EAAG,CA9RhB,CAACyD,IACxB,MAAMC,EAAWpC,EAAgBzB,MACjCT,EAASS,MAAQ4D,EAAUE,KAAI,CAACC,EAAMvB,KACpC,MAAMwB,EAAYD,EAAKlC,KAAKoC,MAAM,KAC3B,MAAA,CACLpC,KAAMkC,EAAKlC,KACXqC,aAAc,GAAGF,EAAU,KAC3BG,WAAYhC,EAAW4B,EAAKlC,MAC5BuC,QAASL,EAAKlC,OAASgC,EACvBvD,GAAI,QAAQkC,IAClB,IAEUvB,QAAA0B,IAAI,uBAAwBpD,EAASS,MAAK,EAmR5CqE,CAAiBV,GACjB,MAAMW,EAAaX,EAASY,cAAkBR,EAAKlC,OAASJ,EAAgBzB,QACtEwE,GAA8B,IAAfF,EAAoBA,EAAa,EAIrB,IAA7B5E,EAAmBM,OAAsC,KAAvBP,EAAaO,OAC/CN,EAAmBM,MAAQwE,EACd/E,EAAAO,MAAQ2D,EAASa,GAAc3C,KAC5CZ,QAAQ0B,IAAI,2CAA4C6B,EAAc,iBAAkB/E,EAAaO,OAEpG+C,EAAAA,YAAS,KACLpD,EAAmBK,MAAQ,QAAQwE,IAEnCvD,QAAQ0B,IAAI,IAAIf,KAAKoB,mEAAoErD,EAAmBK,MAAK,KAK7GiB,QAAA0B,IAAI,uDAAwDjD,EAAmBM,MAEpG,MACQT,EAASS,MAAQ,GACjBP,EAAaO,MAAQ,GACrBN,EAAmBM,MAAQ,CAC/B,GACD,CAAEyE,WAAW,EAAMC,MAAM,+JA/PH,EAACC,EAAUnC,KAE9B,GADJvB,QAAQ0B,IAAI,iBAAkBgC,EAAS9C,KAAM,YAAaW,GACtD/C,EAAaO,QAAU2E,EAAS9C,KAAM,CACxCpC,EAAaO,MAAQ2E,EAAS9C,KAExB,MAAA+C,EAAchC,EAAMC,MAAM0B,cAAkBR,EAAKlC,OAAS8C,EAAS9C,QACrD,IAAhB+C,GAAsBlF,EAAmBM,QAAU4E,GAC3C3D,QAAA0B,IAAI,2BAA4BiC,GACvChF,EAAmBI,OAAQ,EAE3BN,EAAmBM,MAAQ4E,EAE3BnB,YAAW,KACR7D,EAAmBI,OAAQ,CAAA,GAC3B,OAEuB,IAApB4E,GACC3D,QAAAC,KAAK,yCAA0CyD,EAAS9C,MAKnEkB,EAAAA,YAAS,KACLpD,EAAmBK,MAAQ,QAAQwC,IAEnCvB,QAAQ0B,IAAI,IAAIf,KAAKoB,gEAAiErD,EAAmBK,MAAK,GAErH,onBAe0B6E,QAAQC,MAG5BA,EAAMC,uBAEVC,EAAAA,MAAM,0CAA0CH,uBALzB,IAACA,EAAQC,yLAaZG,OAAOJ,EAAQC,KAEnC,IAAKI,EAAYA,aAAC,GAAI,YAClB,OAIJ,MAAMC,EAAcvC,EAAMC,MAAMuC,MAAarB,GAAAA,EAAKzD,KAAOuE,IACzD,IAAKM,EAED,YADAhC,EAAGX,MAACY,UAAU,CAAEnD,MAAO,UAAWoD,KAAM,SAKxCyB,GACAA,EAAMC,kBAIV,MAAMM,EAAqBF,EAAYG,QACjCC,EAAoBJ,EAAYK,UAE1BL,EAAAG,SAAWH,EAAYG,QACvBH,EAAAK,WAAaL,EAAYG,QAAU,GAAI,EAE/C,IACArE,QAAQ0B,IAAI,UAAW,CACnBrC,GAAIuE,EACJY,IAAKhF,EAAKA,QAAGC,OAAOgF,SAASD,IAC7BE,MAAOlF,EAAKA,QAAGC,OAAOgF,SAASC,QAG7B,MAAAC,QAAYC,WAAS,CACvBvF,GAAIuE,EACJY,IAAKhF,EAAKA,QAAGC,OAAOgF,SAASD,IAC7BE,MAAOlF,EAAKA,QAAGC,OAAOgF,SAASC,QAG3B1E,QAAA0B,IAAI,QAASiD,GAEF,OAAfA,EAAIE,QAEJX,EAAYG,QAAUD,EACtBF,EAAYK,UAAYD,UACpBnC,UAAU,CAAEnD,MAAO2F,EAAIG,KAAO,OAAQ1C,KAAM,kBAE5CD,UAAU,CAAEnD,MAAO2F,EAAIG,KAAO,OAAQ1C,KAAM,WAEvD,OAAQ/B,GAEL6D,EAAYG,QAAUD,EACtBF,EAAYK,UAAYD,EAChBtE,QAAAK,MAAM,QAASA,GACvB6B,EAAGX,MAACY,UAAU,CAAEnD,MAAO,WAAYoD,KAAM,QAC7C,iKAOwB4B,OAAOJ,EAAQC,KAEvC,IAAKI,EAAYA,aAAC,GAAI,YAClB,OAIAJ,GACAA,EAAMC,kBAIV,MAAMI,EAAcvC,EAAMC,MAAMuC,MAAarB,GAAAA,EAAKzD,KAAOuE,IACzD,IAAKM,EAED,YADAhC,EAAGX,MAACY,UAAU,CAAEnD,MAAO,UAAWoD,KAAM,SAKtC,MAAA2C,EAAyBb,EAAYc,cAAe,EAC9Cd,EAAAc,aAAed,EAAYc,YAEnC,IACM,MAAAL,QAAYM,eAAa,CAC3B5F,GAAIuE,EACJY,IAAKhF,EAAKA,QAAGC,OAAOgF,SAASD,IAC7BE,MAAOlF,EAAKA,QAAGC,OAAOgF,SAASC,QAGhB,OAAfC,EAAIE,QAEJX,EAAYc,YAAcD,UACtB5C,UAAU,CAAEnD,MAAO2F,EAAIG,KAAO,OAAQ1C,KAAM,kBAE5CD,UAAU,CAAEnD,MAAO2F,EAAIG,KAAO,OAAQ1C,KAAM,WAEvD,OAAQ/B,GAEL6D,EAAYc,YAAcD,EAClB/E,QAAAK,MAAM,QAASA,GACvB6B,EAAGX,MAACY,UAAU,CAAEnD,MAAO,WAAYoD,KAAM,QAC7C,yDA3HqBwB,YACvBG,EAAAA,MAAM,0CAA0CH,KAD1B,IAACA,iuBCrLzBsB,GAAGC,gBAAgBC"}