"use strict";const e=require("../common/vendor.js"),t=require("../store/index.js");if(require("../store/counter.js"),require("../utils/index.js"),require("../api/index.js"),require("../utils/request.js"),require("../utils/BaseUrl.js"),require("../utils/auth.js"),require("../utils/cacheManager.js"),require("../utils/systemInfo.js"),!Array){(e.resolveComponent("u-status-bar")+e.resolveComponent("u-icon"))()}Math||((()=>"../node-modules/uview-plus/components/u-status-bar/u-status-bar.js")+(()=>"../node-modules/uview-plus/components/u-icon/u-icon.js"))();const r={__name:"myTitle",props:{title:{type:String,default:""},img:{type:String},width:{type:String,default:"750rpx"},height:{type:String,default:"128rpx"},bgColor:{type:String,default:"transparent"},color:{type:String,default:"#000"},blod:{type:Boolean,default:!0},backShow:{type:Boolean,default:!0},mode:{type:String,default:"aspectFill"},backColor:{type:String,default:"#000"},radius:{type:String},back:{type:String,default:""},topBar:{type:Boolean,default:!0},size:{type:String,default:"44rpx"}},setup(r){const i=r,o=e.ref();e.nextTick$1((()=>{o.value=a(e.index.getMenuButtonBoundingClientRect().height)}));const n=()=>{"switch"==i.back?e.index.reLaunch({url:"/pages/index"}):e.index.navigateBack({delta:1,fail:t=>e.index.reLaunch({url:"/pages/index"})})},a=t=>{const r=e.index.getWindowInfo().windowWidth;return 750*Number.parseInt(t)/r};return(i,o)=>e.e({a:r.img},r.img?{b:r.width,c:r.height,d:r.radius,e:-1===r.img.indexOf("http")?e.unref(t.store)().$state.url+r.img:r.img,f:r.mode}:{},{g:r.topBar&&!r.img},r.topBar&&!r.img?{h:e.p({bgColor:r.bgColor})}:{},{i:e.p({bgColor:r.bgColor}),j:r.backShow},r.backShow?{k:e.o(n),l:e.p({color:r.backColor,name:"arrow-left",size:r.size})}:{},{m:r.title},r.title?{n:e.t(r.title),o:r.color,p:r.blod?"700":"400"}:{},{q:e.n(r.title?"jcc":""),r:"88rpx",s:"88rpx",t:r.bgColor,v:r.width,w:r.height})}},i=e._export_sfc(r,[["__scopeId","data-v-e086af11"]]);wx.createComponent(i);
//# sourceMappingURL=myTitle.js.map
