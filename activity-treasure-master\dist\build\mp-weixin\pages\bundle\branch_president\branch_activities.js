"use strict";const e=require("../../../common/vendor.js"),t=require("../../../store/index.js"),o=require("../../../api/index.js");if(require("../../../store/counter.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-button")+e.resolveComponent("u-tag")+e.resolveComponent("u-icon")+e.resolveComponent("u-empty")+e.resolveComponent("u-loadmore"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-tag/u-tag.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../../node-modules/uview-plus/components/u-loadmore/u-loadmore.js"))();const n={__name:"branch_activities",setup(n){const r=e.ref(!1),l=e.ref([]),i=e.ref("all"),a=e.ref(1),s=e.ref(!0),u=e.ref("loadmore"),c=e.reactive({total:0,ongoing:0,completed:0,cancelled:0}),p=e.computed((()=>c.total)),d=e.computed((()=>c.ongoing)),m=e.computed((()=>c.completed));e.onLoad((()=>{f(!0)})),e.onPullDownRefresh((()=>{f(!0),setTimeout((()=>{e.index.stopPullDownRefresh()}),1e3)})),e.onReachBottom((()=>{s.value&&!r.value&&v()}));const f=async(n=!1)=>{try{if(n)a.value=1,s.value=!0,r.value=!0,u.value="loading";else if(!s.value)return;const p=t.store().$state.userInfo,d=await o.branch_presidentget_activities({uid:p.uid,token:p.token,page:a.value,page_size:20,status:i.value});if("ok"===d.status){const e=d.data||[];n?l.value=e:l.value.push(...e),d.stats&&(c.total=d.stats.total||0,c.ongoing=d.stats.ongoing||0,c.completed=d.stats.completed||0,c.cancelled=d.stats.cancelled||0),s.value=20===e.length,a.value++,u.value=s.value?"loadmore":"nomore"}else"relogin"===d.status?e.index.showToast({title:"登录已过期，请重新登录",icon:"none"}):(e.index.showToast({title:d.msg||"加载失败",icon:"none"}),u.value="loadmore")}catch(p){console.error("加载活动列表失败:",p),e.index.showToast({title:"网络错误，请重试",icon:"none"}),u.value="loadmore"}finally{r.value=!1}},g=e=>{i.value=e,f(!0)},v=()=>{!r.value&&s.value&&(u.value="loading",f(!1))},h=e=>({1:"进行中",2:"已完成",3:"已取消"}[e.display_status||e.status]||"未知"),x=e=>({1:"success",2:"info",3:"error"}[e.display_status||e.status]||"info"),y=e=>{if(!e||0===e)return"时间待定";const t="string"==typeof e?parseInt(e):e;if(isNaN(t))return"时间格式错误";const o=new Date(1e3*t);if(isNaN(o.getTime()))return"时间格式错误";const n=o.getMonth()+1,r=o.getDate(),l=o.getHours(),i=o.getMinutes();return`${n}月${r}日 ${l.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`};return(t,o)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"分会活动管理",color:"#ffffff",blod:!0}),b:e.t(e.unref(p)),c:e.t(e.unref(d)),d:e.t(e.unref(m)),e:e.o((e=>g("all"))),f:e.p({type:"all"===i.value?"primary":"info",size:"mini",customStyle:"border-radius: 30rpx; margin-right: 15rpx; width: 140rpx; height: 60rpx; font-size: 24rpx;",customTextStyle:"all"===i.value?"color: #ffffff":"color: #6AC086"}),g:e.o((e=>g("ongoing"))),h:e.p({type:"ongoing"===i.value?"primary":"info",size:"mini",customStyle:"border-radius: 30rpx; margin-right: 15rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;",customTextStyle:"ongoing"===i.value?"color: #ffffff":"color: #6AC086"}),i:e.o((e=>g("completed"))),j:e.p({type:"completed"===i.value?"primary":"info",size:"mini",customStyle:"border-radius: 30rpx; margin-right: 15rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;",customTextStyle:"completed"===i.value?"color: #ffffff":"color: #6AC086"}),k:e.o((e=>g("cancelled"))),l:e.p({type:"cancelled"===i.value?"primary":"info",size:"mini",customStyle:"border-radius: 30rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;",customTextStyle:"cancelled"===i.value?"color: #ffffff":"color: #6AC086"}),m:l.value.length>0},l.value.length>0?{n:e.f(l.value,((t,o,n)=>({a:t.img_url||"/static/default-activity.png",b:"13e0b2b2-5-"+n,c:e.p({text:h(t),type:x(t),size:"mini"}),d:e.t(t.name),e:e.t(t.title),f:"13e0b2b2-6-"+n,g:e.t(y(t.start_time)),h:"13e0b2b2-7-"+n,i:e.t(t.signup_count||0),j:e.t(t.organizer_name),k:"13e0b2b2-8-"+n,l:t.id,m:e.o((o=>(t=>{e.index.navigateTo({url:`/pages/bundle/index/activeInfo?id=${t.id}`})})(t)),t.id)}))),o:e.p({name:"clock",color:"#999",size:"24"}),p:e.p({name:"account",color:"#999",size:"24"}),q:e.p({name:"arrow-right",color:"#999",size:"24"})}:r.value?{}:{s:e.p({mode:"list",text:"暂无活动数据",textColor:"#999999",textSize:"28"})},{r:!r.value,t:s.value&&l.value.length>0},s.value&&l.value.length>0?{v:e.o(v),w:e.p({status:u.value})}:{})}},r=e._export_sfc(n,[["__scopeId","data-v-13e0b2b2"]]);wx.createPage(r);
//# sourceMappingURL=branch_activities.js.map
