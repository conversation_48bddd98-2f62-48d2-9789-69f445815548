{"version": 3, "file": "u-back-top.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-back-top/u-back-top.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWJhY2stdG9wL3UtYmFjay10b3AudnVl"], "sourcesContent": ["<template>\n\t<u-transition\n\t    mode=\"fade\"\n\t    :customStyle=\"backTopStyle\"\n\t    :show=\"show\"\n\t>\n\t\t<view\n\t\t    class=\"u-back-top\"\n\t\t\t:style=\"[contentStyle]\"\n\t\t    v-if=\"!$slots.default && !$slots.$default\"\n\t\t\t@click=\"backToTop\"\n\t\t>\n\t\t\t<u-icon\n\t\t\t    :name=\"icon\"\n\t\t\t    :custom-style=\"iconStyle\"\n\t\t\t></u-icon>\n\t\t\t<text\n\t\t\t    v-if=\"text\"\n\t\t\t    class=\"u-back-top__text\"\n\t\t\t>{{text}}</text>\n\t\t</view>\n\t\t<slot v-else />\n\t</u-transition>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, getPx, deepMerge, error } from '../../libs/function/index';\n\t// #ifdef APP-NVUE\n\tconst dom = weex.requireModule('dom')\n\t// #endif\n\t/**\n\t * backTop 返回顶部\n\t * @description 本组件一个用于长页面，滑动一定距离后，出现返回顶部按钮，方便快速返回顶部的场景。\n\t * @tutorial https://uview-plus.jiangruyi.com/components/backTop.html\n\t * \n\t * @property {String}\t\t\tmode  \t\t返回顶部的形状，circle-圆形，square-方形 （默认 'circle' ）\n\t * @property {String} \t\t\ticon \t\t自定义图标 （默认 'arrow-upward' ） 见官方文档示例\n\t * @property {String} \t\t\ttext \t\t提示文字 \n\t * @property {String | Number}  duration\t返回顶部滚动时间 （默认 100）\n\t * @property {String | Number}  scrollTop\t滚动距离 （默认 0 ）\n\t * @property {String | Number}  top  \t\t距离顶部多少距离显示，单位px （默认 400 ）\n\t * @property {String | Number}  bottom  \t返回顶部按钮到底部的距离，单位px （默认 100 ）\n\t * @property {String | Number}  right  \t\t返回顶部按钮到右边的距离，单位px （默认 20 ）\n\t * @property {String | Number}  zIndex \t\t层级   （默认 9 ）\n\t * @property {Object<Object>}  \ticonStyle \t图标的样式，对象形式   （默认 {color: '#909399',fontSize: '19px'}）\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * \n\t * @example <u-back-top :scrollTop=\"scrollTop\"></u-back-top>\n\t */\n\texport default {\n\t\tname: 'u-back-top',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tbackTopStyle() {\n\t\t\t\t// 动画组件样式\n\t\t\t\tconst style = {\n\t\t\t\t\tbottom: addUnit(this.bottom),\n\t\t\t\t\tright: addUnit(this.right),\n\t\t\t\t\twidth: '40px',\n\t\t\t\t\theight: '40px',\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tzIndex: 10,\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tshow() {\n\t\t\t\treturn getPx(this.scrollTop) > getPx(this.top)\n\t\t\t},\n\t\t\tcontentStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tlet radius = 0\n\t\t\t\t// 是否圆形\n\t\t\t\tif(this.mode === 'circle') {\n\t\t\t\t\tradius = '100px'\n\t\t\t\t} else {\n\t\t\t\t\tradius = '4px'\n\t\t\t\t}\n\t\t\t\t// 为了兼容安卓nvue，只能这么分开写\n\t\t\t\tstyle.borderTopLeftRadius = radius\n\t\t\t\tstyle.borderTopRightRadius = radius\n\t\t\t\tstyle.borderBottomLeftRadius = radius\n\t\t\t\tstyle.borderBottomRightRadius = radius\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\temits: [\"click\"],\n\t\tmethods: {\n\t\t\tbackToTop() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tif (!this.$parent.$refs['u-back-top']) {\n\t\t\t\t\terror(`nvue页面需要给页面最外层元素设置\"ref='u-back-top'`)\n\t\t\t\t}\n\t\t\t\tdom.scrollToElement(this.$parent.$refs['u-back-top'], {\n\t\t\t\t\toffset: 0\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\tscrollTop: 0,\n\t\t\t\t\tduration: this.duration\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\tthis.$emit('click')\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import '../../libs/css/components.scss';\n     $u-back-top-flex:1 !default;\n     $u-back-top-height:100% !default;\n     $u-back-top-background-color:#E1E1E1 !default;\n     $u-back-top-tips-font-size:12px !default;\n\t.u-back-top {\n\t\t@include flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tflex:$u-back-top-flex;\n\t\theight: $u-back-top-height;\n\t\tjustify-content: center;\n\t\tbackground-color: $u-back-top-background-color;\n\n\t\t&__tips {\n\t\t\tfont-size:$u-back-top-tips-font-size;\n\t\t\ttransform: scale(0.8);\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-back-top/u-back-top.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "computed", "backTopStyle", "bottom", "addUnit", "this", "right", "width", "height", "position", "zIndex", "show", "getPx", "scrollTop", "top", "contentStyle", "style", "radius", "mode", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "deepMerge", "addStyle", "customStyle", "emits", "methods", "backToTop", "uni", "pageScrollTo", "duration", "$emit", "wx", "createComponent", "Component"], "mappings": "6DAoDMA,EAAU,CACdC,KAAM,aACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,WACzBC,SAAU,CACT,YAAAC,GAUQ,MARO,CACbC,OAAQC,EAAAA,QAAQC,KAAKF,QACrBG,MAAOF,EAAAA,QAAQC,KAAKC,OACpBC,MAAO,OACPC,OAAQ,OACRC,SAAU,QACVC,OAAQ,GAGT,EACD,IAAAC,GACQC,OAAAA,EAAAA,MAAMP,KAAKQ,WAAaD,EAAKA,MAACP,KAAKS,IAC1C,EACD,YAAAC,GACC,MAAMC,EAAQ,CAAC,EACf,IAAIC,EAAS,EAYb,OATUA,EADO,WAAdZ,KAAKa,KACE,QAEA,MAGVF,EAAMG,oBAAsBF,EAC5BD,EAAMI,qBAAuBH,EAC7BD,EAAMK,uBAAyBJ,EAC/BD,EAAMM,wBAA0BL,EACzBM,EAASA,UAACP,EAAOQ,EAAQA,SAACnB,KAAKoB,aACvC,GAEDC,MAAO,CAAC,SACRC,QAAS,CACR,SAAAC,GAWCC,EAAAA,MAAIC,aAAa,CAChBjB,UAAW,EACXkB,SAAU1B,KAAK0B,WAGhB1B,KAAK2B,MAAM,QACZ,2jBC1GHC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}