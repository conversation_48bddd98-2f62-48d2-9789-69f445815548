"use strict";const e=require("../../../common/vendor.js"),t=require("../../../utils/auth.js"),o=require("../../../api/index.js"),r=require("../../../store/index.js");if(require("../../../store/counter.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-button")+e.resolveComponent("u-empty")+e.resolveComponent("u-loading-page"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-page/u-loading-page.js"))();const i={__name:"commission",setup(i){const n=e.ref([]),a=e.ref(!0),l=e.ref(!1),s=e.ref(!0),u=e.ref(1),c=e.ref("all"),m=e.ref("0.00"),p=e.ref("0.00");e.onLoad((()=>{t.requireLogin()&&v()})),e.onShow((()=>{y()}));const v=async(t=!1)=>{try{if(t)u.value=1,s.value=!0,a.value=!0;else{if(!s.value)return;a.value=!0}const i=r.store().$state.userInfo;if(!i||!i.uid||!i.token)return void e.index.showToast({title:"请先登录",icon:"none"});const l={uid:i.uid,token:i.token,page:u.value,page_size:20};"all"!==c.value&&(l.commission_type=c.value),console.log("加载佣金数据，参数:",l);const m=await o.branch_presidentget_commission(l);if(console.log("佣金数据API响应:",m),"ok"===m.status){const e=m.data||[];t?n.value=e:n.value.push(...e),s.value=20===e.length,t&&f()}else"empty"===m.status?(t&&(n.value=[]),s.value=!1):"relogin"===m.status?e.index.showToast({title:"登录已过期，请重新登录",icon:"none"}):e.index.showToast({title:m.msg||"加载失败",icon:"none"})}catch(i){console.error("加载佣金数据失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}finally{a.value=!1,l.value=!1}},d=e=>{if(!e)return"未知";const t=new Date(1e3*e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}`},f=()=>{const e=(new Date).toISOString().slice(0,7);let t=0,o=0;n.value.forEach((r=>{const i=parseFloat(r.money)||0;t+=i,r.time&&r.time.startsWith(e)&&(o+=i)})),m.value=t.toFixed(2),p.value=o.toFixed(2)},g=e=>{c.value!==e&&(c.value=e,y())},y=()=>{v(!0)},h=()=>{s.value&&!l.value&&(l.value=!0,u.value++,v())},x=e=>{switch(e){case"invite":default:return"邀请佣金";case"operation":return"运营佣金"}},j=e=>{switch(e){case"invite":default:return"type-invite";case"operation":return"type-operation"}},b=e=>e.remark&&e.remark.includes("系统分配用户运营佣金"),w=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})};return(t,o)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"运营佣金",color:"#ffffff",blod:!0}),b:e.t(m.value),c:e.t(p.value),d:e.o((e=>g("all"))),e:e.p({type:"all"===c.value?"primary":"info",size:"small",customStyle:"border-radius: 30rpx; margin-right: 20rpx;",customTextStyle:"all"===c.value?"color: #ffffff":"color: #6AC086"}),f:e.o((e=>g("invite"))),g:e.p({type:"invite"===c.value?"primary":"info",size:"small",customStyle:"border-radius: 30rpx; margin-right: 20rpx;",customTextStyle:"invite"===c.value?"color: #ffffff":"color: #6AC086"}),h:e.o((e=>g("operation"))),i:e.p({type:"operation"===c.value?"primary":"info",size:"small",customStyle:"border-radius: 30rpx;",customTextStyle:"operation"===c.value?"color: #ffffff":"color: #6AC086"}),j:n.value.length>0},n.value.length>0?{k:e.f(n.value,((t,o,r)=>e.e({a:e.t(x(t.commission_type)),b:e.n(j(t.commission_type)),c:e.t(t.money),d:e.t(w(t.time)),e:"operation"===t.commission_type},"operation"===t.commission_type?e.e({f:e.t(d(t.time)),g:b(t)},(b(t),{})):t.user&&t.user.nickname?{i:e.t(t.user.nickname)}:{},{h:t.user&&t.user.nickname,j:t.id})))}:a.value?{}:{m:e.p({mode:"list",text:"暂无佣金记录",textColor:"#999999",textSize:"28"})},{l:!a.value,n:s.value&&!a.value},s.value&&!a.value?{o:e.t(l.value?"加载中...":"加载更多"),p:e.o(h),q:e.p({type:"primary",loading:l.value,customStyle:"background: #6AC086; border: none; border-radius: 50rpx;"})}:{},{r:e.p({loading:a.value,"loading-text":"加载中...","bg-color":"#f8f9fa"})})}},n=e._export_sfc(i,[["__scopeId","data-v-321a664c"]]);wx.createPage(n);
//# sourceMappingURL=commission.js.map
