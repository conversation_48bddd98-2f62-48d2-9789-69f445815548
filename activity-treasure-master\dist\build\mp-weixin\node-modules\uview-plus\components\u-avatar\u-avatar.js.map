{"version": 3, "file": "u-avatar.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-avatar/u-avatar.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWF2YXRhci91LWF2YXRhci52dWU"], "sourcesContent": ["<template>\n\t<view\n\t\tclass=\"u-avatar\"\n\t\t:class=\"[`u-avatar--${shape}`]\"\n\t\t:style=\"[{\n\t\t\tbackgroundColor: (text || icon) ? (randomBgColor ? colors[colorIndex !== '' ? colorIndex : random(0, 19)] : bgColor) : 'transparent',\n\t\t\twidth: addUnit(size),\n\t\t\theight: addUnit(size),\n\t\t}, addStyle(customStyle)]\"\n\t\t@tap=\"clickHandler\"\n\t>\n\t\t<slot>\n\t\t\t<!-- #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU  -->\n\t\t\t<open-data\n\t\t\t\tv-if=\"mpAvatar && allowMp\"\n\t\t\t\ttype=\"userAvatarUrl\"\n\t\t\t\t:style=\"[{\n\t\t\t\t\twidth: addUnit(size),\n\t\t\t\t\theight: addUnit(size)\n\t\t\t\t}]\"\n\t\t\t/>\n\t\t\t<!-- #endif -->\n\t\t\t<!-- #ifndef MP-WEIXIN && MP-QQ && MP-BAIDU  -->\n\t\t\t<template v-if=\"mpAvatar && allowMp\"></template>\n\t\t\t<!-- #endif -->\n\t\t\t<u-icon\n\t\t\t\tv-else-if=\"icon\"\n\t\t\t\t:name=\"icon\"\n\t\t\t\t:size=\"fontSize\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t\t<up-text\n\t\t\t\tv-else-if=\"text\"\n\t\t\t\t:text=\"text\"\n\t\t\t\t:size=\"fontSize\"\n\t\t\t\t:color=\"color\"\n\t\t\t\talign=\"center\"\n\t\t\t\tcustomStyle=\"justify-content: center\"\n\t\t\t></up-text>\n\t\t\t<image\n\t\t\t\tclass=\"u-avatar__image\"\n\t\t\t\tv-else\n\t\t\t\t:class=\"[`u-avatar__image--${shape}`]\"\n\t\t\t\t:src=\"avatarUrl || defaultUrl\"\n\t\t\t\t:mode=\"mode\"\n\t\t\t\t@error=\"errorHandler\"\n\t\t\t\t:style=\"[{\n\t\t\t\t\twidth: addUnit(size),\n\t\t\t\t\theight: addUnit(size)\n\t\t\t\t}]\"\n\t\t\t></image>\n\t\t</slot>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, addUnit, random } from '../../libs/function/index';\n\tconst base64Avatar =\n\t\t\"data:image/jpg;base64,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\";\n\t/**\n\t * Avatar  头像\n\t * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\n\t * @tutorial https://ijry.github.io/uview-plus/components/avatar.html\n\t *\n\t * @property {String}\t\t\tsrc\t\t\t\t头像路径，如加载失败，将会显示默认头像(不能为相对路径)\n\t * @property {String}\t\t\tshape\t\t\t头像形状  （ circle (默认) | square）\n\t * @property {String | Number}\tsize\t\t\t头像尺寸，可以为指定字符串(large, default, mini)，或者数值 （默认 40 ）\n\t * @property {String}\t\t\tmode\t\t\t头像图片的裁剪类型，与uni的image组件的mode参数一致，如效果达不到需求，可尝试传widthFix值 （默认 'scaleToFill' ）\n\t * @property {String}\t\t\ttext\t\t\t用文字替代图片，级别优先于src\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色，一般显示文字时用 （默认 '#c0c4cc' ）\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色 （默认 '#ffffff' ）\n\t * @property {String | Number}\tfontSize\t\t文字大小  （默认 18 ）\n\t * @property {String}\t\t\ticon\t\t\t显示的图标\n\t * @property {Boolean}\t\t\tmpAvatar\t\t显示小程序头像，只对百度，微信，QQ小程序有效  （默认 false ）\n\t * @property {Boolean}\t\t\trandomBgColor\t是否使用随机背景色  （默认 false ）\n\t * @property {String}\t\t\tdefaultUrl\t\t加载失败的默认头像(组件有内置默认图片)\n\t * @property {String | Number}\tcolorIndex\t\t如果配置了randomBgColor为true，且配置了此值，则从默认的背景色数组中取出对应索引的颜色值，取值0-19之间\n\t * @property {String}\t\t\tname\t\t\t组件标识符  （默认 'level' ）\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t *\n\t * @event    {Function}        click       点击组件时触发   index: 用户传递的标识符\n\t * @example  <u-avatar :src=\"src\" mode=\"square\"></u-avatar>\n\t */\n\texport default {\n\t\tname: 'u-avatar',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 如果配置randomBgColor参数为true，在图标或者文字的模式下，会随机从中取出一个颜色值当做背景色\n\t\t\t\tcolors: ['#ffb34b', '#f2bba9', '#f7a196', '#f18080', '#88a867', '#bfbf39', '#89c152', '#94d554', '#f19ec2',\n\t\t\t\t\t'#afaae4', '#e1b0df', '#c38cc1', '#72dcdc', '#9acdcb', '#77b1cc', '#448aca', '#86cefa', '#98d1ee',\n\t\t\t\t\t'#73d1f1',\n\t\t\t\t\t'#80a7dc'\n\t\t\t\t],\n\t\t\t\tavatarUrl: this.src,\n\t\t\t\tallowMp: false\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 监听头像src的变化，赋值给内部的avatarUrl变量，因为图片加载失败时，需要修改图片的src为默认值\n\t\t\t// 而组件内部不能直接修改props的值，所以需要一个中间变量\n\t\t\tsrc: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.avatarUrl = newVal\n\t\t\t\t\t// 如果没有传src，则主动触发error事件，用于显示默认的头像，否则src为''空字符等的时候，会无内容展示\n\t\t\t\t\tif(!newVal) {\n\t\t\t\t\t\tthis.errorHandler()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\timageStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\treturn style\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.init()\n\t\t},\n\t\temits: [\"click\"],\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\trandom,\n\t\t\tinit() {\n\t\t\t\t// 目前只有这几个小程序平台具有open-data标签\n\t\t\t\t// 其他平台可以通过uni.getUserInfo类似接口获取信息，但是需要弹窗授权(首次)，不合符组件逻辑\n\t\t\t\t// 故目前自动获取小程序头像只支持这几个平台\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU\n\t\t\t\tthis.allowMp = true\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\t\tisImg() {\n\t\t\t\treturn this.src.indexOf('/') !== -1\n\t\t\t},\n\t\t\t// 图片加载时失败时触发\n\t\t\terrorHandler() {\n\t\t\t\tthis.avatarUrl = this.defaultUrl || base64Avatar\n\t\t\t},\n\t\t\tclickHandler() {\n\t\t\t\tthis.$emit('click', this.name)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-avatar {\n\t\t@include flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t&--circle {\n\t\t\tborder-radius: 100px;\n\t\t}\n\n\t\t&--square {\n\t\t\tborder-radius: 4px;\n\t\t}\n\n\t\t&__image {\n\t\t\t&--circle {\n\t\t\t\tborder-radius: 100px;\n                overflow: hidden;\n\t\t\t}\n\n\t\t\t&--square {\n\t\t\t\tborder-radius: 4px;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-avatar/u-avatar.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "colors", "avatarUrl", "this", "src", "allowMp", "watch", "immediate", "handler", "newVal", "<PERSON><PERSON><PERSON><PERSON>", "computed", "imageStyle", "created", "init", "emits", "methods", "addStyle", "addUnit", "random", "isImg", "indexOf", "defaultUrl", "clickHandler", "$emit", "wx", "createComponent", "Component"], "mappings": "6DAsFMA,EAAU,CACdC,KAAM,WACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,WACzB,IAAAC,GACQ,MAAA,CAENC,OAAQ,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAChG,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UACxF,UACA,WAEDC,UAAWC,KAAKC,IAChBC,SAAS,EAEV,EACDC,MAAO,CAGNF,IAAK,CACJG,WAAW,EACX,OAAAC,CAAQC,GACPN,KAAKD,UAAYO,EAEbA,GACHN,KAAKO,cAEP,IAGFC,SAAU,CACTC,WAAa,KACE,CAAC,IAIjB,OAAAC,GACCV,KAAKW,MACL,EACDC,MAAO,CAAC,SACRC,QAAS,CACRC,SAAAA,EAAQA,SACRC,QAAAA,EAAOA,QACPC,OAAAA,EAAMA,OACN,IAAAL,GAKCX,KAAKE,SAAU,CAEf,EAED,KAAAe,GACC,OAAiC,IAA1BjB,KAAKC,IAAIiB,QAAQ,IACxB,EAED,YAAAX,GACMP,KAAAD,UAAYC,KAAKmB,YAlFxB,ogHAmFE,EACD,YAAAC,GACMpB,KAAAqB,MAAM,QAASrB,KAAKR,KAC1B,skCClJH8B,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}