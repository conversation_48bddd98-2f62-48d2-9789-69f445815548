<view class="diary-detail-container data-v-522e305b"><view class="diary-header data-v-522e305b"><view class="back-button data-v-522e305b" bindtap="{{b}}"><u-icon wx:if="{{a}}" class="data-v-522e305b" u-i="522e305b-0" bind:__l="__l" u-p="{{a}}"></u-icon></view><text class="header-title data-v-522e305b">日记详情</text></view><view wx:if="{{c}}" class="loading-state data-v-522e305b"><u-loading-icon wx:if="{{d}}" class="data-v-522e305b" u-i="522e305b-1" bind:__l="__l" u-p="{{d}}"></u-loading-icon></view><view wx:if="{{e}}" class="background-image data-v-522e305b"><image wx:if="{{f}}" src="{{g}}" class="full-background-image data-v-522e305b" mode="aspectFill"></image><view wx:else class="white-background data-v-522e305b"></view></view><view wx:if="{{h}}" class="content-overlay data-v-522e305b"><view class="user-info-top data-v-522e305b"><image class="user-avatar-small data-v-522e305b" src="{{i}}" mode="aspectFill"></image><view class="user-meta-overlay data-v-522e305b"><text class="user-nickname-overlay data-v-522e305b">{{j}}</text><text class="post-time-overlay data-v-522e305b">{{k}}</text></view><view wx:if="{{l}}" class="action-buttons-overlay data-v-522e305b"><view class="edit-btn-overlay data-v-522e305b" bindtap="{{n}}"><u-icon wx:if="{{m}}" class="data-v-522e305b" u-i="522e305b-2" bind:__l="__l" u-p="{{m}}"></u-icon></view><view class="delete-btn-overlay data-v-522e305b" bindtap="{{p}}"><u-icon wx:if="{{o}}" class="data-v-522e305b" u-i="522e305b-3" bind:__l="__l" u-p="{{o}}"></u-icon></view></view></view><view class="{{['content-text-below', 'data-v-522e305b', w && 'has-background']}}"><text class="diary-text-overlay data-v-522e305b">{{q}}</text><view wx:if="{{r}}" class="location-info-inline data-v-522e305b"><u-icon wx:if="{{s}}" class="data-v-522e305b" u-i="522e305b-4" bind:__l="__l" u-p="{{s}}"></u-icon><text class="{{['location-text-inline', 'data-v-522e305b', v && 'white-text']}}">{{t}}</text></view></view><view wx:if="{{x}}" class="tags-container-overlay data-v-522e305b"><view wx:for="{{y}}" wx:for-item="tag" wx:key="b" class="{{['tag-item-overlay', 'data-v-522e305b', A && 'has-background']}}"><text class="{{['tag-text-overlay', 'data-v-522e305b', z && 'white-text']}}">#{{tag.a}}</text></view></view><view wx:if="{{B}}" class="images-horizontal data-v-522e305b"><view wx:for="{{C}}" wx:for-item="img" wx:key="b" class="image-item-horizontal data-v-522e305b" bindtap="{{img.c}}"><image src="{{img.a}}" mode="aspectFill" class="diary-image-horizontal data-v-522e305b"></image></view></view></view><view wx:else class="error-state data-v-522e305b"><u-icon wx:if="{{D}}" class="data-v-522e305b" u-i="522e305b-5" bind:__l="__l" u-p="{{D}}"></u-icon><text class="error-text data-v-522e305b">加载失败，请重试</text><view class="retry-button data-v-522e305b" bindtap="{{E}}"><text class="retry-text data-v-522e305b">重新加载</text></view></view></view>