"use strict";const o=require("../../../../common/vendor.js"),t={name:"u-loadmore",mixins:[o.mpMixin,o.mixin,o.props$37],data:()=>({dotText:"●"}),computed:{loadTextStyle(){return{color:this.color,fontSize:o.addUnit(this.fontSize),lineHeight:o.addUnit(this.fontSize),backgroundColor:this.bgColor}},showText(){let o="";return o="loadmore"==this.status?this.loadmoreText:"loading"==this.status?this.loadingText:"nomore"==this.status&&this.isDot?this.dotText:this.nomoreText,o}},emits:["loadmore"],methods:{addStyle:o.addStyle,addUnit:o.addUnit,loadMore(){"loadmore"==this.status&&this.$emit("loadmore")}}};if(!Array){(o.resolveComponent("u-line")+o.resolveComponent("u-loading-icon"))()}Math||((()=>"../u-line/u-line.js")+(()=>"../u-loading-icon/u-loading-icon.js"))();const e=o._export_sfc(t,[["render",function(t,e,i,n,a,d){return o.e({a:t.line},t.line?{b:o.p({length:"140rpx",color:t.lineColor,hairline:!1,dashed:t.dashed})}:{},{c:"loading"===t.status&&t.icon},"loading"===t.status&&t.icon?{d:o.p({color:t.iconColor,size:t.iconSize,mode:t.loadingIcon})}:{},{e:o.t(d.showText),f:o.s(d.loadTextStyle),g:o.n("nomore"==t.status&&1==t.isDot?"u-loadmore__content__dot-text":"u-loadmore__content__text"),h:o.o(((...o)=>d.loadMore&&d.loadMore(...o))),i:o.n("loadmore"==t.status||"nomore"==t.status?"u-more":""),j:t.line},t.line?{k:o.p({length:"140rpx",color:t.lineColor,hairline:!1,dashed:t.dashed})}:{},{l:o.s(d.addStyle(t.customStyle)),m:o.s({backgroundColor:t.bgColor,marginBottom:d.addUnit(t.marginBottom),marginTop:d.addUnit(t.marginTop),height:d.addUnit(t.height)})})}],["__scopeId","data-v-2b0468aa"]]);wx.createComponent(e);
//# sourceMappingURL=u-loadmore.js.map
