"use strict";const e=require("../../../../common/vendor.js"),o={name:"u-button",mixins:[e.mpMixin,e.mixin,e.buttonMixin,e.openType,e.props$10],data:()=>({}),computed:{bemClass(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor(){return this.plain?this.color?this.color:e.color[`u-${this.type}`]:"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor(){let e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle(){let e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize(){let e=14,{size:o}=this;return"large"===o&&(e=16),"normal"===o&&(e=14),"small"===o&&(e=12),"mini"===o&&(e=10),e}},emits:["click","getphonenumber","getuserinfo","error","opensetting","launchapp","agreeprivacyauthorization"],methods:{addStyle:e.addStyle,clickHandler(o){this.disabled||this.loading||e.throttle((()=>{this.$emit("click",o)}),this.throttleTime),this.stop&&this.preventEvent(o)},getphonenumber(e){this.$emit("getphonenumber",e)},getuserinfo(e){this.$emit("getuserinfo",e)},error(e){this.$emit("error",e)},opensetting(e){this.$emit("opensetting",e)},launchapp(e){this.$emit("launchapp",e)},agreeprivacyauthorization(e){this.$emit("agreeprivacyauthorization",e)}}};if(!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon"))()}Math||((()=>"../u-loading-icon/u-loading-icon.js")+(()=>"../u-icon/u-icon.js"))();const t=e._export_sfc(o,[["render",function(o,t,i,r,n,s){return e.e({a:o.loading},o.loading?{b:e.p({mode:o.loadingMode,size:1.15*o.loadingSize,color:s.loadingColor}),c:e.t(o.loadingText||o.text),d:e.s({fontSize:s.textSize+"px"})}:e.e({e:o.icon},o.icon?{f:e.p({name:o.icon,color:s.iconColorCom,size:1.35*s.textSize,customStyle:{marginRight:"2px"}})}:{},{g:e.t(o.text),h:e.s({fontSize:s.textSize+"px"})}),{i:Number(o.hoverStartTime),j:Number(o.hoverStayTime),k:o.formType,l:o.openType,m:o.appParameter,n:o.hoverStopPropagation,o:o.sendMessageTitle,p:o.sendMessagePath,q:o.lang,r:o.dataName,s:o.sessionFrom,t:o.sendMessageImg,v:o.showMessageCard,w:e.o(((...e)=>s.getphonenumber&&s.getphonenumber(...e))),x:e.o(((...e)=>s.getuserinfo&&s.getuserinfo(...e))),y:e.o(((...e)=>s.error&&s.error(...e))),z:e.o(((...e)=>s.opensetting&&s.opensetting(...e))),A:e.o(((...e)=>s.launchapp&&s.launchapp(...e))),B:e.o(((...e)=>s.agreeprivacyauthorization&&s.agreeprivacyauthorization(...e))),C:o.disabled||o.loading?"":"u-button--active",D:e.s(s.baseColor),E:e.s(s.addStyle(o.customStyle)),F:e.o(((...e)=>s.clickHandler&&s.clickHandler(...e))),G:e.n(s.bemClass)})}],["__scopeId","data-v-aa49867e"]]);wx.createComponent(t);
//# sourceMappingURL=u-button.js.map
