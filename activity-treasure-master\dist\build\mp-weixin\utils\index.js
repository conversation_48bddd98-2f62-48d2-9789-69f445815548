"use strict";const e=require("../common/vendor.js"),t=require("../store/index.js"),o=require("../api/index.js"),s=require("./systemInfo.js");exports.back=t=>{(null==t?void 0:t.tip)&&e.index.$u.toast(t.tip),setTimeout((()=>{e.index.navigateBack({delta:(null==t?void 0:t.num)||1})}),(null==t?void 0:t.time)||3e3)},exports.callPhone=t=>e.index.makePhoneCall({phoneNumber:t,success:e=>{},fail:t=>{e.index.$u.toast("联系失败")}}),exports.copy=t=>e.index.setClipboardData({data:t+"",success:function(){e.index.$u.toast("复制成功")}}),exports.getItem=(e,t)=>e[t],exports.getListHeight=async(t="list")=>{const{proxy:o}=e.getCurrentInstance();return await o.$u.getRect("."+t)},exports.getUserInfo=async e=>{try{const i=await o.userget_user_info();if(console.log("获取用户信息响应:",i),i&&"ok"===i.status&&i.data)t.store().changeUserInfo(i.data),console.log("用户信息已更新到store:",i.data);else if(i&&"relogin"===i.status){console.log("用户认证失败，清理登录状态:",i.msg);try{const{handleAuthFailure:e}=await"./auth.js";e(i,"getUserInfo")}catch(s){console.error("处理认证失败时出错:",s);try{const{logout:e}=await"./auth.js";e()}catch(n){console.error("备用登出处理失败:",n)}}}else console.log("获取用户信息失败:",i);e&&"function"==typeof e&&e()}catch(s){console.error("获取用户信息异常:",s)}},exports.login=s=>{e.index.login({provider:"weixin",success:async n=>{let i="";try{const t=await new Promise(((t,o)=>{e.index.getLocation({type:"gcj02",success:t,fail:o})}));if(t&&t.latitude&&t.longitude){const n=await o.getAddr({latitude:t.latitude,longitude:t.longitude});if(n&&1==n.status){const t=n.regeocode.addressComponent;if(i=e.index.$u.test.isEmpty(t.city)?t.province:t.city,t.adcode&&t.adcode.length>=4){const e=t.adcode.substring(0,2)+"0000",o={11e4:"110100",12e4:"120100",31e4:"310100",5e5:"500100"};o[e]?s.city_id=o[e]:s.city_id=t.adcode.substring(0,4)+"00"}}}}catch(a){console.log("获取城市信息失败，将使用空值注册:",a)}"ok"===(await o.userlogin({code:n.code,pid:t.store().$state.pid,city:i})).status?s.options?s.fun(s.options):s.fun():e.index.$u.toast("登录失败")}})},exports.navto=(t,o="nav")=>{switch(o){case"nav":e.index.navigateTo({url:t});break;case"tab":e.index.switchTab({url:t});break;case"red":e.index.redirectTo({url:t});break;case"rel":e.index.reLaunch({url:t})}},exports.pay=t=>new Promise(((o,s)=>{e.index.requestPayment({provider:t.provider,timeStamp:t.timeStamp.toString(),nonceStr:t.nonceStr,package:t.package,signType:t.signType,paySign:t.paySign,success:function(e){o(e)},fail:function(e){o(e)}})})),exports.pxToRpx=e=>{try{return s.pxToRpx(e)}catch(t){return console.warn("pxToRpx转换失败，使用默认值:",t),750*Number.parseInt(e)/375}},exports.setListHeight=async(t="list")=>{const{proxy:o}=e.getCurrentInstance(),s=await o.$u.getRect("."+t);return e.index.$u.sys().windowHeight-s.top};
//# sourceMappingURL=index.js.map
