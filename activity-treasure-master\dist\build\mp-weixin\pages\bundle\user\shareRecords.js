"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),o=require("../../../store/index.js"),a=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/cacheManager.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||(i+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const i=()=>"../../../components/myTitle.js",l={__name:"shareRecords",setup(i){const l=e.ref([]),u=e.ref(!1),r=e.ref(!1),s=e.ref(1),n=e.ref(0);e.onLoad((()=>{d()})),e.onShow((()=>{c()})),e.onPullDownRefresh((()=>{c()})),e.onReachBottom((()=>{r.value||u.value||v()}));const d=async(i=!1)=>{var d,c;if(!u.value&&a.requireLogin("","请先登录后查看分享记录")){u.value=!0;try{const a=await t.userget_share_records({uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token,page:i?1:s.value,page_size:20});if("ok"===(null==a?void 0:a.status)){const e=(null==(d=a.data)?void 0:d.list)||[];i?(l.value=e,s.value=1,r.value=!1):l.value=[...l.value,...e],n.value=(null==(c=a.data)?void 0:c.total)||0,e.length<20?r.value=!0:s.value++}else e.index.$u.toast((null==a?void 0:a.msg)||"获取分享记录失败")}catch(v){console.error("获取分享记录失败:",v),e.index.$u.toast("获取分享记录失败")}finally{u.value=!1,e.index.stopPullDownRefresh()}}},c=()=>{s.value=1,r.value=!1,d(!0)},v=()=>{d(!1)},g=e=>{if(!e)return"";const t=e.replace(/-/g,"/"),o=new Date(t);return isNaN(o.getTime())?"时间格式错误":o.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},h=e=>({0:"#FF9500",1:"#6AC086",2:"#999"}[e]||"#999");return(t,o)=>e.e({a:e.p({title:"分享记录",bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",color:"#ffffff",blod:!0}),b:n.value>0},n.value>0?{c:e.t(n.value)}:{},{d:l.value.length>0},l.value.length>0?{e:e.f(l.value,((t,o,a)=>{return e.e({a:e.t(g(t.share_time)),b:e.t((i=t.status,{0:"未领取",1:"已领取",2:"已过期"}[i]||"未知")),c:h(t.status),d:e.t(t.trial_days),e:e.t(t.share_code),f:t.expire_time},t.expire_time?{g:e.t(g(t.expire_time))}:{},{h:t.receive_time},t.receive_time?{i:e.t(g(t.receive_time))}:{},{j:0===t.status},0===t.status?{k:e.o((o=>(t=>{var o,a;if(!t.share_code)return void e.index.$u.toast("分享码不存在");const i=(null==(o=null==window?void 0:window.location)?void 0:o.host)||"your-domain.com",l=`${(null==(a=null==window?void 0:window.location)?void 0:a.protocol)||"https:"}//${i}/pages/bundle/user/trialClaim?code=${t.share_code}`;e.index.setClipboardData({data:l,success:()=>{e.index.$u.toast("分享链接已复制到剪贴板")},fail:()=>{e.index.$u.toast("复制失败")}})})(t)),t.id)}:{},{l:t.id});var i}))}:u.value?{}:{g:e.p({name:"share-square",color:"#ccc",size:"120rpx"})},{f:!u.value,h:u.value&&0===l.value.length},u.value&&0===l.value.length?{i:e.p({mode:"circle",color:"#6AC086",size:"60rpx"})}:{},{j:u.value&&l.value.length>0},u.value&&l.value.length>0?{k:e.p({mode:"circle",color:"#6AC086",size:"40rpx"})}:{},{l:r.value&&l.value.length>0},(r.value&&l.value.length,{}))}};wx.createPage(l);
//# sourceMappingURL=shareRecords.js.map
