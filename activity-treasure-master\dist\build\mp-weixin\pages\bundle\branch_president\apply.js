"use strict";const e=require("../../../common/vendor.js"),o=require("../../../utils/index.js"),n=require("../../../utils/auth.js"),a=require("../../../api/index.js"),r=require("../../../store/index.js");if(require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-input")+e.resolveComponent("u-form-item")+e.resolveComponent("u-icon")+e.resolveComponent("u-textarea")+e.resolveComponent("u-form")+e.resolveComponent("u-button"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../node-modules/uview-plus/components/u-form-item/u-form-item.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../node-modules/uview-plus/components/u-form/u-form.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js"))();const t={__name:"apply",setup(t){const i=e.reactive({branch_name:"",branch_location:"",nickname:"",wechat_qr_image:"",branch_description:"",application_reason:""}),s=e.reactive({branch_name:[{required:!0,message:"请输入分会名称",trigger:["blur","change"]},{min:2,max:100,message:"分会名称长度应在2-100字符之间",trigger:["blur","change"]}],branch_location:[{required:!0,message:"请输入分会地区",trigger:["blur","change"]},{max:200,message:"地区名称不能超过200字符",trigger:["blur","change"]}],nickname:[{required:!0,message:"请输入申请人昵称",trigger:["blur","change"]},{min:2,max:20,message:"昵称长度应在2-20个字符之间",trigger:["blur","change"]}],wechat_qr_image:[{required:!0,message:"请上传微信二维码",trigger:["blur","change"]}],application_reason:[{required:!0,message:"请输入申请理由",trigger:["blur","change"]},{min:50,max:500,message:"申请理由应在50-500字符之间",trigger:["blur","change"]}]}),l=e.ref(),c=e.ref(!1);e.onLoad((()=>{if(!n.requireLogin())return;const o=r.store().$state.userInfo;"1"!==(null==o?void 0:o.role_type)||e.index.showModal({title:"提示",content:"您已经是分会长，无需重复申请",showCancel:!1,success:()=>{e.index.navigateBack()}})}));const u=async()=>{try{if(!(await l.value.validate()))return;c.value=!0;const n=r.store().$state.userInfo,t={uid:n.uid,token:n.token,...i},s=await a.branch_presidentapply(t);"ok"===s.status?(e.index.showToast({title:"申请提交成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)):"relogin"===s.status?(e.index.showToast({title:"登录已过期，请重新登录",icon:"none"}),o.navto("/pages/bundle/common/login")):e.index.showToast({title:s.msg||"申请失败",icon:"none"})}catch(n){console.error("申请失败:",n),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}finally{c.value=!1}},p=()=>{e.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:o=>{const n=o.tempFilePaths[0],a=n.split(".").pop().toLowerCase();["jpg","jpeg","png"].includes(a)?m(n):e.index.showToast({title:"请选择jpg或png格式的图片",icon:"none"})},fail:o=>{console.error("选择图片失败:",o),e.index.showToast({title:"选择图片失败",icon:"none"})}})},m=o=>{e.index.showLoading({title:"上传中..."});const n=r.store().$state.userInfo;e.index.uploadFile({url:`${r.store().$state.url}config/upload_img`,filePath:o,name:"img",formData:{uid:n.uid,token:n.token,type:"wechat_qr"},success:o=>{try{const n=JSON.parse(o.data);"ok"===n.status?(i.wechat_qr_image=n.data||n.url,e.index.showToast({title:"上传成功",icon:"success"})):e.index.showToast({title:n.msg||"上传失败",icon:"none"})}catch(n){console.error("解析上传结果失败:",n),console.error("上传响应数据:",o.data),e.index.showToast({title:"上传失败",icon:"none"})}},fail:o=>{console.error("上传失败:",o),e.index.showToast({title:"上传失败，请重试",icon:"none"})},complete:()=>{e.index.hideLoading()}})},d=()=>{e.index.previewImage({urls:[i.wechat_qr_image],current:i.wechat_qr_image})},g=()=>{e.index.showModal({title:"确认删除",content:"确定要删除这张微信二维码图片吗？",success:o=>{o.confirm&&(i.wechat_qr_image="",e.index.showToast({title:"已删除",icon:"success"}))}})};return(o,n)=>e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"申请成为分会长",color:"#ffffff",blod:!0}),b:e.o((e=>i.branch_name=e)),c:e.p({placeholder:"请输入分会名称（2-100字符）",maxlength:"100",clearable:!0,modelValue:i.branch_name}),d:e.p({label:"分会名称",prop:"branch_name",required:!0}),e:e.o((e=>i.branch_location=e)),f:e.p({placeholder:"请输入分会所在地区",maxlength:"200",clearable:!0,modelValue:i.branch_location}),g:e.p({label:"分会地区",prop:"branch_location",required:!0}),h:e.o((e=>i.nickname=e)),i:e.p({placeholder:"请输入您的昵称（2-20个字符）",maxlength:"20",clearable:!0,modelValue:i.nickname}),j:e.p({label:"申请人昵称",prop:"nickname",required:!0}),k:!i.wechat_qr_image},i.wechat_qr_image?{n:i.wechat_qr_image,o:e.o(d),p:e.p({name:"eye",size:"40rpx",color:"#fff"}),q:e.o(g),r:e.p({name:"trash",size:"40rpx",color:"#fff"}),s:e.o(d)}:{l:e.p({name:"camera",size:"60rpx",color:"#6AC086"}),m:e.o(p)},{t:e.p({label:"微信二维码",prop:"wechat_qr_image",required:!0}),v:e.o((e=>i.branch_description=e)),w:e.p({placeholder:"请简要描述分会的特色和定位",maxlength:"500",count:!0,height:"120rpx",modelValue:i.branch_description}),x:e.p({label:"分会描述",prop:"branch_description"}),y:e.o((e=>i.application_reason=e)),z:e.p({placeholder:"请详细说明您申请成为分会长的理由（至少50字）",maxlength:"500",count:!0,height:"200rpx",modelValue:i.application_reason}),A:e.p({label:"申请理由",prop:"application_reason",required:!0}),B:e.sr(l,"dab81d3b-1",{k:"formRef"}),C:e.p({model:i,rules:s,labelPosition:"top",labelWidth:"auto"}),D:e.t(c.value?"提交中...":"提交申请"),E:e.o(u),F:e.p({type:"primary",loading:c.value,disabled:c.value,customStyle:"background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%); border: none; border-radius: 50rpx; height: 88rpx; font-size: 32rpx;"})})}},i=e._export_sfc(t,[["__scopeId","data-v-dab81d3b"]]);wx.createPage(i);
//# sourceMappingURL=apply.js.map
