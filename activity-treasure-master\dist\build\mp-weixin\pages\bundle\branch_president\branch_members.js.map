{"version": 3, "file": "branch_members.js", "sources": ["../../../../../../src/pages/bundle/branch_president/branch_members.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGJyYW5jaF9wcmVzaWRlbnRcYnJhbmNoX21lbWJlcnMudnVl"], "sourcesContent": ["<template>\n  <view class=\"page branch-management\">\n    <myTitle\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      title=\"分会成员管理\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <!-- 统计卡片 -->\n      <view class=\"stats-card\">\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ totalMembers }}</view>\n          <view class=\"stats-label\">总成员数</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ activeMembers }}</view>\n          <view class=\"stats-label\">活跃成员</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ newMembers }}</view>\n          <view class=\"stats-label\">本月新增</view>\n        </view>\n      </view>\n      \n      <!-- 搜索栏 -->\n      <view class=\"search-container\">\n        <u-search\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索成员昵称或手机号\"\n          :show-action=\"false\"\n          bg-color=\"#f5f5f5\"\n          border-color=\"transparent\"\n          @search=\"handleSearch\"\n          @custom=\"handleSearch\"\n        ></u-search>\n      </view>\n      \n      <!-- 筛选器 -->\n      <view class=\"filter-container\">\n        <u-button\n          :type=\"filterType === 'all' ? 'primary' : 'info'\"\n          size=\"small\"\n          @click=\"setFilter('all')\"\n          customStyle=\"border-radius: 30rpx; margin-right: 20rpx;\"\n          :customTextStyle=\"filterType === 'all' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          全部成员\n        </u-button>\n        <u-button\n          :type=\"filterType === 'active' ? 'primary' : 'info'\"\n          size=\"small\"\n          @click=\"setFilter('active')\"\n          customStyle=\"border-radius: 30rpx; margin-right: 20rpx;\"\n          :customTextStyle=\"filterType === 'active' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          活跃成员\n        </u-button>\n        <u-button\n          :type=\"filterType === 'new' ? 'primary' : 'info'\"\n          size=\"small\"\n          @click=\"setFilter('new')\"\n          customStyle=\"border-radius: 30rpx;\"\n          :customTextStyle=\"filterType === 'new' ? 'color: #ffffff' : 'color: #6AC086'\"\n        >\n          新成员\n        </u-button>\n      </view>\n      \n      <!-- 成员列表 -->\n      <view class=\"member-list\" v-if=\"memberList.length > 0\">\n        <view \n          class=\"member-item\"\n          v-for=\"member in memberList\"\n          :key=\"member.uid\"\n          @click=\"viewMemberDetail(member)\"\n        >\n          <view class=\"member-avatar\">\n            <u-avatar\n              :src=\"member.avatar || '/static/default-avatar.png'\"\n              size=\"80\"\n              shape=\"circle\"\n            ></u-avatar>\n            <view class=\"member-status\" v-if=\"member.is_active\">\n              <u-icon name=\"checkmark-circle-fill\" color=\"#6AC086\" size=\"20\"></u-icon>\n            </view>\n          </view>\n          \n          <view class=\"member-info\">\n            <view class=\"member-name-container\">\n              <text class=\"member-name\">{{ member.nickname || '未设置昵称' }}</text>\n              <!-- 🆕 新增：系统分配用户标识 -->\n              <text class=\"system-assigned-badge\" v-if=\"isSystemAssignedUser(member)\">系统分配用户</text>\n            </view>\n            <view class=\"member-phone\">{{ formatPhone(member.mobile) }}</view>\n            <view class=\"member-meta\">\n              <text class=\"join-time\">{{ formatJoinTime(member.time) }}</text>\n              <text class=\"activity-count\">参与{{ member.activity_count || 0 }}次活动</text>\n            </view>\n          </view>\n          \n          <view class=\"member-actions\">\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"24\"></u-icon>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-else-if=\"!loading\">\n        <u-empty\n          mode=\"list\"\n          text=\"暂无成员数据\"\n          textColor=\"#999999\"\n          textSize=\"28\"\n        ></u-empty>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMore && memberList.length > 0\">\n        <u-loadmore \n          :status=\"loadStatus\"\n          @loadmore=\"loadMore\"\n        ></u-loadmore>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';\nimport { store } from '@/store';\nimport { branch_presidentget_members } from '@/api';\n\n// 响应式数据\nconst loading = ref(false);\nconst memberList = ref([]);\nconst searchKeyword = ref('');\nconst filterType = ref('all');\nconst currentPage = ref(1);\nconst hasMore = ref(true);\nconst loadStatus = ref('loadmore');\nconst pageSize = 20;\n\n// 统计数据\nconst stats = reactive({\n  total: 0,\n  active: 0,\n  new: 0\n});\n\n// 计算属性\nconst totalMembers = computed(() => stats.total);\nconst activeMembers = computed(() => stats.active);\nconst newMembers = computed(() => stats.new);\n\n// 页面加载\nonLoad(() => {\n  loadMembers(true);\n});\n\n// 下拉刷新\nonPullDownRefresh(() => {\n  loadMembers(true);\n  setTimeout(() => {\n    uni.stopPullDownRefresh();\n  }, 1000);\n});\n\n// 上拉加载更多\nonReachBottom(() => {\n  if (hasMore.value && !loading.value) {\n    loadMore();\n  }\n});\n\n// 加载成员列表\nconst loadMembers = async (isRefresh = false) => {\n  try {\n    if (isRefresh) {\n      currentPage.value = 1;\n      hasMore.value = true;\n      loading.value = true;\n      loadStatus.value = 'loading';\n    } else if (!hasMore.value) {\n      return;\n    }\n    \n    const userInfo = store().$state.userInfo;\n    const res = await branch_presidentget_members({\n      uid: userInfo.uid,\n      token: userInfo.token,\n      page: currentPage.value,\n      page_size: pageSize,\n      keyword: searchKeyword.value,\n      filter: filterType.value\n    });\n    \n    if (res.status === 'ok') {\n      const newMembers = res.data || [];\n      \n      if (isRefresh) {\n        memberList.value = newMembers;\n      } else {\n        memberList.value.push(...newMembers);\n      }\n      \n      // 更新统计数据\n      if (res.stats) {\n        stats.total = res.stats.total || 0;\n        stats.active = res.stats.active || 0;\n        stats.new = res.stats.new || 0;\n      }\n      \n      // 判断是否还有更多数据\n      hasMore.value = newMembers.length === pageSize;\n      currentPage.value++;\n      \n      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';\n      \n    } else if (res.status === 'relogin') {\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n    } else {\n      uni.showToast({\n        title: res.msg || '加载失败',\n        icon: 'none'\n      });\n      loadStatus.value = 'loadmore';\n    }\n  } catch (error) {\n    console.error('加载成员列表失败:', error);\n    uni.showToast({\n      title: '网络错误，请重试',\n      icon: 'none'\n    });\n    loadStatus.value = 'loadmore';\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 搜索处理\nconst handleSearch = () => {\n  loadMembers(true);\n};\n\n// 设置筛选器\nconst setFilter = (type) => {\n  filterType.value = type;\n  loadMembers(true);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (!loading.value && hasMore.value) {\n    loadStatus.value = 'loading';\n    loadMembers(false);\n  }\n};\n\n// 查看成员详情\nconst viewMemberDetail = (member) => {\n  uni.showModal({\n    title: '成员详情',\n    content: `昵称：${member.nickname}\\n手机：${formatPhone(member.mobile)}\\n加入时间：${formatJoinTime(member.time)}\\n参与活动：${member.activity_count || 0}次`,\n    showCancel: false,\n    confirmText: '知道了'\n  });\n};\n\n// 🆕 新增：判断是否为系统分配用户\nconst isSystemAssignedUser = (member) => {\n  return member.assignment_type === 'system';\n};\n\n// 格式化手机号\nconst formatPhone = (phone) => {\n  if (!phone) return '未绑定';\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n};\n\n// 格式化加入时间\nconst formatJoinTime = (timestamp) => {\n  if (!timestamp) return '未知';\n  const date = new Date(timestamp * 1000);\n  const now = new Date();\n  const diff = now - date;\n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  \n  if (days === 0) return '今天加入';\n  if (days === 1) return '昨天加入';\n  if (days < 30) return `${days}天前加入`;\n  if (days < 365) return `${Math.floor(days / 30)}个月前加入`;\n  return `${Math.floor(days / 365)}年前加入`;\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/style/wcag-colors.scss';\n.page {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.content-container {\n  padding: 30rpx;\n}\n\n.stats-card {\n  display: flex;\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 40rpx 20rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(106, 192, 134, 0.1);\n}\n\n.stats-item {\n  flex: 1;\n  text-align: center;\n  \n  .stats-number {\n    font-size: 48rpx;\n    font-weight: bold;\n    color: #6AC086;\n    margin-bottom: 10rpx;\n  }\n  \n  .stats-label {\n    font-size: 24rpx;\n    color: #666666; /* 提高对比度，从#999改为#666666 */\n  }\n}\n\n.search-container {\n  margin-bottom: 30rpx;\n}\n\n.filter-container {\n  display: flex;\n  margin-bottom: 30rpx;\n}\n\n.member-list {\n  .member-item {\n    display: flex;\n    align-items: center;\n    background: #ffffff;\n    border-radius: 20rpx;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n    \n    .member-avatar {\n      position: relative;\n      margin-right: 30rpx;\n      \n      .member-status {\n        position: absolute;\n        bottom: -5rpx;\n        right: -5rpx;\n        background: #ffffff;\n        border-radius: 50%;\n        padding: 2rpx;\n      }\n    }\n    \n    .member-info {\n      flex: 1;\n      \n      .member-name-container {\n        display: flex;\n        align-items: center;\n        margin-bottom: 10rpx;\n      }\n\n      .member-name {\n        font-size: 32rpx;\n        font-weight: bold;\n        color: #1a1a1a; /* 提高对比度 */\n        margin-right: 16rpx;\n      }\n\n      // 🆕 新增：系统分配用户标识样式\n      .system-assigned-badge {\n        background: linear-gradient(135deg, #FF9800, #FF5722);\n        color: white;\n        font-size: 20rpx;\n        padding: 4rpx 12rpx;\n        border-radius: 20rpx;\n        font-weight: bold;\n        position: relative;\n\n        &::before {\n          content: '🤖';\n          margin-right: 4rpx;\n        }\n      }\n\n      .member-phone {\n        font-size: 28rpx;\n        color: #4a4a4a; /* 提高对比度，从#666改为#4a4a4a */\n        margin-bottom: 10rpx;\n      }\n\n      .member-meta {\n        display: flex;\n        font-size: 24rpx;\n        color: #666666; /* 提高对比度，从#999改为#666666 */\n        \n        .join-time {\n          margin-right: 30rpx;\n        }\n      }\n    }\n    \n    .member-actions {\n      padding: 10rpx;\n    }\n  }\n}\n\n.empty-state {\n  padding: 100rpx 0;\n}\n\n.load-more {\n  padding: 30rpx 0;\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/branch_president/branch_members.vue'\nwx.createPage(MiniProgramPage)"], "names": ["loading", "ref", "memberList", "searchKeyword", "filterType", "currentPage", "hasMore", "loadStatus", "stats", "reactive", "total", "active", "new", "totalMembers", "computed", "activeMembers", "newMembers", "onLoad", "loadMembers", "onPullDownRefresh", "setTimeout", "uni", "index", "stopPullDownRefresh", "onReachBottom", "value", "async", "isRefresh", "userInfo", "store", "$state", "res", "branch_presidentget_members", "uid", "token", "page", "page_size", "keyword", "filter", "status", "data", "push", "length", "showToast", "title", "icon", "msg", "error", "console", "handleSearch", "setFilter", "type", "loadMore", "isSystemAssignedUser", "member", "assignment_type", "formatPhone", "phone", "replace", "formatJoinTime", "timestamp", "date", "Date", "diff", "days", "Math", "floor", "showModal", "content", "nickname", "mobile", "time", "activity_count", "showCancel", "confirmText", "wx", "createPage", "MiniProgramPage"], "mappings": "6nCAyIM,MAAAA,EAAUC,EAAAA,KAAI,GACdC,EAAaD,EAAAA,IAAI,IACjBE,EAAgBF,EAAAA,IAAI,IACpBG,EAAaH,EAAAA,IAAI,OACjBI,EAAcJ,EAAAA,IAAI,GAClBK,EAAUL,EAAAA,KAAI,GACdM,EAAaN,EAAAA,IAAI,YAIjBO,EAAQC,EAAAA,SAAS,CACrBC,MAAO,EACPC,OAAQ,EACRC,IAAK,IAIDC,EAAeC,EAAQA,UAAC,IAAMN,EAAME,QACpCK,EAAgBD,EAAQA,UAAC,IAAMN,EAAMG,SACrCK,EAAaF,EAAQA,UAAC,IAAMN,EAAMI,MAGxCK,EAAAA,QAAO,KACLC,GAAY,EAAI,IAIlBC,EAAAA,mBAAkB,KAChBD,GAAY,GACZE,YAAW,KACTC,EAAGC,MAACC,qBAAmB,GACtB,IAAI,IAITC,EAAAA,eAAc,KACRlB,EAAQmB,QAAUzB,EAAQyB,UAE9B,IAII,MAAAP,EAAcQ,MAAOC,GAAY,KACjC,IACF,GAAIA,EACFtB,EAAYoB,MAAQ,EACpBnB,EAAQmB,OAAQ,EAChBzB,EAAQyB,OAAQ,EAChBlB,EAAWkB,MAAQ,eACzB,IAAgBnB,EAAQmB,MAClB,OAGF,MAAMG,EAAWC,EAAAA,QAAQC,OAAOF,SAC1BG,QAAYC,8BAA4B,CAC5CC,IAAKL,EAASK,IACdC,MAAON,EAASM,MAChBC,KAAM9B,EAAYoB,MAClBW,UAnDW,GAoDXC,QAASlC,EAAcsB,MACvBa,OAAQlC,EAAWqB,QAGjB,GAAe,OAAfM,EAAIQ,OAAiB,CACjBvB,MAAAA,EAAae,EAAIS,MAAQ,GAE3Bb,EACFzB,EAAWuB,MAAQT,EAERd,EAAAuB,MAAMgB,QAAQzB,GAIvBe,EAAIvB,QACAA,EAAAE,MAAQqB,EAAIvB,MAAME,OAAS,EAC3BF,EAAAG,OAASoB,EAAIvB,MAAMG,QAAU,EAC7BH,EAAAI,IAAMmB,EAAIvB,MAAMI,KAAO,GAIvBN,EAAAmB,MAzEG,KAyEKT,EAAW0B,OACfrC,EAAAoB,QAEDlB,EAAAkB,MAAQnB,EAAQmB,MAAQ,WAAa,QAEtD,KAA8B,YAAfM,EAAIQ,OACblB,EAAAA,MAAIsB,UAAU,CACZC,MAAO,cACPC,KAAM,UAGRxB,EAAAA,MAAIsB,UAAU,CACZC,MAAOb,EAAIe,KAAO,OAClBD,KAAM,SAERtC,EAAWkB,MAAQ,WAEtB,OAAQsB,GACCC,QAAAD,MAAM,YAAaA,GAC3B1B,EAAAA,MAAIsB,UAAU,CACZC,MAAO,WACPC,KAAM,SAERtC,EAAWkB,MAAQ,UACvB,CAAY,QACRzB,EAAQyB,OAAQ,CAClB,GAIIwB,EAAe,KACnB/B,GAAY,EAAI,EAIZgC,EAAaC,IACjB/C,EAAWqB,MAAQ0B,EACnBjC,GAAY,EAAI,EAIZkC,EAAW,MACVpD,EAAQyB,OAASnB,EAAQmB,QAC5BlB,EAAWkB,MAAQ,UACnBP,GAAY,GACd,EAcImC,EAAwBC,GACM,WAA3BA,EAAOC,gBAIVC,EAAeC,GACdA,EACEA,EAAMC,QAAQ,sBAAuB,YADzB,MAKfC,EAAkBC,IACtB,IAAKA,EAAkB,MAAA,KACvB,MAAMC,EAAO,IAAIC,KAAiB,IAAZF,GAEhBG,EADM,IAAID,KACGD,EACbG,EAAOC,KAAKC,MAAMH,SAExB,OAAa,IAATC,EAAmB,OACV,IAATA,EAAmB,OACnBA,EAAO,GAAW,GAAGA,QACrBA,EAAO,IAAY,GAAGC,KAAKC,MAAMF,EAAO,WACrC,GAAGC,KAAKC,MAAMF,EAAO,UAAG,o2CAhCR,CAACV,IACxBjC,EAAAA,MAAI8C,UAAU,CACZvB,MAAO,OACPwB,QAAS,MAAMd,EAAOe,gBAAgBb,EAAYF,EAAOgB,iBAAiBX,EAAeL,EAAOiB,eAAejB,EAAOkB,gBAAkB,KACxIC,YAAY,EACZC,YAAa,OACd,yTC/QHC,GAAGC,WAAWC"}