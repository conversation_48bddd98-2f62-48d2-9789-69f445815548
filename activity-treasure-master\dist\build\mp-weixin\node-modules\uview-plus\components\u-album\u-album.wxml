<view class="u-album data-v-20e13790"><view wx:for="{{a}}" wx:for-item="arr" wx:key="b" class="u-album__row data-v-20e13790" ref="u-album__row" forComputedUse="{{d}}" style="{{'flex-wrap:' + e}}"><view wx:for="{{arr.a}}" wx:for-item="item" wx:key="f" class="u-album__row__wrapper data-v-20e13790" style="{{item.g}}" bindtap="{{item.h}}"><image class="data-v-20e13790" src="{{item.a}}" mode="{{b}}" style="{{c}}"></image><view wx:if="{{item.b}}" class="u-album__row__wrapper__text data-v-20e13790" style="{{'border-radius:' + item.e}}"><up-text wx:if="{{item.d}}" class="data-v-20e13790" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"></up-text></view></view></view></view>