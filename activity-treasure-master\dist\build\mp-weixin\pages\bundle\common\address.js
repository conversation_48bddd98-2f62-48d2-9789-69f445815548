"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),s=require("../../../store/index.js"),t=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-line")+e.resolveComponent("u-radio")+e.resolveComponent("u-icon")+e.resolveComponent("u-radio-group")+e.resolveComponent("u-gap")+e.resolveComponent("u-button")+e.resolveComponent("u-safe-bottom"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-line/u-line.js")+(()=>"../../../node-modules/uview-plus/components/u-radio/u-radio.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-radio-group/u-radio-group.js")+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-safe-bottom/u-safe-bottom.js"))();const u={__name:"address",setup(u){const a=e.ref(0),n=e.ref([]),r=e.ref(""),l=e.ref("");e.onLoad((async e=>{e.type&&(r.value=e.type)})),e.onReady((async()=>{const e=await t.getListHeight("bottomBox");a.value=e.height})),e.onShow((async()=>{i()}));const i=async()=>{l.value="";const e=await o.userget_addr_list();"ok"===e.status&&(n.value=e.data),e.data&&e.data.map((e=>{1===e.is_default&&(l.value=e.id)}))},d=async s=>{const t=await o.userset_default_addr({id:s});e.index.$u.toast(t.msg)};return(u,p)=>({a:e.f(n.value,((u,a,n)=>({a:e.t(u.username),b:e.t(u.mobile),c:e.t(null==u?void 0:u.sheng),d:e.t(null==u?void 0:u.shi),e:e.t(null==u?void 0:u.qu),f:e.t(null==u?void 0:u.addr),g:"17a316c2-1-"+n+",17a316c2-0",h:e.o(d,a),i:"17a316c2-2-"+n+",17a316c2-0",j:e.p({customStyle:{marginBottom:"8px"},"active-color":"#ef662d","label-color":l.value==u.id?"#ef662d":"#8A857C",label:l.value==u.id?"已设为默认":"设为默认",name:u.id}),k:"17a316c2-3-"+n+",17a316c2-0",l:e.o((o=>e.unref(t.navto)(`/pages/bundle/common/addAddress?info=${JSON.stringify({...u,edit:!0})}`)),a),m:e.o((e=>(async e=>{"ok"===(await o.userdel_addr({ids:e.id})).status&&i()})(u)),a),n:"17a316c2-4-"+n+",17a316c2-0",o:a,p:e.o((o=>{return t=u,void("select"===r.value&&(s.store().changeAddr(t),e.index.navigateBack()));var t}),a)}))),b:e.p({color:"#F5F2ED",length:"750rpx",margin:"38rpx 0 18rpx"}),c:e.p({name:`${e.unref(s.store)().$state.url}editAddress.png`,size:"28rpx",label:"编辑","label-color":"#8A857C","label-pos":"right",stop:!0}),d:e.p({name:`${e.unref(s.store)().$state.url}delAddress.png`,size:"28rpx",label:"删除","label-color":"#8A857C","label-pos":"right",stop:!0}),e:e.o((e=>l.value=e)),f:e.p({placement:"column",modelValue:l.value}),g:e.p({height:a.value}),h:e.o((o=>e.unref(t.navto)("/pages/bundle/common/addAddress"))),i:e.p({color:"#FAD000",text:"添加新地址",customStyle:{width:"750rpx",height:"98rpx",color:"#000",fontSize:"34rpx"}})})},__runtimeHooks:1};wx.createPage(u);
//# sourceMappingURL=address.js.map
