"use strict";const e=require("../../../common/vendor.js"),a=require("../../../api/index.js");require("../../../store/index.js");const t=require("../../../utils/index.js"),i=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-avatar")+e.resolveComponent("u-text")+e.resolveComponent("u-icon")+e.resolveComponent("u-button")+e.resolveComponent("u-gap"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-gap/u-gap.js"))();const o={__name:"personage",setup(o){const l=e.ref(0),s=e.ref({}),n=e.ref({created:[],joined:[],dynamics:[],diaries:[]}),u=e.ref(0);e.onLoad((async t=>{const i=(null==t?void 0:t.to_uid)||(null==t?void 0:t.uid);if(i){const t=await a.userget_other_user_info({to_uid:i});if("ok"===t.status){const e=await a.userguanzhu_check({to_uid:i});s.value={...t.data,is_guanzhu:e.data},await d(i)}else e.index.$u.toast(t.msg)}})),e.onReady((async()=>{const e=await t.getListHeight("bottomBox");l.value=e.height}));const r=async()=>{if(!i.requireLogin("","请先登录后再关注"))return;const t=s.value.is_guanzhu?a.userguanzhu_del:a.userguanzhu_add,o=await t({to_uid:s.value.uid});"ok"===o.status&&(s.value.is_guanzhu=s.value.is_guanzhu?0:1),e.index.$u.toast(o.msg)},d=async e=>{try{const t=await a.userget_other_user_info({to_uid:e,include_details:1});"ok"===t.status&&t.data?n.value={created:t.data.created_activities||[],joined:t.data.joined_activities||[],dynamics:t.data.published_feeds||[]}:(console.error("获取用户活动数据失败:",t.msg),n.value={created:[],joined:[],dynamics:[]})}catch(t){console.error("获取用户活动数据失败:",t),n.value={created:[],joined:[],dynamics:[]}}},c=e=>{u.value=e},v=a=>{e.index.navigateTo({url:`/pages/bundle/index/activeInfo?id=${a}`})};return(a,t)=>{var i,o,d,g,p,m;return e.e({a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"个人信息",color:"#ffffff",blod:!0}),b:e.p({size:"108rpx",src:s.value.avatar,mode:"aspectFill"}),c:e.p({size:"32rpx",bold:!0,text:s.value.nickname}),d:(null==(i=s.value)?void 0:i.sex)&&0!=(null==(o=s.value)?void 0:o.sex)},(null==(d=s.value)?void 0:d.sex)&&0!=(null==(g=s.value)?void 0:g.sex)?{e:e.p({name:1==(null==(p=s.value)?void 0:p.sex)?"man":"woman",size:"30rpx",color:1==(null==(m=s.value)?void 0:m.sex)?"#007AFF":"#FF69B4"})}:{},{f:e.o(r),g:e.p({text:s.value.is_guanzhu?"已关注":"关注",shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{width:"120rpx",height:"48rpx",fontSize:"24rpx",color:"#ffffff",marginTop:"12rpx"}}),h:s.value.gexingqianming},s.value.gexingqianming?{i:e.p({size:"28rpx",text:s.value.gexingqianming,color:"#666666"})}:{},{j:s.value.labels&&s.value.labels.length>0},s.value.labels&&s.value.labels.length>0?{k:e.f(s.value.labels,((a,t,i)=>({a:e.t(a.label),b:t})))}:{},{l:0===u.value?1:"",m:e.o((e=>c(0))),n:1===u.value?1:"",o:e.o((e=>c(1))),p:2===u.value?1:"",q:e.o((e=>c(2))),r:3===u.value?1:"",s:e.o((e=>c(3))),t:0===u.value},0===u.value?e.e({v:0===n.value.created.length},0===n.value.created.length?{w:e.p({size:"28rpx",color:"#999",text:"暂无发起的活动"})}:{x:e.f(n.value.created,((a,t,i)=>({a:a.img_url,b:"02b0d673-7-"+i,c:e.p({size:"30rpx",bold:!0,text:a.name}),d:"02b0d673-8-"+i,e:e.p({size:"24rpx",color:"#666",text:a.start_time}),f:a.id,g:e.o((e=>v(a.id)),a.id)})))}):{},{y:1===u.value},1===u.value?e.e({z:0===n.value.joined.length},0===n.value.joined.length?{A:e.p({size:"28rpx",color:"#999",text:"暂无报名的活动"})}:{B:e.f(n.value.joined,((a,t,i)=>({a:a.img_url,b:"02b0d673-10-"+i,c:e.p({size:"30rpx",bold:!0,text:a.name}),d:"02b0d673-11-"+i,e:e.p({size:"24rpx",color:"#666",text:a.start_time}),f:a.id,g:e.o((e=>v(a.id)),a.id)})))}):{},{C:2===u.value},2===u.value?e.e({D:0===n.value.dynamics.length},0===n.value.dynamics.length?{E:e.p({size:"28rpx",color:"#999",text:"暂无发布的动态"})}:{F:e.f(n.value.dynamics,((a,t,i)=>e.e({a:"02b0d673-13-"+i,b:e.p({size:"28rpx",text:a.content}),c:a.images&&a.images.length>0},a.images&&a.images.length>0?e.e({d:e.f(a.images.slice(0,3),((e,a,t)=>({a:a,b:e}))),e:a.images.length>3},a.images.length>3?{f:"02b0d673-14-"+i,g:e.p({size:"20rpx",color:"#999",text:"+"+(a.images.length-3)})}:{}):{},{h:"02b0d673-15-"+i,i:e.p({size:"22rpx",color:"#999",text:a.created_at}),j:a.like_count>0},a.like_count>0?{k:"02b0d673-16-"+i,l:e.p({name:"heart",size:"20rpx",color:"#999"}),m:"02b0d673-17-"+i,n:e.p({size:"20rpx",color:"#999",text:a.like_count})}:{},{o:a.comment_count>0},a.comment_count>0?{p:"02b0d673-18-"+i,q:e.p({name:"chat",size:"20rpx",color:"#999"}),r:"02b0d673-19-"+i,s:e.p({size:"20rpx",color:"#999",text:a.comment_count})}:{},{t:a.id,v:e.o((t=>{return i=a.id,void e.index.navigateTo({url:`/pages/bundle/world/feed/detail?feedId=${i}`});var i}),a.id)})))}):{},{G:3===u.value},3===u.value?e.e({H:n.value.diaries&&0===n.value.diaries.length},n.value.diaries&&0===n.value.diaries.length?{I:e.p({size:"28rpx",color:"#999",text:"暂无发布的日记"})}:n.value.diaries?{K:e.f(n.value.diaries,((a,t,i)=>e.e({a:"02b0d673-21-"+i,b:e.p({size:"28rpx",text:a.content}),c:a.images&&a.images.length>0},a.images&&a.images.length>0?e.e({d:e.f(a.images.slice(0,3),((e,a,t)=>({a:a,b:e}))),e:a.images.length>3},a.images.length>3?{f:"02b0d673-22-"+i,g:e.p({size:"20rpx",color:"#999",text:"+"+(a.images.length-3)})}:{}):{},{h:"02b0d673-23-"+i,i:e.p({size:"22rpx",color:"#999",text:a.created_at}),j:a.id,k:e.o((t=>{return i=a.id,void e.index.navigateTo({url:`/pages/bundle/world/diary/detail?diaryId=${i}`});var i}),a.id)})))}:{},{J:n.value.diaries}):{},{L:e.p({height:l.value})})}}},l=e._export_sfc(o,[["__scopeId","data-v-02b0d673"]]);o.__runtimeHooks=1,wx.createPage(l);
//# sourceMappingURL=personage.js.map
