"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),t=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u--textarea")+e.resolveComponent("u-upload")+e.resolveComponent("u-switch")+e.resolveComponent("u-loading-icon"))()}Math||(s+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const s=()=>"../../../../components/customNavbar.js",a={__name:"post",setup(s){const a=e.ref(""),l=e.ref([]),n=e.ref(null),i=e.ref("");e.ref("public");const u=e.ref(!1),r=e.ref(!1),c=e.ref(null),d=e.computed((()=>n.value?n.value.name||n.value.address:"添加位置")),v=e.computed((()=>{const e=t.store().$state.userInfo;return 1===(null==e?void 0:e.role_type)||2===(null==e?void 0:e.role_type)?4:1}));e.onLoad((e=>{e&&e.id&&(c.value=e.id,console.log("Editing feed with ID:",c.value))}));const p=()=>{e.index.navigateBack()},m=async t=>{let s=[].concat(t.file),a=l.value.length;s.map((e=>{l.value.push({...e,status:"uploading",message:"上传中"})}));for(let i=0;i<s.length;i++){const t=a+i;try{const a=await o.upload_img(s[i].url);if(console.log(`Upload result for index ${t}:`,JSON.stringify(a)),"ok"===a.status&&a.data){let e=l.value[t];e&&l.value.splice(t,1,{...e,status:"success",message:"",url:a.data})}else l.value[t]&&(l.value[t].status="failed",l.value[t].message=a.msg||"上传失败"),console.error("Upload API error:",a),e.index.showToast({title:a.msg||"图片上传失败",icon:"none"})}catch(n){l.value[t]&&(l.value[t].status="failed",l.value[t].message="上传失败"),console.error("Upload exception:",n),e.index.showToast({title:"图片上传失败，请重试",icon:"none"})}}},f=e=>{l.value.splice(e.index,1)},g=()=>{e.index.chooseLocation({success:e=>{n.value={name:e.name,address:e.address,latitude:e.latitude,longitude:e.longitude}},fail:e=>{console.error("Choose location failed:",e)}})},h=async()=>{var s,d;if(console.log("handleSubmit triggered!"),(null==(s=t.store().$state.userInfo)?void 0:s.uid)&&(null==(d=t.store().$state.userInfo)?void 0:d.token))if(a.value.trim()||0!==l.value.filter((e=>"success"===e.status)).length){if(!r.value){r.value=!0;try{if(l.value.filter((e=>"uploading"===e.status)).length>0)return e.index.showToast({title:"图片上传中，请稍候再试",icon:"none"}),void(r.value=!1);const s=l.value.filter((e=>"success"===e.status&&e.url)).map((e=>e.url));console.log("Submitting images:",s),console.log("Images type:",Array.isArray(s)?"Array":typeof s);const d={uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,content:a.value.trim(),images:s,location:n.value?JSON.stringify({name:n.value.name,address:n.value.address,latitude:n.value.latitude,longitude:n.value.longitude}):"",tags:i.value.trim(),privacy:u.value?"private":"public",type:"feed"};let v;if(console.log("Submitting params:",JSON.parse(JSON.stringify(d))),console.log("Images param type:",Array.isArray(d.images)?"Array":typeof d.images),console.log("Images param value:",d.images),c.value)return e.index.showToast({title:"编辑功能待实现",icon:"none"}),void(r.value=!1);v=await o.publishFeed(d),"ok"===v.status?(e.index.showToast({title:c.value?"修改成功":"发布成功",icon:"success"}),e.index.$emit("refreshFeedList"),setTimeout((()=>{e.index.navigateBack()}),1e3)):"relogin"===v.status?e.index.showToast({title:"请先登录",icon:"none"}):e.index.showToast({title:v.msg||(c.value?"修改失败":"发布失败"),icon:"none"})}catch(v){console.error("Submit feed error:",v),e.index.showToast({title:(c.value?"修改失败":"发布失败")+"，请重试",icon:"none"})}finally{r.value=!1}}}else e.index.showToast({title:"内容或图片至少要有一个哦",icon:"none"});else e.index.showToast({title:"请先登录",icon:"none"})};return(o,t)=>e.e({a:e.o(p),b:e.p({title:"动态",backIcon:"close"}),c:e.p({name:"info-circle-fill",size:"14",color:"#E6A23C"}),d:e.o((e=>a.value=e)),e:e.p({placeholder:"发表原创文字，沉淀灵感，留住思考",height:"300",maxlength:"-1",border:"none",customStyle:{padding:"32rpx",lineHeight:"1.6",fontSize:"32rpx",color:"#333333",backgroundColor:"transparent"},modelValue:a.value}),f:e.o(m),g:e.o(f),h:e.p({fileList:l.value,name:"file",multiple:!0,maxCount:e.unref(v),previewImage:!0,width:"200rpx",height:"200rpx",uploadIconColor:"#ccc"}),i:1===e.unref(v)},(e.unref(v),{}),{j:e.p({name:"map",size:"16",color:"#6AC086"}),k:e.t(e.unref(d)),l:n.value?1:"",m:e.p({name:"arrow-right",size:"14",color:"#999",customStyle:"margin-left: auto;"}),n:e.o(g),o:e.o((e=>u.value=e)),p:e.p({activeColor:"#6AC086",size:"24",modelValue:u.value}),q:!r.value},r.value?{}:{r:e.p({name:"checkmark",size:"44rpx",color:"#ffffff"})},{s:r.value},r.value?{t:e.p({color:"#ffffff",size:"40rpx"})}:{},{v:!r.value},(r.value,{}),{w:r.value||!a.value.trim()&&0===l.value.filter((e=>"success"===e.status)).length?1:"",x:e.o(h)})}},l=e._export_sfc(a,[["__scopeId","data-v-6f9afe42"]]);wx.createPage(l);
//# sourceMappingURL=post.js.map
