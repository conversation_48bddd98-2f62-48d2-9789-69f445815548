{"version": 3, "file": "u-picker.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-picker/u-picker.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBpY2tlci91LXBpY2tlci52dWU"], "sourcesContent": ["<template>\n    <view class=\"u-picker-warrper\">\n        <view v-if=\"hasInput\" class=\"u-picker-input cursor-pointer\" @click=\"showByClickInput = !showByClickInput\">\n            <slot>\n                <view>\n\t\t\t\t\t{{ inputLabel && inputLabel.length ? inputLabel.join('/') : placeholder }}\n\t\t\t\t</view>\n            </slot>\n        </view>\n\t\t<u-popup\n\t\t\t:show=\"show || (hasInput && showByClickInput)\"\n\t\t\t:mode=\"popupMode\"\n\t\t\t@close=\"closeHandler\"\n\t\t>\n\t\t\t<view class=\"u-picker\">\n\t\t\t\t<u-toolbar\n\t\t\t\t\tv-if=\"showToolbar\"\n\t\t\t\t\t:cancelColor=\"cancelColor\"\n\t\t\t\t\t:confirmColor=\"confirmColor\"\n\t\t\t\t\t:cancelText=\"cancelText\"\n\t\t\t\t\t:confirmText=\"confirmText\"\n\t\t\t\t\t:title=\"title\"\n\t\t\t\t\t:rightSlot=\"toolbarRightSlot ? true : false\"\n\t\t\t\t\t@cancel=\"cancel\"\n\t\t\t\t\t@confirm=\"confirm\"\n\t\t\t\t>\n\t\t\t\t\t<template #right>\n\t\t\t\t\t\t<slot name=\"toolbar-right\"></slot>\n\t\t\t\t\t</template>\n\t\t\t\t</u-toolbar>\n\t\t\t\t<slot name=\"toolbar-bottom\"></slot>\n\t\t\t\t<picker-view\n\t\t\t\t\tclass=\"u-picker__view\"\n\t\t\t\t\t:indicatorStyle=\"`height: ${addUnit(itemHeight)}`\"\n\t\t\t\t\t:value=\"innerIndex\"\n\t\t\t\t\t:immediateChange=\"immediateChange\"\n\t\t\t\t\t:style=\"{\n\t\t\t\t\t\theight: `${addUnit(visibleItemCount * itemHeight)}`\n\t\t\t\t\t}\"\n\t\t\t\t\t@change=\"changeHandler\"\n\t\t\t\t>\n\t\t\t\t\t<picker-view-column\n\t\t\t\t\t\tv-for=\"(item, index) in innerColumns\"\n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\tclass=\"u-picker__view__column\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tv-if=\"testArray(item)\"\n\t\t\t\t\t\t\tclass=\"u-picker__view__column__item u-line-1\"\n\t\t\t\t\t\t\tv-for=\"(item1, index1) in item\"\n\t\t\t\t\t\t\t:key=\"index1\"\n\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\theight: addUnit(itemHeight),\n\t\t\t\t\t\t\t\tlineHeight: addUnit(itemHeight),\n\t\t\t\t\t\t\t\tfontWeight: index1 === innerIndex[index] ? 'bold' : 'normal',\n\t\t\t\t\t\t\t\tdisplay: 'block'\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t>{{ getItemText(item1) }}</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t</picker-view>\n\t\t\t\t<view\n\t\t\t\t\tv-if=\"loading\"\n\t\t\t\t\tclass=\"u-picker--loading\"\n\t\t\t\t>\n\t\t\t\t\t<u-loading-icon mode=\"circle\"></u-loading-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n    </view>\n</template>\n\n<script>\n/**\n * u-picker\n * @description 选择器\n * @property {Boolean}\t\t\tshow\t\t\t\t是否显示picker弹窗（默认 false ）\n * @property {Boolean}\t\t\tshowToolbar\t\t\t是否显示顶部的操作栏（默认 true ）\n * @property {String}\t\t\ttitle\t\t\t\t顶部标题\n * @property {Array}\t\t\tcolumns\t\t\t\t对象数组，设置每一列的数据\n * @property {Boolean}\t\t\tloading\t\t\t\t是否显示加载中状态（默认 false ）\n * @property {String | Number}\titemHeight\t\t\t各列中，单个选项的高度（默认 44 ）\n * @property {String}\t\t\tcancelText\t\t\t取消按钮的文字（默认 '取消' ）\n * @property {String}\t\t\tconfirmText\t\t\t确认按钮的文字（默认 '确定' ）\n * @property {String}\t\t\tcancelColor\t\t\t取消按钮的颜色（默认 '#909193' ）\n * @property {String}\t\t\tconfirmColor\t\t确认按钮的颜色（默认 '#3c9cff' ）\n * @property {String | Number}\tvisibleItemCount\t每列中可见选项的数量（默认 5 ）\n * @property {String}\t\t\tkeyName\t\t\t\t选项对象中，需要展示的属性键名（默认 'text' ）\n * @property {Boolean}\t\t\tcloseOnClickOverlay\t是否允许点击遮罩关闭选择器（默认 false ）\n * @property {Array}\t\t\tdefaultIndex\t\t各列的默认索引\n * @property {Boolean}\t\t\timmediateChange\t\t是否在手指松开时立即触发change事件（默认 true ）\n * @event {Function} close\t\t关闭选择器时触发\n * @event {Function} cancel\t\t点击取消按钮触发\n * @event {Function} change\t\t当选择值变化时触发\n * @event {Function} confirm\t点击确定按钮，返回当前选择的值\n */\nimport { props } from './props';\nimport { mpMixin } from '../../libs/mixin/mpMixin';\nimport { mixin } from '../../libs/mixin/mixin';\nimport { addUnit, deepClone, sleep } from '../../libs/function/index';\nimport test from '../../libs/function/test';\nexport default {\n\tname: 'u-picker',\n\tmixins: [mpMixin, mixin, props],\n\tdata() {\n\t\treturn {\n\t\t\t// 上一次选择的列索引\n\t\t\tlastIndex: [],\n\t\t\t// 索引值 ，对应picker-view的value\n\t\t\tinnerIndex: [],\n\t\t\t// 各列的值\n\t\t\tinnerColumns: [],\n\t\t\t// 上一次的变化列索引\n\t\t\tcolumnIndex: 0,\n            showByClickInput: false,\n\t\t}\n\t},\n\twatch: {\n\t\t// 监听默认索引的变化，重新设置对应的值\n\t\tdefaultIndex: {\n\t\t\timmediate: true,\n\t\t\tdeep:true,\n\t\t\thandler(n) {\n\t\t\t\tthis.setIndexs(n, true)\n\t\t\t}\n\t\t},\n\t\t// 监听columns参数的变化\n\t\tcolumns: {\n\t\t\timmediate: true,\n\t\t\tdeep:true,\n\t\t\thandler(n) {\n\t\t\t\tthis.setColumns(n)\n\t\t\t}\n\t\t},\n\t},\n\temits: ['close', 'cancel', 'confirm', 'change', 'update:modelValue', 'update:show'],\n    computed: {\n        inputLabel() {\n            let items = this.innerColumns.map((item, index) => item[this.innerIndex[index]])\n            let res = []\n            items.forEach(element => {\n                res.push(element[this.keyName])\n            });\n            return res\n        },\n        inputValue() {\n            let items = this.innerColumns.map((item, index) => item[this.innerIndex[index]])\n            let res = []\n            items.forEach(element => {\n                res.push(element['id'])\n            });\n            return res\n        }\n    },\n\tmethods: {\n\t\taddUnit,\n\t\ttestArray: test.array,\n\t\t// 获取item需要显示的文字，判别为对象还是文本\n\t\tgetItemText(item) {\n\t\t\tif (test.object(item)) {\n\t\t\t\treturn item[this.keyName]\n\t\t\t} else {\n\t\t\t\treturn item\n\t\t\t}\n\t\t},\n\t\t// 关闭选择器\n\t\tcloseHandler() {\n\t\t\tif (this.closeOnClickOverlay) {\n                if (this.hasInput) {\n                    this.showByClickInput = false\n                }\n\t\t\t\tthis.$emit('update:show', false)\n\t\t\t\tthis.$emit('close')\n\t\t\t}\n\t\t},\n\t\t// 点击工具栏的取消按钮\n\t\tcancel() {\n            if (this.hasInput) {\n                this.showByClickInput = false\n            }\n\t\t\tthis.$emit('update:show', false)\n\t\t\tthis.$emit('cancel')\n\t\t},\n\t\t// 点击工具栏的确定按钮\n\t\tconfirm() {\n            this.$emit('update:modelValue', this.inputValue)\n            if (this.hasInput) {\n                this.showByClickInput = false\n            }\n\t\t\tthis.$emit('update:show', false)\n\t\t\tthis.$emit('confirm', {\n\t\t\t\tindexs: this.innerIndex,\n\t\t\t\tvalue: this.innerColumns.map((item, index) => item[this.innerIndex[index]]),\n\t\t\t\tvalues: this.innerColumns\n\t\t\t})\n\t\t},\n\t\t// 选择器某一列的数据发生变化时触发\n\t\tchangeHandler(e) {\n\t\t\tconst {\n\t\t\t\tvalue\n\t\t\t} = e.detail\n\t\t\tlet index = 0,\n\t\t\t\tcolumnIndex = 0\n\t\t\t// 通过对比前后两次的列索引，得出当前变化的是哪一列\n\t\t\tfor (let i = 0; i < value.length; i++) {\n\t\t\t\tlet item = value[i]\n\t\t\t\tif (item !== (this.lastIndex[i] || 0)) { // 把undefined转为合法假值0\n\t\t\t\t\t// 设置columnIndex为当前变化列的索引\n\t\t\t\t\tcolumnIndex = i\n\t\t\t\t\t// index则为变化列中的变化项的索引\n\t\t\t\t\tindex = item\n\t\t\t\t\tbreak // 终止循环，即使少一次循环，也是性能的提升\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.columnIndex = columnIndex\n\t\t\tconst values = this.innerColumns\n\t\t\t// 将当前的各项变化索引，设置为\"上一次\"的索引变化值\n\t\t\tthis.setLastIndex(value)\n\t\t\tthis.setIndexs(value)\n\n            this.$emit('update:modelValue', this.inputValue)\n\n\t\t\tthis.$emit('change', {\n\t\t\t\t// #ifndef MP-WEIXIN || MP-LARK\n\t\t\t\t// 微信小程序不能传递this，会因为循环引用而报错\n\t\t\t\t// picker: this,\n\t\t\t\t// #endif\n\t\t\t\tvalue: this.innerColumns.map((item, index) => item[value[index]]),\n\t\t\t\tindex,\n\t\t\t\tindexs: value,\n\t\t\t\t// values为当前变化列的数组内容\n\t\t\t\tvalues,\n\t\t\t\tcolumnIndex\n\t\t\t})\n\t\t},\n\t\t// 设置index索引，此方法可被外部调用设置\n\t\tsetIndexs(index, setLastIndex) {\n\t\t\tthis.innerIndex = deepClone(index)\n\t\t\tif (setLastIndex) {\n\t\t\t\tthis.setLastIndex(index)\n\t\t\t}\n\t\t},\n\t\t// 记录上一次的各列索引位置\n\t\tsetLastIndex(index) {\n\t\t\t// 当能进入此方法，意味着当前设置的各列默认索引，即为“上一次”的选中值，需要记录，是因为changeHandler中\n\t\t\t// 需要拿前后的变化值进行对比，得出当前发生改变的是哪一列\n\t\t\tthis.lastIndex = deepClone(index)\n\t\t},\n\t\t// 设置对应列选项的所有值\n\t\tsetColumnValues(columnIndex, values) {\n\t\t\t// 替换innerColumns数组中columnIndex索引的值为values，使用的是数组的splice方法\n\t\t\tthis.innerColumns.splice(columnIndex, 1, values)\n            // 替换完成之后将修改列之后的已选值置空\n\t\t\tthis.setLastIndex(this.innerIndex.slice(0, columnIndex))\n\t\t\t// 拷贝一份原有的innerIndex做临时变量，将大于当前变化列的所有的列的默认索引设置为0\n\t\t\tlet tmpIndex = deepClone(this.innerIndex)\n\t\t\tfor (let i = 0; i < this.innerColumns.length; i++) {\n\t\t\t\tif (i > this.columnIndex) {\n\t\t\t\t\ttmpIndex[i] = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\t// 一次性赋值，不能单个修改，否则无效\n\t\t\tthis.setIndexs(tmpIndex)\n\t\t},\n\t\t// 获取对应列的所有选项\n\t\tgetColumnValues(columnIndex) {\n\t\t\t// 进行同步阻塞，因为外部得到change事件之后，可能需要执行setColumnValues更新列的值\n\t\t\t// 索引如果在外部change的回调中调用getColumnValues的话，可能无法得到变更后的列值，这里进行一定延时，保证值的准确性\n\t\t\t(async () => {\n\t\t\t\tawait sleep()\n\t\t\t})()\n\t\t\treturn this.innerColumns[columnIndex]\n\t\t},\n\t\t// 设置整体各列的columns的值\n\t\tsetColumns(columns) {\n\t\t\t// console.log(columns)\n\t\t\tthis.innerColumns = deepClone(columns)\n\t\t\t// 如果在设置各列数据时，没有被设置默认的各列索引defaultIndex，那么用0去填充它，数组长度为列的数量\n\t\t\tif (this.innerIndex.length === 0) {\n\t\t\t\tthis.innerIndex = new Array(columns.length).fill(0)\n\t\t\t}\n\t\t},\n\t\t// 获取各列选中值对应的索引\n\t\tgetIndexs() {\n\t\t\treturn this.innerIndex\n\t\t},\n\t\t// 获取各列选中的值\n\t\tgetValues() {\n\t\t\t// 进行同步阻塞，因为外部得到change事件之后，可能需要执行setColumnValues更新列的值\n\t\t\t// 索引如果在外部change的回调中调用getValues的话，可能无法得到变更后的列值，这里进行一定延时，保证值的准确性\n\t\t\t(async () => {\n\t\t\t\tawait sleep()\n\t\t\t})()\n\t\t\treturn this.innerColumns.map((item, index) => item[this.innerIndex[index]])\n\t\t}\n\t},\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-picker {\n\t\tposition: relative;\n\n\t\t&__view {\n\n\t\t\t&__column {\n\t\t\t\t@include flex;\n\t\t\t\tflex: 1;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t&__item {\n\t\t\t\t\t@include flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\t/* #endif */\n\t\t\t\t\tcolor: $u-main-color;\n\n\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t\topacity: 0.35;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&--loading {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t\tbottom: 0;\n\t\t\t@include flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tbackground-color: rgba(255, 255, 255, 0.87);\n\t\t\tz-index: 1000;\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-picker/u-picker.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "lastIndex", "innerIndex", "innerColumns", "columnIndex", "showByClickInput", "watch", "defaultIndex", "immediate", "deep", "handler", "n", "this", "setIndexs", "columns", "setColumns", "emits", "computed", "inputLabel", "items", "map", "item", "index", "res", "for<PERSON>ach", "element", "push", "keyName", "inputValue", "methods", "addUnit", "testArray", "test", "array", "getItemText", "object", "<PERSON><PERSON><PERSON><PERSON>", "closeOnClickOverlay", "hasInput", "$emit", "cancel", "confirm", "indexs", "value", "values", "<PERSON><PERSON><PERSON><PERSON>", "e", "detail", "i", "length", "setLastIndex", "deepClone", "setColumnValues", "splice", "slice", "tmpIndex", "getColumnValues", "sleep", "Array", "fill", "getIndexs", "getV<PERSON>ues", "wx", "createComponent", "Component"], "mappings": "6DAoGKA,EAAU,CACdC,KAAM,WACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,WACzBC,KAAO,KACC,CAENC,UAAW,GAEXC,WAAY,GAEZC,aAAc,GAEdC,YAAa,EACJC,kBAAkB,IAG7BC,MAAO,CAENC,aAAc,CACbC,WAAW,EACXC,MAAK,EACL,OAAAC,CAAQC,GACFC,KAAAC,UAAUF,GAAG,EACnB,GAGDG,QAAS,CACRN,WAAW,EACXC,MAAK,EACL,OAAAC,CAAQC,GACPC,KAAKG,WAAWJ,EACjB,IAGFK,MAAO,CAAC,QAAS,SAAU,UAAW,SAAU,oBAAqB,eAClEC,SAAU,CACN,UAAAC,GACI,IAAIC,EAAQP,KAAKT,aAAaiB,KAAI,CAACC,EAAMC,IAAUD,EAAKT,KAAKV,WAAWoB,MACpEC,EAAM,GAIH,OAHPJ,EAAMK,SAAmBC,IACrBF,EAAIG,KAAKD,EAAQb,KAAKe,SAAQ,IAE3BJ,CACV,EACD,UAAAK,GACI,IAAIT,EAAQP,KAAKT,aAAaiB,KAAI,CAACC,EAAMC,IAAUD,EAAKT,KAAKV,WAAWoB,MACpEC,EAAM,GAIH,OAHPJ,EAAMK,SAAmBC,IACjBF,EAAAG,KAAKD,EAAY,GAAC,IAEnBF,CACX,GAEPM,QAAS,CACRC,QAAAA,EAAOA,QACPC,UAAWC,EAAIA,KAACC,MAEhB,WAAAC,CAAYb,GACX,OAAIW,EAAIA,KAACG,OAAOd,GACRA,EAAKT,KAAKe,SAEVN,CAER,EAED,YAAAe,GACKxB,KAAKyB,sBACQzB,KAAK0B,WACL1B,KAAKP,kBAAmB,GAEnCO,KAAA2B,MAAM,eAAe,GAC1B3B,KAAK2B,MAAM,SAEZ,EAED,MAAAC,GACc5B,KAAK0B,WACL1B,KAAKP,kBAAmB,GAEhCO,KAAA2B,MAAM,eAAe,GAC1B3B,KAAK2B,MAAM,SACX,EAED,OAAAE,GACe7B,KAAA2B,MAAM,oBAAqB3B,KAAKgB,YACjChB,KAAK0B,WACL1B,KAAKP,kBAAmB,GAEhCO,KAAA2B,MAAM,eAAe,GAC1B3B,KAAK2B,MAAM,UAAW,CACrBG,OAAQ9B,KAAKV,WACbyC,MAAO/B,KAAKT,aAAaiB,KAAI,CAACC,EAAMC,IAAUD,EAAKT,KAAKV,WAAWoB,MACnEsB,OAAQhC,KAAKT,cAEd,EAED,aAAA0C,CAAcC,GACP,MAAAH,MACLA,GACGG,EAAEC,OACF,IAAAzB,EAAQ,EACXlB,EAAc,EAEf,IAAA,IAAS4C,EAAI,EAAGA,EAAIL,EAAMM,OAAQD,IAAK,CAClC,IAAA3B,EAAOsB,EAAMK,GACjB,GAAI3B,KAAUT,KAAKX,UAAU+C,IAAM,GAAI,CAExB5C,EAAA4C,EAEN1B,EAAAD,EACR,KACD,CACD,CACAT,KAAKR,YAAcA,EACnB,MAAMwC,EAAShC,KAAKT,aAEpBS,KAAKsC,aAAaP,GAClB/B,KAAKC,UAAU8B,GAED/B,KAAA2B,MAAM,oBAAqB3B,KAAKgB,YAE9ChB,KAAK2B,MAAM,SAAU,CAKpBI,MAAO/B,KAAKT,aAAaiB,KAAI,CAACC,EAAMC,IAAUD,EAAKsB,EAAMrB,MACzDA,QACAoB,OAAQC,EAERC,SACAxC,eAED,EAED,SAAAS,CAAUS,EAAO4B,GACXtC,KAAAV,WAAaiD,EAASA,UAAC7B,GACxB4B,GACHtC,KAAKsC,aAAa5B,EAEnB,EAED,YAAA4B,CAAa5B,GAGPV,KAAAX,UAAYkD,EAASA,UAAC7B,EAC3B,EAED,eAAA8B,CAAgBhD,EAAawC,GAE5BhC,KAAKT,aAAakD,OAAOjD,EAAa,EAAGwC,GAEzChC,KAAKsC,aAAatC,KAAKV,WAAWoD,MAAM,EAAGlD,IAE3C,IAAImD,EAAWJ,EAAAA,UAAUvC,KAAKV,YAC9B,IAAA,IAAS8C,EAAI,EAAGA,EAAIpC,KAAKT,aAAa8C,OAAQD,IACzCA,EAAIpC,KAAKR,cACZmD,EAASP,GAAK,GAIhBpC,KAAKC,UAAU0C,EACf,EAED,eAAAC,CAAgBpD,GAMR,MAHP,iBACOqD,SACJ,EAFH,GAGO7C,KAAKT,aAAaC,EACzB,EAED,UAAAW,CAAWD,GAELF,KAAAT,aAAegD,EAASA,UAACrC,GAEC,IAA3BF,KAAKV,WAAW+C,SACnBrC,KAAKV,WAAa,IAAIwD,MAAM5C,EAAQmC,QAAQU,KAAK,GAElD,EAED,SAAAC,GACC,OAAOhD,KAAKV,UACZ,EAED,SAAA2D,GAMQ,MAHP,iBACOJ,SACJ,EAFH,GAGO7C,KAAKT,aAAaiB,KAAI,CAACC,EAAMC,IAAUD,EAAKT,KAAKV,WAAWoB,KACpE,gwCCpSFwC,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}