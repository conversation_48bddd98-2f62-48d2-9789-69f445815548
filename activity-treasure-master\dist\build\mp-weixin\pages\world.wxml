<view class="world-page-container data-v-237ddecc" bindtap="{{A}}"><view class="world-inner-wrapper data-v-237ddecc"><u-sticky wx:if="{{c}}" class="data-v-237ddecc" u-s="{{['d']}}" u-i="237ddecc-0" bind:__l="__l" u-p="{{c}}"><view class="world-navbar data-v-237ddecc"><u-tabs wx:if="{{b}}" class="data-v-237ddecc" bindchange="{{a}}" u-i="237ddecc-1,237ddecc-0" bind:__l="__l" u-p="{{b}}"></u-tabs></view></u-sticky><view class="world-content data-v-237ddecc"><view hidden="{{!e}}" class="tab-content data-v-237ddecc"><card-index wx:if="{{d}}" class="data-v-237ddecc" u-i="237ddecc-2" bind:__l="__l" u-p="{{d}}"/></view><view hidden="{{!g}}" class="tab-content data-v-237ddecc"><feed-index class="r data-v-237ddecc" u-r="feedIndexRef" u-i="237ddecc-3" bind:__l="__l"/></view><view hidden="{{!i}}" class="tab-content data-v-237ddecc"><diary-index class="r data-v-237ddecc" u-r="diaryIndexRef" u-i="237ddecc-4" bind:__l="__l"/></view><view hidden="{{!k}}" class="tab-content data-v-237ddecc"><quote-index class="r data-v-237ddecc" u-r="quoteIndexRef" u-i="237ddecc-5" bind:__l="__l"/></view></view><view class="fab-container data-v-237ddecc"><view wx:if="{{l}}" class="fab-options data-v-237ddecc"><view class="{{['fab-option', 'option-feed', 'data-v-237ddecc', n && 'visible']}}" bindtap="{{o}}"><u-icon wx:if="{{m}}" class="data-v-237ddecc" u-i="237ddecc-6" bind:__l="__l" u-p="{{m}}"></u-icon><text class="fab-option-text data-v-237ddecc">动态</text></view><view class="{{['fab-option', 'option-diary', 'data-v-237ddecc', q && 'visible']}}" bindtap="{{r}}"><u-icon wx:if="{{p}}" class="data-v-237ddecc" u-i="237ddecc-7" bind:__l="__l" u-p="{{p}}"></u-icon><text class="fab-option-text data-v-237ddecc">日记</text></view><view class="{{['fab-option', 'option-quote', 'data-v-237ddecc', t && 'visible']}}" bindtap="{{v}}"><u-icon wx:if="{{s}}" class="data-v-237ddecc" u-i="237ddecc-8" bind:__l="__l" u-p="{{s}}"></u-icon><text class="fab-option-text data-v-237ddecc">摘录</text></view></view><view class="{{['fab-main', 'data-v-237ddecc', x && 'rotated']}}" catchtap="{{y}}"><u-icon wx:if="{{w}}" class="data-v-237ddecc" u-i="237ddecc-9" bind:__l="__l" u-p="{{w}}"></u-icon></view></view></view><custom-tab-bar wx:if="{{z}}" class="data-v-237ddecc" u-i="237ddecc-10" bind:__l="__l" u-p="{{z}}"/></view>