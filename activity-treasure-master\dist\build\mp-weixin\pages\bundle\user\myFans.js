"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),s=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js");require("../../../store/index.js");const r=require("../../../utils/index.js"),n=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-icon")+e.resolveComponent("u-text")+e.resolveComponent("u-avatar")+e.resolveComponent("mescroll-uni"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js"))();const t={__name:"myFans",setup(t){const u=e.ref([]),{mescrollInit:i,downCallback:l,getMescroll:a}=s.useMescroll(e.onPageScroll,e.onReachBottom),c=e.ref("");e.onReady((async()=>{c.value=await r.setListHeight()+"px"}));const m=async s=>{if(!n.isLoggedIn())return e.index.$u.toast("请先登录"),void s.endErr();try{const r=await n.safeApiCall((()=>o.userget_fans_list({...n.getAuthParams(),page:s.num,page_size:s.size})),"获取粉丝列表");if(r&&"ok"===r.status){const e=r.data||[];1==s.num&&(u.value=[]),u.value=u.value.concat(e),s.endBySize(e.length,r.count)}else r&&"empty"===r.status?(1==s.num&&(u.value=[]),s.endBySize(0,0)):(e.index.$u.toast((null==r?void 0:r.msg)||"获取粉丝列表失败"),s.endErr())}catch(r){console.error("获取粉丝列表错误:",r),e.index.$u.toast("网络错误，请稍后重试"),s.endErr()}};return(o,s)=>e.e({a:e.p({"bg-color":"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"我的粉丝",titleStyle:{color:"#ffffff",fontWeight:"600",fontSize:"36rpx"}}),b:0===u.value.length},0===u.value.length?{c:e.p({name:"account",color:"#ccc",size:"120rpx"}),d:e.p({color:"#999",size:"28rpx",text:"暂无粉丝",margin:"20rpx 0 0 0"})}:{e:e.f(u.value,((o,s,n)=>{var t,u,i;return{a:"e9379693-4-"+n+",e9379693-1",b:e.p({size:"96rpx",src:null==(t=o.fan_user)?void 0:t.avatar,mode:"aspectFill",shape:"circle"}),c:"e9379693-5-"+n+",e9379693-1",d:e.p({size:"32rpx",bold:!0,color:"#333",text:null==(u=o.fan_user)?void 0:u.nickname}),e:"e9379693-6-"+n+",e9379693-1",f:e.p({size:"24rpx",color:"#666",lines:"1",text:(null==(i=o.fan_user)?void 0:i.gexingqianming)||"这个用户很神秘，什么都没有留下~"}),g:"e9379693-7-"+n+",e9379693-1",h:s,i:e.o((e=>{return s=o,void r.navto(`/pages/bundle/msg/personage?to_uid=${s.fan_user.uid}`);var s}),s)}})),f:e.p({name:"arrow-right",color:"#999",size:"32rpx"})},{g:e.o(e.unref(i)),h:e.o(e.unref(l)),i:e.o(m),j:e.o((e=>e.scrollTo(0))),k:e.p({height:c.value})})}},u=e._export_sfc(t,[["__scopeId","data-v-e9379693"]]);t.__runtimeHooks=1,wx.createPage(u);
//# sourceMappingURL=myFans.js.map
