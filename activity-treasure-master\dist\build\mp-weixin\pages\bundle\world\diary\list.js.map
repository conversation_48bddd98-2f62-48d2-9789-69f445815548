{"version": 3, "file": "list.js", "sources": ["../../../../../../../src/pages/bundle/world/diary/list.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGRpYXJ5XGxpc3QudnVl"], "sourcesContent": ["<script setup>\nimport { ref, onMounted } from 'vue';\nimport { getDiaryList } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// 状态管理\nconst diaryList = ref([]);\nconst loading = ref(false);\nconst refreshing = ref(false);\nconst hasMore = ref(true);\nconst page = ref(1);\nconst pageSize = 10;\n\n// 获取日记列表\nconst fetchDiaryList = async (isRefresh = false) => {\n  if (loading.value && !isRefresh) return;\n\n  loading.value = true;\n\n  try {\n    const currentPage = isRefresh ? 1 : page.value;\n    const params = {\n      page: currentPage,\n      page_size: pageSize,\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || '',\n      type: 'diary'\n    };\n\n    console.log('请求日记列表参数:', params);\n    const res = await getDiaryList(params);\n    console.log('日记列表API响应:', res);\n\n    if (res.status === 'ok' && res.data) {\n      const newList = res.data.list || [];\n      console.log('获取到的日记列表:', newList);\n\n      if (isRefresh) {\n        diaryList.value = newList;\n        page.value = 1;\n      } else {\n        diaryList.value = [...diaryList.value, ...newList];\n      }\n\n      hasMore.value = newList.length === pageSize;\n      if (!isRefresh) page.value++;\n    } else if (res.status === 'empty') {\n      console.log('服务器返回空数据:', res.msg);\n      if (isRefresh) {\n        diaryList.value = [];\n      }\n      hasMore.value = false;\n    } else {\n      console.error('API返回错误:', res);\n      uni.showToast({ title: res.msg || '获取日记列表失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('获取日记列表失败:', error);\n    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n  } finally {\n    loading.value = false;\n    refreshing.value = false;\n  }\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  refreshing.value = true;\n  fetchDiaryList(true);\n};\n\n// 上拉加载更多\nconst onLoadMore = () => {\n  if (hasMore.value && !loading.value) {\n    fetchDiaryList();\n  }\n};\n\n// 跳转到日记详情\nconst goToDiaryDetail = (diary) => {\n  uni.navigateTo({\n    url: `/pages/bundle/world/diary/detail?id=${diary.id}`\n  });\n};\n\n// 跳转到写日记\nconst goToWriteDiary = () => {\n  uni.navigateTo({\n    url: '/pages/bundle/world/diary/post'\n  });\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n\n  let date;\n  // 处理不同的时间格式\n  if (typeof timeStr === 'string') {\n    // 如果是字符串格式的时间戳或日期字符串\n    if (/^\\d+$/.test(timeStr)) {\n      // 纯数字字符串，当作时间戳处理\n      date = new Date(parseInt(timeStr) * 1000);\n    } else {\n      // 日期字符串\n      date = new Date(timeStr);\n    }\n  } else if (typeof timeStr === 'number') {\n    // 数字时间戳\n    date = new Date(timeStr * 1000);\n  } else {\n    return '';\n  }\n\n  if (isNaN(date.getTime())) {\n    console.warn('无效的时间格式:', timeStr);\n    return '';\n  }\n\n  const now = new Date();\n  const diff = now - date;\n\n  if (diff < 60000) return '刚刚';\n  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;\n  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;\n  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;\n\n  // {{ AURA-X: Modify - 修复时间格式显示，使用yyyy-MM-dd格式. Confirmed via 寸止. }}\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  return `${year}-${month}-${day}`;\n};\n\nonMounted(() => {\n  fetchDiaryList();\n});\n</script>\n\n<template>\n  <view class=\"diary-list-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar title=\"我的日记\">\n      <template #right>\n        <view class=\"write-btn\" @click=\"goToWriteDiary\">\n          <u-icon name=\"plus\" size=\"44rpx\" color=\"#ffffff\"></u-icon>\n        </view>\n      </template>\n    </customNavbar>\n\n    <!-- 日记列表 -->\n    <scroll-view \n      scroll-y \n      class=\"diary-scroll\"\n      @scrolltolower=\"onLoadMore\"\n      refresher-enabled\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n    >\n      <view class=\"diary-list\">\n        <view \n          v-for=\"diary in diaryList\" \n          :key=\"diary.id\"\n          class=\"diary-item\"\n          @click=\"goToDiaryDetail(diary)\"\n        >\n          <view class=\"diary-content\">\n            <view class=\"diary-text\">{{ diary.content }}</view>\n            <view class=\"diary-meta\">\n              <text class=\"diary-time\">{{ formatTime(diary.created_at) }}</text>\n              <text class=\"diary-mood\" v-if=\"diary.mood\">{{ diary.mood }}</text>\n            </view>\n          </view>\n          <image \n            v-if=\"diary.cover_image\" \n            :src=\"diary.cover_image\" \n            class=\"diary-cover\"\n            mode=\"aspectFill\"\n          ></image>\n        </view>\n      </view>\n\n      <!-- 加载状态 -->\n      <view class=\"load-status\" v-if=\"loading && !refreshing\">\n        <u-loading-icon size=\"40rpx\" color=\"#6AC086\"></u-loading-icon>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n\n      <!-- 没有更多 -->\n      <view class=\"load-status\" v-if=\"!hasMore && diaryList.length > 0\">\n        <text class=\"load-text\">没有更多了</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && diaryList.length === 0\">\n        <u-icon name=\"edit-pen\" size=\"120rpx\" color=\"#cccccc\"></u-icon>\n        <text class=\"empty-text\">还没有日记，开始记录生活吧</text>\n        <view class=\"empty-btn\" @click=\"goToWriteDiary\">\n          <text class=\"btn-text\">写日记</text>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n/* 统一设计变量 */\n:root {\n  --spacing-md: 24rpx;\n  --spacing-lg: 32rpx;\n  --radius-card: 20rpx;\n  --radius-button: 50rpx;\n  --color-bg-page: #f8f9fa;\n  --color-bg-card: #ffffff;\n  --color-text-title: #333333;\n  --color-text-body: #666666;\n  --color-text-caption: #999999;\n  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);\n}\n\n.diary-list-page {\n  height: 100vh;\n  background-color: var(--color-bg-page);\n  display: flex;\n  flex-direction: column;\n}\n\n.write-btn {\n  width: 80rpx;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: var(--radius-button);\n  box-shadow: var(--shadow-card);\n  transition: all 0.3s ease;\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.diary-scroll {\n  flex: 1;\n  padding: var(--spacing-lg);\n}\n\n.diary-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n\n.diary-item {\n  background-color: var(--color-bg-card);\n  border-radius: var(--radius-card);\n  padding: var(--spacing-lg);\n  box-shadow: var(--shadow-card);\n  display: flex;\n  gap: var(--spacing-md);\n  transition: all 0.3s ease;\n\n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.diary-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n\n.diary-text {\n  font-size: 28rpx;\n  color: var(--color-text-body);\n  line-height: 1.6;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.diary-meta {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n\n.diary-time {\n  font-size: 24rpx;\n  color: var(--color-text-caption);\n}\n\n.diary-mood {\n  font-size: 24rpx;\n  color: #6AC086;\n  background-color: rgba(106, 192, 134, 0.1);\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.diary-cover {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: var(--radius-card);\n  flex-shrink: 0;\n}\n\n.load-status {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n\n.load-text {\n  font-size: 24rpx;\n  color: var(--color-text-caption);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx var(--spacing-lg);\n  gap: var(--spacing-lg);\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: var(--color-text-caption);\n}\n\n.empty-btn {\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: var(--radius-button);\n  padding: 24rpx 48rpx;\n  box-shadow: var(--shadow-card);\n\n  .btn-text {\n    font-size: 28rpx;\n    color: #ffffff;\n    font-weight: 500;\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/diary/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "diaryList", "ref", "loading", "refreshing", "hasMore", "page", "fetchDiaryList", "async", "isRefresh", "value", "params", "page_size", "uid", "store", "$state", "userInfo", "token", "type", "console", "log", "res", "getDiaryList", "status", "data", "newList", "list", "length", "msg", "error", "showToast", "title", "icon", "uni", "index", "onRefresh", "onLoadMore", "goToWriteDiary", "navigateTo", "url", "formatTime", "timeStr", "date", "test", "Date", "parseInt", "isNaN", "getTime", "warn", "diff", "Math", "floor", "getFullYear", "String", "getMonth", "padStart", "getDate", "onMounted", "diary", "id", "wx", "createPage", "MiniProgramPage"], "mappings": "mqBAIA,MAAMA,EAAe,IAAW,mEAGhC,MAAMC,EAAYC,EAAAA,IAAI,IAChBC,EAAUD,EAAAA,KAAI,GACdE,EAAaF,EAAAA,KAAI,GACjBG,EAAUH,EAAAA,KAAI,GACdI,EAAOJ,EAAAA,IAAI,GAIXK,EAAiBC,MAAOC,GAAY,aACpC,IAAAN,EAAQO,OAAUD,EAAlB,CAEJN,EAAQO,OAAQ,EAEZ,IACI,MACAC,EAAS,CACbL,KAFkBG,EAAY,EAAIH,EAAKI,MAGvCE,UAZW,GAaXC,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,GACzCC,KAAM,SAGAC,QAAAC,IAAI,YAAaT,GACzB,MAAMU,QAAYC,eAAaX,GAG/B,GAFQQ,QAAAC,IAAI,aAAcC,GAEP,OAAfA,EAAIE,QAAmBF,EAAIG,KAAM,CACnC,MAAMC,EAAUJ,EAAIG,KAAKE,MAAQ,GACzBP,QAAAC,IAAI,YAAaK,GAErBhB,GACFR,EAAUS,MAAQe,EAClBnB,EAAKI,MAAQ,GAEbT,EAAUS,MAAQ,IAAIT,EAAUS,SAAUe,GAGpCpB,EAAAK,MAjCG,KAiCKe,EAAQE,OACnBlB,GAAgBH,EAAAI,OAC3B,KAA8B,UAAfW,EAAIE,QACLJ,QAAAC,IAAI,YAAaC,EAAIO,KACzBnB,IACFR,EAAUS,MAAQ,IAEpBL,EAAQK,OAAQ,IAERS,QAAAU,MAAM,WAAYR,WACtBS,UAAU,CAAEC,MAAOV,EAAIO,KAAO,WAAYI,KAAM,SAEvD,OAAQH,GACCV,QAAAU,MAAM,YAAaA,GAC3BI,EAAGC,MAACJ,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC/C,CAAY,QACR7B,EAAQO,OAAQ,EAChBN,EAAWM,OAAQ,CACrB,CA/CiC,CA+CjC,EAIIyB,EAAY,KAChB/B,EAAWM,OAAQ,EACnBH,GAAe,EAAI,EAIf6B,EAAa,KACb/B,EAAQK,QAAUP,EAAQO,UAE9B,EAWI2B,EAAiB,KACrBJ,EAAAA,MAAIK,WAAW,CACbC,IAAK,kCACN,EAIGC,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAEjB,IAAAC,EAEA,GAAmB,iBAAZD,EAIPC,EAFE,QAAQC,KAAKF,GAER,IAAIG,KAAyB,IAApBC,SAASJ,IAGlB,IAAIG,KAAKH,OAEtB,IAAgC,iBAAZA,EAIT,MAAA,GAFAC,EAAA,IAAIE,KAAe,IAAVH,EAGlB,CAEA,GAAIK,MAAMJ,EAAKK,WAEN,OADC5B,QAAA6B,KAAK,WAAYP,GAClB,GAGH,MACAQ,EADM,IAAIL,KACGF,EAEnB,GAAIO,EAAO,IAAc,MAAA,KACzB,GAAIA,EAAO,KAAS,MAAO,GAAGC,KAAKC,MAAMF,EAAO,UAChD,GAAIA,EAAO,MAAU,MAAO,GAAGC,KAAKC,MAAMF,EAAO,WACjD,GAAIA,EAAO,OAAW,MAAO,GAAGC,KAAKC,MAAMF,EAAO,WAM3C,MAAA,GAHMP,EAAKU,iBACJC,OAAOX,EAAKY,WAAa,GAAGC,SAAS,EAAG,QAC1CF,OAAOX,EAAKc,WAAWD,SAAS,EAAG,MACpB,SAG7BE,EAAAA,WAAU,yRAvDc,CAACC,IACvBzB,EAAAA,MAAIK,WAAW,CACbC,IAAK,uCAAuCmB,EAAMC,MACnD,wXClFHC,GAAGC,WAAWC"}