"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../api/index.js"),o=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-upload")+e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||(t+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const t=()=>"../../../../components/customNavbar.js",i={__name:"create",setup(t){const i=e.ref({name:"",category:"",description:"",publisher:"",publish_year:"",isbn:"",url:"",cover_image:""}),u=e.ref(!1),l=e.ref([]),s=getCurrentPages(),n=s[s.length-1],r="select"===n.options.type,c=n.options.keyword||"",v=["图书","期刊","报纸","网站","博客","学术论文","演讲","电影","纪录片","播客","社交媒体","访谈","会议","其他"],d=async o=>{let t=[].concat(o.file);l.value.length>0&&(e.index.showToast({title:"只能上传一张封面，将替换现有封面",icon:"none"}),l.value=[]),t.length>1&&(e.index.showToast({title:"只能上传一张封面，已自动选择第一张",icon:"none"}),t=[t[0]]);let u=l.value.length;t.map((e=>{l.value.push({...e,status:"uploading",message:"上传中"})}));for(let n=0;n<t.length;n++){const o=u+n;try{const e=await a.upload_img(t[n]);if(!e||!e.url)throw new Error("上传失败");l.value[o].status="success",l.value[o].url=e.url,i.value.cover_image=e.url}catch(s){l.value[o].status="failed",l.value[o].message="上传失败",e.index.showToast({title:"封面上传失败",icon:"none"})}}},p=e=>{l.value.splice(e.index,1),i.value.cover_image=""},m=()=>{e.index.showActionSheet({itemList:v,success:e=>{i.value.category=v[e.tapIndex]}})},h=async()=>{if(i.value.name.trim())if(!i.value.url||g(i.value.url)){if(!u.value){u.value=!0;try{const t={uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token,name:i.value.name.trim(),category:i.value.category.trim(),description:i.value.description.trim(),publisher:i.value.publisher.trim(),publish_year:i.value.publish_year.trim(),isbn:i.value.isbn.trim(),url:i.value.url.trim(),cover_image:i.value.cover_image},u=await a.createSource(t);if("ok"===u.status){if(e.index.showToast({title:"创建成功",icon:"success"}),r){const a={id:u.data.source_id,name:i.value.name,category:i.value.category,description:i.value.description,publisher:i.value.publisher,cover_image:i.value.cover_image,quote_count:0};e.index.$emit("sourceCreated",a)}setTimeout((()=>{e.index.navigateBack()}),1e3)}else"relogin"===u.status?e.index.showToast({title:"请先登录",icon:"none"}):e.index.showToast({title:u.msg||"创建失败",icon:"none"})}catch(t){console.error("创建出处失败:",t),e.index.showToast({title:"创建失败，请稍后重试",icon:"none"})}finally{u.value=!1}}}else e.index.showToast({title:"请输入正确的网址格式",icon:"none"});else e.index.showToast({title:"请输入出处名称",icon:"none"})},g=e=>{try{return new URL(e),!0}catch{return/^https?:\/\/.+/.test(e)}};return e.onMounted((()=>{c&&(i.value.name=decodeURIComponent(c))})),(a,o)=>e.e({a:e.p({title:r?"创建出处":"新建出处",backIcon:"arrow-left"}),b:e.o(d),c:e.o(p),d:e.p({fileList:l.value,name:"file",maxCount:1,previewImage:!0,width:"200rpx",height:"260rpx",uploadIconColor:"#6AC086"}),e:i.value.name,f:e.o((e=>i.value.name=e.detail.value)),g:e.t(i.value.category||"请选择出处类别"),h:i.value.category?"":1,i:e.p({name:"arrow-right",size:"16",color:"#ccc"}),j:e.o(m),k:i.value.publisher,l:e.o((e=>i.value.publisher=e.detail.value)),m:i.value.publish_year,n:e.o((e=>i.value.publish_year=e.detail.value)),o:i.value.isbn,p:e.o((e=>i.value.isbn=e.detail.value)),q:i.value.url,r:e.o((e=>i.value.url=e.detail.value)),s:i.value.description,t:e.o((e=>i.value.description=e.detail.value)),v:u.value},u.value?{w:e.p({mode:"spinner",color:"white",size:"20"})}:{},{x:!u.value},(u.value,{}),{y:e.o(h),z:u.value?1:""})}},u=e._export_sfc(i,[["__scopeId","data-v-0dfae0ae"]]);wx.createPage(u);
//# sourceMappingURL=create.js.map
