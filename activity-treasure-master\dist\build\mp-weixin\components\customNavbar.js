"use strict";const e=require("../common/vendor.js");if(!Array){(e.resolveComponent("u-status-bar")+e.resolveComponent("u-icon"))()}Math||((()=>"../node-modules/uview-plus/components/u-status-bar/u-status-bar.js")+(()=>"../node-modules/uview-plus/components/u-icon/u-icon.js"))();const t={__name:"customNavbar",props:{title:{type:String,default:""},bgColor:{type:String,default:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"},height:{type:String,default:"200rpx"},titleColor:{type:String,default:"#ffffff"},iconColor:{type:String,default:"#ffffff"},showBack:{type:Boolean,default:!0},backIcon:{type:String,default:"arrow-left"},backType:{type:String,default:"back"},bold:{type:Boolean,default:!0}},emits:["back"],setup(t,{emit:o}){const a=t,n=()=>{"home"===a.backType?e.index.reLaunch({url:"/pages/index"}):e.index.navigateBack({delta:1,fail:()=>e.index.reLaunch({url:"/pages/index"})}),o("back")};return(o,a)=>e.e({a:e.p({bgColor:t.bgColor}),b:t.showBack},t.showBack?{c:e.p({name:t.backIcon,size:"44rpx",color:t.iconColor}),d:e.o(n)}:{},{e:e.t(t.title),f:t.titleColor,g:t.bold?"700":"400",h:t.bgColor,i:t.height})}},o=e._export_sfc(t,[["__scopeId","data-v-d4937f61"]]);wx.createComponent(o);
//# sourceMappingURL=customNavbar.js.map
