"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../api/index.js"),t=require("../../../../store/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-upload")+e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||(o+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const o=()=>"../../../../components/customNavbar.js",i={__name:"create",setup(o){const i=e.ref({name:"",category:"",description:"",birth_year:"",death_year:"",nationality:"",avatar:""}),n=e.ref(!1),r=e.ref([]),l=getCurrentPages(),s=l[l.length-1],u="select"===s.options.type,c=s.options.keyword||"",v=["作家","诗人","哲学家","思想家","政治家","科学家","艺术家","音乐家","历史学家","经济学家","心理学家","社会学家","教育家","企业家","其他"],d=async t=>{let o=[].concat(t.file);r.value.length>0&&(e.index.showToast({title:"只能上传一张头像，将替换现有头像",icon:"none"}),r.value=[]),o.length>1&&(e.index.showToast({title:"只能上传一张头像，已自动选择第一张",icon:"none"}),o=[o[0]]);let n=r.value.length;o.map((e=>{r.value.push({...e,status:"uploading",message:"上传中"})}));for(let s=0;s<o.length;s++){const t=n+s;try{const e=await a.upload_img(o[s]);if(!e||!e.url)throw new Error("上传失败");r.value[t].status="success",r.value[t].url=e.url,i.value.avatar=e.url}catch(l){r.value[t].status="failed",r.value[t].message="上传失败",e.index.showToast({title:"头像上传失败",icon:"none"})}}},m=e=>{r.value.splice(e.index,1),i.value.avatar=""},p=()=>{e.index.showActionSheet({itemList:v,success:e=>{i.value.category=v[e.tapIndex]}})},h=async()=>{if(i.value.name.trim()){if(!n.value){n.value=!0;try{const o={uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,name:i.value.name.trim(),category:i.value.category.trim(),description:i.value.description.trim(),birth_year:i.value.birth_year.trim(),death_year:i.value.death_year.trim(),nationality:i.value.nationality.trim(),avatar:i.value.avatar},n=await a.createAuthor(o);if("ok"===n.status){if(e.index.showToast({title:"创建成功",icon:"success"}),u){const a={id:n.data.author_id,name:i.value.name,category:i.value.category,description:i.value.description,avatar:i.value.avatar,quote_count:0};e.index.$emit("authorCreated",a)}setTimeout((()=>{e.index.navigateBack()}),1e3)}else"relogin"===n.status?e.index.showToast({title:"请先登录",icon:"none"}):e.index.showToast({title:n.msg||"创建失败",icon:"none"})}catch(o){console.error("创建作者失败:",o),e.index.showToast({title:"创建失败，请稍后重试",icon:"none"})}finally{n.value=!1}}}else e.index.showToast({title:"请输入作者姓名",icon:"none"})};return e.onMounted((()=>{c&&(i.value.name=decodeURIComponent(c))})),(a,t)=>e.e({a:e.p({title:u?"创建作者":"新建作者",backIcon:"arrow-left"}),b:e.o(d),c:e.o(m),d:e.p({fileList:r.value,name:"file",maxCount:1,previewImage:!0,width:"160rpx",height:"160rpx",uploadIconColor:"#6AC086"}),e:i.value.name,f:e.o((e=>i.value.name=e.detail.value)),g:e.t(i.value.category||"请选择作者类别"),h:i.value.category?"":1,i:e.p({name:"arrow-right",size:"16",color:"#ccc"}),j:e.o(p),k:i.value.nationality,l:e.o((e=>i.value.nationality=e.detail.value)),m:i.value.birth_year,n:e.o((e=>i.value.birth_year=e.detail.value)),o:i.value.death_year,p:e.o((e=>i.value.death_year=e.detail.value)),q:i.value.description,r:e.o((e=>i.value.description=e.detail.value)),s:n.value},n.value?{t:e.p({mode:"spinner",color:"white",size:"20"})}:{},{v:!n.value},(n.value,{}),{w:e.o(h),x:n.value?1:""})}},n=e._export_sfc(i,[["__scopeId","data-v-cf66c6da"]]);wx.createPage(n);
//# sourceMappingURL=create.js.map
