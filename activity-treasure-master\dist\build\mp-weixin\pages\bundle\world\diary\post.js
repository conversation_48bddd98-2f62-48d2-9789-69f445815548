"use strict";const e=require("../../../../common/vendor.js"),s=require("../../../../api/index.js"),t=require("../../../../store/index.js"),o=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/index.js"),require("../../../../utils/systemInfo.js"),require("../../../../utils/cacheManager.js"),require("../../../../store/counter.js"),!Array){(e.resolveComponent("u-upload")+e.resolveComponent("u-icon")+e.resolveComponent("u-switch")+e.resolveComponent("u-loading-icon"))()}Math||(a+(()=>"../../../../node-modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const a=()=>"../../../../components/customNavbar.js",u={__name:"post",setup(a){const u=e.ref(""),l=e.ref([]),i=e.ref(null);e.ref("public");const n=e.ref(!1),r=e.ref(!1),c=e.computed((()=>{const e=t.store().$state.userInfo;return 1===(null==e?void 0:e.role_type)||2===(null==e?void 0:e.role_type)?4:1})),v=async t=>{let o=[].concat(t.file),a=l.value.length;o.map((e=>{l.value.push({...e,status:"uploading",message:"上传中"})}));for(let i=0;i<o.length;i++){const t=a+i;try{const a=await s.upload_img(o[i].url);if("ok"===a.status&&a.data){let e=l.value[t];e&&l.value.splice(t,1,{...e,status:"success",message:"",url:a.data})}else l.value[t]&&(l.value[t].status="failed",l.value[t].message=a.msg||"上传失败"),e.index.showToast({title:a.msg||"图片上传失败",icon:"none"})}catch(u){l.value[t]&&(l.value[t].status="failed",l.value[t].message="上传失败"),e.index.showToast({title:"图片上传失败，请重试",icon:"none"})}}},d=e=>{l.value.splice(e.index,1)},p=()=>{e.index.chooseLocation({success:e=>{i.value={name:e.name,address:e.address,latitude:e.latitude,longitude:e.longitude}},fail:()=>{e.index.showToast({title:"选择位置失败",icon:"none"})}})},m=()=>{i.value=null},f=async()=>{if(o.requireLogin("","请先登录后再发布日记"))if(u.value.trim()||0!==l.value.filter((e=>"success"===e.status)).length){if(!r.value){r.value=!0;try{const o=[];for(const e of l.value)"success"===e.status&&e.url&&o.push(e.url);const a={uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token,content:u.value.trim(),images:o,location:i.value?JSON.stringify(i.value):"",tags:"",privacy:n.value?"private":"public",type:"diary"},r=await s.publishDiary(a);"ok"===r.status?(e.index.showToast({title:"发布成功",icon:"success"}),e.index.$emit("refreshFeedList"),setTimeout((()=>{e.index.navigateBack()}),1e3)):"relogin"===r.status?e.index.showToast({title:"请先登录",icon:"none"}):e.index.showToast({title:r.msg||"发布失败",icon:"none"})}catch(a){e.index.showToast({title:"发布失败，请重试",icon:"none"})}finally{r.value=!1}}}else e.index.showToast({title:"请输入日记内容或添加图片",icon:"none"})},g=()=>{e.index.navigateBack()};return(s,t)=>e.e({a:e.o(g),b:e.p({title:"日记",backIcon:"close"}),c:u.value,d:e.o((e=>u.value=e.detail.value)),e:e.t(u.value.length),f:e.o(v),g:e.o(d),h:e.p({fileList:l.value,name:"file",multiple:!0,maxCount:e.unref(c),previewImage:!0,width:"200rpx",height:"200rpx",uploadIconColor:"#ccc"}),i:1===e.unref(c)},(e.unref(c),{}),{j:e.p({name:"map",size:"16",color:"#6AC086"}),k:i.value},i.value?{l:e.t(i.value.name),m:e.t(i.value.address),n:e.o(m),o:e.p({name:"close",size:"16",color:"#999"})}:{p:e.p({name:"arrow-right",size:"14",color:"#999"}),q:e.o(p)},{r:e.o((e=>n.value=e)),s:e.p({activeColor:"#6AC086",size:"24",modelValue:n.value}),t:!r.value},r.value?{}:{v:e.p({name:"checkmark",size:"44rpx",color:"#ffffff"})},{w:r.value},r.value?{x:e.p({color:"#ffffff",size:"40rpx"})}:{},{y:!r.value},(r.value,{}),{z:r.value||!u.value.trim()&&0===l.value.filter((e=>"success"===e.status)).length?1:"",A:e.o(f)})}},l=e._export_sfc(u,[["__scopeId","data-v-999cefb4"]]);wx.createPage(l);
//# sourceMappingURL=post.js.map
