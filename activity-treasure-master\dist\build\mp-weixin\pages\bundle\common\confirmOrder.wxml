<view class="page b6f"><view class="df aic p30" bindtap="{{e}}"><view class="df fdc f1"><view class="df aic mb20"><view class="mr50"><u-text wx:if="{{a}}" u-i="2087c390-0" bind:__l="__l" u-p="{{a}}"></u-text></view><u-text wx:if="{{b}}" u-i="2087c390-1" bind:__l="__l" u-p="{{b}}"></u-text></view><u-text wx:if="{{c}}" u-i="2087c390-2" bind:__l="__l" u-p="{{c}}"></u-text></view><u-icon wx:if="{{d}}" u-i="2087c390-3" bind:__l="__l" u-p="{{d}}"></u-icon></view><u-image wx:if="{{f}}" u-i="2087c390-4" bind:__l="__l" u-p="{{f}}"></u-image><u-gap wx:if="{{g}}" u-i="2087c390-5" bind:__l="__l" u-p="{{g}}"></u-gap><view class="p30"><u-text wx:if="{{h}}" u-i="2087c390-6" bind:__l="__l" u-p="{{h}}"></u-text><view wx:for="{{i}}" wx:for-item="val" wx:key="l" class="df mb30 r30"><u-image wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></u-image><view class="ml20 df fdc jcsa f1"><view><u-text wx:if="{{val.d}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{val.d}}"></u-text></view><view class="{{['df', l]}}"><view><u-text wx:if="{{j}}" u-i="{{val.e}}" bind:__l="__l" u-p="{{j}}"></u-text></view><block wx:if="{{k}}"><view><u-text wx:for="{{val.f}}" wx:for-item="value" wx:key="a" u-i="{{value.b}}" bind:__l="__l" u-p="{{value.c}}"></u-text></view></block><block wx:else><view wx:for="{{val.g}}" wx:for-item="value" wx:key="c"><u-text wx:if="{{value.b}}" u-i="{{value.a}}" bind:__l="__l" u-p="{{value.b}}"></u-text></view></block></view><view class="df aic jcsb"><u-text wx:if="{{val.i}}" u-i="{{val.h}}" bind:__l="__l" u-p="{{val.i}}"></u-text><u-text wx:if="{{val.k}}" u-i="{{val.j}}" bind:__l="__l" u-p="{{val.k}}"></u-text></view></view></view></view><u-gap wx:if="{{m}}" u-i="2087c390-14" bind:__l="__l" u-p="{{m}}"></u-gap><view class="p30"><u-cell wx:if="{{n}}" u-i="2087c390-15" bind:__l="__l" u-p="{{n}}"></u-cell><u-cell wx:if="{{p}}" u-s="{{['value']}}" u-i="2087c390-16" bind:__l="__l" u-p="{{p}}"><u-text u-i="2087c390-17,2087c390-16" bind:__l="__l" u-p="{{o}}" slot="value"></u-text></u-cell><view class="px30"><u-radio-group wx:if="{{t}}" u-s="{{['d']}}" bindchange="{{r}}" u-i="2087c390-18" bind:__l="__l" bindupdateModelValue="{{s}}" u-p="{{t}}"><u-radio wx:for="{{q}}" wx:for-item="val" wx:key="a" u-i="{{val.b}}" bind:__l="__l" u-p="{{val.c}}"></u-radio></u-radio-group></view></view><view class="pfx bottom0 w b6f"><view class="df aic jcr"><view><u-text wx:if="{{v}}" u-i="2087c390-20" bind:__l="__l" u-p="{{v}}"></u-text></view><view><u-text wx:if="{{w}}" u-i="2087c390-21" bind:__l="__l" u-p="{{w}}"></u-text></view><u-button wx:if="{{y}}" bindclick="{{x}}" u-i="2087c390-22" bind:__l="__l" u-p="{{y}}"></u-button></view><u-safe-bottom u-i="2087c390-23" bind:__l="__l"></u-safe-bottom></view></view>