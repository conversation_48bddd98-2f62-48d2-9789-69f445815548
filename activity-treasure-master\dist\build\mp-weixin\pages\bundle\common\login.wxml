<view class="page df fdc aic b6f data-v-ddbfa397"><u-gap wx:if="{{a}}" class="data-v-ddbfa397" u-i="ddbfa397-0" bind:__l="__l" u-p="{{a}}"></u-gap><block class="data-v-ddbfa397"><view class="container data-v-ddbfa397"><view class="image-container data-v-ddbfa397"><image class="fixed-image data-v-ddbfa397" src="{{b}}" mode="widthFix" lazy-load></image></view></view></block><u-gap wx:if="{{c}}" class="data-v-ddbfa397" u-i="ddbfa397-1" bind:__l="__l" u-p="{{c}}"></u-gap><view class="data-v-ddbfa397"><u-text wx:if="{{d}}" class="data-v-ddbfa397" u-i="ddbfa397-2" bind:__l="__l" u-p="{{d}}"></u-text></view><u-gap wx:if="{{e}}" class="data-v-ddbfa397" u-i="ddbfa397-3" bind:__l="__l" u-p="{{e}}"></u-gap><block wx:if="{{f}}"><view class="form-container data-v-ddbfa397"><view class="avatar-container data-v-ddbfa397"><button class="avatar-button data-v-ddbfa397" open-type="chooseAvatar" bindchooseavatar="{{h}}"><view class="avatar-wrapper data-v-ddbfa397"><image class="avatar data-v-ddbfa397" src="{{g}}" mode="aspectFill"></image><view class="upload-text data-v-ddbfa397">点击更换头像</view></view></button><view class="avatar-tip data-v-ddbfa397">微信头像或本地照片</view></view><u-gap wx:if="{{i}}" class="data-v-ddbfa397" u-i="ddbfa397-4" bind:__l="__l" u-p="{{i}}"></u-gap><input type="nickname" class="mb50 tac data-v-ddbfa397" value="{{j}}" placeholder="建议使用微信昵称" bindfocus="{{k}}" bindblur="{{l}}" bindinput="{{m}}"/><view wx:if="{{n}}" class="phone-status mb50 data-v-ddbfa397"><u-icon wx:if="{{o}}" class="data-v-ddbfa397" style="margin-right:16rpx" u-i="ddbfa397-5" bind:__l="__l" u-p="{{o}}"></u-icon> {{p}}</view><view wx:if="{{q}}" class="phone-input-section mb50 data-v-ddbfa397"><view class="phone-input-wrapper data-v-ddbfa397"><input type="number" class="phone-input data-v-ddbfa397" placeholder="请输入手机号" maxlength="11" value="{{r}}" bindinput="{{s}}"/><u-button wx:if="{{v}}" class="data-v-ddbfa397" bindclick="{{t}}" u-i="ddbfa397-6" bind:__l="__l" u-p="{{v}}"></u-button></view><view class="phone-tip data-v-ddbfa397"><text class="tip-text data-v-ddbfa397">请输入手机号完成注册</text></view></view><view class="{{['mb50', 'tac', 'gender-select', 'data-v-ddbfa397', x]}}" bindtap="{{y}}">{{w}}</view><u-gap wx:if="{{z}}" class="data-v-ddbfa397" u-i="ddbfa397-7" bind:__l="__l" u-p="{{z}}"></u-gap></view></block><view wx:if="{{A}}" class="agreement-section-global data-v-ddbfa397"><u-checkbox-group wx:if="{{F}}" class="data-v-ddbfa397" u-s="{{['d']}}" bindchange="{{D}}" u-i="ddbfa397-8" bind:__l="__l" bindupdateModelValue="{{E}}" u-p="{{F}}"><view class="df aic data-v-ddbfa397"><u-checkbox wx:if="{{B}}" class="data-v-ddbfa397" u-i="ddbfa397-9,ddbfa397-8" bind:__l="__l" u-p="{{B}}"></u-checkbox> 同意 <view class="text call data-v-ddbfa397" bindtap="{{C}}">   《用户注册协议》   </view></view></u-checkbox-group></view><view class="button-container-global data-v-ddbfa397"><button wx:if="{{G}}" class="wechat-submit-btn data-v-ddbfa397" open-type="getPhoneNumber" bindgetphonenumber="{{H}}"> 提交 </button><u-button wx:else class="data-v-ddbfa397" bindclick="{{I}}" u-i="ddbfa397-10" bind:__l="__l" u-p="{{J||''}}"></u-button></view><u-picker wx:if="{{N}}" class="data-v-ddbfa397" bindclose="{{K}}" bindcancel="{{L}}" bindconfirm="{{M}}" u-i="ddbfa397-11" bind:__l="__l" u-p="{{N}}"></u-picker></view>