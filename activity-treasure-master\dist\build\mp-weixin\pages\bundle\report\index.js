"use strict";const e=require("../../../common/vendor.js"),t=require("../../../utils/auth.js"),a=require("../../../store/index.js");if(require("../../../store/counter.js"),require("../../../utils/index.js"),require("../../../api/index.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-navbar")+e.resolveComponent("u-text")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-empty")+e.resolveComponent("u-avatar")+e.resolveComponent("u-icon")+e.resolveComponent("u-button")+e.resolveComponent("u-textarea"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-navbar/u-navbar.js")+(()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../../node-modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js"))();const o={__name:"index",setup(o){const r=e.ref(""),u=e.ref(1),n=e.ref(!1),i=e.ref(!1),s=e.ref(!1),l=e.ref([]),d=e.ref([]),c=e.ref(null),v=e.ref(null),p=e.ref(""),m={primary:{color:"#fff",fontWeight:"bold",borderRadius:"30rpx",fontSize:"28rpx",width:"45%"},secondary:{color:"#666",fontWeight:"bold",borderRadius:"30rpx",fontSize:"28rpx",width:"45%"}},x={borderColor:"#E5E5E5",borderRadius:"20rpx",padding:"20rpx",backgroundColor:"#fafafa"};e.onLoad((t=>{t.activity_id?(r.value=t.activity_id,f()):(e.index.$u.toast("活动ID缺失"),setTimeout((()=>{e.index.navigateBack()}),1500))}));const f=async()=>{n.value=!0;try{const o=t.getAuthParams();if(!o)return e.index.$u.toast("请先登录"),void e.index.navigateBack();const u=await e.index.request({url:`${a.store().$state.config.apiBaseUrl}User/check_report_permission`,method:"POST",data:o});if("ok"!==u.data.status)return e.index.$u.toast(u.data.msg||"无举报权限"),void e.index.navigateBack();const n=await e.index.request({url:`${a.store().$state.config.apiBaseUrl}User/get_activity_participants`,method:"POST",data:{...o,activity_id:r.value}});"ok"===n.data.status?l.value=n.data.data||[]:e.index.$u.toast(n.data.msg||"获取参与者列表失败")}catch(o){console.error("加载参与者列表失败:",o),e.index.$u.toast("操作失败，请稍后重试")}finally{n.value=!1}},g=async()=>{i.value=!0;try{const t=await e.index.request({url:`${a.store().$state.config.apiBaseUrl}User/get_report_options`,method:"GET"});"ok"===t.data.status?d.value=t.data.data||[]:e.index.$u.toast("获取举报选项失败")}catch(t){console.error("加载举报选项失败:",t),e.index.$u.toast("操作失败，请稍后重试")}finally{i.value=!1}},b=()=>{u.value>1&&u.value--},h=async()=>{if(p.value.trim()){s.value=!0;try{const o=t.getAuthParams(),u=await e.index.request({url:`${a.store().$state.config.apiBaseUrl}User/submit_report`,method:"POST",data:{...o,reported_uid:c.value.uid,activity_id:r.value,report_option_id:v.value.id,report_detail:p.value}});"ok"===u.data.status?e.index.showModal({title:"举报成功",content:"您的举报已提交，我们会尽快处理。感谢您的反馈！",showCancel:!1,success:()=>{e.index.navigateBack()}}):e.index.$u.toast(u.data.msg||"举报提交失败")}catch(o){console.error("提交举报失败:",o),e.index.$u.toast("操作失败，请稍后重试")}finally{s.value=!1}}else e.index.$u.toast("请输入举报详情")},y=()=>{e.index.navigateBack()};return(t,a)=>{var o,r,f;return e.e({a:e.o(y),b:e.p({title:"投诉/举报",border:!1,background:{backgroundColor:"#ffffff"},titleStyle:{color:"#333333",fontSize:"32rpx",fontWeight:"bold"},leftIcon:"arrow-left"}),c:u.value>=1?1:"",d:u.value>1?1:"",e:u.value>1?1:"",f:u.value>=2?1:"",g:u.value>2?1:"",h:u.value>2?1:"",i:u.value>=3?1:"",j:1===u.value},1===u.value?e.e({k:e.p({text:"请选择要举报的用户",size:"32rpx",bold:!0,color:"#333"}),l:n.value},n.value?{m:e.p({mode:"spinner",size:"60rpx"})}:0===l.value.length?{o:e.p({text:"暂无其他参与者",mode:"data"})}:{p:e.f(l.value,((t,a,o)=>{return{a:"861b5b94-4-"+o,b:e.p({src:t.avatar,size:"80rpx",customStyle:{marginRight:"24rpx"}}),c:e.t(t.nickname),d:e.t((r=t.role_type,{0:"管理员",1:"分会长",2:"普通用户",3:"场地方",4:"城市分会长",5:"邀请用户"}[r]||"用户")),e:"861b5b94-5-"+o,f:t.uid,g:e.o((e=>(e=>{c.value=e,u.value=2,g()})(t)),t.uid)};var r})),q:e.p({name:"arrow-right",size:"20",color:"#999"})},{n:0===l.value.length}):{},{r:2===u.value},2===u.value?e.e({s:e.p({text:"请选择举报理由",size:"32rpx",bold:!0,color:"#333"}),t:e.t(null==(o=c.value)?void 0:o.nickname),v:i.value},i.value?{w:e.p({mode:"spinner",size:"60rpx"})}:{x:e.f(d.value,((t,a,o)=>({a:e.t(t.title),b:e.t(t.description),c:"861b5b94-8-"+o,d:t.id,e:e.o((e=>(e=>{v.value=e,u.value=3})(t)),t.id)}))),y:e.p({name:"arrow-right",size:"20",color:"#999"})},{z:e.o(b),A:e.p({text:"上一步",color:"#f5f5f5",customStyle:m.secondary})}):{},{B:3===u.value},3===u.value?{C:e.p({text:"举报详情",size:"32rpx",bold:!0,color:"#333"}),D:e.t(null==(r=c.value)?void 0:r.nickname),E:e.t(null==(f=v.value)?void 0:f.title),F:e.o((e=>p.value=e)),G:e.p({placeholder:"请详细描述举报内容，提供具体事实和情况说明...",height:300,maxlength:500,"show-confirm-bar":!1,customStyle:x,modelValue:p.value}),H:e.t(p.value.length),I:e.o(b),J:e.p({text:"上一步",color:"#f5f5f5",customStyle:m.secondary}),K:e.o(h),L:e.p({text:"提交举报",color:"linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%)",customStyle:m.primary,loading:s.value})}:{})}}},r=e._export_sfc(o,[["__scopeId","data-v-861b5b94"]]);wx.createPage(r);
//# sourceMappingURL=index.js.map
