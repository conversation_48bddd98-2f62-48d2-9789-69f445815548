"use strict";const t=require("../../../../common/vendor.js"),e={name:"u-overlay",mixins:[t.mpMixin,t.mixin,t.props$43],computed:{overlayStyle(){const e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return t.deepMerge(e,t.addStyle(this.customStyle))}},emits:["click"],methods:{clickHandler(){this.$emit("click")}}};if(!Array){t.resolveComponent("u-transition")()}Math;const o=t._export_sfc(e,[["render",function(e,o,r,i,n,c){return{a:t.o(c.clickHandler),b:t.p({show:e.show,"custom-class":"u-overlay",duration:e.duration,"custom-style":c.overlayStyle})}}],["__scopeId","data-v-3734cd6c"]]);wx.createComponent(o);
//# sourceMappingURL=u-overlay.js.map
