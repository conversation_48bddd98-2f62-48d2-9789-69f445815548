{"version": 3, "file": "CustomTabBar.js", "sources": ["../../../../src/components/CustomTabBar.vue", "../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvQ3VzdG9tVGFiQmFyLnZ1ZQ"], "sourcesContent": ["<script setup>\nimport { ref, computed, onMounted } from 'vue'\n\n// 定义props\nconst props = defineProps({\n  current: {\n    type: Number,\n    default: 0\n  }\n})\n\n// 定义emits\nconst emit = defineEmits(['change'])\n\n// 当前选中的tab索引\nconst currentIndex = ref(props.current)\n\n// 导航栏配置\nconst tabList = [\n  {\n    pagePath: '/pages/index',\n    iconPath: '/static/index.png',\n    selectedIconPath: '/static/index.svg',\n    text: '活动',\n    index: 0\n  },\n  {\n    pagePath: '/pages/world',\n    iconPath: '/static/world.png',\n    selectedIconPath: '/static/world.svg',\n    text: '世界',\n    index: 1\n  },\n  {\n    pagePath: '/pages/addActive',\n    iconPath: '/static/addActive.svg',\n    selectedIconPath: '/static/addActive.svg',\n    text: '发布',\n    index: 2,\n    isSpecial: true // 标记为特殊按钮（骑缝式）\n  },\n  {\n    pagePath: '/pages/notifications',\n    iconPath: '/static/notification.svg',\n    selectedIconPath: '/static/notification-active.svg',\n    text: '通知',\n    index: 3\n  },\n  {\n    pagePath: '/pages/my', // 个人中心页面路径\n    iconPath: '/static/my.png',\n    selectedIconPath: '/static/myd.png',\n    text: '我的',\n    index: 4\n  }\n]\n\n// 获取当前页面路径并设置对应的tab\nonMounted(() => {\n  const pages = getCurrentPages()\n  if (pages.length > 0) {\n    const currentPage = pages[pages.length - 1]\n    const route = '/' + currentPage.route\n    \n    const tabIndex = tabList.findIndex(tab => tab.pagePath === route)\n    if (tabIndex !== -1) {\n      currentIndex.value = tabIndex\n    }\n  }\n})\n\n// 处理tab点击 - 优化：使用switchTab提升性能\nconst handleTabClick = (item, index) => {\n  if (currentIndex.value === index) return\n\n  // 添加点击动画反馈 - 微信小程序环境优化\n  try {\n    // 使用uni-app的方式添加点击反馈\n    uni.vibrateShort({\n      type: 'light'\n    })\n  } catch (e) {\n    // 忽略震动错误\n  }\n\n  currentIndex.value = index\n  emit('change', index)\n\n  // 发布页面特殊处理 - 修复重复点击失效问题\n  if (item.pagePath === '/pages/addActive') {\n    // 直接跳转到会员验证页面，移除页面检查逻辑以确保每次都能正常跳转\n    uni.navigateTo({\n      url: '/pages/addActive',\n      animationType: 'slide-in-bottom',\n      animationDuration: 200,\n      fail: (err) => {\n        console.error('跳转发布页面失败:', err)\n        // 降级处理：使用reLaunch\n        uni.reLaunch({\n          url: '/pages/addActive'\n        })\n      }\n    })\n  } else {\n    // 其他页面使用redirectTo实现页面切换，避免reLaunch导致的状态丢失\n    uni.redirectTo({\n      url: item.pagePath,\n      fail: (err) => {\n        console.error('页面跳转失败:', err)\n        // 降级处理：使用navigateTo\n        uni.navigateTo({\n          url: item.pagePath,\n          fail: (navErr) => {\n            console.error('navigateTo也失败:', navErr)\n            // 最后降级：使用reLaunch\n            uni.reLaunch({\n              url: item.pagePath,\n              fail: (relaunchErr) => {\n                console.error('reLaunch也失败:', relaunchErr)\n                uni.showToast({\n                  title: '页面跳转失败',\n                  icon: 'none'\n                })\n              }\n            })\n          }\n        })\n      }\n    })\n  }\n}\n\n// 判断是否为当前选中的tab\nconst isActive = (index) => {\n  return currentIndex.value === index\n}\n</script>\n\n<template>\n  <view class=\"custom-tab-bar\">\n    <!-- SVG波浪曲线背景 -->\n    <!-- 动态波浪曲线背景 - 微信小程序兼容版 -->\n    <svg class=\"wave-background\" viewBox=\"0 0 375 120\" preserveAspectRatio=\"none\">\n      <defs>\n        <!-- 优化的渐变效果，融入主色调 -->\n        <linearGradient id=\"waveGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:#ffffff;stop-opacity:0.95\" />\n          <stop offset=\"30%\" style=\"stop-color:#f8fdf9;stop-opacity:0.92\" />\n          <stop offset=\"70%\" style=\"stop-color:#f5fcf7;stop-opacity:0.88\" />\n          <stop offset=\"100%\" style=\"stop-color:#f0faf2;stop-opacity:0.85\" />\n        </linearGradient>\n        <!-- 添加蒙版渐变 -->\n        <linearGradient id=\"maskGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:#6AC086;stop-opacity:0.02\" />\n          <stop offset=\"100%\" style=\"stop-color:#ffffff;stop-opacity:0.08\" />\n        </linearGradient>\n      </defs>\n\n      <!-- 简化的波浪路径：保持发布按钮凸起效果 -->\n      <path\n        d=\"M 0,80\n           Q 75,75 150,70\n           Q 187.5,45 225,70\n           Q 300,75 375,80\n           L 375,120\n           L 0,120 Z\"\n        fill=\"url(#waveGradient)\"\n      />\n    </svg>\n\n    <!-- 导航栏背景蒙版层 -->\n    <view class=\"tab-bar-background\"></view>\n\n    <!-- 额外的渐变蒙版层 -->\n    <view class=\"tab-bar-mask\"></view>\n    \n    <!-- 导航项容器 -->\n    <view class=\"tab-bar-container\">\n      <view \n        v-for=\"(item, index) in tabList\" \n        :key=\"index\"\n        class=\"tab-item\"\n        :class=\"{ \n          'tab-item-active': isActive(index),\n          'tab-item-special': item.isSpecial \n        }\"\n        @click=\"handleTabClick(item, index)\"\n      >\n        <!-- 统一的按钮样式，不区分特殊按钮 -->\n        <image\n          class=\"tab-icon\"\n          :src=\"isActive(index) ? item.selectedIconPath : item.iconPath\"\n          mode=\"aspectFit\"\n        />\n        <text\n          class=\"tab-text\"\n          :class=\"{ 'tab-text-active': isActive(index) }\"\n        >\n          {{ item.text }}\n        </text>\n      </view>\n    </view>\n    \n    <!-- 安全区域适配 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.custom-tab-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  /* 确保波浪背景正确显示 */\n  overflow: visible;\n}\n\n/* 动态波浪曲线背景样式 - 优化版 */\n.wave-background {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 160rpx; /* 增加高度，确保曲线完全显示 */\n  z-index: 1;\n  pointer-events: none;\n  opacity: 1; /* 提高透明度，确保曲线可见 */\n}\n\n.tab-bar-background {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 160rpx; /* 增加高度确保完全覆盖 */\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(25rpx);\n  -webkit-backdrop-filter: blur(25rpx);\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1),\n              0 -8rpx 32rpx rgba(106, 192, 134, 0.08);\n  z-index: 2;\n  border-radius: 0;\n  /* 添加柔和的上边缘渐变 */\n  background-image: linear-gradient(\n    to bottom,\n    rgba(255, 255, 255, 0.85) 0%,\n    rgba(255, 255, 255, 0.9) 50%,\n    rgba(255, 255, 255, 0.95) 100%\n  );\n}\n\n.tab-bar-mask {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 160rpx; /* 增加高度确保完全覆盖 */\n  background: linear-gradient(\n    to bottom,\n    rgba(106, 192, 134, 0.02) 0%,\n    rgba(255, 255, 255, 0.05) 50%,\n    rgba(248, 249, 250, 0.08) 100%\n  );\n  z-index: 2.5;\n  pointer-events: none;\n  /* 添加边缘模糊效果 */\n  filter: blur(0.5rpx);\n}\n\n.tab-bar-container {\n  display: flex;\n  align-items: flex-end;\n  justify-content: space-around;\n  padding: 16rpx 0 12rpx 0;\n  background: transparent;\n  position: relative;\n  z-index: 4;\n  pointer-events: auto;\n  /* 确保内容在蒙版之上 */\n  height: 160rpx;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 8rpx 0;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n\n\n.tab-icon {\n  width: 48rpx;\n  height: 48rpx;\n  transition: all 0.3s ease;\n}\n\n/* 发布按钮特殊样式 - 移除背景，放大图标 */\n.tab-item-special .tab-icon {\n  width: 80rpx; /* 在62rpx基础上再放大30% (62 * 1.3 ≈ 80) */\n  height: 80rpx;\n  /* 移除所有背景样式 */\n  background: none;\n  border-radius: 0;\n  padding: 0;\n  box-shadow: none;\n}\n\n.tab-item-special .tab-text {\n  margin-top: 12rpx;\n  font-weight: 600;\n}\n\n.tab-text {\n  font-size: 24rpx;\n  color: #666666;\n  margin-top: 8rpx;\n  transition: all 0.3s ease;\n  line-height: 1;\n}\n\n.tab-text-active {\n  color: #6AC086;\n  font-weight: 600;\n}\n\n.tab-item-active .tab-icon {\n  transform: scale(1.1);\n}\n\n/* 特殊按钮激活状态 - 移除背景相关样式 */\n.tab-item-special.tab-item-active .tab-icon {\n  transform: scale(1.1); /* 与普通按钮保持一致的激活缩放效果 */\n  box-shadow: none; /* 移除阴影效果 */\n}\n\n// 安全区域适配\n.safe-area-bottom {\n  height: constant(safe-area-inset-bottom);\n  height: env(safe-area-inset-bottom);\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  -webkit-backdrop-filter: blur(20rpx);\n  position: relative;\n  z-index: 3;\n}\n\n// 响应式适配\n@media screen and (max-width: 750rpx) {\n  .tab-text {\n    font-size: 22rpx;\n  }\n\n  .tab-icon {\n    width: 44rpx;\n    height: 44rpx;\n  }\n\n  /* 小屏幕下发布按钮图标也相应调整 */\n  .tab-item-special .tab-icon {\n    width: 74rpx; /* 在57rpx基础上再放大30% (57 * 1.3 ≈ 74) */\n    height: 74rpx;\n  }\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/CustomTabBar.vue'\nwx.createComponent(Component)"], "names": ["currentIndex", "ref", "props", "current", "tabList", "pagePath", "iconPath", "selected<PERSON><PERSON><PERSON><PERSON>", "text", "index", "isSpecial", "onMounted", "pages", "getCurrentPages", "length", "route", "tabIndex", "findIndex", "tab", "value", "isActive", "item", "uni", "vibrateShort", "type", "e", "emit", "navigateTo", "url", "animationType", "animationDuration", "fail", "err", "console", "error", "reLaunch", "redirectTo", "navErr", "relaunchErr", "showToast", "title", "icon", "wx", "createComponent", "Component"], "mappings": "uUAeMA,EAAeC,EAAAA,IAAIC,EAAMC,SAGzBC,EAAU,CACd,CACEC,SAAU,eACVC,SAAU,oBACVC,iBAAkB,oBAClBC,KAAM,KACNC,MAAO,GAET,CACEJ,SAAU,eACVC,SAAU,oBACVC,iBAAkB,oBAClBC,KAAM,KACNC,MAAO,GAET,CACEJ,SAAU,mBACVC,SAAU,wBACVC,iBAAkB,wBAClBC,KAAM,KACNC,MAAO,EACPC,WAAW,GAEb,CACEL,SAAU,uBACVC,SAAU,2BACVC,iBAAkB,kCAClBC,KAAM,KACNC,MAAO,GAET,CACEJ,SAAU,YACVC,SAAU,iBACVC,iBAAkB,kBAClBC,KAAM,KACNC,MAAO,IAKXE,EAAAA,WAAU,KACR,MAAMC,EAAQC,kBACV,GAAAD,EAAME,OAAS,EAAG,CACpB,MACMC,EAAQ,IADMH,EAAMA,EAAME,OAAS,GACTC,MAE1BC,EAAWZ,EAAQa,WAAiBC,GAAAA,EAAIb,WAAaU,KACtC,IAAjBC,IACFhB,EAAamB,MAAQH,EAEzB,KAII,MA6DAI,EAAYX,GACTT,EAAamB,QAAUV,wnBA9DT,EAACY,EAAMZ,KAC5B,GAAIT,EAAamB,QAAUV,EAA3B,CAGI,IAEFa,EAAAA,MAAIC,aAAa,CACfC,KAAM,SAET,OAAQC,GAET,CAEAzB,EAAamB,MAAQV,EACrBiB,EAAK,SAAUjB,GAGO,qBAAlBY,EAAKhB,SAEPiB,EAAAA,MAAIK,WAAW,CACbC,IAAK,mBACLC,cAAe,kBACfC,kBAAmB,IACnBC,KAAOC,IACGC,QAAAC,MAAM,YAAaF,GAE3BV,EAAAA,MAAIa,SAAS,CACXP,IAAK,oBACN,IAKLN,EAAAA,MAAIc,WAAW,CACbR,IAAKP,EAAKhB,SACV0B,KAAOC,IACGC,QAAAC,MAAM,UAAWF,GAEzBV,EAAAA,MAAIK,WAAW,CACbC,IAAKP,EAAKhB,SACV0B,KAAOM,IACGJ,QAAAC,MAAM,iBAAkBG,GAEhCf,EAAAA,MAAIa,SAAS,CACXP,IAAKP,EAAKhB,SACV0B,KAAOO,IACGL,QAAAC,MAAM,eAAgBI,GAC9BhB,EAAAA,MAAIiB,UAAU,CACZC,MAAO,SACPC,KAAM,QACP,GAEJ,GAEJ,GArD2B,CAwDlC,yEChIFC,GAAGC,gBAAgBC"}