"use strict";const e=require("../../../../common/vendor.js");e.Schema.warning=function(){};const t={name:"u-form",mixins:[e.mpMixin,e.mixin,e.props$28],provide(){return{uForm:this}},data:()=>({formRules:{},validator:{},originalModel:null}),watch:{rules:{immediate:!0,handler(e){this.setRules(e)}},propsChange(e){var t;(null==(t=this.children)?void 0:t.length)&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))},model:{immediate:!0,handler(t){this.originalModel||(this.originalModel=e.deepClone(t))}}},computed:{propsChange(){return[this.errorType,this.borderBottom,this.labelPosition,this.labelWidth,this.labelAlign,this.labelStyle]}},created(){this.children=[]},methods:{setRules(t){0!==Object.keys(t).length&&(this.formRules=t,this.validator=new e.Schema(t))},resetFields(){this.resetModel()},resetModel(t){this.children.map((t=>{const i=null==t?void 0:t.prop,r=e.getProperty(this.originalModel,i);e.setProperty(this.model,i,r)}))},clearValidate(e){e=[].concat(e),this.children.map((t=>{(void 0===e[0]||e.includes(t.prop))&&(t.message=null)}))},async validateField(t,i,r=null){this.$nextTick((()=>{const o=[];t=[].concat(t);let s=this.children.map((i=>new Promise(((s,n)=>{const l=[];if(t.includes(i.prop)){const t=e.getProperty(this.model,i.prop),n=i.prop.split("."),a=n[n.length-1];let h=[];if(h=i.itemRules&&i.itemRules.length>0?i.itemRules:this.formRules[i.prop],!h)return void s();const c=[].concat(h);c.length||s();for(let d=0;d<c.length;d++){const n=c[d],h=[].concat(null==n?void 0:n.trigger);if(r&&!h.includes(r)){s();continue}new e.Schema({[a]:n}).validate({[a]:t},((t,r)=>{var n;e.test.array(t)&&(t.forEach((e=>{e.prop=i.prop})),o.push(...t),l.push(...t)),i.message=(null==(n=l[0])?void 0:n.message)?l[0].message:null,d==c.length-1&&s(o)}))}}else s({})}))));Promise.all(s).then((e=>{"function"==typeof i&&i(o)})).catch((e=>{console.error("An error occurred:",e)}))}))},validate(t){return new Promise(((t,i)=>{this.$nextTick((()=>{const r=this.children.map((e=>e.prop));this.validateField(r,(r=>{r.length?("toast"===this.errorType&&e.toast(r[0].message),i(r)):t(!0)}))}))}))}}};const i=e._export_sfc(t,[["render",function(e,t,i,r,o,s){return{}}]]);wx.createComponent(i);
//# sourceMappingURL=u-form.js.map
