<script setup>
import { watch, ref, reactive } from "vue";
import {
  userget_user_likes,
  userget_user_favorites,
  userget_user_published,
  userget_user_comments
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { navto } from "@/utils";

const activityType = ref("");
const pageTitle = ref("");
const currentTab = ref("feed"); // 当前选中的tab: feed, card, quote
const goods = ref([]);

// 调用mescroll的hook
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

// tab选项配置 - 转换为u-tabs组件格式
const tabList = [
  { name: "动态" },
  { name: "日卡" },
  { name: "摘录" },
  { name: "日记" }
];

// tab键值映射
const tabKeyMap = ["feed", "card", "quote", "diary"];

onLoad((options) => {
  activityType.value = options.type || "likes";

  // 设置页面标题
  const titleMap = {
    likes: "我赞过的",
    favorites: "我收藏的",
    published: "我发布的",
    comments: "我评论的"
  };
  pageTitle.value = titleMap[activityType.value] || "我的活动";
});

onReady(async () => {
  // 这样可以避免高度计算错误导致的遮挡问题
  height.value = "auto";
  console.log('使用自动高度布局');
});

onShow(() => {
  // 页面显示时刷新数据，确保数据同步
  const mescroll = getMescroll();
  if (mescroll) {
    mescroll.resetUpScroll();
  }
});

// 获取当前tab索引
const getCurrentTabIndex = () => {
  return tabKeyMap.indexOf(currentTab.value);
};

// 处理tab点击事件
const handleTabClick = (item) => {
  const newTabKey = tabKeyMap[item.index];
  if (currentTab.value !== newTabKey) {
    currentTab.value = newTabKey;
    goods.value = [];
    // 重新加载数据
    const mescroll = getMescroll();
    if (mescroll) {
      mescroll.resetUpScroll();
    }
  }
};

// 上拉加载的回调
const upCallback = async (mescroll) => {
  try {
    let res;
    const params = {
      page: mescroll.num,
      page_size: mescroll.size,
      type: getApiType() // 根据当前tab获取对应的API类型
    };

    console.log(`API调用参数:`, params, `活动类型: ${activityType.value}`);

    switch (activityType.value) {
      case "likes":
        res = await userget_user_likes(params);
        break;
      case "favorites":
        res = await userget_user_favorites(params);
        break;
      case "published":
        res = await userget_user_published(params);
        break;
      case "comments":
        res = await userget_user_comments(params);
        break;
      default:
        res = { status: "error", msg: "未知类型" };
    }

    console.log(`API响应结果:`, res);

    if (res.status === "ok") {
      let curPageData = [];

      // 根据当前tab和活动类型处理数据
      if (currentTab.value === "feed") {
        if (activityType.value === "likes") {
          curPageData = res.data.feeds.map(item => ({ ...item, type: "feed" }));
        } else if (activityType.value === "favorites") {
          curPageData = res.data.feeds.map(item => ({ ...item, type: "feed" }));
        } else if (activityType.value === "published") {
          curPageData = res.data.feeds.map(item => ({ ...item, type: "feed" }));
        } else if (activityType.value === "comments") {
          curPageData = res.data.feed_comments.map(item => ({ ...item, type: "feed_comment" }));
        }
      } else if (currentTab.value === "card") {
        if (activityType.value === "likes") {
          curPageData = res.data.cards.map(item => ({ ...item, type: "card" }));
        } else if (activityType.value === "favorites") {
          curPageData = res.data.cards.map(item => ({ ...item, type: "card" }));
        } else if (activityType.value === "published") {
          curPageData = []; // 日卡不支持发布
        } else if (activityType.value === "comments") {
          curPageData = res.data.card_comments.map(item => ({ ...item, type: "card_comment" }));
        }
      } else if (currentTab.value === "quote") {
        if (activityType.value === "likes") {
          curPageData = res.data.quotes ? res.data.quotes.map(item => ({ ...item, type: "quote" })) : [];
        } else if (activityType.value === "favorites") {
          curPageData = res.data.quotes ? res.data.quotes.map(item => ({ ...item, type: "quote" })) : [];
        } else if (activityType.value === "published") {
          curPageData = res.data.quotes.map(item => ({ ...item, type: "quote" }));
        } else if (activityType.value === "comments") {
          curPageData = res.data.quote_comments ? res.data.quote_comments.map(item => ({ ...item, type: "quote_comment" })) : [];
        }
      } else if (currentTab.value === "diary") {
        if (activityType.value === "likes") {
          curPageData = res.data.diaries ? res.data.diaries.map(item => ({ ...item, type: "diary" })) : [];
        } else if (activityType.value === "favorites") {
          curPageData = res.data.diaries ? res.data.diaries.map(item => ({ ...item, type: "diary" })) : [];
        } else if (activityType.value === "published") {
          curPageData = res.data.diaries ? res.data.diaries.map(item => ({ ...item, type: "diary" })) : [];
        } else if (activityType.value === "comments") {
          curPageData = res.data.diary_comments ? res.data.diary_comments.map(item => ({ ...item, type: "diary_comment" })) : [];
        }
      }

      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    } else if (res.status === "empty" || res === "n") {
      // 🔧 修复：服务器返回空数据时不显示错误提示
      if (mescroll.num == 1) goods.value = [];
      mescroll.endBySize(0, 0);
    } else {
      // 🔧 修复：只在真正的错误时显示提示，空数据不显示
      if (res.status !== "empty" && res !== "n") {
        console.error("获取数据失败:", res);
        // 只在网络错误或服务器错误时显示提示
        if (res.msg && !res.msg.includes('暂无') && !res.msg.includes('没有')) {
          uni.$u.toast(res.msg);
        }
      }
      mescroll.endErr();
    }
  } catch (error) {
    console.error("获取用户活动错误:", error);
    if (error.name === 'NetworkError' || error.message.includes('network') || error.message.includes('timeout')) {
      uni.$u.toast("网络连接失败，请检查网络后重试");
    }
    mescroll.endErr();
  }
};

// 根据当前tab获取API类型参数
const getApiType = () => {
  const typeMap = {
    feed: "feed",
    card: "card",
    quote: "quote",
    diary: "diary"
  };
  return typeMap[currentTab.value] || "";
};

// 点击项目跳转
const handleItemClick = (item) => {
  if (item.type === "feed" || item.type === "feed_comment") {
    const feedId = item.feed?.id || item.id;
    navto(`/pages/bundle/world/feed/detail?feedId=${feedId}`);
  } else if (item.type === "card" || item.type === "card_comment") {
    const cardId = item.card?.id || item.id;
    navto(`/pages/bundle/world/card/detail?cardId=${cardId}`);
  } else if (item.type === "quote" || item.type === "quote_comment") {
    const quoteId = item.quote?.id || item.id;
    navto(`/pages/bundle/world/quote/detail?quoteId=${quoteId}`);
  } else if (item.type === "diary" || item.type === "diary_comment") {
    const diaryId = item.diary?.id || item.id;
    navto(`/pages/bundle/world/diary/detail?diaryId=${diaryId}`);
  }
};

// 获取显示内容
const getDisplayContent = (item) => {
  if (item.type === "feed") {
    return item.feed?.content || item.content || "动态内容";
  } else if (item.type === "card") {
    return item.card?.content || item.content || "日卡内容";
  } else if (item.type === "quote") {
    return item.content || "摘录内容";
  } else if (item.type === "diary") {
    return item.diary?.content || item.content || "日记内容";
  } else if (item.type === "feed_comment") {
    return item.content || "评论内容";
  } else if (item.type === "card_comment") {
    return item.content || "评论内容";
  } else if (item.type === "quote_comment") {
    return item.content || "评论内容";
  } else if (item.type === "diary_comment") {
    return item.content || "评论内容";
  }
  return "内容";
};

// 获取引用内容（用于评论类型）
const getReferencedContent = (item) => {
  if (item.type === "feed_comment" && item.feed) {
    return item.feed.display_content || item.feed.content;
  } else if (item.type === "card_comment" && item.card) {
    return item.card.display_content || item.card.content;
  } else if (item.type === "quote_comment" && item.quote) {
    return item.quote.display_content || item.quote.content;
  } else if (item.type === "diary_comment" && item.diary) {
    return item.diary.display_content || item.diary.content;
  }
  return null;
};

// 获取显示图片
const getDisplayImage = (item) => {
  if (item.type === "feed") {
    const images = item.feed?.images || item.images || [];
    return images.length > 0 ? images[0] : null;
  } else if (item.type === "card") {
    return item.card?.image_url || item.image_url;
  } else if (item.type === "diary") {
    const images = item.diary?.images || item.images || [];
    return images.length > 0 ? images[0] : null;
  }
  return null;
};

// 获取类型标签
const getTypeLabel = (item) => {
  const typeMap = {
    feed: "动态",
    card: "日卡",
    quote: "摘录",
    diary: "日记",
    feed_comment: "动态评论",
    card_comment: "日卡评论",
    quote_comment: "摘录评论",
    diary_comment: "日记评论"
  };
  return typeMap[item.type] || "内容";
};
</script>

<template>
  <view class="page">
    <!-- 标题栏 -->
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      :title="pageTitle"
      color="#ffffff"
      :blod="true"
    ></myTitle>

    <!-- Tab选项卡 -->
    <view class="tab-container">
      <u-tabs
        :current="getCurrentTabIndex()"
        :list="tabList"
        :active-style="{
          borderRadius: '25rpx',
          textAlign: 'center',
          lineHeight: '56rpx',
          fontSize: '28rpx',
          color: '#ffffff',
          fontWeight: '600',
          backgroundColor: '#6AC086',
          boxShadow: '0 8rpx 24rpx rgba(106, 192, 134, 0.3), 0 4rpx 12rpx rgba(106, 192, 134, 0.2)',
          transition: 'all 0.3s ease',
          transform: 'translateY(-2rpx)'
        }"
        :inactiveStyle="{
          fontSize: '26rpx',
          color: '#6c757d',
          fontWeight: '400',
          borderRadius: '20rpx',
          backgroundColor: 'transparent'
        }"
        :itemStyle="{
          padding: '20rpx 32rpx',
          margin: '0 8rpx',
          minWidth: '120rpx'
        }"
        lineWidth="0"
        @click="handleTabClick"
      ></u-tabs>
    </view>

    <!-- 内容区域 -->
    <view class="px30">
      <mescroll-uni
        class="list"
        :height="height"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
      >
        <!-- 空状态 -->
        <view v-if="goods.length === 0" class="empty">
          <image :src="`${store().$state.url}empty.png`" mode="aspectFit" class="empty-img"></image>
          <text class="empty-text">暂无{{pageTitle.replace('我', '')}}内容</text>
        </view>

        <!-- 列表 -->
        <view v-else>
          <view
            class="item"
            v-for="(val, i) in goods"
            :key="i"
            @click="handleItemClick(val)"
          >
            <!-- 图片 -->
            <view class="img" v-if="getDisplayImage(val)">
              <image
                class="pic"
                :src="getDisplayImage(val)"
                mode="aspectFill"
                lazy-load
              ></image>
            </view>

            <!-- 内容 -->
            <view class="info" :class="{ 'full': !getDisplayImage(val) }">
              <view class="header">
                <view class="type">
                  <text class="type-tag">{{ getTypeLabel(val) }}</text>
                </view>
                <view class="time">
                  <u-text
                    size="20rpx"
                    color="#999"
                    :text="val.created_at || val.time || ''"
                  ></u-text>
                </view>
              </view>

              <view class="title">
                <u-text
                  size="28rpx"
                  bold
                  color="#333"
                  lines="2"
                  :text="getDisplayContent(val)"
                ></u-text>
              </view>

              <!-- 引用内容 -->
              <view class="quote" v-if="val.type.includes('comment') && getReferencedContent(val)">
                <view class="quote-label">引用内容：</view>
                <u-text
                  size="24rpx"
                  color="#666"
                  lines="1"
                  :text="getReferencedContent(val)"
                ></u-text>
              </view>
            </view>

            <!-- 箭头 -->
            <view class="arrow">
              <u-icon name="arrow-right" color="#999" size="32rpx"></u-icon>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less">
/* {{ AURA-X: Modify - 调整页面布局，移除旧tab样式，添加标准tab容器. Confirmed via 寸止. }} */
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
}

.tab-container {
  padding: 24rpx 30rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.tab-container .u-tabs {
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 8rpx;
}

/* {{ AURA-X: Modify - 移除过度的padding-top，使用标准布局. Confirmed via 寸止. }} */
.list {
  width: 100%;
  min-height: calc(100vh - 400rpx);
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-img {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.item {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
}

.item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.img {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.pic {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info.full {
  margin-left: 0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.type {
  display: flex;
  align-items: center;
}

.type-tag {
  font-size: 20rpx;
  color: #6AC086;
  background: rgba(106, 192, 134, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.2);
}

.title {
  margin-bottom: 8rpx;
}

.time {
  opacity: 0.8;
}

.quote {
  margin-top: 12rpx;
  padding: 12rpx;
  background: rgba(106, 192, 134, 0.05);
  border-radius: 8rpx;
  border-left: 3rpx solid #6AC086;
}

.quote-label {
  font-size: 20rpx;
  color: #6AC086;
  margin-bottom: 4rpx;
  font-weight: 500;
}

.arrow {
  padding-left: 20rpx;
  align-self: center;
}
</style>
