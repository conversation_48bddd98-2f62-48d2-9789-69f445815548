"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/index.js"),o=require("../../../store/index.js"),r=require("../../../utils/index.js"),s=require("../../../utils/auth.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),require("../../../store/counter.js"),!Array){e.resolveComponent("u-icon")()}Math;const i={__name:"trialGift",setup(i){const a=e.reactive({days:30,title:"30天会员体验券",description:"邀请好友免费体验会员服务",benefits:["免费参与所有活动","专属会员标识","优先客服支持","会员专享内容"],validDays:7}),l=e.computed((()=>{var e;const t=parseInt((null==(e=o.store().$state.userInfo)?void 0:e.role_type)||2);return[0,1,3,4,5].includes(t)}));e.onShareAppMessage((async e=>{var r,s,i,l,n,u,c,d,p;try{console.log("onShareAppMessage 被触发, 来源:",null==e?void 0:e.from,"目标:",null==e?void 0:e.target);try{const e=await t.usercreate_trial_share({uid:o.store().$state.userInfo.uid,token:o.store().$state.userInfo.token,trial_days:a.days});if(console.log("创建分享链接结果:",e),"ok"===(null==e?void 0:e.status)){const t={title:`赠送您一张${a.days}天会员体验券`,path:`/pages/bundle/user/trialClaim?code=${e.data.share_code}`,imageUrl:(null==(i=null==(s=null==(r=o.store().$state.config)?void 0:r.img_config)?void 0:s.app_logo)?void 0:i.val)||""};return console.log("体验券分享配置:",t),t}}catch(g){console.error("生成分享链接失败:",g)}return{title:"赠送您一张30天会员体验券",path:"/pages/bundle/user/trialGift",imageUrl:(null==(u=null==(n=null==(l=o.store().$state.config)?void 0:l.img_config)?void 0:n.app_logo)?void 0:u.val)||""}}catch(g){return console.error("分享配置失败:",g),{title:"赠送您一张30天会员体验券",path:"/pages/bundle/user/trialGift",imageUrl:(null==(p=null==(d=null==(c=o.store().$state.config)?void 0:c.img_config)?void 0:d.app_logo)?void 0:p.val)||""}}})),e.onLoad((()=>{l.value?s.requireLogin("","需要登录后才能分享体验券"):e.index.showModal({title:"权限不足",content:"您暂无权限分享会员体验券",showCancel:!1,success:()=>{e.index.navigateBack()}})}));const n=()=>{e.index.navigateBack()},u=()=>{r.navto("/pages/bundle/user/vip")};return(t,o)=>({a:e.p({name:"arrow-left",size:"22",color:"#333"}),b:e.o(n),c:e.p({name:"gift",color:"#245D3C",size:"32"}),d:e.t(a.title),e:e.t(a.days),f:e.t(a.description),g:e.t(a.validDays),h:e.p({name:"star",color:"#245D3C",size:"18"}),i:e.f(a.benefits,((t,o,r)=>({a:"b1ce7384-3-"+r,b:e.t(t),c:o}))),j:e.p({name:"checkmark",color:"#3AEE55",size:"16"}),k:e.p({name:"info-circle",color:"#245D3C",size:"18"}),l:e.t(a.validDays),m:e.o(u)})}},a=e._export_sfc(i,[["__scopeId","data-v-b1ce7384"]]);i.__runtimeHooks=2,wx.createPage(a);
//# sourceMappingURL=trialGift.js.map
