<view class="page b6f"><u-sticky wx:if="{{b}}" u-s="{{['d']}}" u-i="4b3c3e68-0" bind:__l="__l" u-p="{{b}}"><my-title wx:if="{{a}}" u-i="4b3c3e68-1,4b3c3e68-0" bind:__l="__l" u-p="{{a}}"/></u-sticky><view class="pa left50 tl50" style="{{'width:90rpx;height:90rpx;top:150rpx' + ';' + ('z-index:' + i)}}"><view class="pa w df aic jcc" style="{{'z-index:' + h}}"><button class="u-reset-button df aic jcc" open-type="chooseAvatar" type="balanced" bindchooseavatar="{{g}}"><image style="width:90rpx;height:90rpx;border-radius:50%" src="{{c}}" mode="aspectFill"></image><image class="pa" style="{{'z-index:' + d + ';' + 'bottom:0;right:0;width:50rpx;height:50rpx;z-index:9999999999999999'}}" src="{{e}}" mode="scaleToFill" bindchooseavatar="{{f}}"/></button></view></view><view class="px30 mt50"><view class="py30 df aic jcsb borderBottom"><view class="x26" style="color:#7e7d7d">昵称：</view><u-input wx:if="{{m}}" bindblur="{{j}}" bindinput="{{k}}" u-i="4b3c3e68-2" bind:__l="__l" bindupdateModelValue="{{l}}" u-p="{{m}}"></u-input></view><view class="py30 df aic jcsb borderBottom" bindtap="{{p}}"><view class="x26" style="color:#7e7d7d">出生日期：</view><view class="x26" style="{{'color:' + o}}">{{n}}</view></view><view class="py30 df aic jcsb borderBottom"><view class="x26" style="color:#7e7d7d">ID：</view><u-input wx:if="{{r}}" u-i="4b3c3e68-3" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"></u-input></view><view class="py30 df aic jcsb borderBottom"><view class="x26" style="color:#7e7d7d">手机号：</view><button class="u-reset-button btnm" open-type="getPhoneNumber" type="balanced" bindgetphonenumber="{{v}}"><u-input wx:if="{{t}}" u-i="4b3c3e68-4" bind:__l="__l" bindupdateModelValue="{{s}}" u-p="{{t}}"></u-input></button></view><view class="py30 df aic jcsb borderBottom" bindtap="{{y}}"><view class="x26" style="color:#7e7d7d">性别：</view><view class="x26" style="{{'color:' + x}}">{{w}}</view></view><view class="py30 df aic jcsb borderBottom"><view class="x26" style="color:#7e7d7d">常住城市：</view><u-input wx:if="{{A}}" u-i="4b3c3e68-5" bind:__l="__l" bindupdateModelValue="{{z}}" u-p="{{A}}"></u-input></view><view class="py30 df aic jcsb borderBottom" bindtap="{{C}}"><view class="x26" style="color:#7e7d7d">添加标签：</view><u-icon wx:if="{{B}}" u-i="4b3c3e68-6" bind:__l="__l" u-p="{{B}}"></u-icon></view><view class="py30 borderBottom"><view class="x26" style="color:#7e7d7d">简介：</view><view class="mt20" style="background-color:#f7f7f7;border-radius:20rpx"><u-textarea wx:if="{{E}}" u-i="4b3c3e68-7" bind:__l="__l" bindupdateModelValue="{{D}}" u-p="{{E}}"></u-textarea></view></view><view class="py30 borderBottom"><view class="x26" style="color:#7e7d7d"> 上传社交图：（可上传九张图片） </view><view class="mt20" style=""><u-upload wx:if="{{I}}" u-s="{{['d']}}" bindafterRead="{{G}}" binddelete="{{H}}" u-i="4b3c3e68-8" bind:__l="__l" u-p="{{I}}"><view class="df aic jcc" style="width:215rpx;height:215rpx;background:#eeeeee;border-radius:10rpx"><u-icon wx:if="{{F}}" u-i="4b3c3e68-9,4b3c3e68-8" bind:__l="__l" u-p="{{F}}"></u-icon></view></u-upload></view></view><u-gap wx:if="{{J}}" u-i="4b3c3e68-10" bind:__l="__l" u-p="{{J}}"></u-gap><view class="pfx bottom0 w690 b6f bottomBox"><u-button wx:if="{{L}}" bindclick="{{K}}" u-i="4b3c3e68-11" bind:__l="__l" u-p="{{L}}"></u-button><u-safe-bottom u-i="4b3c3e68-12" bind:__l="__l"></u-safe-bottom></view></view><u-picker wx:if="{{P}}" bindclose="{{M}}" bindcancel="{{N}}" bindconfirm="{{O}}" u-i="4b3c3e68-13" bind:__l="__l" u-p="{{P}}"></u-picker><u-datetime-picker wx:if="{{U}}" bindcancel="{{Q}}" bindclose="{{R}}" bindconfirm="{{S}}" u-i="4b3c3e68-14" bind:__l="__l" bindupdateModelValue="{{T}}" u-p="{{U}}"></u-datetime-picker><u-popup wx:if="{{ae}}" u-s="{{['d']}}" bindclose="{{ad}}" u-i="4b3c3e68-15" bind:__l="__l" u-p="{{ae}}"><view class="p30"><view class="df aic jcsb px20 pt10 pb10 r20" style="border:2rpx #aaa solid"><u-input wx:if="{{W}}" u-i="4b3c3e68-16,4b3c3e68-15" bind:__l="__l" bindupdateModelValue="{{V}}" u-p="{{W}}"></u-input><u-button wx:if="{{Y}}" bindclick="{{X}}" u-i="4b3c3e68-17,4b3c3e68-15" bind:__l="__l" u-p="{{Y}}"></u-button></view><view class="my20"><scroll-view scroll-y style="max-height:500rpx"><view class="df aic fw"><view wx:for="{{Z}}" wx:for-item="val" wx:key="d" class="pr m20 p20 x24" style="background-color:#eeeeee;border-radius:26rpx">{{val.a}} <view class="pa" style="top:-5rpx;right:-5rpx"><u-icon wx:if="{{aa}}" bindclick="{{val.b}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{aa}}"></u-icon></view></view></view></scroll-view></view><u-button wx:if="{{ac}}" bindclick="{{ab}}" u-i="4b3c3e68-19,4b3c3e68-15" bind:__l="__l" u-p="{{ac}}"></u-button></view></u-popup></view>