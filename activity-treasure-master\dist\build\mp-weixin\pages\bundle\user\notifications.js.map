{"version": 3, "file": "notifications.js", "sources": ["../../../../../../src/pages/bundle/user/notifications.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHVzZXJcbm90aWZpY2F0aW9ucy52dWU"], "sourcesContent": ["<script setup>\nimport { ref, reactive, onMounted } from \"vue\";\nimport { onLoad, onShow, onReachBottom, onPullDownRefresh } from \"@dcloudio/uni-app\";\nimport { userget_notifications, usermark_notification_read } from \"@/api\";\nimport { store } from \"@/store\";\nimport { navto } from \"@/utils\";\n\nconst notifications = ref([]);\nconst loading = ref(false);\nconst finished = ref(false);\nconst page = ref(1);\nconst pageSize = 10;\nconst total = ref(0);\nconst unreadCount = ref(0);\n\nonLoad(() => {\n  loadNotifications();\n});\n\nonShow(() => {\n  // 标记所有通知为已读\n  markAllAsRead();\n});\n\nonReachBottom(() => {\n  if (!finished.value && !loading.value) {\n    loadMore();\n  }\n});\n\nonPullDownRefresh(() => {\n  refresh();\n});\n\n// 加载通知列表\nconst loadNotifications = async (isRefresh = false) => {\n  if (loading.value) return;\n\n  if (isRefresh) {\n    page.value = 1;\n    finished.value = false;\n  }\n\n  loading.value = true;\n\n  try {\n    const res = await userget_notifications({\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      page: page.value,\n      page_size: pageSize\n    });\n\n    if (res?.status === 'ok') {\n      const newNotifications = res.data.list || [];\n\n      if (isRefresh) {\n        notifications.value = newNotifications;\n      } else {\n        notifications.value = [...notifications.value, ...newNotifications];\n      }\n\n      total.value = res.data.total || 0;\n      unreadCount.value = res.data.unread_count || 0;\n\n      // 检查是否还有更多数据\n      if (newNotifications.length < pageSize) {\n        finished.value = true;\n      }\n    } else if (res?.status === 'empty') {\n      if (isRefresh) {\n        notifications.value = [];\n      }\n      finished.value = true;\n    } else {\n      uni.$u.toast(res?.msg || '获取通知失败');\n    }\n  } catch (error) {\n    console.error('获取通知失败:', error);\n    uni.$u.toast('获取通知失败');\n  } finally {\n    loading.value = false;\n    if (isRefresh) {\n      uni.stopPullDownRefresh();\n    }\n  }\n};\n\n// 加载更多\nconst loadMore = () => {\n  page.value++;\n  loadNotifications();\n};\n\n// 刷新\nconst refresh = () => {\n  loadNotifications(true);\n};\n\n// 标记所有通知为已读\nconst markAllAsRead = async () => {\n  try {\n    await usermark_notification_read({\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      notification_id: 0 // 0表示标记所有为已读\n    });\n  } catch (error) {\n    console.error('标记已读失败:', error);\n  }\n};\n\n// 获取通知类型显示文本\nconst getNotificationTypeText = (type) => {\n  const typeMap = {\n    'share_success': '分享成功',\n    'receive_vip': '会员领取',\n    'activity_update': '活动更新',\n    'activity_cancelled': '活动取消',\n    'activity_registration': '活动报名',\n    'registration_cancelled': '取消报名',\n    'system': '系统通知'\n  };\n  return typeMap[type] || '通知';\n};\n\n// 判断是否为全局通知\nconst isGlobalNotification = (notification) => {\n  return notification.is_global === 1 || notification.is_global === '1';\n};\n\n// 获取通知类型图标\nconst getNotificationIcon = (type) => {\n  const iconMap = {\n    'share_success': 'share-square',\n    'receive_vip': 'vip',\n    'activity_update': 'calendar',\n    'system': 'bell'\n  };\n  return iconMap[type] || 'bell';\n};\n\n// 获取通知类型颜色\nconst getNotificationColor = (type) => {\n  const colorMap = {\n    'share_success': '#6AC086',\n    'receive_vip': '#D19C69',\n    'activity_update': '#5DADE2',\n    'system': '#999'\n  };\n  return colorMap[type] || '#999';\n};\n\n// {{ AURA-X: Modify - 修复时间格式显示，统一使用yyyy-MM-dd HH:mm:ss格式. Confirmed via 寸止. }}\n// 格式化时间\nconst formatTime = (timeStr) => {\n  // 修复iOS日期格式问题：将 \"2025-05-27 12:39:27\" 转换为 \"2025/05/27 12:39:27\"\n  const formattedTimeStr = timeStr.replace(/-/g, '/');\n  const time = new Date(formattedTimeStr);\n\n  // 检查日期是否有效\n  if (isNaN(time.getTime())) {\n    return '时间格式错误';\n  }\n\n  const now = new Date();\n  const diff = now - time;\n\n  if (diff < 60000) { // 1分钟内\n    return '刚刚';\n  } else if (diff < 3600000) { // 1小时内\n    return Math.floor(diff / 60000) + '分钟前';\n  } else if (diff < 86400000) { // 1天内\n    return Math.floor(diff / 3600000) + '小时前';\n  } else if (diff < 604800000) { // 1周内\n    return Math.floor(diff / 86400000) + '天前';\n  } else {\n    // 使用自定义格式化，确保显示为 yyyy-MM-dd HH:mm:ss 格式\n    const year = time.getFullYear();\n    const month = String(time.getMonth() + 1).padStart(2, '0');\n    const day = String(time.getDate()).padStart(2, '0');\n    const hours = String(time.getHours()).padStart(2, '0');\n    const minutes = String(time.getMinutes()).padStart(2, '0');\n    const seconds = String(time.getSeconds()).padStart(2, '0');\n\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n  }\n};\n\n// 点击通知项\nconst onNotificationClick = (notification) => {\n  // 根据通知类型进行相应的跳转\n  if (notification.type === 'activity_update' && notification.related_id) {\n    navto(`/pages/bundle/active/activeInfo?id=${notification.related_id}`);\n  } else if (notification.type === 'share_success' && notification.related_id) {\n    navto(`/pages/bundle/user/shareRecords`);\n  }\n  // 其他类型的通知可以根据需要添加跳转逻辑\n};\n</script>\n\n<template>\n  <view class=\"page\">\n    <myTitle\n      title=\"我的通知\"\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n      backColor=\"#ffffff\"\n      :backShow=\"true\"\n    ></myTitle>\n\n    <view class=\"notification-container\">\n      <!-- 通知统计 -->\n      <view v-if=\"total > 0\" class=\"notification-stats\">\n        <text class=\"stats-text\">共 {{ total }} 条通知</text>\n        <text v-if=\"unreadCount > 0\" class=\"unread-text\">{{ unreadCount }} 条未读</text>\n      </view>\n\n      <!-- 通知列表 -->\n      <view v-if=\"notifications.length > 0\" class=\"notification-list\">\n        <view\n          v-for=\"(item, index) in notifications\"\n          :key=\"item.id\"\n          class=\"notification-item\"\n          :class=\"{ 'unread': item.is_read == 0 }\"\n          @click=\"onNotificationClick(item)\"\n        >\n          <!-- 通知图标 -->\n          <view class=\"notification-icon\" :style=\"{ backgroundColor: getNotificationColor(item.type) + '20' }\">\n            <u-icon\n              :name=\"getNotificationIcon(item.type)\"\n              :color=\"getNotificationColor(item.type)\"\n              size=\"32rpx\"\n            ></u-icon>\n          </view>\n\n          <!-- 通知内容 -->\n          <view class=\"notification-content\">\n            <view class=\"notification-header\">\n              <text class=\"notification-title\">{{ item.title }}</text>\n              <text class=\"notification-time\">{{ formatTime(item.created_at) }}</text>\n            </view>\n            <text class=\"notification-text\">{{ item.content }}</text>\n            <view class=\"notification-footer\">\n              <view class=\"notification-tags\">\n                <text class=\"notification-type\">{{ getNotificationTypeText(item.type) }}</text>\n                <text v-if=\"isGlobalNotification(item)\" class=\"global-tag\">全局</text>\n              </view>\n              <view v-if=\"item.is_read == 0\" class=\"unread-dot\"></view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-else-if=\"!loading\" class=\"empty-state\">\n        <u-icon name=\"bell\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">暂无通知</text>\n        <text class=\"empty-desc\">您的通知消息将在这里显示</text>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading && notifications.length === 0\" class=\"loading-state\">\n        <u-loading-icon mode=\"circle\" color=\"#6AC086\" size=\"60rpx\"></u-loading-icon>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"loading && notifications.length > 0\" class=\"load-more\">\n        <u-loading-icon mode=\"circle\" color=\"#6AC086\" size=\"40rpx\"></u-loading-icon>\n        <text class=\"load-more-text\">加载中...</text>\n      </view>\n\n      <!-- 没有更多 -->\n      <view v-if=\"finished && notifications.length > 0\" class=\"no-more\">\n        <text class=\"no-more-text\">没有更多通知了</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"less\">\n.page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);\n  padding-bottom: 40rpx;\n  position: relative;\n  /* 确保页面不会被导航栏遮挡 */\n  overflow-x: hidden;\n}\n\n/* 确保myTitle组件正确显示 */\n:deep(.title) {\n  position: fixed !important;\n  top: 0 !important;\n  left: 0 !important;\n  right: 0 !important;\n  z-index: 9999 !important;\n  width: 100% !important;\n  /* 添加安全区域适配 */\n  padding-top: constant(safe-area-inset-top);\n  padding-top: env(safe-area-inset-top);\n}\n\n.notification-container {\n  padding: 20rpx 30rpx;\n  /* 增加顶部间距，确保不被导航栏遮挡 */\n  margin-top: calc(200rpx + constant(safe-area-inset-top));\n  margin-top: calc(200rpx + env(safe-area-inset-top));\n  min-height: calc(100vh - 200rpx - constant(safe-area-inset-top));\n  min-height: calc(100vh - 200rpx - env(safe-area-inset-top));\n}\n\n.notification-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);\n  border: 1rpx solid rgba(106, 192, 134, 0.08);\n}\n\n.stats-text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.unread-text {\n  font-size: 24rpx;\n  color: #FF6B6B;\n  background: rgba(255, 107, 107, 0.1);\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  border: 1rpx solid rgba(255, 107, 107, 0.2);\n}\n\n.notification-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.notification-item {\n  display: flex;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24rpx;\n  padding: 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);\n  border: 1rpx solid rgba(106, 192, 134, 0.08);\n  transition: all 0.3s ease;\n  position: relative;\n\n  &.unread {\n    border-left: 6rpx solid #6AC086;\n    background: linear-gradient(135deg, rgba(106, 192, 134, 0.02) 0%, rgba(255, 255, 255, 0.98) 100%);\n  }\n\n  &:active {\n    transform: scale(0.98);\n    box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.15);\n  }\n}\n\n.notification-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 20rpx;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.notification-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 8rpx;\n}\n\n.notification-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 600;\n  flex: 1;\n  margin-right: 16rpx;\n}\n\n.notification-time {\n  font-size: 22rpx;\n  color: #999;\n  flex-shrink: 0;\n}\n\n.notification-text {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 12rpx;\n}\n\n.notification-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.notification-tags {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.notification-type {\n  font-size: 22rpx;\n  color: #999;\n  background: rgba(106, 192, 134, 0.1);\n  padding: 4rpx 12rpx;\n  border-radius: 12rpx;\n}\n\n.global-tag {\n  font-size: 20rpx;\n  color: #fff;\n  background: #FF6B6B;\n  padding: 2rpx 8rpx;\n  border-radius: 8rpx;\n  font-weight: 500;\n}\n\n.unread-dot {\n  width: 12rpx;\n  height: 12rpx;\n  background: #FF6B6B;\n  border-radius: 50%;\n  border: 2rpx solid #fff;\n  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n  text-align: center;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  font-weight: 500;\n  margin: 24rpx 0 12rpx;\n}\n\n.empty-desc {\n  font-size: 26rpx;\n  color: #ccc;\n  line-height: 1.5;\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 40rpx;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-top: 16rpx;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-more-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-left: 16rpx;\n}\n\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.no-more-text {\n  font-size: 24rpx;\n  color: #999;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/user/notifications.vue'\nwx.createPage(MiniProgramPage)"], "names": ["notifications", "ref", "loading", "finished", "page", "total", "unreadCount", "onLoad", "onShow", "onReachBottom", "value", "onPullDownRefresh", "loadNotifications", "async", "isRefresh", "res", "userget_notifications", "uid", "store", "$state", "userInfo", "token", "page_size", "status", "newNotifications", "data", "list", "unread_count", "length", "uni", "index", "$u", "toast", "msg", "error", "console", "stopPullDownRefresh", "loadMore", "refresh", "markAllAsRead", "usermark_notification_read", "notification_id", "getNotificationTypeText", "type", "share_success", "receive_vip", "activity_update", "activity_cancelled", "activity_registration", "registration_cancelled", "system", "isGlobalNotification", "notification", "is_global", "getNotificationColor", "formatTime", "timeStr", "formattedTimeStr", "replace", "time", "Date", "isNaN", "getTime", "diff", "Math", "floor", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getSeconds", "related_id", "navto", "wx", "createPage", "MiniProgramPage"], "mappings": "6uBAOA,MAAMA,EAAgBC,EAAAA,IAAI,IACpBC,EAAUD,EAAAA,KAAI,GACdE,EAAWF,EAAAA,KAAI,GACfG,EAAOH,EAAAA,IAAI,GAEXI,EAAQJ,EAAAA,IAAI,GACZK,EAAcL,EAAAA,IAAI,GAExBM,EAAAA,QAAO,YAIPC,EAAAA,QAAO,YAKPC,EAAAA,eAAc,KACPN,EAASO,OAAUR,EAAQQ,UAEhC,IAGFC,EAAAA,mBAAkB,YAKZ,MAAAC,EAAoBC,MAAOC,GAAY,KAC3C,IAAIZ,EAAQQ,MAAZ,CAEII,IACFV,EAAKM,MAAQ,EACbP,EAASO,OAAQ,GAGnBR,EAAQQ,OAAQ,EAEZ,IACI,MAAAK,QAAYC,wBAAsB,CACtCC,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/BjB,KAAMA,EAAKM,MACXY,UAvCW,KA0CT,GAAgB,QAAhB,MAAAP,OAAA,EAAAA,EAAKQ,QAAiB,CACxB,MAAMC,EAAmBT,EAAIU,KAAKC,MAAQ,GAGxC1B,EAAcU,MADZI,EACoBU,EAEA,IAAIxB,EAAcU,SAAUc,GAG9CnB,EAAAK,MAAQK,EAAIU,KAAKpB,OAAS,EACpBC,EAAAI,MAAQK,EAAIU,KAAKE,cAAgB,EAGzCH,EAAiBI,OAvDV,KAwDTzB,EAASO,OAAQ,EAEzB,KAA+B,WAAX,MAALK,OAAK,EAAAA,EAAAQ,SACVT,IACFd,EAAcU,MAAQ,IAExBP,EAASO,OAAQ,GAEjBmB,EAAGC,MAACC,GAAGC,OAAM,MAAAjB,OAAA,EAAAA,EAAKkB,MAAO,SAE5B,OAAQC,GACCC,QAAAD,MAAM,UAAWA,GACzBL,EAAAA,MAAIE,GAAGC,MAAM,SACjB,CAAY,QACR9B,EAAQQ,OAAQ,EACZI,GACFe,EAAGC,MAACM,qBAER,CAjDmB,CAiDnB,EAIIC,EAAW,KACVjC,EAAAM,aAKD4B,EAAU,KACd1B,GAAkB,EAAI,EAIlB2B,EAAgB1B,UAChB,UACI2B,6BAA2B,CAC/BvB,IAAKC,EAAKA,QAAGC,OAAOC,SAASH,IAC7BI,MAAOH,EAAKA,QAAGC,OAAOC,SAASC,MAC/BoB,gBAAiB,GAEpB,OAAQP,GACCC,QAAAD,MAAM,UAAWA,EAC3B,GAIIQ,EAA2BC,IACf,CACdC,cAAiB,OACjBC,YAAe,OACfC,gBAAmB,OACnBC,mBAAsB,OACtBC,sBAAyB,OACzBC,uBAA0B,OAC1BC,OAAU,QAEGP,IAAS,MAIpBQ,EAAwBC,GACM,IAA3BA,EAAaC,WAA8C,MAA3BD,EAAaC,UAehDC,EAAwBX,IACX,CACfC,cAAiB,UACjBC,YAAe,UACfC,gBAAmB,UACnBI,OAAU,QAEIP,IAAS,QAKrBY,EAAcC,IAElB,MAAMC,EAAmBD,EAAQE,QAAQ,KAAM,KACzCC,EAAO,IAAIC,KAAKH,GAGtB,GAAII,MAAMF,EAAKG,WACN,MAAA,SAGH,MACAC,EADM,IAAIH,KACGD,EAEnB,GAAII,EAAO,IACF,MAAA,KACX,GAAaA,EAAO,KAChB,OAAOC,KAAKC,MAAMF,EAAO,KAAS,MACtC,GAAaA,EAAO,MAChB,OAAOC,KAAKC,MAAMF,EAAO,MAAW,MACxC,GAAaA,EAAO,OAChB,OAAOC,KAAKC,MAAMF,EAAO,OAAY,KAUrC,MAAO,GAPMJ,EAAKO,iBACJC,OAAOR,EAAKS,WAAa,GAAGC,SAAS,EAAG,QAC1CF,OAAOR,EAAKW,WAAWD,SAAS,EAAG,QACjCF,OAAOR,EAAKY,YAAYF,SAAS,EAAG,QAClCF,OAAOR,EAAKa,cAAcH,SAAS,EAAG,QACtCF,OAAOR,EAAKc,cAAcJ,SAAS,EAAG,MAGxD,mXAtD2B1B,SACX,CACdC,cAAiB,eACjBC,YAAe,MACfC,gBAAmB,WACnBI,OAAU,QAEGP,IAAS,yNAmDE,IAACS,EAED,qBAFCA,KAEVT,MAA8BS,EAAasB,WAC1DC,EAAAA,MAAM,sCAAsCvB,EAAasB,cAC1B,kBAAtBtB,EAAaT,MAA4BS,EAAasB,YAC/DC,QAAM,6CA/DkB,IAAChC,kXCnI7BiC,GAAGC,WAAWC"}