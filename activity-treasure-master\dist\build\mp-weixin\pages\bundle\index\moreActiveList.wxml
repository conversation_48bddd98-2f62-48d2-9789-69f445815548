<view class="page"><my-title wx:if="{{a}}" u-i="212bcd48-0" bind:__l="__l" u-p="{{a}}"></my-title><view class="pa px30 w" style="top:178rpx"><mescroll-uni wx:if="{{g}}" u-s="{{['d']}}" class="list" bindinit="{{c}}" binddown="{{d}}" bindup="{{e}}" bindtopclick="{{f}}" u-i="212bcd48-1" bind:__l="__l" u-p="{{g}}"><view wx:for="{{b}}" wx:for-item="val" wx:key="q" class="pr ma py30 df w690 borderBottom" bindtap="{{val.r}}"><view class="pa z20 ball r20" style="padding:10rpx 14rpx;top:46rpx;left:20rpx"><u-text wx:if="{{val.b}}" u-i="{{val.a}}" bind:__l="__l" u-p="{{val.b}}"></u-text></view><u-image wx:if="{{val.d}}" u-i="{{val.c}}" bind:__l="__l" u-p="{{val.d}}"></u-image><view class="ml20 df fdc jcsb"><view class="df aic"><u-avatar wx:if="{{val.f}}" u-i="{{val.e}}" bind:__l="__l" u-p="{{val.f}}"></u-avatar><u-text wx:if="{{val.h}}" u-i="{{val.g}}" bind:__l="__l" u-p="{{val.h}}"></u-text></view><u-text wx:if="{{val.j}}" u-i="{{val.i}}" bind:__l="__l" u-p="{{val.j}}"></u-text><u-icon wx:if="{{val.l}}" u-i="{{val.k}}" bind:__l="__l" u-p="{{val.l}}"></u-icon><u-text wx:if="{{val.n}}" u-i="{{val.m}}" bind:__l="__l" u-p="{{val.n}}"></u-text><u-icon wx:if="{{val.p}}" u-i="{{val.o}}" bind:__l="__l" u-p="{{val.p}}"></u-icon></view></view></mescroll-uni></view></view>