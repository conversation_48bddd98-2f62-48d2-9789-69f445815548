"use strict";const e=require("../../../common/vendor.js"),a=require("../../../api/index.js");require("../../../store/index.js");const u=require("../../../utils/china.js"),i=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-input")+e.resolveComponent("u-textarea")+e.resolveComponent("u-switch")+e.resolveComponent("u-button")+e.resolveComponent("u-safe-bottom")+e.resolveComponent("u-picker"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../node-modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../node-modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../node-modules/uview-plus/components/u-safe-bottom/u-safe-bottom.js")+(()=>"../../../node-modules/uview-plus/components/u-picker/u-picker.js"))();const o={__name:"addAddress",setup(o){const l=e.ref(!1),n=e.ref(""),s=e.ref(""),t=e.ref(""),r=e.ref(""),d=e.ref(!1),v=e.ref([[],[],[]]),c=e.ref(""),m=e.ref(""),h=e.ref(""),p=e.ref(""),f=e.ref(!1),x=e.ref([0,0,0]);e.onLoad((e=>{if(e.info){const a=JSON.parse(e.info);n.value=a.id,s.value=a.username,t.value=a.mobile,r.value=a.addr,d.value=1==a.is_default,l.value=a.edit,c.value=`${a.sheng}-${a.shi}-${a.qu}`,m.value=a.sheng_id,h.value=a.shi_id,p.value=a.qu_id}u.china_area.map((e=>{v.value[0].push({name:e.name,id:e.id})})),v.value[1]=u.china_area[0].children,v.value[2]=u.china_area[0].children[0].children}));const w=e=>{m.value=e.value[0].id,h.value=e.value[1].id,p.value=e.value[2].id,x.value=e.indexs,c.value=`${e.value[0].name}-${e.value[1].name}-${e.value[2].name}`,f.value=!1},_=e=>{switch(e.columnIndex){case 0:v.value[1]=u.china_area[e.indexs[0]].children,v.value[2]=u.china_area[e.indexs[0]].children[e.indexs[1]].children;break;case 1:v.value[2]=u.china_area[e.indexs[0]].children[e.indexs[1]].children}},j=e=>{d.value=e},q=()=>{e.index.showModal({title:"删除提示",content:"你将删除这个收货地址",success:async u=>{"ok"===(await a.userdel_addr({ids:n.value})).status&&e.index.navigateBack()}})},g=async()=>{let u={username:s.value,mobile:t.value,sheng_id:m.value,shi_id:h.value,qu_id:p.value,addr:r.value,is_default:d.value?1:0};if(!u.username)return void e.index.showToast({title:"请输入收件人姓名",icon:"none"});if(!u.mobile)return void e.index.showToast({title:"请输入收件人电话号码",icon:"none"});if(!u.sheng_id)return void e.index.showToast({title:"请选择收件地址",icon:"none"});if(!u.addr)return void e.index.showToast({title:"请输入收件人详细地址",icon:"none"});e.index.showLoading({title:"正在提交"});if("ok"===(await a.useradd_addr(u)).status)if(l.value){"ok"===(await a.userdel_addr({ids:n.value})).status&&(e.index.hideLoading(),i.back({tip:"提交成功，3秒后返回上一级页面"}))}else e.index.hideLoading(),e.index.navigateBack()};return(a,u)=>e.e({a:e.o((e=>s.value=e)),b:e.p({placeholder:"请输入收件人姓名",type:"text",modelValue:s.value}),c:e.o((e=>t.value=e)),d:e.p({placeholder:"请输入收件人电话号码",type:"text",modelValue:t.value}),e:e.t(c.value?c.value:"请选择地址"),f:e.o((e=>f.value=!0)),g:e.o((e=>r.value=e)),h:e.p({autoHeight:!0,placeholder:"输入详细地址",modelValue:r.value}),i:e.o(j),j:e.o((e=>d.value=e)),k:e.p({activeColor:"#EF662D",modelValue:d.value}),l:l.value},l.value?{m:e.o(q)}:{},{n:e.o(g),o:e.p({color:"#FAD000",text:"提交",customStyle:{width:"750rpx",height:"98rpx",color:"#000",fontSize:"34rpx"}}),p:e.o(w),q:e.o(_),r:e.o((e=>f.value=!1)),s:e.o((e=>f.value=!1)),t:e.p({defaultIndex:x.value,show:f.value,columns:v.value,keyName:"name",closeOnClickOverlay:!0})})},__runtimeHooks:1};wx.createPage(o);
//# sourceMappingURL=addAddress.js.map
