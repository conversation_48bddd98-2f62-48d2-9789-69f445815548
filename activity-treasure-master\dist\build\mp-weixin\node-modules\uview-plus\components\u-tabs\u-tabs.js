"use strict";const e=require("../../../../common/vendor.js"),t={name:"u-tabs",mixins:[e.mpMixin,e.mixin,e.props$15],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent="string"==typeof e?parseInt(e):e,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return t=>{const i={},s=t==this.innerCurrent?e.addStyle(this.activeStyle):e.addStyle(this.inactiveStyle);return this.list[t].disabled&&(i.color="#c8c9cc"),e.deepMerge(s,i)}},propsBadge:()=>e.defProps.badge},async mounted(){this.init()},emits:["click","longPress","change","update:current"],methods:{addStyle:e.addStyle,addUnit:e.addUnit,setLineLeft(){const t=this.list[this.innerCurrent];if(!t)return;let i=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0);const s=e.getPx(this.lineWidth);this.lineOffsetLeft=i+(t.rect.width-s)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),10)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t},t),e.disabled||(this.innerCurrent=t,this.resize(),this.$emit("update:current",t),this.$emit("change",{...e,index:t},t))},longPressHandler(e,t){this.$emit("longPress",{...e,index:t})},init(){e.sleep().then((()=>{this.resize()}))},setScrollLeft(){this.innerCurrent<0&&(this.innerCurrent=0);const t=this.list[this.innerCurrent],i=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),s=e.sys().windowWidth;let r=i-(this.tabsRect.width-t.rect.width)/2-(s-this.tabsRect.right)/2+this.tabsRect.left/2;r=Math.min(r,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,r)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([e,t=[]])=>{e.left>e.width&&(e.right=e.right-Math.floor(e.left/e.width)*e.width,e.left=e.left%e.width),this.tabsRect=e,this.scrollViewWidth=0,t.map(((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((e=>{this.queryRect("u-tabs__wrapper__scroll-view").then((t=>e(t)))}))},getAllItemRect(){return new Promise((e=>{const t=this.list.map(((e,t)=>this.queryRect(`u-tabs__wrapper__nav__item-${t}`,!0)));Promise.all(t).then((t=>e(t)))}))},queryRect(e,t){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))}}};if(!Array){e.resolveComponent("u-badge")()}Math;const i=e._export_sfc(t,[["render",function(t,i,s,r,n,a){return{a:e.f(t.list,((i,s,r)=>e.e(t.$slots.content?{a:"content-"+r,b:e.r("content",{item:i,keyName:t.keyName,index:s},r)}:t.$slots.content||!t.$slots.default&&!t.$slots.$default?{e:e.t(i[t.keyName]),f:e.n(i.disabled&&"u-tabs__wrapper__nav__item__text--disabled"),g:e.s(a.textStyle(s))}:{c:"d-"+r,d:e.r("d",{item:i,keyName:t.keyName,index:s},r)},{h:"93e76184-0-"+r,i:e.p({show:!(!i.badge||!(i.badge.show||i.badge.isDot||i.badge.value)),isDot:i.badge&&i.badge.isDot||a.propsBadge.isDot,value:i.badge&&i.badge.value||a.propsBadge.value,max:i.badge&&i.badge.max||a.propsBadge.max,type:i.badge&&i.badge.type||a.propsBadge.type,showZero:i.badge&&i.badge.showZero||a.propsBadge.showZero,bgColor:i.badge&&i.badge.bgColor||a.propsBadge.bgColor,color:i.badge&&i.badge.color||a.propsBadge.color,shape:i.badge&&i.badge.shape||a.propsBadge.shape,numberType:i.badge&&i.badge.numberType||a.propsBadge.numberType,inverted:i.badge&&i.badge.inverted||a.propsBadge.inverted,customStyle:"margin-left: 4px;"}),j:s,k:e.o((e=>a.clickHandler(i,s)),s),l:e.o((e=>a.longPressHandler(i,s)),s),m:`u-tabs__wrapper__nav__item-${s}`,n:e.n(`u-tabs__wrapper__nav__item-${s}`),o:e.n(i.disabled&&"u-tabs__wrapper__nav__item--disabled")}))),b:t.$slots.content,c:!t.$slots.content&&(t.$slots.default||t.$slots.$default),d:e.s(a.addStyle(t.itemStyle)),e:e.s({flex:t.scrollable?"":1}),f:e.s({width:a.addUnit(t.lineWidth),transform:`translate(${n.lineOffsetLeft}px)`,transitionDuration:`${n.firstTime?0:t.duration}ms`,height:a.addUnit(t.lineHeight),background:t.lineColor,backgroundSize:t.lineBgSize}),g:t.scrollable,h:n.scrollLeft,i:e.n(t.customClass)}}],["__scopeId","data-v-93e76184"]]);wx.createComponent(i);
//# sourceMappingURL=u-tabs.js.map
