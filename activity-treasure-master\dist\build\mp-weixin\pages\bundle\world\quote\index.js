"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),t=require("../../../../store/index.js"),n=require("../../../../utils/index.js"),s=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/cacheManager.js"),require("../../../../utils/systemInfo.js"),require("../../../../store/counter.js"),!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-empty")+e.resolveComponent("u-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js"))();const a={__name:"index",setup(a,{expose:i}){const r=e.ref([]),u=e.ref(!0),l=e.ref(!1),c=e.ref(!1),d=e.ref(!0),v=e.ref(1),g=e.ref(new Map),f=()=>{var e;return`quotes_list_user_${(null==(e=t.store().$state.userInfo)?void 0:e.uid)||"anonymous"}`},p=async(n=1,s=!1)=>{var a,i,p;try{if(console.log("QuoteIndex: 开始加载摘录数据",{page:n,isRefresh:s}),s)l.value=!0,v.value=1;else if(n>1)c.value=!0;else if(u.value=!0,1===n&&!s){const e=f(),o=g.value.get(e);if(o&&o.timestamp&&Date.now()-o.timestamp<3e5)return r.value=o.data,v.value=o.page,d.value=o.hasMore,u.value=!1,void console.log(`QuoteIndex: 从缓存恢复摘录数据，共 ${r.value.length} 条`)}const m={page:n,page_size:20,uid:(null==(a=t.store().$state.userInfo)?void 0:a.uid)||0,token:(null==(i=t.store().$state.userInfo)?void 0:i.token)||""};console.log("QuoteIndex: API请求参数",m);const h=await o.getQuotes(m);if(console.log("QuoteIndex: API响应结果",h),"ok"===h.status){const e=(null==(p=h.data)?void 0:p.list)||[];if(console.log("QuoteIndex: 获取到摘录数据",e.length,"条"),s||1===n){if(r.value=e,1===n){const o=f();g.value.set(o,{data:[...e],page:n,hasMore:20===e.length,timestamp:Date.now()}),console.log(`QuoteIndex: 缓存摘录数据，共 ${e.length} 条`)}}else r.value=[...r.value,...e];d.value=20===e.length,v.value=n}else"empty"===h.status?(console.log("QuoteIndex: 服务器返回空数据"),(s||1===n)&&(r.value=[]),d.value=!1):(console.warn("QuoteIndex: API返回错误状态",h.status,h.msg),e.index.showToast({title:h.msg||"加载失败",icon:"none"}))}catch(m){console.error("QuoteIndex: 加载摘录失败:",m),e.index.showToast({title:"加载失败",icon:"none"})}finally{u.value=!1,l.value=!1,c.value=!1}},m=()=>{p(1,!0)},h=()=>{!c.value&&d.value&&p(v.value+1)},_=e=>{if(!e)return"";const o=e.replace(/-/g,"/"),t=new Date(o),n=new Date,s=n-t;if(s<36e5){const e=Math.floor(s/6e4);return e<=0?"刚刚":`${e}分钟前`}if(s<864e5){return`${Math.floor(s/36e5)}小时前`}const a=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0"),u=String(t.getHours()).padStart(2,"0"),l=String(t.getMinutes()).padStart(2,"0");return a===n.getFullYear()?`${i}-${r} ${u}:${l}`:`${a}-${i}-${r} ${u}:${l}`},x=e=>{if(!e.images)return null;try{if("string"==typeof e.images){const o=JSON.parse(e.images);return Array.isArray(o)&&o.length>0?o[0]:null}if(Array.isArray(e.images))return e.images.length>0?e.images[0]:null}catch(o){console.warn("解析摘录图片失败:",o,e.images)}return null},w=()=>{console.log("QuoteIndex: 收到刷新事件，重新加载数据"),p(1,!0)};e.onMounted((()=>{console.log("QuoteIndex: 组件挂载，等待按需加载"),e.index.$on("refreshQuoteList",w)}));return i({loadQuoteData:()=>{console.log("QuoteIndex loadQuoteData - 被父组件调用"),p()}}),e.onUnmounted((()=>{e.index.$off("refreshQuoteList",w)})),(a,i)=>e.e({a:u.value},u.value?{b:e.p({mode:"circle",size:"30",color:"#6AC086"})}:e.e({c:0===r.value.length},0===r.value.length?{d:e.p({mode:"list",text:"还没有摘录哦",description:"收藏美好的文字片段"})}:{e:e.f(r.value,((a,i,r)=>{var u,l;return e.e({a:e.t(a.content),b:x(a)},x(a)?{c:x(a),d:e.o((o=>{return t=x(a),void e.index.previewImage({urls:[t],current:t});var t}),a.id)}:{},{e:a.author||a.source},a.author||a.source?e.e({f:a.author},a.author?{g:e.t(a.author)}:{},{h:a.source},a.source?{i:e.t(a.source)}:{}):{},{j:(null==(u=a.user)?void 0:u.avatar_url)||"/static/default-avatar.png",k:e.t((null==(l=a.user)?void 0:l.nickname)||"匿名"),l:"private"===a.privacy},"private"===a.privacy?{m:"0a8ef25b-2-"+r,n:e.p({name:"lock",size:"10",color:"#999"})}:{},{o:e.t(_(a.created_at)),p:"0a8ef25b-3-"+r,q:e.p({name:a.is_liked?"heart-fill":"heart",color:a.is_liked?"#ff4757":"#999",size:"16"}),r:e.t(a.like_count||0),s:e.o((n=>(async(n,a)=>{if(a.stopPropagation(),s.requireLogin("","请先登录后再点赞"))try{const s=await o.likeQuote({id:n.id,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});"ok"===s.status?(n.is_liked=!n.is_liked,n.like_count=n.is_liked?(n.like_count||0)+1:(n.like_count||1)-1,e.index.showToast({title:n.is_liked?"点赞成功":"取消点赞",icon:"success"})):e.index.showToast({title:s.msg||"操作失败",icon:"none"})}catch(i){console.error("点赞失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}})(a,n)),a.id),t:"0a8ef25b-4-"+r,v:e.p({name:a.is_favorited?"star-fill":"star",color:a.is_favorited?"#ffa502":"#999",size:"16"}),w:e.t(a.favorite_count||0),x:e.o((n=>(async(n,a)=>{if(a.stopPropagation(),s.requireLogin("","请先登录后再收藏"))try{const s=await o.favoriteQuote({id:n.id,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});"ok"===s.status?(n.is_favorited=!n.is_favorited,n.favorite_count=n.is_favorited?(n.favorite_count||0)+1:(n.favorite_count||1)-1,e.index.showToast({title:n.is_favorited?"收藏成功":"取消收藏",icon:"success"})):e.index.showToast({title:s.msg||"操作失败",icon:"none"})}catch(i){console.error("收藏失败:",i),e.index.showToast({title:"网络错误，请稍后重试",icon:"none"})}})(a,n)),a.id),y:"0a8ef25b-5-"+r,z:e.t(a.comment_count||0),A:e.o((e=>((e,o)=>{o.stopPropagation(),s.requireLogin("","请先登录后再评论")&&n.navto(`/pages/bundle/world/quote/detail?id=${e.id}&showComments=true`)})(a,e)),a.id),B:a.id,C:e.o((e=>(e=>{console.log("查看摘录:",e),n.navto(`/pages/bundle/world/quote/detail?id=${e.id}`)})(a)),a.id)})})),f:e.p({name:"chat",color:"#999",size:"16"})},{g:c.value},c.value?{h:e.p({mode:"circle",size:"20",color:"#6AC086"})}:{},{i:!d.value&&r.value.length>0},(!d.value&&r.value.length,{}),{j:l.value,k:e.o(m),l:e.o(h)}))}},i=e._export_sfc(a,[["__scopeId","data-v-0a8ef25b"]]);wx.createComponent(i);
//# sourceMappingURL=index.js.map
