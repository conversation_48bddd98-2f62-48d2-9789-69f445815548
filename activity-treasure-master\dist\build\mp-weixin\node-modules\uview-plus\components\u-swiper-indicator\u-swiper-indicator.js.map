{"version": 3, "file": "u-swiper-indicator.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-swiper-indicator/u-swiper-indicator.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN3aXBlci1pbmRpY2F0b3IvdS1zd2lwZXItaW5kaWNhdG9yLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"u-swiper-indicator\">\n\t\t<view\n\t\t\tclass=\"u-swiper-indicator__wrapper\"\n\t\t\tv-if=\"indicatorMode === 'line'\"\n\t\t\t:class=\"[`u-swiper-indicator__wrapper--${indicatorMode}`]\"\n\t\t\t:style=\"{\n\t\t\t\twidth: addUnit(lineWidth * length),\n\t\t\t\tbackgroundColor: indicatorInactiveColor\n\t\t\t}\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-swiper-indicator__wrapper--line__bar\"\n\t\t\t\t:style=\"[lineStyle]\"\n\t\t\t></view>\n\t\t</view>\n\t\t<view\n\t\t\tclass=\"u-swiper-indicator__wrapper\"\n\t\t\tv-if=\"indicatorMode === 'dot'\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-swiper-indicator__wrapper__dot\"\n\t\t\t\tv-for=\"(item, index) in length\"\n\t\t\t\t:key=\"index\"\n\t\t\t\t:class=\"[index === current && 'u-swiper-indicator__wrapper__dot--active']\"\n\t\t\t\t:style=\"[dotStyle(index)]\"\n\t\t\t>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit } from '../../libs/function/index';\n\t/**\n\t * SwiperIndicator 轮播图指示器\n\t * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用，\n\t * @tutorial https://ijry.github.io/uview-plus/components/swiper.html\n\t * @property {String | Number}\tlength\t\t\t\t\t轮播的长度（默认 0 ）\n\t * @property {String | Number}\tcurrent\t\t\t\t\t当前处于活动状态的轮播的索引（默认 0 ）\n\t * @property {String}\t\t\tindicatorActiveColor\t指示器非激活颜色\n\t * @property {String}\t\t\tindicatorInactiveColor\t指示器的激活颜色\n\t * @property {String}\t\t\tindicatorMode\t\t\t指示器模式（默认 'line' ）\n\t * @example\t<u-swiper :list=\"list4\" indicator keyName=\"url\" :autoplay=\"false\"></u-swiper>\n\t */\n\texport default {\n\t\tname: 'u-swiper-indicator',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlineWidth: 22\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 指示器为线型的样式\n\t\t\tlineStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle.width = addUnit(this.lineWidth)\n\t\t\t\tstyle.transform = `translateX(${ addUnit(this.current * this.lineWidth) })`\n\t\t\t\tstyle.backgroundColor = this.indicatorActiveColor\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 指示器为点型的样式\n\t\t\tdotStyle() {\n\t\t\t\treturn index => {\n\t\t\t\t\tlet style = {}\n\t\t\t\t\tstyle.backgroundColor = index === this.current ? this.indicatorActiveColor : this.indicatorInactiveColor\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddUnit\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-swiper-indicator {\n\n\t\t&__wrapper {\n\t\t\t@include flex;\n\n\t\t\t&--line {\n\t\t\t\tborder-radius: 100px;\n\t\t\t\theight: 4px;\n\n\t\t\t\t&__bar {\n\t\t\t\t\twidth: 22px;\n\t\t\t\t\theight: 4px;\n\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\tbackground-color: #FFFFFF;\n\t\t\t\t\ttransition: transform 0.3s;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__dot {\n\t\t\t\twidth: 5px;\n\t\t\t\theight: 5px;\n\t\t\t\tborder-radius: 100px;\n\t\t\t\tmargin: 0 4px;\n\n\t\t\t\t&--active {\n\t\t\t\t\twidth: 12px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-swiper-indicator/u-swiper-indicator.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "lineWidth", "computed", "lineStyle", "style", "width", "addUnit", "this", "transform", "current", "backgroundColor", "indicatorActiveColor", "dotStyle", "index", "indicatorInactiveColor", "methods", "wx", "createComponent", "Component"], "mappings": "6DAgDMA,EAAU,CACdC,KAAM,qBACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,KAAO,KACC,CACNC,UAAW,KAGbC,SAAU,CAET,SAAAC,GACC,IAAIC,EAAQ,CAAC,EAIN,OAHPA,EAAMC,MAAQC,UAAQC,KAAKN,WAC3BG,EAAMI,UAAY,cAAeF,EAAOA,QAACC,KAAKE,QAAUF,KAAKN,cAC7DG,EAAMM,gBAAkBH,KAAKI,qBACtBP,CACP,EAED,QAAAQ,GACC,OAAgBC,IACf,IAAIT,EAAQ,CAAC,EAEN,OADPA,EAAMM,gBAAkBG,IAAUN,KAAKE,QAAUF,KAAKI,qBAAuBJ,KAAKO,uBAC3EV,CAAA,CAET,GAEDW,QAAS,CACRT,QAAAA,EAAMA,yeC1ETU,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}