"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../api/index.js"),t=require("../../../../store/index.js"),l=require("../../../../utils/index.js"),a=require("../../../../utils/auth.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/cacheManager.js"),require("../../../../utils/systemInfo.js"),require("../../../../store/counter.js"),!Array){(e.resolveComponent("u-sticky")+e.resolveComponent("u-loadmore")+e.resolveComponent("u-empty"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-sticky/u-sticky.js")+(()=>"../../../../node-modules/uview-plus/components/u-loadmore/u-loadmore.js")+(()=>"../../../../node-modules/uview-plus/components/u-empty/u-empty.js")+n)();const n=()=>"../../../../components/share-popup/share-popup.js",i={__name:"index",setup(n,{expose:i}){const s=e.ref([]),u=e.ref(!1),d=e.ref(!1),r=e.ref(!1),v=e.ref(""),c=e.ref(!0),g=e.ref(new Map),p=e.ref(new Map),m=e.ref(new Map),f=e.reactive({page:1,page_size:10,uid:0,token:"",user_id:0,category:"latest",type:"feed"}),h=e.ref([{name:"最新",id:"latest"},{name:"热门",id:"hot"}]),k=e.ref("latest"),$=e=>{if(!e)return"";const o=e.replace(/-/g,"/"),t=new Date(o),l=new Date,a=l-t;if(a<36e5){const e=Math.floor(a/6e4);return e<=0?"刚刚":`${e}分钟前`}if(a<864e5){return`${Math.floor(a/36e5)}小时前`}const n=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),u=String(t.getHours()).padStart(2,"0"),d=String(t.getMinutes()).padStart(2,"0");return n===l.getFullYear()?`${i}-${s} ${u}:${d}`:`${n}-${i}-${s} ${u}:${d}`},w=(e,o=null)=>{const l=t.store().$state.userInfo;return`feed_${e}_user_${o||f.uid||(null==l?void 0:l.uid)||"anonymous"}`},F=async(e=!1,l=!1)=>{var a;const n=t.store().$state.userInfo,i=(null==n?void 0:n.uid)||0,h=(null==n?void 0:n.token)||"";if(console.log(`fetchFeeds called - loadMore: ${e}, isRefresh: ${l}, uid: ${i}, token present: ${!!h}, category: ${k.value}`),!u.value||l){l?(d.value=!0,f.page=1,c.value=!0,console.log("下拉刷新，重置分页状态")):u.value=!0,r.value=!1,v.value="",e||l?e&&console.log(`加载更多，当前页码: ${f.page}`):(f.page=1,c.value=!0,console.log("首次加载，重置分页状态"));try{f.uid=i,f.token=h,f.category=k.value,f.type="feed",console.log("Executing API call: getFeeds with params:",JSON.parse(JSON.stringify(f)));const l=await o.getFeeds(f);if(console.log("API getFeeds response:",l),"ok"===l.status&&(null==(a=l.data)?void 0:a.list)){const o=l.data.list.map((e=>{const o=t.store().getFeedLikeState(e.id),l=t.store().getFeedFavoriteState(e.id);return{...e,isLiked:o?o.isLiked:e.isLiked,likeCount:o?o.likeCount:e.likeCount,isFavorited:null!==l?l:e.isFavorited,isExpanded:!1}}));e?s.value.push(...o):s.value=o,c.value=s.value.length<(l.data.total||0),f.page++;const a=w(k.value,f.uid);g.value.set(a,[...s.value]),p.value.set(a,f.page),m.value.set(a,c.value),console.log(`更新分类 ${k.value} 的缓存，用户 ${f.uid}，共 ${s.value.length} 条数据`)}else"empty"===l.status?(e||(s.value=[]),c.value=!1):(r.value=!0,v.value=l.msg||"获取动态失败",e||(s.value=[]),c.value=!1)}catch($){console.error("Error caught in fetchFeeds:",$),console.error("Error fetching feeds:",$),r.value=!0,v.value="加载失败，请稍后重试",e||(s.value=[]),c.value=!1}finally{u.value=!1,d.value=!1}}else console.log("正在加载中，跳过重复请求")},x=()=>{console.log("触发下拉刷新"),F(!1,!0)},y=()=>{console.log("重置动态列表状态"),s.value=[],f.page=1,c.value=!0,u.value=!1,r.value=!1,v.value="",e.nextTick$1((()=>{console.log("动态列表状态重置完成，数据长度:",s.value.length)}))},I=e=>{e?l.navto(`/pages/bundle/user/userInfo?uid=${e}`):console.warn("用户ID为空，无法跳转")},L=()=>{},_=()=>{},T=e.ref(!1),j=e.ref(null);e.onShareAppMessage((()=>{var e,o,l,a,n,i,s,u,d,r;try{return j.value?{title:j.value.content?j.value.content.length>30?j.value.content.substring(0,30)+"...":j.value.content:"分享一条精彩动态",path:`/pages/bundle/world/feed/detail?feedId=${j.value.id}`,imageUrl:(null==(a=j.value.images)?void 0:a[0])||(null==(s=null==(i=null==(n=t.store().$state.config)?void 0:n.img_config)?void 0:i.app_logo)?void 0:s.val)||""}:(console.warn("动态信息未设置，使用默认分享信息"),{title:"分享一条精彩动态",path:"/pages/bundle/world/feed/index",imageUrl:(null==(l=null==(o=null==(e=t.store().$state.config)?void 0:e.img_config)?void 0:o.app_logo)?void 0:l.val)||""})}catch(v){return console.error("动态分享配置失败:",v),{title:"分享一条精彩动态",path:"/pages/bundle/world/feed/index",imageUrl:(null==(r=null==(d=null==(u=t.store().$state.config)?void 0:u.img_config)?void 0:d.app_logo)?void 0:r.val)||""}}}));const q=o=>{console.log("分享成功:",o),e.index.showToast({title:"分享成功",icon:"success"})},C=o=>{console.error("分享失败:",o),e.index.showToast({title:"分享失败",icon:"none"})},M=(o,t)=>{e.index.previewImage({urls:o,current:t})};e.onLoad((()=>{console.log("FeedIndex onLoad - 开始初始化");const e=t.store().$state.userInfo;(null==e?void 0:e.uid)&&(f.uid=e.uid,f.token=e.token||"",console.log("用户信息已设置:",{uid:f.uid,hasToken:!!f.token})),console.log("FeedIndex onLoad - 初始化完成，等待按需加载"),t.store().loadWorldStateFromLocal(),k.value="latest",console.log("设置默认分类为:",k.value),y()})),e.onMounted((()=>{console.log("FeedIndex onMounted - 组件已挂载")}));i({loadFeedData:()=>{console.log("FeedIndex loadFeedData - 被父组件调用"),F()}});let S=null;return e.onReachBottom((()=>{S&&clearTimeout(S),S=setTimeout((()=>{c.value&&!u.value?(console.log("Reached bottom, loading more feeds..."),F(!0)):c.value||console.log("No more feeds to load.")}),300)})),(n,i)=>{var S,b,D,U,A,E,N,z;return e.e({a:e.f(h.value,((o,t,l)=>e.e({a:e.t(o.name),b:k.value===o.id},(k.value,o.id,{}),{c:o.id,d:k.value===o.id?1:"",e:e.o((t=>(o=>{if(k.value===o)return void console.log(`分类 ${o} 已经是当前选中状态，跳过切换`);if(console.log(`分类切换: ${k.value} -> ${o}`),s.value.length>0){const e=w(k.value,f.uid);g.value.set(e,[...s.value]),p.value.set(e,f.page),m.value.set(e,c.value),console.log(`缓存分类 ${k.value} 的数据，用户 ${f.uid}，共 ${s.value.length} 条`)}k.value=o;const t=w(o,f.uid);g.value.has(t)?(s.value=[...g.value.get(t)],f.page=p.value.get(t)||1,c.value=m.value.get(t)||!0,u.value=!1,r.value=!1,v.value="",console.log(`从缓存恢复分类 ${o} 的数据，用户 ${f.uid}，共 ${s.value.length} 条`)):(y(),e.nextTick$1((()=>{console.log(`开始获取分类 ${o} 的新数据，用户 ${f.uid}`),F()})))})(o.id)),o.id)}))),b:e.p({"offset-top":"0",customNavHeight:"0"}),c:e.f(s.value,((n,i,s)=>e.e({a:n.user.avatar_url||"/static/default-avatar.png",b:e.o((e=>I(n.user.uid)),n.id),c:e.t(n.user.nickname),d:e.t($(n.created_at)),e:e.o((e=>I(n.user.uid)),n.id),f:e.t(n.content),g:e.o((o=>(o=>{try{console.log(`handleFeedClick triggered for feedId: ${o}`),l.navto(`/pages/bundle/world/feed/detail?feedId=${o}&showComments=true`)}catch(t){console.error("导航到详情页失败:",t),e.index.showToast({title:"页面加载失败，请重试",icon:"none",duration:2e3})}})(n.id)),n.id),h:n.images&&n.images.length>0},n.images&&n.images.length>0?e.e({i:1===n.images.length},1===n.images.length?{j:n.images[0],k:e.o((e=>M(n.images,0)),n.id),l:e.o(L,n.id),m:e.o(_,n.id)}:{n:e.f(n.images.slice(0,9),((o,t,l)=>({a:o,b:e.o(L,t),c:e.o(_,t),d:t,e:e.o((e=>M(n.images,t)),t)})))}):{},{o:n.isLiked?"/static/dianzanqianhou.svg":"/static/dianzanqian.svg",p:n.isLiked?"none":"opacity(0.7)",q:e.o((l=>(async l=>{const n=t.store().$state.userInfo,i=null==n?void 0:n.token,s=null==n?void 0:n.uid;if(console.log(`handleLike triggered - Token exists: ${!!i}, UserID: ${s}`),!a.requireLogin("","请先登录后再点赞"))return;const u=l.isLiked,d=l.likeCount;l.isLiked=!l.isLiked,l.likeCount+=l.isLiked?1:-1;try{const a=await o.likeFeed({id:l.id,uid:s,token:i});"ok"!==a.status?(l.isLiked=u,l.likeCount=d,e.index.showToast({title:a.msg||"操作失败",icon:"none"})):(t.store().updateFeedLike(l.id,l.isLiked,l.likeCount),console.log("Like/Unlike success",a))}catch(r){l.isLiked=u,l.likeCount=d,e.index.showToast({title:"操作失败，请重试",icon:"none"}),console.error("Like feed error:",r)}})(n)),n.id),r:e.o((e=>{return o=n.id,console.log("Comment clicked for feed:",o),void l.navto(`/pages/bundle/world/feed/detail?feedId=${o}&showComments=true`);var o}),n.id),s:n.isFavorited?"/static/shoucanghou.svg":"/static/shoucangqian.svg",t:n.isFavorited?"none":"opacity(0.7)",v:e.o((l=>(async l=>{const a=t.store().$state.userInfo,n=null==a?void 0:a.token,i=null==a?void 0:a.uid;if(!n)return void e.index.showToast({title:"请先登录",icon:"none"});const s=l.isFavorited||!1;l.isFavorited=!l.isFavorited;try{const a=await o.favoriteFeed({id:l.id,uid:i,token:n});"ok"!==a.status?(l.isFavorited=s,e.index.showToast({title:a.msg||"操作失败",icon:"none"})):(t.store().updateFeedFavorite(l.id,l.isFavorited),e.index.showToast({title:a.msg||"操作成功",icon:"success"}))}catch(u){l.isFavorited=s,e.index.showToast({title:"操作失败，请重试",icon:"none"}),console.error("Favorite feed error:",u)}})(n)),n.id),w:n.id}))),d:!u.value&&s.value.length>0},!u.value&&s.value.length>0?{e:e.p({status:c.value?u.value?"loading":"loadmore":"nomore",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"到底啦"})}:{},{f:!u.value&&!r.value&&0===s.value.length},u.value||r.value||0!==s.value.length?{}:{g:e.p({mode:"list",text:"还没有人发布动态哦"})},{h:r.value&&0===s.value.length},r.value&&0===s.value.length?{i:e.p({mode:"network",text:v.value})}:{},{j:d.value,k:e.o(x),l:e.o(((...e)=>n.loadMoreFeeds&&n.loadMoreFeeds(...e))),m:e.o((e=>T.value=!1)),n:e.o(q),o:e.o(C),p:e.p({show:T.value,title:"分享动态","share-data":{image:null==(b=null==(S=j.value)?void 0:S.images)?void 0:b[0],content:null==(D=j.value)?void 0:D.content,author:null==(A=null==(U=j.value)?void 0:U.user)?void 0:A.nickname,date:null==(E=j.value)?void 0:E.created_at,template:"dynamic"},"show-member-invite":0===(null==(N=e.unref(t.store)().$state.userInfo)?void 0:N.role_type)||1===(null==(z=e.unref(t.store)().$state.userInfo)?void 0:z.role_type)})})}}},s=e._export_sfc(i,[["__scopeId","data-v-f9c372cd"]]);wx.createComponent(s);
//# sourceMappingURL=index.js.map
