{"version": 3, "file": "detail.js", "sources": ["../../../../../../../src/pages/bundle/world/author/detail.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGF1dGhvclxkZXRhaWwudnVl"], "sourcesContent": ["<script setup>\nimport { ref, onMounted } from 'vue';\nimport { getAuthorDetail } from '@/api/index.js';\nimport { store } from '@/store';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// {{ AURA-X: Add - 作者详情页面. Confirmed via 寸止 }}\nconst authorInfo = ref(null);\nconst loading = ref(true);\nconst error = ref('');\n\n// 获取页面参数\nconst pages = getCurrentPages();\nconst currentPageInstance = pages[pages.length - 1];\nconst authorId = currentPageInstance.options.id;\n\n// 获取作者详情\nconst fetchAuthorDetail = async () => {\n  if (!authorId) {\n    error.value = '作者ID无效';\n    loading.value = false;\n    return;\n  }\n\n  try {\n    loading.value = true;\n    const params = {\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || '',\n      author_id: authorId\n    };\n\n    const response = await getAuthorDetail(params);\n    \n    if (response.status === 'ok') {\n      authorInfo.value = response.data.author;\n    } else {\n      error.value = response.msg || '获取作者详情失败';\n    }\n  } catch (err) {\n    console.error('获取作者详情失败:', err);\n    error.value = '网络错误，请稍后重试';\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 格式化时间信息\nconst formatLifespan = (birthYear, deathYear) => {\n  if (!birthYear && !deathYear) return '';\n  if (birthYear && deathYear) return `${birthYear} - ${deathYear}`;\n  if (birthYear && !deathYear) return `${birthYear} - 至今`;\n  if (!birthYear && deathYear) return `? - ${deathYear}`;\n  return '';\n};\n\n// 页面加载\nonMounted(() => {\n  fetchAuthorDetail();\n});\n</script>\n\n<template>\n  <view class=\"author-detail-page\">\n    <!-- 统一导航栏 -->\n    <customNavbar \n      :title=\"authorInfo?.name || '作者详情'\" \n      backIcon=\"arrow-left\"\n    />\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <u-loading-icon mode=\"spinner\" color=\"#6AC086\" size=\"40\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n    \n    <!-- 错误状态 -->\n    <view v-else-if=\"error\" class=\"error-container\">\n      <u-icon name=\"error-circle\" size=\"60\" color=\"#ff4757\"></u-icon>\n      <text class=\"error-text\">{{ error }}</text>\n      <view class=\"retry-btn\" @click=\"fetchAuthorDetail\">\n        <text>重试</text>\n      </view>\n    </view>\n    \n    <!-- 作者详情内容 -->\n    <view v-else-if=\"authorInfo\" class=\"detail-container\">\n      <!-- 作者基本信息 -->\n      <view class=\"author-header\">\n        <view class=\"avatar-section\">\n          <image \n            v-if=\"authorInfo.avatar\" \n            :src=\"authorInfo.avatar\" \n            class=\"author-avatar\"\n            mode=\"aspectFill\"\n          />\n          <view v-else class=\"default-avatar\">\n            <u-icon name=\"account-fill\" size=\"60\" color=\"#ccc\"></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"info-section\">\n          <view class=\"author-name\">{{ authorInfo.name }}</view>\n          <view v-if=\"authorInfo.category\" class=\"author-category\">{{ authorInfo.category }}</view>\n          <view v-if=\"authorInfo.nationality\" class=\"author-nationality\">{{ authorInfo.nationality }}</view>\n          <view v-if=\"formatLifespan(authorInfo.birth_year, authorInfo.death_year)\" class=\"author-lifespan\">\n            {{ formatLifespan(authorInfo.birth_year, authorInfo.death_year) }}\n          </view>\n        </view>\n      </view>\n      \n      <!-- 统计信息 -->\n      <view class=\"stats-section\">\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ authorInfo.quote_count || 0 }}</view>\n          <view class=\"stat-label\">摘录引用</view>\n        </view>\n      </view>\n      \n      <!-- 作者简介 -->\n      <view v-if=\"authorInfo.description\" class=\"description-section\">\n        <view class=\"section-title\">简介</view>\n        <view class=\"description-content\">{{ authorInfo.description }}</view>\n      </view>\n      \n      <!-- 详细信息 -->\n      <view class=\"details-section\">\n        <view class=\"section-title\">详细信息</view>\n        \n        <view v-if=\"authorInfo.birth_year\" class=\"detail-item\">\n          <view class=\"detail-label\">出生年份</view>\n          <view class=\"detail-value\">{{ authorInfo.birth_year }}</view>\n        </view>\n        \n        <view v-if=\"authorInfo.death_year\" class=\"detail-item\">\n          <view class=\"detail-label\">逝世年份</view>\n          <view class=\"detail-value\">{{ authorInfo.death_year }}</view>\n        </view>\n        \n        <view v-if=\"authorInfo.nationality\" class=\"detail-item\">\n          <view class=\"detail-label\">国籍</view>\n          <view class=\"detail-value\">{{ authorInfo.nationality }}</view>\n        </view>\n        \n        <view v-if=\"authorInfo.category\" class=\"detail-item\">\n          <view class=\"detail-label\">类别</view>\n          <view class=\"detail-value\">{{ authorInfo.category }}</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.author-detail-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  \n  .loading-container, .error-container {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 120rpx 32rpx;\n    \n    .loading-text, .error-text {\n      margin-top: 24rpx;\n      font-size: 28rpx;\n      color: #666;\n    }\n    \n    .retry-btn {\n      margin-top: 32rpx;\n      padding: 16rpx 32rpx;\n      background-color: #6AC086;\n      border-radius: 50rpx;\n      color: white;\n      font-size: 28rpx;\n    }\n  }\n  \n  .detail-container {\n    padding: 32rpx;\n    \n    .author-header {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 40rpx;\n      margin-bottom: 32rpx;\n      display: flex;\n      align-items: center;\n      \n      .avatar-section {\n        margin-right: 32rpx;\n        \n        .author-avatar {\n          width: 120rpx;\n          height: 120rpx;\n          border-radius: 60rpx;\n        }\n        \n        .default-avatar {\n          width: 120rpx;\n          height: 120rpx;\n          border-radius: 60rpx;\n          background-color: #f5f5f5;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n      }\n      \n      .info-section {\n        flex: 1;\n        \n        .author-name {\n          font-size: 36rpx;\n          font-weight: 600;\n          color: #333;\n          margin-bottom: 12rpx;\n        }\n        \n        .author-category {\n          font-size: 28rpx;\n          color: #6AC086;\n          margin-bottom: 8rpx;\n        }\n        \n        .author-nationality, .author-lifespan {\n          font-size: 26rpx;\n          color: #666;\n          margin-bottom: 6rpx;\n        }\n      }\n    }\n    \n    .stats-section {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 32rpx;\n      display: flex;\n      justify-content: center;\n      \n      .stat-item {\n        text-align: center;\n        \n        .stat-number {\n          font-size: 48rpx;\n          font-weight: 600;\n          color: #6AC086;\n          margin-bottom: 8rpx;\n        }\n        \n        .stat-label {\n          font-size: 26rpx;\n          color: #666;\n        }\n      }\n    }\n    \n    .description-section, .details-section {\n      background-color: white;\n      border-radius: 20rpx;\n      padding: 32rpx;\n      margin-bottom: 32rpx;\n      \n      .section-title {\n        font-size: 32rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 24rpx;\n      }\n      \n      .description-content {\n        font-size: 30rpx;\n        color: #666;\n        line-height: 1.6;\n      }\n      \n      .detail-item {\n        display: flex;\n        align-items: center;\n        padding: 20rpx 0;\n        border-bottom: 1rpx solid #f0f0f0;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        .detail-label {\n          width: 160rpx;\n          font-size: 30rpx;\n          color: #666;\n          flex-shrink: 0;\n        }\n        \n        .detail-value {\n          flex: 1;\n          font-size: 30rpx;\n          color: #333;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/author/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "authorInfo", "ref", "loading", "error", "pages", "getCurrentPages", "authorId", "length", "options", "id", "fetchAuthorDetail", "async", "value", "params", "uid", "store", "$state", "userInfo", "token", "author_id", "response", "getAuthorDetail", "status", "data", "author", "msg", "err", "console", "formatLifespan", "birthYear", "deathYear", "onMounted", "wx", "createPage", "MiniProgramPage"], "mappings": "mqBAIA,MAAMA,EAAe,IAAW,qEAG1B,MAAAC,EAAaC,EAAAA,IAAI,MACjBC,EAAUD,EAAAA,KAAI,GACdE,EAAQF,EAAAA,IAAI,IAGZG,EAAQC,kBAERC,EADsBF,EAAMA,EAAMG,OAAS,GACZC,QAAQC,GAGvCC,EAAoBC,kBACxB,IAAKL,EAGH,OAFAH,EAAMS,MAAQ,cACdV,EAAQU,OAAQ,GAId,IACFV,EAAQU,OAAQ,EAChB,MAAMC,EAAS,CACbC,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,GACzCC,UAAWb,GAGPc,QAAiBC,kBAAgBR,GAEf,OAApBO,EAASE,OACAtB,EAAAY,MAAQQ,EAASG,KAAKC,OAE3BrB,EAAAS,MAAQQ,EAASK,KAAO,UAEjC,OAAQC,GACCC,QAAAxB,MAAM,YAAauB,GAC3BvB,EAAMS,MAAQ,YAClB,CAAY,QACRV,EAAQU,OAAQ,CACjB,GAIGgB,EAAiB,CAACC,EAAWC,IAC5BD,GAAcC,EACfD,GAAaC,EAAkB,GAAGD,OAAeC,IACjDD,IAAcC,EAAkB,GAAGD,UAClCA,GAAaC,EAAkB,OAAOA,IACpC,GAJ8B,UAQvCC,EAAAA,WAAU,wpCCxDVC,GAAGC,WAAWC"}