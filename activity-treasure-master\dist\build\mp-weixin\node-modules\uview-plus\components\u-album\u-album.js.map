{"version": 3, "file": "u-album.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-album/u-album.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWFsYnVtL3UtYWxidW0udnVl"], "sourcesContent": ["<template>\r\n    <view class=\"u-album\">\r\n        <view\r\n            class=\"u-album__row\"\r\n            ref=\"u-album__row\"\r\n            v-for=\"(arr, index) in showUrls\"\r\n            :forComputedUse=\"albumWidth\"\r\n            :key=\"index\"\r\n            :style=\"{flexWrap: autoWrap ? 'wrap' : 'nowrap'}\"\r\n        >\r\n            <view\r\n                class=\"u-album__row__wrapper\"\r\n                v-for=\"(item, index1) in arr\"\r\n                :key=\"index1\"\r\n                :style=\"[imageStyle(index + 1, index1 + 1)]\"\r\n                @tap=\"previewFullImage ? onPreviewTap($event, getSrc(item)) : ''\"\r\n            >\r\n                <image\r\n                    :src=\"getSrc(item)\"\r\n                    :mode=\"\r\n                        urls.length === 1\r\n                            ? imageHeight > 0\r\n                                ? singleMode\r\n                                : 'widthFix'\r\n                            : multipleMode\r\n                    \"\r\n                    :style=\"[\r\n                        {\r\n                            width: imageWidth,\r\n                            height: imageHeight,\r\n                            borderRadius: shape == 'circle' ? '10000px' : addUnit(radius)\r\n                        }\r\n                    ]\"\r\n                ></image>\r\n                <view\r\n                    v-if=\"\r\n                        showMore &&\r\n                        urls.length > rowCount * showUrls.length &&\r\n                        index === showUrls.length - 1 &&\r\n                        index1 === showUrls[showUrls.length - 1].length - 1\r\n                    \"\r\n                    class=\"u-album__row__wrapper__text\"\r\n                    :style=\"{\r\n\t\t\t\t\t    borderRadius: shape == 'circle' ? '50%' : addUnit(radius),\r\n\t\t\t\t    }\"\r\n                >\r\n                    <up-text\r\n                        :text=\"`+${urls.length - maxCount}`\"\r\n                        color=\"#fff\"\r\n                        :size=\"multipleSize * 0.3\"\r\n                        align=\"center\"\r\n                        customStyle=\"justify-content: center\"\r\n                    ></up-text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport { props } from './props';\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { addUnit, sleep } from '../../libs/function/index';\r\nimport test from '../../libs/function/test';\r\n// #ifdef APP-NVUE\r\n// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\r\nconst dom = uni.requireNativePlugin('dom')\r\n// #endif\r\n\r\n/**\r\n * Album 相册\r\n * @description 本组件提供一个类似相册的功能，让开发者开发起来更加得心应手。减少重复的模板代码\r\n * @tutorial https://ijry.github.io/uview-plus/components/album.html\r\n *\r\n * @property {Array}           urls             图片地址列表 Array<String>|Array<Object>形式\r\n * @property {String}          keyName          指定从数组的对象元素中读取哪个属性作为图片地址\r\n * @property {String | Number} singleSize       单图时，图片长边的长度  （默认 180 ）\r\n * @property {String | Number} multipleSize     多图时，图片边长 （默认 70 ）\r\n * @property {String | Number} space            多图时，图片水平和垂直之间的间隔 （默认 6 ）\r\n * @property {String}          singleMode       单图时，图片缩放裁剪的模式 （默认 'scaleToFill' ）\r\n * @property {String}          multipleMode     多图时，图片缩放裁剪的模式 （默认 'aspectFill' ）\r\n * @property {String | Number} maxCount         取消按钮的提示文字 （默认 9 ）\r\n * @property {Boolean}         previewFullImage 是否可以预览图片 （默认 true ）\r\n * @property {String | Number} rowCount         每行展示图片数量，如设置，singleSize和multipleSize将会无效\t（默认 3 ）\r\n * @property {Boolean}         showMore         超出maxCount时是否显示查看更多的提示 （默认 true ）\r\n * @property {String}          shape            图片形状，circle-圆形，square-方形 （默认 'square' ）\r\n * @property {String | Number} radius           圆角值，单位任意，如果为数值，则为px单位 （默认 0 ）\r\n * @property {Boolean}         autoWrap         自适应换行模式，不受rowCount限制，图片会自动换行 （默认 false ）\r\n * @property {String}          unit             图片单位 （默认 px ）\r\n * @event    {Function}        albumWidth       某些特殊的情况下，需要让文字与相册的宽度相等，这里事件的形式对外发送  （回调参数 width ）\r\n * @example <u-album :urls=\"urls2\" @albumWidth=\"width => albumWidth = width\" multipleSize=\"68\" ></u-album>\r\n */\r\nexport default {\r\n    name: 'u-album',\r\n    mixins: [mpMixin, mixin, props],\r\n    data() {\r\n        return {\r\n            // 单图的宽度\r\n            singleWidth: 0,\r\n            // 单图的高度\r\n            singleHeight: 0,\r\n            // 单图时，如果无法获取图片的尺寸信息，让图片宽度默认为容器的一定百分比\r\n            singlePercent: 0.6\r\n        }\r\n    },\r\n    watch: {\r\n        urls: {\r\n            immediate: true,\r\n            handler(newVal) {\r\n                if (newVal.length === 1) {\r\n                    this.getImageRect()\r\n                }\r\n            }\r\n        }\r\n    },\r\n\temits: [\"albumWidth\"],\r\n    computed: {\r\n        imageStyle() {\r\n            return (index1, index2) => {\r\n                const { space, rowCount, multipleSize, urls } = this,\r\n                    { addUnit, addStyle } = uni.$u,\r\n                    rowLen = this.showUrls.length,\r\n                    allLen = this.urls.length\r\n                const style = {\r\n                    marginRight: addUnit(space),\r\n                    marginBottom: addUnit(space)\r\n                }\r\n                // 如果为最后一行，则每个图片都无需下边框\r\n                if (index1 === rowLen && !this.autoWrap) style.marginBottom = 0\r\n                // 每行的最右边一张和总长度的最后一张无需右边框\r\n                if (!this.autoWrap) {\r\n                    if (\r\n                        index2 === rowCount ||\r\n                        (index1 === rowLen &&\r\n                            index2 === this.showUrls[index1 - 1].length)\r\n                    )\r\n                        style.marginRight = 0\r\n                }\r\n                return style\r\n            }\r\n        },\r\n        // 将数组划分为二维数组\r\n        showUrls() {\r\n            if (this.autoWrap) {\r\n                return [ this.urls.slice(0, this.maxCount) ];\r\n            } else {\r\n                const arr = []\r\n                this.urls.map((item, index) => {\r\n                    // 限制最大展示数量\r\n                    if (index + 1 <= this.maxCount) {\r\n                        // 计算该元素为第几个素组内\r\n                        const itemIndex = Math.floor(index / this.rowCount)\r\n                        // 判断对应的索引是否存在\r\n                        if (!arr[itemIndex]) {\r\n                            arr[itemIndex] = []\r\n                        }\r\n                        arr[itemIndex].push(item)\r\n                    }\r\n                })\r\n                return arr\r\n            }\r\n        },\r\n        imageWidth() {\r\n            return addUnit(\r\n                this.urls.length === 1 ? this.singleWidth : this.multipleSize, this.unit\r\n            )\r\n        },\r\n        imageHeight() {\r\n            return addUnit(\r\n                this.urls.length === 1 ? this.singleHeight : this.multipleSize, this.unit\r\n            )\r\n        },\r\n        // 此变量无实际用途，仅仅是为了利用computed特性，让其在urls长度等变化时，重新计算图片的宽度\r\n        // 因为用户在某些特殊的情况下，需要让文字与相册的宽度相等，所以这里事件的形式对外发送\r\n        albumWidth() {\r\n            let width = 0\r\n            if (this.urls.length === 1) {\r\n                width = this.singleWidth\r\n            } else {\r\n                width =\r\n                    this.showUrls[0].length * this.multipleSize +\r\n                    this.space * (this.showUrls[0].length - 1)\r\n            }\r\n            this.$emit('albumWidth', width)\r\n            return width\r\n        }\r\n    },\r\n    methods: {\r\n        addUnit,\r\n        // 预览图片\r\n        onPreviewTap(e, url) {\r\n            const urls = this.urls.map((item) => {\r\n                return this.getSrc(item)\r\n            })\r\n            uni.previewImage({\r\n                current: url,\r\n                urls\r\n            })\r\n            // 是否阻止事件传播\r\n\t\t\tthis.stop && this.preventEvent(e)\r\n        },\r\n        // 获取图片的路径\r\n        getSrc(item) {\r\n            return test.object(item)\r\n                ? (this.keyName && item[this.keyName]) || item.src\r\n                : item\r\n        },\r\n        // 单图时，获取图片的尺寸\r\n        // 在小程序中，需要将网络图片的的域名添加到小程序的download域名才可能获取尺寸\r\n        // 在没有添加的情况下，让单图宽度默认为盒子的一定宽度(singlePercent)\r\n        getImageRect() {\r\n            const src = this.getSrc(this.urls[0])\r\n            uni.getImageInfo({\r\n                src,\r\n                success: (res) => {\r\n                    // 判断图片横向还是竖向展示方式\r\n                    const isHorizotal = res.width >= res.height\r\n                    this.singleWidth = isHorizotal\r\n                        ? this.singleSize\r\n                        : (res.width / res.height) * this.singleSize\r\n                    this.singleHeight = !isHorizotal\r\n                        ? this.singleSize\r\n                        : (res.height / res.width) * this.singleWidth\r\n                },\r\n                fail: () => {\r\n                    this.getComponentWidth()\r\n                }\r\n            })\r\n        },\r\n        // 获取组件的宽度\r\n        async getComponentWidth() {\r\n            // 延时一定时间，以获取dom尺寸\r\n            await sleep(30)\r\n            // #ifndef APP-NVUE\r\n            this.$uGetRect('.u-album__row').then((size) => {\r\n                this.singleWidth = size.width * this.singlePercent\r\n            })\r\n            // #endif\r\n\r\n            // #ifdef APP-NVUE\r\n            // 这里ref=\"u-album__row\"所在的标签为通过for循环出来，导致this.$refs['u-album__row']是一个数组\r\n            const ref = this.$refs['u-album__row'][0]\r\n            ref &&\r\n                dom.getComponentRect(ref, (res) => {\r\n                    this.singleWidth = res.size.width * this.singlePercent\r\n                })\r\n            // #endif\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '../../libs/css/components.scss';\r\n\r\n.u-album {\r\n    @include flex(column);\r\n\r\n    &__row {\r\n        @include flex(row);\r\n\r\n        &__wrapper {\r\n            position: relative;\r\n\r\n            &__text {\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                background-color: rgba(0, 0, 0, 0.3);\r\n                @include flex(row);\r\n                justify-content: center;\r\n                align-items: center;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-album/u-album.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "singleWidth", "singleHeight", "singlePercent", "watch", "urls", "immediate", "handler", "newVal", "length", "this", "getImageRect", "emits", "computed", "imageStyle", "index1", "index2", "space", "rowCount", "multipleSize", "addUnit", "addStyle", "uni", "$u", "rowLen", "showUrls", "style", "marginRight", "marginBottom", "autoWrap", "slice", "maxCount", "arr", "map", "item", "index", "itemIndex", "Math", "floor", "push", "imageWidth", "unit", "imageHeight", "albumWidth", "width", "$emit", "methods", "onPreviewTap", "e", "url", "getSrc", "previewImage", "current", "stop", "preventEvent", "test", "object", "keyName", "src", "getImageInfo", "success", "res", "isHorizotal", "height", "singleSize", "fail", "getComponentWidth", "sleep", "$uGetRect", "then", "size", "wx", "createComponent", "Component"], "mappings": "6DA6FKA,EAAU,CACXC,KAAM,UACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,KAAO,KACI,CAEHC,YAAa,EAEbC,aAAc,EAEdC,cAAe,KAGvBC,MAAO,CACHC,KAAM,CACFC,WAAW,EACX,OAAAC,CAAQC,GACkB,IAAlBA,EAAOC,QACPC,KAAKC,cAEb,IAGXC,MAAO,CAAC,cACLC,SAAU,CACN,UAAAC,GACW,MAAA,CAACC,EAAQC,WACNC,MAAEA,EAAOC,SAAAA,EAAAC,aAAUA,EAAcd,KAAAA,GAASK,MAC5CU,QAAEA,EAASC,SAAAA,GAAaC,EAAAA,MAAIC,GAC5BC,EAASd,KAAKe,SAAShB,OACdC,KAAKL,KAAKI,OACvB,MAAMiB,EAAQ,CACVC,YAAaP,EAAQH,GACrBW,aAAcR,EAAQH,IAanB,OAVHF,IAAWS,GAAWd,KAAKmB,WAAUH,EAAME,aAAe,GAEzDlB,KAAKmB,WAEFb,IAAWE,GACVH,IAAWS,GACRR,IAAWN,KAAKe,SAASV,EAAS,GAAGN,UAEzCiB,EAAMC,YAAc,GAErBD,CAAA,CAEd,EAED,QAAAD,GACI,GAAIf,KAAKmB,SACL,MAAO,CAAEnB,KAAKL,KAAKyB,MAAM,EAAGpB,KAAKqB,WAC9B,CACH,MAAMC,EAAM,GAaL,OAZPtB,KAAKL,KAAK4B,KAAI,CAACC,EAAMC,KAEb,GAAAA,EAAQ,GAAKzB,KAAKqB,SAAU,CAE5B,MAAMK,EAAYC,KAAKC,MAAMH,EAAQzB,KAAKQ,UAErCc,EAAII,KACDJ,EAAAI,GAAa,IAEjBJ,EAAAI,GAAWG,KAAKL,EACxB,KAEGF,CACX,CACH,EACD,UAAAQ,GACI,OAAOpB,EAAOA,QACW,IAArBV,KAAKL,KAAKI,OAAeC,KAAKT,YAAcS,KAAKS,aAAcT,KAAK+B,KAE3E,EACD,WAAAC,GACI,OAAOtB,EAAOA,QACW,IAArBV,KAAKL,KAAKI,OAAeC,KAAKR,aAAeQ,KAAKS,aAAcT,KAAK+B,KAE5E,EAGD,UAAAE,GACI,IAAIC,EAAQ,EASL,OAPHA,EADqB,IAArBlC,KAAKL,KAAKI,OACFC,KAAKT,YAGTS,KAAKe,SAAS,GAAGhB,OAASC,KAAKS,aAC/BT,KAAKO,OAASP,KAAKe,SAAS,GAAGhB,OAAS,GAE3CC,KAAAmC,MAAM,aAAcD,GAClBA,CACX,GAEJE,QAAS,CACL1B,QAAAA,EAAOA,QAEP,YAAA2B,CAAaC,EAAGC,GACZ,MAAM5C,EAAOK,KAAKL,KAAK4B,KAAKC,GACjBxB,KAAKwC,OAAOhB,KAEvBZ,EAAAA,MAAI6B,aAAa,CACbC,QAASH,EACT5C,SAGRK,KAAA2C,MAAQ3C,KAAK4C,aAAaN,EACzB,EAED,MAAAE,CAAOhB,GACH,OAAOqB,EAAIA,KAACC,OAAOtB,GACZxB,KAAK+C,SAAWvB,EAAKxB,KAAK+C,UAAavB,EAAKwB,IAC7CxB,CACT,EAID,YAAAvB,GACI,MAAM+C,EAAMhD,KAAKwC,OAAOxC,KAAKL,KAAK,IAClCiB,EAAAA,MAAIqC,aAAa,CACbD,MACAE,QAAUC,IAEA,MAAAC,EAAcD,EAAIjB,OAASiB,EAAIE,OAChCrD,KAAAT,YAAc6D,EACbpD,KAAKsD,WACJH,EAAIjB,MAAQiB,EAAIE,OAAUrD,KAAKsD,WACjCtD,KAAAR,aAAgB4D,EAEdD,EAAIE,OAASF,EAAIjB,MAASlC,KAAKT,YADhCS,KAAKsD,UAC2B,EAE1CC,KAAM,KACFvD,KAAKwD,mBAAkB,GAGlC,EAED,uBAAMA,SAEIC,EAAAA,MAAM,IAEZzD,KAAK0D,UAAU,iBAAiBC,MAAMC,IAC7B5D,KAAAT,YAAcqE,EAAK1B,MAAQlC,KAAKP,aAAA,GAY7C,ggCCvPRoE,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}