{"version": 3, "file": "iptInfo.js", "sources": ["../../../../src/components/iptInfo.vue", "../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL2NvbXBvbmVudHMvaXB0SW5mby52dWU"], "sourcesContent": ["<script setup>\nimport { watch, ref, reactive } from \"vue\";\nimport {} from \"@/api\";\nimport {\n  onLoad,\n  onShow,\n  onReady,\n  onPageScroll,\n  onPullDownRefresh,\n  onReachBottom,\n  onShareAppMessage,\n} from \"@dcloudio/uni-app\";\nimport { store } from \"@/store\";\n\nconst props = defineProps({\n  popupShow: {\n    type: Boolean,\n    default: false,\n  },\n  src: {\n    type: String\n  },\n  ipt: {\n    type: Boolean,\n    default: true,\n  },\n});\nconst emit = defineEmits([\"update:popupShow\", \"submit\"]);\n\nconst baomingForm = ref({\n  lianxi_name: store().$state.userInfo.nickname,\n  lianxi_mobile: store().$state.userInfo.mobile,\n});\n</script>\n<template>\n  <view class=\"\">\n    <u-popup\n      :show=\"popupShow\"\n      :close-on-click-overlay=\"true\"\n      round=\"30rpx\"\n      mode=\"center\"\n      :safe-area-inset-bottom=\"false\"\n      @close=\"emit('update:popupShow', false)\"\n    >\n      <view class=\"pt50 pb50 px30 w690 r30\">\n        <template v-if=\"ipt\">\n          <view class=\"mb50 df aic jcc fb x34\"> 报名信息输入框 </view>\n          <u-input\n            type=\"text\"\n            :adjust-position=\"false\"\n            border=\"surround\"\n            shape=\"circle\"\n            placeholder=\"请输入姓名\"\n            v-model=\"baomingForm.lianxi_name\"\n          ></u-input>\n          <u-gap height=\"50rpx\" bg-color=\"\"></u-gap>\n          <u-input\n            type=\"number\"\n            :adjust-position=\"false\"\n            border=\"surround\"\n            shape=\"circle\"\n            placeholder=\"请输入联系方式\"\n            v-model=\"baomingForm.lianxi_mobile\"\n          ></u-input>\n          <u-gap height=\"50rpx\"></u-gap>\n          <u-button\n            shape=\"circle\"\n            color=\"linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)\"\n            :customStyle=\"{\n              color: '#000',\n            }\"\n            text=\"提交\"\n            @click=\"emit('submit', baomingForm)\"\n          ></u-button>\n        </template>\n        <template v-else>\n          <view class=\"df aic jcc mb20\">\n            <u-image :src=\"src\" width=\"200rpx\" height=\"200rpx\"></u-image>\n          </view>\n          <u-text\n            text=\"长按识别二维码，以便联系活动发起人\"\n            align=\"center\"\n            bold\n            color=\"#333\"\n            size=\"32rpx\"\n          ></u-text>\n        </template>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<style scoped lang=\"less\"></style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/components/iptInfo.vue'\nwx.createComponent(Component)"], "names": ["baomingForm", "ref", "lianxi_name", "store", "$state", "userInfo", "nickname", "lianxi_mobile", "mobile", "wx", "createComponent", "Component"], "mappings": "ykCA6BM,MAAAA,EAAcC,EAAAA,IAAI,CACtBC,YAAaC,EAAKA,QAAGC,OAAOC,SAASC,SACrCC,cAAeJ,EAAKA,QAAGC,OAAOC,SAASG,w2BC9BzCC,GAAGC,gBAAgBC"}