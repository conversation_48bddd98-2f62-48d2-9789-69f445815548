"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../api/index.js"),t=require("../../../../store/index.js"),o=require("../../../../utils/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),require("../../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js"))();const n={__name:"index",setup(n,{expose:r}){console.log("DiaryIndex: 脚本开始执行");const l=e.ref([]),i=e.ref(!0),s=e.ref(!1),u=e.ref(!1),c=e.ref(!0),d=e.ref(1);console.log("DiaryIndex: 初始化完成");const g=async(o=1,n=!1)=>{var r,g,v;try{console.log("DiaryIndex: 开始加载日记数据",{page:o,isRefresh:n}),n?(s.value=!0,d.value=1):o>1?u.value=!0:i.value=!0;const p={page:o,page_size:10,uid:(null==(r=t.store().$state.userInfo)?void 0:r.uid)||0,token:(null==(g=t.store().$state.userInfo)?void 0:g.token)||"",type:"diary"};console.log("DiaryIndex: API请求参数",p);const m=await a.getDiaries(p);if(console.log("DiaryIndex: API响应结果",m),"ok"===m.status){const e=(null==(v=m.data)?void 0:v.list)||[];console.log("DiaryIndex: 获取到日记数据",e.length,"条"),l.value=n||1===o?e:[...l.value,...e],c.value=10===e.length,d.value=o}else"empty"===m.status?(console.log("DiaryIndex: 服务器返回空数据"),(n||1===o)&&(l.value=[]),c.value=!1):(console.warn("DiaryIndex: API返回错误状态",m.status,m.msg),e.index.showToast({title:m.msg||"加载失败",icon:"none"}))}catch(p){console.error("DiaryIndex: 加载日记失败:",p),e.index.showToast({title:"加载失败",icon:"none"})}finally{i.value=!1,s.value=!1,u.value=!1}},v=()=>{g(1,!0)},p=()=>{!u.value&&c.value&&g(d.value+1)},m=e=>{if(!e)return"";const a=e.replace(/-/g,"/"),t=new Date(a),o=new Date,n=o-t;if(n<36e5){const e=Math.floor(n/6e4);return e<=0?"刚刚":`${e}分钟前`}if(n<864e5){return`${Math.floor(n/36e5)}小时前`}const r=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),s=String(t.getHours()).padStart(2,"0"),u=String(t.getMinutes()).padStart(2,"0");return r===o.getFullYear()?`${l}-${i} ${s}:${u}`:`${r}-${l}-${i} ${s}:${u}`},f=e=>{if(!e)return"";try{if("string"==typeof e&&e.trim().startsWith("{")){let a=e.replace(/&quot;/g,'"').replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#39;/g,"'");const t=JSON.parse(a);return t.name||t.address||e}return e}catch(a){return console.warn("位置信息JSON解析失败:",a,"原始数据:",e),e}},h=()=>{console.log("DiaryIndex: 收到刷新事件，重新加载数据"),g(1,!0)};e.onMounted((()=>{console.log("DiaryIndex: 组件挂载，等待按需加载"),e.index.$on("refreshFeedList",h)}));return r({loadDiaryData:()=>{console.log("DiaryIndex loadDiaryData - 被父组件调用"),g()}}),e.onActivated((()=>{console.log("DiaryIndex: 组件激活，重新加载数据"),0===l.value.length&&g()})),e.onUnmounted((()=>{e.index.$off("refreshFeedList",h)})),(a,t)=>e.e({a:i.value},i.value?{b:e.p({mode:"circle",size:"30",color:"#6AC086"})}:e.e({c:0===l.value.length},0===l.value.length?{}:{d:e.f(l.value,((a,t,n)=>{var r,l;return e.e({a:(null==(r=a.user)?void 0:r.avatar_url)||"/static/default-avatar.png",b:e.t((null==(l=a.user)?void 0:l.nickname)||"匿名用户"),c:e.t(m(a.created_at)),d:"private"===a.privacy},"private"===a.privacy?{e:"d18d080a-1-"+n,f:e.p({name:"lock",size:"12",color:"#999"})}:{},{g:e.t(a.content),h:a.images&&a.images.length>0},a.images&&a.images.length>0?e.e({i:e.f(a.images.slice(0,3),((e,a,t)=>({a:a,b:e}))),j:a.images.length>3},a.images.length>3?{k:e.t(a.images.length-3)}:{}):{},{l:a.location},a.location?{m:"d18d080a-2-"+n,n:e.p({name:"map",size:"12",color:"#999"}),o:e.t(f(a.location))}:{},{p:a.id,q:e.o((e=>(e=>{o.navto(`/pages/bundle/world/diary/detail?id=${e.id}`)})(a)),a.id)})}))},{e:u.value},u.value?{f:e.p({mode:"circle",size:"20",color:"#6AC086"})}:{},{g:!c.value&&l.value.length>0},(!c.value&&l.value.length,{}),{h:s.value,i:e.o(v),j:e.o(p)}))}},r=e._export_sfc(n,[["__scopeId","data-v-d18d080a"]]);wx.createComponent(r);
//# sourceMappingURL=index.js.map
