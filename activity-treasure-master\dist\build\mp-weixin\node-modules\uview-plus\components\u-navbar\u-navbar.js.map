{"version": 3, "file": "u-navbar.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-navbar/u-navbar.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LW5hdmJhci91LW5hdmJhci52dWU"], "sourcesContent": ["<template>\n\t<view class=\"u-navbar\" :class=\"[customClass]\">\n\t\t<view\n\t\t\tclass=\"u-navbar__placeholder\"\n\t\t\tv-if=\"fixed && placeholder\"\n\t\t\t:style=\"{\n\t\t\t\theight: addUnit(getPx(height) + sys().statusBarHeight,'px'),\n\t\t\t}\"\n\t\t></view>\n\t\t<view :class=\"[fixed && 'u-navbar--fixed']\">\n\t\t\t<u-status-bar\n\t\t\t\tv-if=\"safeAreaInsetTop\"\n\t\t\t\t:bgColor=\"bgColor\"\n\t\t\t></u-status-bar>\n\t\t\t<view\n\t\t\t\tclass=\"u-navbar__content\"\n\t\t\t\t:class=\"[border && 'u-border-bottom']\"\n\t\t\t\t:style=\"{\n\t\t\t\t\theight: addUnit(height),\n\t\t\t\t\tbackgroundColor: bgColor,\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"u-navbar__content__left\"\n\t\t\t\t\thover-class=\"u-navbar__content__left--hover\"\n\t\t\t\t\thover-start-time=\"150\"\n\t\t\t\t\t@tap=\"leftClick\"\n\t\t\t\t>\n\t\t\t\t\t<slot name=\"left\">\n\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\tv-if=\"leftIcon\"\n\t\t\t\t\t\t\t:name=\"leftIcon\"\n\t\t\t\t\t\t\t:size=\"leftIconSize\"\n\t\t\t\t\t\t\t:color=\"leftIconColor\"\n\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t\t<text\n\t\t\t\t\t\t\tv-if=\"leftText\"\n\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\tcolor: leftIconColor\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\tclass=\"u-navbar__content__left__text\"\n\t\t\t\t\t\t>{{ leftText }}</text>\n\t\t\t\t\t</slot>\n\t\t\t\t</view>\n\t\t\t\t<slot name=\"center\">\n\t\t\t\t\t<text\n\t\t\t\t\t\tclass=\"u-line-1 u-navbar__content__title\"\n\t\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\t\twidth: addUnit(titleWidth),\n\t\t\t\t\t\t\tcolor: titleColor,\n\t\t\t\t\t\t}, addStyle(titleStyle)]\"\n\t\t\t\t\t>{{ title }}</text>\n\t\t\t\t</slot>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"u-navbar__content__right\"\n\t\t\t\t\tv-if=\"$slots.right || rightIcon || rightText\"\n\t\t\t\t\t@tap=\"rightClick\"\n\t\t\t\t>\n\t\t\t\t\t<slot name=\"right\">\n\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\tv-if=\"rightIcon\"\n\t\t\t\t\t\t\t:name=\"rightIcon\"\n\t\t\t\t\t\t\tsize=\"20\"\n\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t\t<text\n\t\t\t\t\t\t\tv-if=\"rightText\"\n\t\t\t\t\t\t\tclass=\"u-navbar__content__right__text\"\n\t\t\t\t\t\t>{{ rightText }}</text>\n\t\t\t\t\t</slot>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, getPx, sys } from '../../libs/function/index';\n\t/**\n\t * Navbar 自定义导航栏\n\t * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uni-app带的导航栏。\n\t * @tutorial https://ijry.github.io/uview-plus/components/navbar.html\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否开启顶部安全区适配  （默认 true ）\n\t * @property {Boolean}\t\t\tplaceholder\t\t\t固定在顶部时，是否生成一个等高元素，以防止塌陷 （默认 false ）\n\t * @property {Boolean}\t\t\tfixed\t\t\t\t导航栏是否固定在顶部 （默认 false ）\n\t * @property {Boolean}\t\t\tborder\t\t\t\t导航栏底部是否显示下边框 （默认 false ）\n\t * @property {String}\t\t\tleftIcon\t\t\t左边返回图标的名称，只能为uview-pls自带的图标 （默认 'arrow-left' ）\n\t * @property {String}\t\t\tleftText\t\t\t左边的提示文字\n\t * @property {String}\t\t\trightText\t\t\t右边的提示文字\n\t * @property {String}\t\t\trightIcon\t\t\t右边返回图标的名称，只能为uview-plus自带的图标\n\t * @property {String}\t\t\ttitle\t\t\t\t导航栏标题，如设置为空字符，将会隐藏标题占位区域\n\t * @property {String}\t\t\ttitleColor\t\t\t文字颜色 （默认 '' ）\n\t * @property {String}\t\t\tbgColor\t\t\t\t导航栏背景设置 （默认 '#ffffff' ）\n\t * @property {String | Number}\ttitleWidth\t\t\t导航栏标题的最大宽度，内容超出会以省略号隐藏 （默认 '400rpx' ）\n\t * @property {String | Number}\theight\t\t\t\t导航栏高度(不包括状态栏高度在内，内部自动加上)（默认 '44px' ）\n\t * @property {String | Number}\tleftIconSize\t\t左侧返回图标的大小（默认 20px ）\n\t * @property {String | Number}\tleftIconColor\t\t左侧返回图标的颜色（默认 #303133 ）\n\t * @property {Boolean}\t        autoBack\t\t\t点击左侧区域(返回图标)，是否自动返回上一页（默认 false ）\n\t * @property {Object | String}\ttitleStyle\t\t\t标题的样式，对象或字符串\n\t * @event {Function} leftClick\t\t点击左侧区域\n\t * @event {Function} rightClick\t\t点击右侧区域\n\t * @example <u-navbar title=\"剑未配妥，出门已是江湖\" left-text=\"返回\" right-text=\"帮助\" @click-left=\"onClickBack\" @click-right=\"onClickRight\"></u-navbar>\n\t */\n\texport default {\n\t\tname: 'u-navbar',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t}\n\t\t},\n\t\temits: [\"leftClick\", \"rightClick\"],\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tsys,\n\t\t\tgetPx,\n\t\t\t// 点击左侧区域\n\t\t\tleftClick() {\n\t\t\t\t// 如果配置了autoBack，自动返回上一页\n\t\t\t\tthis.$emit('leftClick')\n\t\t\t\tif(this.autoBack) {\n\t\t\t\t\tuni.navigateBack()\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 点击右侧区域\n\t\t\trightClick() {\n\t\t\t\tthis.$emit('rightClick')\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-navbar {\n\n\t\t&--fixed {\n\t\t\tposition: fixed;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\ttop: 0;\n\t\t\tz-index: 11;\n\t\t}\n\n\t\t&__content {\n\t\t\t@include flex(row);\n\t\t\talign-items: center;\n\t\t\theight: 44px;\n\t\t\tbackground-color: #9acafc;\n\t\t\tposition: relative;\n\t\t\tjustify-content: center;\n\n\t\t\t&__left,\n\t\t\t&__right {\n\t\t\t\tpadding: 0 13px;\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\t@include flex(row);\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t&__left {\n\t\t\t\tleft: 0;\n\t\t\t\t\n\t\t\t\t&--hover {\n\t\t\t\t\topacity: 0.7;\n\t\t\t\t}\n\n\t\t\t\t&__text {\n\t\t\t\t\tfont-size: 15px;\n\t\t\t\t\tmargin-left: 3px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__title {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 16px;\n\t\t\t\tcolor: $u-main-color;\n\t\t\t}\n\n\t\t\t&__right {\n\t\t\t\tright: 0;\n\n\t\t\t\t&__text {\n\t\t\t\t\tfont-size: 15px;\n\t\t\t\t\tmargin-left: 3px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-navbar/u-navbar.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "emits", "methods", "addStyle", "addUnit", "sys", "getPx", "leftClick", "this", "$emit", "autoBack", "uni", "navigateBack", "rightClick", "wx", "createComponent", "Component"], "mappings": "6DAyGMA,EAAU,CACdC,KAAM,WACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,KAAO,KACC,CACP,GAEDC,MAAO,CAAC,YAAa,cACrBC,QAAS,CACRC,SAAAA,EAAQA,SACRC,QAAAA,EAAOA,QACPC,IAAAA,EAAGA,UACHC,EAAKA,MAEL,SAAAC,GAECC,KAAKC,MAAM,aACRD,KAAKE,UACPC,EAAAA,MAAIC,cAEL,EAED,UAAAC,GACCL,KAAKC,MAAM,aACX,8oCChIJK,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}