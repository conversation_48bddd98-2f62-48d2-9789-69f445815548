{"version": 3, "file": "post.js", "sources": ["../../../../../../../src/pages/bundle/world/diary/post.vue", "../../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHdvcmxkXGRpYXJ5XHBvc3QudnVl"], "sourcesContent": ["<script setup>\nimport { ref, reactive, computed } from 'vue';\nimport { publishDiary, upload_img } from '@/api/index.js';\nimport { store } from '@/store';\nimport { requireLogin } from '@/utils/auth';\nimport customNavbar from '@/components/customNavbar.vue';\n\n// 状态管理\nconst content = ref(''); // 日记内容\nconst images = ref([]); // 图片列表\nconst location = ref(null); // 位置信息\nconst privacy = ref('public'); // 隐私设置\nconst isPrivate = ref(false); // 是否设为私密\nconst isSubmitting = ref(false); // 提交状态\n\n// 隐私选项\nconst privacyOptions = [\n  { label: '公开', value: 'public' },\n  { label: '私密', value: 'private' }\n];\n\n// 计算最大上传图片数量（会员4张，非会员1张）\nconst maxImageCount = computed(() => {\n    const userInfo = store().$state.userInfo;\n    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员\n    return isVip ? 4 : 1;\n});\n\n// 处理图片上传\nconst handleAfterRead = async (event) => {\n  // 将新选择的文件添加到列表，并标记为上传中\n  let lists = [].concat(event.file);\n  let fileListLen = images.value.length;\n\n  lists.map((item) => {\n    images.value.push({\n      ...item,\n      status: 'uploading',\n      message: '上传中'\n    });\n  });\n\n  // 依次上传新选择的文件\n  for (let i = 0; i < lists.length; i++) {\n    const currentFileIndex = fileListLen + i;\n    try {\n      const res = await upload_img(lists[i].url);\n\n      if (res.status === 'ok' && res.data) {\n        let item = images.value[currentFileIndex];\n        if (item) {\n          images.value.splice(currentFileIndex, 1, {\n            ...item,\n            status: 'success',\n            message: '',\n            url: res.data\n          });\n        }\n      } else {\n        if (images.value[currentFileIndex]) {\n          images.value[currentFileIndex].status = 'failed';\n          images.value[currentFileIndex].message = res.msg || '上传失败';\n        }\n        uni.showToast({ title: res.msg || '图片上传失败', icon: 'none' });\n      }\n    } catch (error) {\n      if (images.value[currentFileIndex]) {\n        images.value[currentFileIndex].status = 'failed';\n        images.value[currentFileIndex].message = '上传失败';\n      }\n      uni.showToast({ title: '图片上传失败，请重试', icon: 'none' });\n    }\n  }\n};\n\n// 删除图片\nconst handleDeletePic = (event) => {\n  images.value.splice(event.index, 1);\n};\n\n// 选择位置\nconst chooseLocation = () => {\n  uni.chooseLocation({\n    success: (res) => {\n      location.value = {\n        name: res.name,\n        address: res.address,\n        latitude: res.latitude,\n        longitude: res.longitude\n      };\n    },\n    fail: () => {\n      uni.showToast({ title: '选择位置失败', icon: 'none' });\n    }\n  });\n};\n\n// 清除位置\nconst clearLocation = () => {\n  location.value = null;\n};\n\n// 发布日记\nconst handleSubmit = async () => {\n  // 使用统一的登录校验\n  if (!requireLogin('', '请先登录后再发布日记')) {\n    return;\n  }\n\n  if (!content.value.trim() && images.value.filter(img => img.status === 'success').length === 0) {\n    uni.showToast({ title: '请输入日记内容或添加图片', icon: 'none' });\n    return;\n  }\n\n  if (isSubmitting.value) return;\n\n  isSubmitting.value = true;\n\n  try {\n    // 上传图片\n    const uploadedImageUrls = [];\n    for (const img of images.value) {\n      if (img.status === 'success' && img.url) {\n        uploadedImageUrls.push(img.url);\n      }\n    }\n\n    // 准备参数 - 与后端API保持一致\n    const params = {\n      uid: store().$state.userInfo.uid,\n      token: store().$state.userInfo.token,\n      content: content.value.trim(),\n      images: uploadedImageUrls, // 传递数组，与动态页面保持一致\n      location: location.value ? JSON.stringify(location.value) : '',\n      tags: '', // 日记暂不支持标签，但保持字段一致性\n      privacy: isPrivate.value ? 'private' : 'public', // 使用isPrivate开关\n      type: 'diary' // 标识为日记类型\n    };\n\n    const res = await publishDiary(params);\n\n    if (res.status === 'ok') {\n      uni.showToast({ title: '发布成功', icon: 'success' });\n\n      // {{ AURA-X: Add - 发布成功后触发日记列表刷新. Confirmed via 寸止 }}\n      // 触发日记列表刷新\n      uni.$emit('refreshFeedList');\n\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1000);\n    } else if (res.status === 'relogin') {\n      uni.showToast({ title: '请先登录', icon: 'none' });\n    } else {\n      uni.showToast({ title: res.msg || '发布失败', icon: 'none' });\n    }\n  } catch (error) {\n    uni.showToast({ title: '发布失败，请重试', icon: 'none' });\n  } finally {\n    isSubmitting.value = false;\n  }\n};\n\n// 返回\nconst goBack = () => {\n  uni.navigateBack();\n};\n</script>\n\n<template>\n  <view class=\"diary-post-container\">\n    <!-- 统一导航栏 -->\n    <customNavbar\n      title=\"日记\"\n      backIcon=\"close\"\n      @back=\"goBack\"\n    />\n\n    <!-- 内容区域 -->\n    <scroll-view scroll-y class=\"content-area\">\n\n\n\n      <!-- 日记内容输入 -->\n      <view class=\"content-section\">\n        <textarea\n          v-model=\"content\"\n          class=\"content-textarea\"\n          placeholder=\"记录你的一天\"\n          auto-height\n          :maxlength=\"2000\"\n          show-confirm-bar\n        ></textarea>\n        <view class=\"char-count\">\n          <text class=\"count-text\">{{ content.length }}/500</text>\n        </view>\n      </view>\n\n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <u-upload\n          :fileList=\"images\"\n          @afterRead=\"handleAfterRead\"\n          @delete=\"handleDeletePic\"\n          name=\"file\"\n          multiple\n          :maxCount=\"maxImageCount\"\n          :previewImage=\"true\"\n          width=\"200rpx\"\n          height=\"200rpx\"\n          uploadIconColor=\"#ccc\"\n        ></u-upload>\n        <view v-if=\"maxImageCount === 1\" class=\"upload-tip\">\n          <text class=\"tip-text\">非会员最多上传1张图片，升级会员可上传4张</text>\n        </view>\n      </view>\n\n      <!-- 位置信息 -->\n      <view class=\"location-section\">\n        <view class=\"section-header\">\n          <u-icon name=\"map\" size=\"16\" color=\"#6AC086\"></u-icon>\n          <text class=\"section-title\">位置</text>\n        </view>\n        <view v-if=\"location\" class=\"location-info\">\n          <view class=\"location-content\">\n            <text class=\"location-name\">{{ location.name }}</text>\n            <text class=\"location-address\">{{ location.address }}</text>\n          </view>\n          <u-icon name=\"close\" size=\"16\" color=\"#999\" @click=\"clearLocation\"></u-icon>\n        </view>\n        <view v-else class=\"location-empty\" @click=\"chooseLocation\">\n          <text class=\"location-placeholder\">添加位置</text>\n          <u-icon name=\"arrow-right\" size=\"14\" color=\"#999\"></u-icon>\n        </view>\n      </view>\n\n      <!-- 隐私设置 -->\n      <view class=\"privacy-section\">\n        <view class=\"privacy-item\">\n          <text class=\"privacy-label\">设为私密</text>\n          <u-switch v-model=\"isPrivate\" activeColor=\"#6AC086\" size=\"24\"></u-switch>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 发布按钮 - 固定在页面底部右侧 -->\n    <view class=\"publish-button-container\">\n      <view\n        class=\"publish-btn\"\n        :class=\"{ 'disabled': isSubmitting || (!content.trim() && images.filter(img => img.status === 'success').length === 0) }\"\n        @click=\"handleSubmit\"\n      >\n        <u-icon name=\"checkmark\" size=\"44rpx\" color=\"#ffffff\" v-if=\"!isSubmitting\"></u-icon>\n        <u-loading-icon v-if=\"isSubmitting\" color=\"#ffffff\" size=\"40rpx\"></u-loading-icon>\n        <text class=\"publish-text\" v-if=\"!isSubmitting\">发布</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n/* {{ AURA-X: Modify - 修改背景色为白色. Confirmed via 寸止 }} */\n.diary-post-container {\n  height: 100vh;\n  background-color: #ffffff;\n  display: flex;\n  flex-direction: column;\n  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;\n}\n\n/* 橙色提示条样式 */\n.warning-notice {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  margin: 24rpx;\n  background-color: #FFF3CD;\n  border: 1rpx solid #FFEAA7;\n  border-radius: 16rpx;\n\n  .notice-text {\n    margin-left: 16rpx;\n    font-size: 28rpx;\n    color: #856404;\n    line-height: 1.4;\n  }\n}\n\n.custom-navbar {\n  background: #ffffff;\n  padding-top: calc(var(--status-bar-height) + 60rpx);\n  border-bottom: 1rpx solid #f0f0f0;\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 999;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);\n\n  .navbar-content {\n    height: calc(44px + 40rpx);\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 30rpx;\n\n    .navbar-left {\n      width: 120rpx;\n      display: flex;\n      align-items: center;\n      justify-content: flex-start;\n    }\n\n    .navbar-title {\n      .title-text {\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #333;\n      }\n    }\n\n    .navbar-right {\n      width: 120rpx;\n      display: flex;\n      justify-content: flex-end;\n\n      .publish-btn {\n        width: 80rpx;\n        height: 80rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n        border-radius: 40rpx;\n        position: relative;\n        box-shadow: 0 4rpx 8rpx rgba(106, 192, 134, 0.2);\n\n        &.disabled {\n          opacity: 0.6;\n          pointer-events: none;\n        }\n\n        .publish-icon {\n          width: 40rpx;\n          height: 40rpx;\n        }\n\n        .loading-icon {\n          position: absolute;\n          width: 32rpx;\n          height: 32rpx;\n          border: 3rpx solid #ffffff;\n          border-top-color: transparent;\n          border-radius: 50%;\n          animation: loading 0.8s linear infinite;\n        }\n      }\n\n      @keyframes loading {\n        from {\n          transform: rotate(0deg);\n        }\n        to {\n          transform: rotate(360deg);\n        }\n      }\n    }\n  }\n}\n\n/* {{ AURA-X: Modify - 去除卡片样式，扩大输入区域. Confirmed via 寸止 }} */\n.content-area {\n  flex: 1;\n  padding: 0;\n  padding-top: 0;\n  padding-bottom: 200rpx; /* 为底部发布按钮留出空间 */\n  overflow-y: auto;\n  background-color: #ffffff;\n}\n\n.content-section {\n  background: transparent;\n  border-radius: 0;\n  padding: 32rpx;\n  margin-bottom: 0;\n  min-height: 50vh;\n  \n  .content-textarea {\n    width: 100%;\n    min-height: 300rpx;\n    font-size: 32rpx;\n    line-height: 1.6;\n    color: #333333;\n    border: none;\n    outline: none;\n    font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;\n  }\n  \n  .char-count {\n    display: flex;\n    justify-content: flex-end;\n    margin-top: 16rpx;\n    \n    .count-text {\n      font-size: 24rpx;\n      color: #999;\n    }\n  }\n}\n\n\n.image-section {\n  background: transparent;\n  border-radius: 0;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n\n  .upload-slot {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    width: 200rpx;\n    height: 200rpx;\n    border: 2rpx dashed #ddd;\n    border-radius: 12rpx;\n\n    .upload-text {\n      margin-top: 16rpx;\n      font-size: 24rpx;\n      color: #999;\n    }\n  }\n\n\n  .upload-tip {\n    margin-top: 16rpx;\n    padding: 10rpx 16rpx;\n    background-color: #fff3cd;\n    border: 1rpx solid #ffeaa7;\n    border-radius: 8rpx;\n\n    .tip-text {\n      font-size: 22rpx;\n      color: #856404;\n      line-height: 1.3;\n    }\n  }\n}\n\n/* {{ AURA-X: Modify - 去除位置和隐私区域卡片样式. Confirmed via 寸止 }} */\n.location-section, .privacy-section {\n  background: transparent;\n  border-radius: 0;\n  padding: 32rpx;\n  margin-bottom: 24rpx;\n  \n  .section-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24rpx;\n    \n    .section-title {\n      margin-left: 12rpx;\n      font-size: 30rpx;\n      font-weight: 500;\n      color: #333333;\n      font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;\n    }\n  }\n}\n\n.location-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  .location-content {\n    flex: 1;\n    \n    .location-name {\n      display: block;\n      font-size: 30rpx;\n      color: #333;\n      margin-bottom: 8rpx;\n    }\n    \n    .location-address {\n      font-size: 26rpx;\n      color: #666;\n    }\n  }\n}\n\n.location-empty {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  .location-placeholder {\n    font-size: 30rpx;\n    color: #999;\n  }\n}\n\n/* 发布按钮容器 */\n.publish-button-container {\n  position: fixed;\n  bottom: 40rpx;\n  right: 40rpx;\n  z-index: 1000;\n}\n\n/* 发布按钮样式 */\n.publish-btn {\n  width: 120rpx;\n  height: 120rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: 60rpx;\n  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);\n  transition: all 0.3s ease;\n\n  .publish-text {\n    font-size: 24rpx;\n    color: #ffffff;\n    margin-top: 8rpx;\n    font-weight: 500;\n  }\n\n  &.disabled {\n    opacity: 0.6;\n    pointer-events: none;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/diary/post.vue'\nwx.createPage(MiniProgramPage)"], "names": ["customNavbar", "content", "ref", "images", "location", "isPrivate", "isSubmitting", "maxImageCount", "computed", "userInfo", "store", "$state", "role_type", "handleAfterRead", "async", "event", "lists", "concat", "file", "fileListLen", "value", "length", "map", "item", "push", "status", "message", "i", "currentFileIndex", "res", "upload_img", "url", "data", "splice", "msg", "showToast", "title", "icon", "error", "uni", "index", "handleDeletePic", "chooseLocation", "success", "name", "address", "latitude", "longitude", "fail", "clearLocation", "handleSubmit", "requireLogin", "trim", "filter", "img", "uploadedImageUrls", "params", "uid", "token", "JSON", "stringify", "tags", "privacy", "type", "publishDiary", "$emit", "setTimeout", "navigateBack", "goBack", "wx", "createPage", "MiniProgramPage"], "mappings": "23BAKA,MAAMA,EAAe,IAAW,mEAG1B,MAAAC,EAAUC,EAAAA,IAAI,IACdC,EAASD,EAAAA,IAAI,IACbE,EAAWF,EAAAA,IAAI,MACLA,EAAGA,IAAC,UACd,MAAAG,EAAYH,EAAAA,KAAI,GAChBI,EAAeJ,EAAAA,KAAI,GASnBK,EAAgBC,EAAQA,UAAC,KAC3B,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAEhC,OADsC,KAAxB,MAAAA,OAAA,EAAAA,EAAUG,YAA2C,WAAxBH,WAAUG,WACtC,EAAI,CAAA,IAIjBC,EAAkBC,MAAOC,IAE7B,IAAIC,EAAQ,GAAGC,OAAOF,EAAMG,MACxBC,EAAchB,EAAOiB,MAAMC,OAEzBL,EAAAM,KAAKC,IACTpB,EAAOiB,MAAMI,KAAK,IACbD,EACHE,OAAQ,YACRC,QAAS,OACV,IAIH,IAAA,IAASC,EAAI,EAAGA,EAAIX,EAAMK,OAAQM,IAAK,CACrC,MAAMC,EAAmBT,EAAcQ,EACnC,IACF,MAAME,QAAYC,EAAUA,WAACd,EAAMW,GAAGI,KAEtC,GAAmB,OAAfF,EAAIJ,QAAmBI,EAAIG,KAAM,CAC/B,IAAAT,EAAOpB,EAAOiB,MAAMQ,GACpBL,GACKpB,EAAAiB,MAAMa,OAAOL,EAAkB,EAAG,IACpCL,EACHE,OAAQ,UACRC,QAAS,GACTK,IAAKF,EAAIG,MAGrB,MACY7B,EAAOiB,MAAMQ,KACRzB,EAAAiB,MAAMQ,GAAkBH,OAAS,SACxCtB,EAAOiB,MAAMQ,GAAkBF,QAAUG,EAAIK,KAAO,gBAElDC,UAAU,CAAEC,MAAOP,EAAIK,KAAO,SAAUG,KAAM,QAErD,OAAQC,GACHnC,EAAOiB,MAAMQ,KACRzB,EAAAiB,MAAMQ,GAAkBH,OAAS,SACjCtB,EAAAiB,MAAMQ,GAAkBF,QAAU,QAE3Ca,EAAGC,MAACL,UAAU,CAAEC,MAAO,aAAcC,KAAM,QAC7C,CACF,GAIII,EAAmB1B,IACvBZ,EAAOiB,MAAMa,OAAOlB,EAAMyB,MAAO,EAAC,EAI9BE,EAAiB,KACrBH,EAAAA,MAAIG,eAAe,CACjBC,QAAUd,IACRzB,EAASgB,MAAQ,CACfwB,KAAMf,EAAIe,KACVC,QAAShB,EAAIgB,QACbC,SAAUjB,EAAIiB,SACdC,UAAWlB,EAAIkB,UACvB,EAEIC,KAAM,KACJT,EAAGC,MAACL,UAAU,CAAEC,MAAO,SAAUC,KAAM,QAAQ,GAElD,EAIGY,EAAgB,KACpB7C,EAASgB,MAAQ,IAAA,EAIb8B,EAAepC,UAEnB,GAAKqC,EAAYA,aAAC,GAAI,cAItB,GAAKlD,EAAQmB,MAAMgC,QAA0E,IAAhEjD,EAAOiB,MAAMiC,QAAOC,GAAsB,YAAfA,EAAI7B,SAAsBJ,QAKlF,IAAIf,EAAac,MAAjB,CAEAd,EAAac,OAAQ,EAEjB,IAEF,MAAMmC,EAAoB,GACf,IAAA,MAAAD,KAAOnD,EAAOiB,MACJ,YAAfkC,EAAI7B,QAAwB6B,EAAIvB,KAChBwB,EAAA/B,KAAK8B,EAAIvB,KAK/B,MAAMyB,EAAS,CACbC,IAAK/C,EAAKA,QAAGC,OAAOF,SAASgD,IAC7BC,MAAOhD,EAAKA,QAAGC,OAAOF,SAASiD,MAC/BzD,QAASA,EAAQmB,MAAMgC,OACvBjD,OAAQoD,EACRnD,SAAUA,EAASgB,MAAQuC,KAAKC,UAAUxD,EAASgB,OAAS,GAC5DyC,KAAM,GACNC,QAASzD,EAAUe,MAAQ,UAAY,SACvC2C,KAAM,SAGFlC,QAAYmC,eAAaR,GAEZ,OAAf3B,EAAIJ,QACNc,EAAGC,MAACL,UAAU,CAAEC,MAAO,OAAQC,KAAM,oBAIjC4B,MAAM,mBAEVC,YAAW,KACT3B,EAAGC,MAAC2B,cAAY,GACf,MACqB,YAAftC,EAAIJ,OACbc,EAAGC,MAACL,UAAU,CAAEC,MAAO,OAAQC,KAAM,iBAEjCF,UAAU,CAAEC,MAAOP,EAAIK,KAAO,OAAQG,KAAM,QAEnD,OAAQC,GACPC,EAAGC,MAACL,UAAU,CAAEC,MAAO,WAAYC,KAAM,QAC7C,CAAY,QACR/B,EAAac,OAAQ,CACvB,CA9CwB,OAJtBmB,EAAGC,MAACL,UAAU,CAAEC,MAAO,eAAgBC,KAAM,QAkD/C,EAII+B,EAAS,KACb7B,EAAGC,MAAC2B,cAAY,67BCpKlBE,GAAGC,WAAWC"}