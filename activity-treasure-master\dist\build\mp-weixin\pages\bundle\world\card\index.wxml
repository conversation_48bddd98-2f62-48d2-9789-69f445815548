<view class="card-page-container data-v-5a05a621"><view class="date-axis-container data-v-5a05a621"><scroll-view class="date-scroll-view data-v-5a05a621" scroll-x scroll-into-view="{{b}}" scroll-with-animation><view wx:for="{{a}}" wx:for-item="item" wx:key="f" class="date-item-wrapper data-v-5a05a621" id="{{item.g}}"><view class="{{['date-item', 'data-v-5a05a621', item.c && 'active', item.d && 'today']}}" bindtap="{{item.e}}"><text class="date-day data-v-5a05a621">{{item.a}}</text><text class="date-short data-v-5a05a621">{{item.b}}</text></view></view></scroll-view><view class="refresh-icon data-v-5a05a621" bindtap="{{d}}"><u-icon wx:if="{{c}}" class="data-v-5a05a621" u-i="5a05a621-0" bind:__l="__l" u-p="{{c}}"></u-icon></view></view><view class="main-content-area data-v-5a05a621"><view wx:if="{{e}}" class="loading-state data-v-5a05a621"><u-loading-icon wx:if="{{f}}" class="data-v-5a05a621" u-i="5a05a621-1" bind:__l="__l" u-p="{{f}}"></u-loading-icon><text class="loading-text data-v-5a05a621">加载中...</text></view><view wx:elif="{{g}}" class="error-state data-v-5a05a621"><u-empty wx:if="{{h}}" class="data-v-5a05a621" u-i="5a05a621-2" bind:__l="__l" u-p="{{h}}"></u-empty></view><view wx:elif="{{i}}" class="empty-state data-v-5a05a621"><u-empty wx:if="{{j}}" class="data-v-5a05a621" u-i="5a05a621-3" bind:__l="__l" u-p="{{j}}"></u-empty></view><swiper wx:else class="card-swiper data-v-5a05a621" current="{{m}}" bindchange="{{n}}" circular="{{true}}" previous-margin="40rpx" next-margin="40rpx" display-multiple-items="{{o}}"><block wx:if="{{k}}"><swiper-item wx:for="{{l}}" wx:for-item="card" wx:key="s" class="data-v-5a05a621"><view wx:if="{{card.a}}" class="swiper-item-container data-v-5a05a621" bindtap="{{card.r}}"><view class="card-content-wrapper data-v-5a05a621"><view class="card-image-section data-v-5a05a621"><image class="card-image data-v-5a05a621" src="{{card.b}}" mode="aspectFill" lazy-load/></view><view class="card-text-section data-v-5a05a621"><text class="card-description data-v-5a05a621">{{card.c}}</text><text wx:if="{{card.d}}" class="card-author data-v-5a05a621">— {{card.e}}</text></view><view class="card-action-bar data-v-5a05a621"><view class="action-item data-v-5a05a621" catchtap="{{card.i}}"><u-icon wx:if="{{card.g}}" class="data-v-5a05a621" u-i="{{card.f}}" bind:__l="__l" u-p="{{card.g}}"></u-icon><text class="action-count data-v-5a05a621">{{card.h}}</text></view><view class="action-item data-v-5a05a621" catchtap="{{card.n}}"><u-icon wx:if="{{card.k}}" class="data-v-5a05a621" u-i="{{card.j}}" bind:__l="__l" u-p="{{card.k}}"></u-icon><text class="{{['action-count', 'data-v-5a05a621', card.m && 'liked']}}">{{card.l}}</text></view><view class="action-item data-v-5a05a621" catchtap="{{card.q}}"><u-icon wx:if="{{card.p}}" class="data-v-5a05a621" u-i="{{card.o}}" bind:__l="__l" u-p="{{card.p}}"></u-icon></view></view></view></view></swiper-item></block></swiper><view wx:if="{{p}}" class="loading-overlay data-v-5a05a621"><u-loading-icon wx:if="{{q}}" class="data-v-5a05a621" u-i="5a05a621-7" bind:__l="__l" u-p="{{q}}"></u-loading-icon></view></view><share-popup wx:if="{{v}}" class="data-v-5a05a621" bindclose="{{r}}" bindshareSuccess="{{s}}" bindshareError="{{t}}" u-i="5a05a621-8" bind:__l="__l" u-p="{{v}}"/><canvas class="data-v-5a05a621" canvas-id="share-canvas" id="share-canvas" style="position:fixed;top:-9999px;left:-9999px;width:750px;height:1334px"></canvas></view>