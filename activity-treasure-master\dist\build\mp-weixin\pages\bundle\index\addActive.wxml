<view class="page"><view class="custom-navbar"><view class="navbar-content"><view class="navbar-left" bindtap="{{b}}"><u-icon wx:if="{{a}}" u-i="3e9dfa88-0" bind:__l="__l" u-p="{{a}}"></u-icon></view><view class="navbar-center"><text class="navbar-title">{{c}}</text></view><view class="navbar-right"></view></view></view><u-form wx:if="{{aX}}" u-s="{{['d']}}" u-r="formRef" class="form-container r" u-i="3e9dfa88-1" bind:__l="__l" u-p="{{aX}}"><view hidden="{{!H}}"><view class="px30 cover-upload-wrapper"><u-form-item wx:if="{{g}}" u-s="{{['d']}}" class="form-item-upload cover-upload-container" u-i="3e9dfa88-2,3e9dfa88-1" bind:__l="__l" u-p="{{g}}"><view class="upload-wrapper"><view class="upload-container"><u-upload wx:if="{{f}}" bindafterRead="{{d}}" binddelete="{{e}}" class="cover-upload-component" u-i="3e9dfa88-3,3e9dfa88-2" bind:__l="__l" u-p="{{f}}"></u-upload></view><view class="upload-hint-text"> 请为你的活动选一个好看的封面吧！ </view></view></u-form-item></view><view class="px30"><u-form-item wx:if="{{j}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-4,3e9dfa88-1" bind:__l="__l" u-p="{{j}}"><view class="input-with-icon"><u-input wx:if="{{i}}" u-i="3e9dfa88-5,3e9dfa88-4" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"></u-input></view></u-form-item></view><view class="px30"><u-form-item wx:if="{{o}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-6,3e9dfa88-1" bind:__l="__l" u-p="{{o}}"><view class="input-with-icon" bindtap="{{n}}"><u-text wx:if="{{k}}" u-i="3e9dfa88-7,3e9dfa88-6" bind:__l="__l" u-p="{{k}}"></u-text><u-icon wx:if="{{m}}" class="dropdown-icon" catchclick="{{l}}" u-i="3e9dfa88-8,3e9dfa88-6" bind:__l="__l" u-p="{{m}}"></u-icon></view></u-form-item></view><view class="px30"><u-form-item wx:if="{{t}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-9,3e9dfa88-1" bind:__l="__l" u-p="{{t}}"><view class="activity-nature-buttons"><view class="{{['nature-button', p && 'nature-button-active']}}" bindtap="{{q}}"> 线下 </view><view class="{{['nature-button', r && 'nature-button-active']}}" bindtap="{{s}}"> 线上 </view></view></u-form-item></view><view wx:if="{{v}}" class="px30"><u-form-item wx:if="{{A}}" u-s="{{['d']}}" u-i="3e9dfa88-10,3e9dfa88-1" bind:__l="__l" u-p="{{A}}"><view class="checkin-switch-container"><view class="checkin-switch-label"><u-text wx:if="{{w}}" u-i="3e9dfa88-11,3e9dfa88-10" bind:__l="__l" u-p="{{w}}"></u-text><u-text wx:if="{{x}}" class="checkin-hint" u-i="3e9dfa88-12,3e9dfa88-10" bind:__l="__l" u-p="{{x}}"></u-text></view><u-switch wx:if="{{z}}" u-i="3e9dfa88-13,3e9dfa88-10" bind:__l="__l" bindupdateModelValue="{{y}}" u-p="{{z}}"></u-switch></view></u-form-item></view><view wx:if="{{B}}" class="px30"><u-form-item wx:if="{{G}}" u-s="{{['d']}}" u-i="3e9dfa88-14,3e9dfa88-1" bind:__l="__l" u-p="{{G}}"><view class="activity-nature-buttons"><view class="{{['nature-button', C && 'nature-button-active']}}" bindtap="{{D}}"> 所有用户 </view><view class="{{['nature-button', E && 'nature-button-active']}}" bindtap="{{F}}"> 仅会员可参加 </view></view></u-form-item></view></view><view hidden="{{!aI}}"><view class="px30"><block wx:if="{{I}}"><u-form-item wx:if="{{N}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-15,3e9dfa88-1" bind:__l="__l" u-p="{{N}}"><view class="input-with-icon" bindtap="{{M}}"><u-text wx:if="{{J}}" u-i="3e9dfa88-16,3e9dfa88-15" bind:__l="__l" u-p="{{J}}"></u-text><u-icon wx:if="{{L}}" class="dropdown-icon" catchclick="{{K}}" u-i="3e9dfa88-17,3e9dfa88-15" bind:__l="__l" u-p="{{L}}"></u-icon></view></u-form-item><u-form-item wx:if="{{Q}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-18,3e9dfa88-1" bind:__l="__l" u-p="{{Q}}"><view class="input-with-icon"><u-input wx:if="{{P}}" u-i="3e9dfa88-19,3e9dfa88-18" bind:__l="__l" bindupdateModelValue="{{O}}" u-p="{{P}}"></u-input></view></u-form-item></block><u-form-item wx:if="{{V}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-20,3e9dfa88-1" bind:__l="__l" u-p="{{V}}"><view class="input-with-icon" bindtap="{{U}}"><u-text wx:if="{{R}}" u-i="3e9dfa88-21,3e9dfa88-20" bind:__l="__l" u-p="{{R}}"></u-text><u-icon wx:if="{{T}}" class="dropdown-icon" catchclick="{{S}}" u-i="3e9dfa88-22,3e9dfa88-20" bind:__l="__l" u-p="{{T}}"></u-icon></view></u-form-item><u-form-item wx:if="{{aa}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-23,3e9dfa88-1" bind:__l="__l" u-p="{{aa}}"><view class="input-with-icon" bindtap="{{Z}}"><u-text wx:if="{{W}}" u-i="3e9dfa88-24,3e9dfa88-23" bind:__l="__l" u-p="{{W}}"></u-text><u-icon wx:if="{{Y}}" class="dropdown-icon" catchclick="{{X}}" u-i="3e9dfa88-25,3e9dfa88-23" bind:__l="__l" u-p="{{Y}}"></u-icon></view></u-form-item><u-form-item wx:if="{{ad}}" u-s="{{['d']}}" u-i="3e9dfa88-26,3e9dfa88-1" bind:__l="__l" u-p="{{ad}}"><view class="input-with-icon"><u-input wx:if="{{ac}}" u-i="3e9dfa88-27,3e9dfa88-26" bind:__l="__l" bindupdateModelValue="{{ab}}" u-p="{{ac}}"></u-input></view></u-form-item><block wx:if="{{ae}}"><u-form-item wx:if="{{an}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-28,3e9dfa88-1" bind:__l="__l" u-p="{{an}}"><view class="payment-type-selector"><view class="{{['payment-option', ah && 'active']}}" bindtap="{{ai}}"><view class="option-content"><u-text wx:if="{{af}}" u-i="3e9dfa88-29,3e9dfa88-28" bind:__l="__l" u-p="{{af}}"></u-text></view><u-text wx:if="{{ag}}" u-i="3e9dfa88-30,3e9dfa88-28" bind:__l="__l" u-p="{{ag}}"></u-text></view><view class="{{['payment-option', al && 'active']}}" bindtap="{{am}}"><view class="option-content"><u-text wx:if="{{aj}}" u-i="3e9dfa88-31,3e9dfa88-28" bind:__l="__l" u-p="{{aj}}"></u-text></view><u-text wx:if="{{ak}}" u-i="3e9dfa88-32,3e9dfa88-28" bind:__l="__l" u-p="{{ak}}"></u-text></view></view></u-form-item><block wx:if="{{ao}}"><u-form-item wx:if="{{ar}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-33,3e9dfa88-1" bind:__l="__l" u-p="{{ar}}"><view class="input-with-icon"><u-input wx:if="{{aq}}" u-i="3e9dfa88-34,3e9dfa88-33" bind:__l="__l" bindupdateModelValue="{{ap}}" u-p="{{aq}}"></u-input></view></u-form-item><u-form-item wx:if="{{ax}}" u-s="{{['d']}}" u-i="3e9dfa88-35,3e9dfa88-1" bind:__l="__l" u-p="{{ax}}"><view style="display:flex;flex-direction:column;width:100%"><view class="input-with-icon"><u-input wx:if="{{at}}" u-i="3e9dfa88-36,3e9dfa88-35" bind:__l="__l" bindupdateModelValue="{{as}}" u-p="{{at}}"></u-input></view><u-text wx:if="{{av}}" u-i="3e9dfa88-37,3e9dfa88-35" bind:__l="__l" u-p="{{aw}}"></u-text></view></u-form-item><u-form-item wx:if="{{ay}}" u-s="{{['d']}}" class="required-field" u-i="3e9dfa88-38,3e9dfa88-1" bind:__l="__l" u-p="{{aD}}"><view class="input-with-icon" bindtap="{{aC}}"><u-text wx:if="{{az}}" u-i="3e9dfa88-39,3e9dfa88-38" bind:__l="__l" u-p="{{az}}"></u-text><u-icon wx:if="{{aB}}" class="dropdown-icon" catchclick="{{aA}}" u-i="3e9dfa88-40,3e9dfa88-38" bind:__l="__l" u-p="{{aB}}"></u-icon></view></u-form-item></block></block><view class="activity-description-container"><u-form-item wx:if="{{aE}}" class="description-form-item required-field" u-i="3e9dfa88-41,3e9dfa88-1" bind:__l="__l" u-p="{{aE}}"></u-form-item><view class="editor-wrapper"><block wx:if="{{r0}}"><editor id="editor" class="ql-container" placeholder="请输入内容..." show-img-size show-img-toolbar show-img-resize space="nbsp" bindinput="{{aF}}" bindblur="{{aG}}" bindready="{{aH}}"></editor></block></view></view></view></view><view hidden="{{!aV}}"><view class="px30"><u-form-item wx:if="{{aM}}" u-s="{{['d']}}" class="qrcode-container required-field" u-i="3e9dfa88-42,3e9dfa88-1" bind:__l="__l" u-p="{{aM}}"><view class="qrcode-wrapper"><u-upload wx:if="{{aL}}" bindafterRead="{{aJ}}" binddelete="{{aK}}" u-i="3e9dfa88-43,3e9dfa88-42" bind:__l="__l" u-p="{{aL}}"></u-upload></view></u-form-item><u-form-item wx:if="{{aQ}}" u-s="{{['d']}}" class="qrcode-container required-field" u-i="3e9dfa88-44,3e9dfa88-1" bind:__l="__l" u-p="{{aQ}}"><view class="qrcode-wrapper"><u-upload wx:if="{{aP}}" bindafterRead="{{aN}}" binddelete="{{aO}}" u-i="3e9dfa88-45,3e9dfa88-44" bind:__l="__l" u-p="{{aP}}"></u-upload></view></u-form-item><u-form-item wx:if="{{aR}}" class="activity-images-container required-field" u-i="3e9dfa88-46,3e9dfa88-1" bind:__l="__l" u-p="{{aR}}"></u-form-item><view class="activity-images-wrapper"><u-upload wx:if="{{aU}}" bindafterRead="{{aS}}" binddelete="{{aT}}" u-i="3e9dfa88-47,3e9dfa88-1" bind:__l="__l" u-p="{{aU}}"></u-upload></view></view></view></u-form><u-gap wx:if="{{aY}}" u-i="3e9dfa88-48" bind:__l="__l" u-p="{{aY}}"></u-gap><view class="pfx w690 bottom0 tl50 left50 bottomBox z20"><view wx:if="{{aZ}}"><u-button wx:if="{{bb}}" bindclick="{{ba}}" u-i="3e9dfa88-49" bind:__l="__l" u-p="{{bb}}"></u-button></view><view wx:if="{{bc}}" class="step2-buttons"><u-button wx:if="{{be}}" bindclick="{{bd}}" u-i="3e9dfa88-50" bind:__l="__l" u-p="{{be}}"></u-button><u-button wx:if="{{bg}}" bindclick="{{bf}}" u-i="3e9dfa88-51" bind:__l="__l" u-p="{{bg}}"></u-button></view><view wx:if="{{bh}}" class="step2-buttons"><u-button wx:if="{{bj}}" bindclick="{{bi}}" u-i="3e9dfa88-52" bind:__l="__l" u-p="{{bj}}"></u-button><u-button wx:if="{{bl}}" bindclick="{{bk}}" u-i="3e9dfa88-53" bind:__l="__l" u-p="{{bl}}"></u-button></view><u-safe-bottom u-i="3e9dfa88-54" bind:__l="__l"></u-safe-bottom></view><u-datetime-picker wx:if="{{bq}}" bindconfirm="{{bm}}" bindcancel="{{bn}}" bindclose="{{bo}}" u-i="3e9dfa88-55" bind:__l="__l" bindupdateModelValue="{{bp}}" u-p="{{bq}}"></u-datetime-picker><u-picker wx:if="{{bv}}" bindcancel="{{br}}" bindclose="{{bs}}" bindconfirm="{{bt}}" u-i="3e9dfa88-56" bind:__l="__l" u-p="{{bv}}"></u-picker><u-modal wx:if="{{bz}}" bindcancel="{{bw}}" bindconfirm="{{bx}}" bindclose="{{by}}" u-i="3e9dfa88-57" bind:__l="__l" u-p="{{bz}}"></u-modal><u-modal wx:if="{{bD}}" u-s="{{['d']}}" bindconfirm="{{bB}}" bindcancel="{{bC}}" u-i="3e9dfa88-58" bind:__l="__l" u-p="{{bD}}"><view class="member-modal-content"><text class="member-modal-text"> 本功能为会员使用，你目前不是会员{{bA}} 是否成为会员？ </text></view></u-modal><u-notify wx:if="{{bF}}" class="r" u-r="notifyRef" u-i="3e9dfa88-59" bind:__l="__l" u-p="{{bF}}"></u-notify><u-datetime-picker wx:if="{{bK}}" class="r" u-r="baomingEndTimePicker" bindconfirm="{{bH}}" bindcancel="{{bI}}" u-i="3e9dfa88-60" bind:__l="__l" bindupdateModelValue="{{bJ}}" u-p="{{bK}}"></u-datetime-picker><u-picker wx:if="{{bQ}}" class="r" u-r="refundRulePicker" bindconfirm="{{bM}}" bindchange="{{bN}}" bindcancel="{{bO}}" bindclose="{{bP}}" u-i="3e9dfa88-61" bind:__l="__l" u-p="{{bQ}}"></u-picker></view>