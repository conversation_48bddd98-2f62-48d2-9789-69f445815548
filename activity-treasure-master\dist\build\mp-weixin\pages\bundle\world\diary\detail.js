"use strict";const e=require("../../../../common/vendor.js"),a=require("../../../../api/index.js"),t=require("../../../../store/index.js"),n=require("../../../../utils/index.js");if(require("../../../../utils/request.js"),require("../../../../utils/BaseUrl.js"),require("../../../../utils/auth.js"),require("../../../../store/counter.js"),require("../../../../utils/cacheManager.js"),require("../../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const i={__name:"detail",setup(i){const o=e.ref(null),s=e.ref(!0),u=e.ref(""),l=e.ref(!1);e.ref(!1);const r=e.ref([]),c=e.ref(0),v=e.ref(!1),d=()=>{e.index.navigateBack()};e.onLoad((a=>{console.log("🚀 日卡详情页面加载，参数:",a),console.log("📱 触摸事件已绑定到容器元素"),a.id?(u.value=a.id,g()):(e.index.showToast({title:"参数错误",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500))})),e.index.$on("diary-updated",(e=>{e.id===u.value&&g()})),e.onUnmounted((()=>{e.index.$off("diary-updated")}));const g=async()=>{var n,i,d;try{s.value=!0;const g=await a.getDiaryDetail({id:u.value,uid:(null==(n=t.store().$state.userInfo)?void 0:n.uid)||0,token:(null==(i=t.store().$state.userInfo)?void 0:i.token)||""});if("ok"===g.status){o.value=g.data,l.value=o.value.user_id===(null==(d=t.store().$state.userInfo)?void 0:d.uid);const e={...o.value,_type:"diary"};r.value=[e],c.value=0,v.value=(()=>{const e=t.store().$state.userInfo;return!!e&&1==e.is_huiyuan&&(!e.huiyuan_end_time||new Date(e.huiyuan_end_time)>new Date)})()}else e.index.showToast({title:g.msg||"加载失败",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500)}catch(g){console.error("加载日记详情失败:",g),e.index.showToast({title:"加载失败",icon:"none"})}finally{s.value=!1}},m=e=>{if(!e)return"";const a=e.replace(/-/g,"/"),t=new Date(a);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},f=e=>{if(!e)return"";try{if("string"==typeof e&&e.startsWith("{")){let a=e.replace(/&quot;/g,'"').replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#39;/g,"'");const t=JSON.parse(a);return t.name||t.address||"未知位置"}return e}catch(a){return console.warn("位置信息解析失败:",a),e||"未知位置"}},p=()=>{n.navto(`/pages/bundle/world/diary/edit?id=${u.value}`)},h=()=>{e.index.showModal({title:"确认删除",content:"删除后无法恢复，确定要删除这篇日记吗？",success:async n=>{if(n.confirm)try{const n=await a.deleteDiary({id:u.value,uid:t.store().$state.userInfo.uid,token:t.store().$state.userInfo.token});"ok"===n.status?(e.index.showToast({title:"删除成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1e3)):e.index.showToast({title:n.msg||"删除失败",icon:"none"})}catch(i){console.error("删除日记失败:",i),e.index.showToast({title:"删除失败",icon:"none"})}}})};return(a,t)=>{var n,i;return e.e({a:e.p({name:"arrow-left",color:"#333",size:"20"}),b:e.o(d),c:s.value},s.value?{d:e.p({mode:"circle",size:"30",color:"#ffffff"})}:{},{e:o.value},o.value?e.e({f:o.value.images&&o.value.images.length>0},o.value.images&&o.value.images.length>0?{g:o.value.images[0]}:{}):{},{h:o.value},o.value?e.e({i:(null==(n=o.value.user)?void 0:n.avatar_url)||"/static/default-avatar.png",j:e.t((null==(i=o.value.user)?void 0:i.nickname)||"用户"),k:e.t(m(o.value.created_at)),l:l.value},l.value?{m:e.p({name:"edit",color:"#6AC086",size:"16"}),n:e.o(p),o:e.p({name:"trash",color:"#666",size:"16"}),p:e.o(h)}:{},{q:e.t(o.value.content),r:o.value.location},o.value.location?{s:e.p({name:"map",size:"14",color:o.value.images&&o.value.images.length>0?"#ffffff":"#666"}),t:e.t(f(o.value.location)),v:o.value.images&&o.value.images.length>0?1:""}:{},{w:o.value.images&&o.value.images.length>0?1:"",x:o.value.tags},o.value.tags?{y:e.f(o.value.tags.split(","),((a,t,n)=>({a:e.t(a.trim()),b:a}))),z:o.value.images&&o.value.images.length>0?1:"",A:o.value.images&&o.value.images.length>0?1:""}:{},{B:o.value.images&&o.value.images.length>0},o.value.images&&o.value.images.length>0?{C:e.f(o.value.images,((a,t,n)=>({a:a,b:t,c:e.o((t=>{return n=a,i=o.value.images,void e.index.previewImage({current:n,urls:i});var n,i}),t)})))}:{}):{D:e.p({name:"warning",size:"40",color:"#999"}),E:e.o(g)})}}},o=e._export_sfc(i,[["__scopeId","data-v-522e305b"]]);wx.createPage(o);
//# sourceMappingURL=detail.js.map
