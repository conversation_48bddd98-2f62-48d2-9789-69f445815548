"use strict";const t=require("../../../../common/vendor.js"),e={name:"u-navbar",mixins:[t.mpMixin,t.mixin,t.props$38],data:()=>({}),emits:["leftClick","rightClick"],methods:{addStyle:t.addStyle,addUnit:t.addUnit,sys:t.sys,getPx:t.getPx,leftClick(){this.$emit("leftClick"),this.autoBack&&t.index.navigateBack()},rightClick(){this.$emit("rightClick")}}};if(!Array){(t.resolveComponent("u-status-bar")+t.resolveComponent("u-icon"))()}Math||((()=>"../u-status-bar/u-status-bar.js")+(()=>"../u-icon/u-icon.js"))();const i=t._export_sfc(e,[["render",function(e,i,o,r,l,n){return t.e({a:e.fixed&&e.placeholder},e.fixed&&e.placeholder?{b:n.addUnit(n.getPx(e.height)+n.sys().statusBarHeight,"px")}:{},{c:e.safeAreaInsetTop},e.safeAreaInsetTop?{d:t.p({bgColor:e.bgColor})}:{},{e:e.leftIcon},e.leftIcon?{f:t.p({name:e.leftIcon,size:e.leftIconSize,color:e.leftIconColor})}:{},{g:e.leftText},e.leftText?{h:t.t(e.leftText),i:e.leftIconColor}:{},{j:t.o(((...t)=>n.leftClick&&n.leftClick(...t))),k:t.t(e.title),l:t.s({width:n.addUnit(e.titleWidth),color:e.titleColor}),m:t.s(n.addStyle(e.titleStyle)),n:e.$slots.right||e.rightIcon||e.rightText},e.$slots.right||e.rightIcon||e.rightText?t.e({o:e.rightIcon},e.rightIcon?{p:t.p({name:e.rightIcon,size:"20"})}:{},{q:e.rightText},e.rightText?{r:t.t(e.rightText)}:{},{s:t.o(((...t)=>n.rightClick&&n.rightClick(...t)))}):{},{t:t.n(e.border&&"u-border-bottom"),v:n.addUnit(e.height),w:e.bgColor,x:t.n(e.fixed&&"u-navbar--fixed"),y:t.n(e.customClass)})}],["__scopeId","data-v-ac8af670"]]);wx.createComponent(i);
//# sourceMappingURL=u-navbar.js.map
