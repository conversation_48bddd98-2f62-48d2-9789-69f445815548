{"version": 3, "file": "commission.js", "sources": ["../../../../../../src/pages/bundle/branch_president/commission.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXGJyYW5jaF9wcmVzaWRlbnRcY29tbWlzc2lvbi52dWU"], "sourcesContent": ["<template>\n  <view class=\"page\">\n    <myTitle\n      bgColor=\"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)\"\n      height=\"200rpx\"\n      title=\"运营佣金\"\n      color=\"#ffffff\"\n      :blod=\"true\"\n    ></myTitle>\n    \n    <view class=\"content-container\">\n      <!-- 佣金统计卡片 -->\n      <view class=\"commission-stats\">\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ totalCommission }}</view>\n          <view class=\"stats-label\">累计佣金</view>\n        </view>\n        <view class=\"stats-item\">\n          <view class=\"stats-number\">{{ monthCommission }}</view>\n          <view class=\"stats-label\">本月佣金</view>\n        </view>\n      </view>\n      \n      <!-- 筛选器 -->\n      <view class=\"filter-container\">\n        <view class=\"filter-item\">\n          <u-button\n            :type=\"filterType === 'all' ? 'primary' : 'info'\"\n            size=\"small\"\n            @click=\"setFilter('all')\"\n            customStyle=\"border-radius: 30rpx; margin-right: 20rpx;\"\n            :customTextStyle=\"filterType === 'all' ? 'color: #ffffff' : 'color: #6AC086'\"\n          >\n            全部\n          </u-button>\n          <u-button\n            :type=\"filterType === 'invite' ? 'primary' : 'info'\"\n            size=\"small\"\n            @click=\"setFilter('invite')\"\n            customStyle=\"border-radius: 30rpx; margin-right: 20rpx;\"\n            :customTextStyle=\"filterType === 'invite' ? 'color: #ffffff' : 'color: #6AC086'\"\n          >\n            邀请佣金\n          </u-button>\n          <u-button\n            :type=\"filterType === 'operation' ? 'primary' : 'info'\"\n            size=\"small\"\n            @click=\"setFilter('operation')\"\n            customStyle=\"border-radius: 30rpx;\"\n            :customTextStyle=\"filterType === 'operation' ? 'color: #ffffff' : 'color: #6AC086'\"\n          >\n            运营佣金\n          </u-button>\n        </view>\n      </view>\n      \n      <!-- 佣金记录列表 -->\n      <view class=\"commission-list\" v-if=\"commissionList.length > 0\">\n        <view \n          class=\"commission-item\"\n          v-for=\"item in commissionList\"\n          :key=\"item.id\"\n        >\n          <view class=\"item-header\">\n            <view class=\"commission-type\">\n              <text class=\"type-badge\" :class=\"getTypeBadgeClass(item.commission_type)\">\n                {{ getTypeText(item.commission_type) }}\n              </text>\n              <text class=\"commission-amount\">+{{ item.money }}</text>\n            </view>\n            <view class=\"commission-time\">{{ formatTime(item.time) }}</view>\n          </view>\n          \n          <view class=\"item-content\" v-if=\"item.commission_type === 'operation'\">\n            <view class=\"commission-detail\">\n              <text class=\"detail-label\">创建月份：</text>\n              <text class=\"detail-value\">{{ formatMonth(item.time) }}</text>\n            </view>\n            <!-- 🆕 新增：系统分配标识 -->\n            <view class=\"commission-detail\" v-if=\"isSystemAssignedCommission(item)\">\n              <text class=\"detail-label\">来源：</text>\n              <text class=\"detail-value system-assigned\">系统分配用户运营佣金</text>\n            </view>\n          </view>\n          \n          <view class=\"item-content\" v-else-if=\"item.user && item.user.nickname\">\n            <view class=\"commission-detail\">\n              <text class=\"detail-label\">来源用户：</text>\n              <text class=\"detail-value\">{{ item.user.nickname }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-else-if=\"!loading\">\n        <u-empty\n          mode=\"list\"\n          text=\"暂无佣金记录\"\n          textColor=\"#999999\"\n          textSize=\"28\"\n        ></u-empty>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMore && !loading\">\n        <u-button\n          type=\"primary\"\n          :loading=\"loadingMore\"\n          @click=\"loadMore\"\n          customStyle=\"background: #6AC086; border: none; border-radius: 50rpx;\"\n        >\n          {{ loadingMore ? '加载中...' : '加载更多' }}\n        </u-button>\n      </view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <u-loading-page \n      :loading=\"loading\" \n      loading-text=\"加载中...\"\n      bg-color=\"#f8f9fa\"\n    ></u-loading-page>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed } from \"vue\";\nimport { onLoad, onShow } from \"@dcloudio/uni-app\";\nimport { requireLogin } from \"@/utils/auth\";\nimport { branch_presidentget_commission } from \"@/api\";\nimport { store } from \"@/store\";\n\n// 数据状态\nconst commissionList = ref([]);\nconst loading = ref(true);\nconst loadingMore = ref(false);\nconst hasMore = ref(true);\nconst currentPage = ref(1);\nconst pageSize = 20;\nconst filterType = ref('all'); // all, invite, operation\n\n// 统计数据\nconst totalCommission = ref('0.00');\nconst monthCommission = ref('0.00');\n\n// 页面加载\nonLoad(() => {\n  if (!requireLogin()) {\n    return;\n  }\n  \n  loadCommissionData();\n});\n\nonShow(() => {\n  // 每次显示页面时刷新数据\n  refreshData();\n});\n\n// 加载佣金数据\nconst loadCommissionData = async (isRefresh = false) => {\n  try {\n    if (isRefresh) {\n      currentPage.value = 1;\n      hasMore.value = true;\n      loading.value = true;\n    } else if (!hasMore.value) {\n      return;\n    } else {\n      // 第一次加载时也要设置loading状态\n      loading.value = true;\n    }\n    \n    const userInfo = store().$state.userInfo;\n\n    // 验证用户登录状态\n    if (!userInfo || !userInfo.uid || !userInfo.token) {\n      uni.showToast({\n        title: '请先登录',\n        icon: 'none'\n      });\n      return;\n    }\n\n    const params = {\n      uid: userInfo.uid,\n      token: userInfo.token,\n      page: currentPage.value,\n      page_size: pageSize\n    };\n\n    // 添加佣金类型筛选\n    if (filterType.value !== 'all') {\n      params.commission_type = filterType.value;\n    }\n\n    console.log('加载佣金数据，参数:', params);\n\n    const res = await branch_presidentget_commission(params);\n\n    console.log('佣金数据API响应:', res);\n    \n    if (res.status === 'ok') {\n      const newCommissions = res.data || [];\n      \n      if (isRefresh) {\n        commissionList.value = newCommissions;\n      } else {\n        commissionList.value.push(...newCommissions);\n      }\n      \n      // 判断是否还有更多数据\n      hasMore.value = newCommissions.length === pageSize;\n      \n      // 计算统计数据（仅在刷新时计算）\n      if (isRefresh) {\n        calculateStats();\n      }\n      \n    } else if (res.status === 'empty') {\n      if (isRefresh) {\n        commissionList.value = [];\n      }\n      hasMore.value = false;\n    } else if (res.status === 'relogin') {\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n    } else {\n      uni.showToast({\n        title: res.msg || '加载失败',\n        icon: 'none'\n      });\n    }\n  } catch (error) {\n    console.error('加载佣金数据失败:', error);\n    uni.showToast({\n      title: '网络错误，请稍后重试',\n      icon: 'none'\n    });\n  } finally {\n    loading.value = false;\n    loadingMore.value = false;\n  }\n};\n\n// 🆕 新增：格式化时间戳为月份\nconst formatMonth = (timestamp) => {\n  if (!timestamp) return '未知';\n  const date = new Date(timestamp * 1000);\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n};\n\n// 计算统计数据\nconst calculateStats = () => {\n  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM格式\n  let total = 0;\n  let monthTotal = 0;\n  \n  commissionList.value.forEach(item => {\n    const amount = parseFloat(item.money) || 0;\n    total += amount;\n    \n    // 计算本月佣金\n    if (item.time && item.time.startsWith(currentMonth)) {\n      monthTotal += amount;\n    }\n  });\n  \n  totalCommission.value = total.toFixed(2);\n  monthCommission.value = monthTotal.toFixed(2);\n};\n\n// 设置筛选器\nconst setFilter = (type) => {\n  if (filterType.value !== type) {\n    filterType.value = type;\n    refreshData();\n  }\n};\n\n// 刷新数据\nconst refreshData = () => {\n  loadCommissionData(true);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (hasMore.value && !loadingMore.value) {\n    loadingMore.value = true;\n    currentPage.value++;\n    loadCommissionData();\n  }\n};\n\n// 获取类型文本\nconst getTypeText = (type) => {\n  switch (type) {\n    case 'invite':\n      return '邀请佣金';\n    case 'operation':\n      return '运营佣金';\n    default:\n      return '邀请佣金';\n  }\n};\n\n// 获取类型徽章样式\nconst getTypeBadgeClass = (type) => {\n  switch (type) {\n    case 'invite':\n      return 'type-invite';\n    case 'operation':\n      return 'type-operation';\n    default:\n      return 'type-invite';\n  }\n};\n\n// 🆕 新增：判断是否为系统分配佣金\nconst isSystemAssignedCommission = (item) => {\n  return item.remark && item.remark.includes('系统分配用户运营佣金');\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n  const date = new Date(timeStr);\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.content-container {\n  padding: 30rpx;\n}\n\n.commission-stats {\n  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  display: flex;\n  justify-content: space-around;\n  color: #ffffff;\n}\n\n.stats-item {\n  text-align: center;\n}\n\n.stats-number {\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.stats-label {\n  font-size: 26rpx;\n  opacity: 0.8;\n}\n\n.filter-container {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n}\n\n.commission-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.commission-item {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.commission-type {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.type-badge {\n  font-size: 22rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 20rpx;\n  color: #ffffff;\n}\n\n.type-invite {\n  background: #6AC086;\n}\n\n.type-operation {\n  background: #ff6b6b;\n}\n\n.commission-amount {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #6AC086;\n}\n\n.commission-time {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.item-content {\n  border-top: 1rpx solid #f0f0f0;\n  padding-top: 16rpx;\n}\n\n.commission-detail {\n  display: flex;\n  align-items: center;\n}\n\n.detail-label {\n  font-size: 26rpx;\n  color: #666666;\n  margin-right: 10rpx;\n}\n\n.detail-value {\n  font-size: 26rpx;\n  color: #333333;\n}\n\n// 🆕 新增：系统分配标识样式\n.system-assigned {\n  color: #FF9800 !important;\n  font-weight: bold;\n  position: relative;\n\n  &::before {\n    content: '🤖';\n    margin-right: 8rpx;\n  }\n}\n\n.empty-state {\n  margin-top: 100rpx;\n}\n\n.load-more {\n  margin-top: 40rpx;\n  text-align: center;\n}\n</style>", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/branch_president/commission.vue'\nwx.createPage(MiniProgramPage)"], "names": ["commissionList", "ref", "loading", "loadingMore", "hasMore", "currentPage", "filterType", "totalCommission", "monthCommission", "onLoad", "requireLogin", "onShow", "loadCommissionData", "async", "isRefresh", "value", "userInfo", "store", "$state", "uid", "token", "uni", "showToast", "title", "icon", "params", "page", "page_size", "commission_type", "console", "log", "res", "branch_presidentget_commission", "status", "newCommissions", "data", "push", "length", "msg", "error", "formatMonth", "timestamp", "date", "Date", "getFullYear", "String", "getMonth", "padStart", "calculateStats", "currentMonth", "toISOString", "slice", "total", "monthTotal", "for<PERSON>ach", "item", "amount", "parseFloat", "money", "time", "startsWith", "toFixed", "setFilter", "type", "refreshData", "loadMore", "getTypeText", "getTypeBadgeClass", "isSystemAssignedCommission", "remark", "includes", "formatTime", "timeStr", "toLocaleString", "year", "month", "day", "hour", "minute", "wx", "createPage", "MiniProgramPage"], "mappings": "q1BAsIA,MAAMA,EAAiBC,EAAAA,IAAI,IACrBC,EAAUD,EAAAA,KAAI,GACdE,EAAcF,EAAAA,KAAI,GAClBG,EAAUH,EAAAA,KAAI,GACdI,EAAcJ,EAAAA,IAAI,GAElBK,EAAaL,EAAAA,IAAI,OAGjBM,EAAkBN,EAAAA,IAAI,QACtBO,EAAkBP,EAAAA,IAAI,QAG5BQ,EAAAA,QAAO,KACAC,EAAYA,uBAOnBC,EAAAA,QAAO,YAMD,MAAAC,EAAqBC,MAAOC,GAAY,KACxC,IACF,GAAIA,EACFT,EAAYU,MAAQ,EACpBX,EAAQW,OAAQ,EAChBb,EAAQa,OAAQ,MACtB,KAAgBX,EAAQW,MAClB,OAGAb,EAAQa,OAAQ,CAClB,CAEA,MAAMC,EAAWC,EAAAA,QAAQC,OAAOF,SAGhC,IAAKA,IAAaA,EAASG,MAAQH,EAASI,MAK1C,YAJAC,EAAAA,MAAIC,UAAU,CACZC,MAAO,OACPC,KAAM,SAKV,MAAMC,EAAS,CACbN,IAAKH,EAASG,IACdC,MAAOJ,EAASI,MAChBM,KAAMrB,EAAYU,MAClBY,UAlDW,IAsDY,QAArBrB,EAAWS,QACbU,EAAOG,gBAAkBtB,EAAWS,OAG9Bc,QAAAC,IAAI,aAAcL,GAE1B,MAAMM,QAAYC,iCAA+BP,GAI7C,GAFII,QAAAC,IAAI,aAAcC,GAEP,OAAfA,EAAIE,OAAiB,CACjB,MAAAC,EAAiBH,EAAII,MAAQ,GAE/BrB,EACFd,EAAee,MAAQmB,EAERlC,EAAAe,MAAMqB,QAAQF,GAIvB9B,EAAAW,MA1EG,KA0EKmB,EAAeG,OAG3BvB,MAIV,KAA8B,UAAfiB,EAAIE,QACTnB,IACFd,EAAee,MAAQ,IAEzBX,EAAQW,OAAQ,GACQ,YAAfgB,EAAIE,OACbZ,EAAAA,MAAIC,UAAU,CACZC,MAAO,cACPC,KAAM,SAGRH,EAAAA,MAAIC,UAAU,CACZC,MAAOQ,EAAIO,KAAO,OAClBd,KAAM,QAGX,OAAQe,GACCV,QAAAU,MAAM,YAAaA,GAC3BlB,EAAAA,MAAIC,UAAU,CACZC,MAAO,aACPC,KAAM,QAEZ,CAAY,QACRtB,EAAQa,OAAQ,EAChBZ,EAAYY,OAAQ,CACtB,GAIIyB,EAAeC,IACnB,IAAKA,EAAkB,MAAA,KACvB,MAAMC,EAAO,IAAIC,KAAiB,IAAZF,GACtB,MAAO,GAAGC,EAAKE,iBAAiBC,OAAOH,EAAKI,WAAa,GAAGC,SAAS,EAAG,MAAG,EAIvEC,EAAiB,KACf,MAAAC,GAAe,IAAIN,MAAOO,cAAcC,MAAM,EAAG,GACvD,IAAIC,EAAQ,EACRC,EAAa,EAEFrD,EAAAe,MAAMuC,SAAgBC,IACnC,MAAMC,EAASC,WAAWF,EAAKG,QAAU,EAChCN,GAAAI,EAGLD,EAAKI,MAAQJ,EAAKI,KAAKC,WAAWX,KACtBI,GAAAG,EAChB,IAGcjD,EAAAQ,MAAQqC,EAAMS,QAAQ,GACtBrD,EAAAO,MAAQsC,EAAWQ,QAAQ,EAAC,EAIxCC,EAAaC,IACbzD,EAAWS,QAAUgD,IACvBzD,EAAWS,MAAQgD,MAErB,EAIIC,EAAc,KAClBpD,GAAmB,EAAI,EAInBqD,EAAW,KACX7D,EAAQW,QAAUZ,EAAYY,QAChCZ,EAAYY,OAAQ,EACRV,EAAAU,YAEd,EAIImD,EAAeH,IACnB,OAAQA,GACN,IAAK,SAIL,QACS,MAAA,OAHT,IAAK,YACI,MAAA,OAGX,EAIII,EAAqBJ,IACzB,OAAQA,GACN,IAAK,SAIL,QACS,MAAA,cAHT,IAAK,YACI,MAAA,iBAGX,EAIIK,EAA8Bb,GAC3BA,EAAKc,QAAUd,EAAKc,OAAOC,SAAS,cAIvCC,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAEd,OADM,IAAI7B,KAAK6B,GACVC,eAAe,QAAS,CAClCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WACT,yhDC/UHC,GAAGC,WAAWC"}