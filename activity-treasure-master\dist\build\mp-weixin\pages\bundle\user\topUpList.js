"use strict";const e=require("../../../common/vendor.js"),r=require("../../../api/index.js");require("../../../store/index.js");const t=require("../../../uni_modules/mescroll-uni/hooks/useMescroll.js"),n=require("../../../utils/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js"),!Array){(e.resolveComponent("u-text")+e.resolveComponent("mescroll-uni"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-text/u-text.js")+(()=>"../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js"))();const s={__name:"topUpList",setup(s){const o=e.ref([]),{mescrollInit:i,downCallback:c,getMescroll:l}=t.useMescroll(e.onPageScroll,e.onReachBottom),u=e.ref("");e.onReady((async()=>{u.value=await n.setListHeight()+"px"}));const a=async e=>{r.userget_chongzhi_log({page:e.num,page_size:e.size}).then((r=>{const t=r.data||[];1==e.num&&(o.value=[]),o.value=o.value.concat(t),e.endBySize(t.length,r.count)})).catch((()=>{e.endErr()}))};return(r,t)=>({a:e.p({margin:"0 10rpx",align:"center",color:"#333",size:"22rpx",text:"充值状态"}),b:e.p({margin:"0 10rpx",align:"center",color:"#333",size:"22rpx",text:"支付日期"}),c:e.p({margin:"0 10rpx",align:"center",color:"#333",size:"22rpx",text:"充值金额(元)"}),d:e.f(o.value,((r,t,s)=>({a:"9c4fcf88-4-"+s+",9c4fcf88-3",b:e.p({margin:"0 10rpx",align:"center",color:"#333",size:"22rpx",text:e.unref(n.getItem)(["未支付","已支付","已取消"],r.status)}),c:"9c4fcf88-5-"+s+",9c4fcf88-3",d:e.p({margin:"0 10rpx",align:"center",color:"#333",size:"22rpx",text:r.pay_time}),e:"9c4fcf88-6-"+s+",9c4fcf88-3",f:e.p({margin:"0 10rpx",align:"center",color:"#FF2A00",size:"22rpx",text:r.money}),g:t}))),e:e.o(e.unref(i)),f:e.o(e.unref(c)),g:e.o(a),h:e.o((e=>e.scrollTo(0))),i:e.p({height:u.value,up:{page:{num:0,size:20,time:null}}})})},__runtimeHooks:1};wx.createPage(s);
//# sourceMappingURL=topUpList.js.map
