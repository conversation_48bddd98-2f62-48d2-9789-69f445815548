"use strict";const e=require("../common/vendor.js"),t=require("./BaseUrl.js"),o=require("../store/index.js"),r=require("./index.js"),s=require("./auth.js");require("./cacheManager.js");const a=new Map,n=new Map,u=["huodong/get_list","lunbotu/index","config/app","config/get_china","config/pop","user/login","huodong/get_type","huodong/get_pingjia","huodong/get_baoming_list_public","config/get_deep_link","world/get_cards","world/get_card_detail","world/get_feeds","world/get_feed_detail","user/get_trial_info"],i=["world/get_daily_cards","user/get_other_user_info","user/get_guanzhu_list","user/get_fans_list","user/guanzhu_check","userguanzhu_check","user/claim_trial_member","user/get_share_records","user/get_unread_count","user/get_daijiesuan_status"],d=["huodong/add_huodong","huodong/add_baoming","huodong/cancel_baoming","huodong/shoucang_add","huodong/shoucang_del","huodong/zan_add","huodong/zan_del","huodong/add_pingjia","world/publish_feed","world/publish_card","world/like_feed","world/like_card","world/favorite_feed","world/favorite_card","world/comment_feed","world/comment_card","world/like_comment","world/create_quote","world/get_quotes","world/delete_feed","world/delete_comment","user/update","user/guanzhu_add","user/guanzhu_del","user/create_trial_share"],l=e=>u.some((t=>e.includes(t))),c=e=>i.some((t=>e.includes(t))),g=e=>d.some((t=>e.includes(t)));e.index.addInterceptor("request",{invoke:e=>{if("https://api.linqingkeji.com/user/update"!=e.url&&"https://api.linqingkeji.com/user/update_mobile"!=e.url&&-1!=e.url.indexOf(t.BaseUrl)&&"POST"===e.method){const r=e.url.replace(t.BaseUrl,""),a=l(r),n=c(r),u=g(r);a?(console.log("公开API请求，使用默认token和uid",{apiPath:r,url:e.url,userLoggedIn:s.isValidAuth()}),e.data.token="00000000000000000000000000000000",e.data.uid="1",e._isPublicRequest=!0):s.isValidAuth()?(console.log("用户已登录，添加真实token和uid",{apiPath:r,uid:o.store().$state.userInfo.uid,tokenLength:o.store().$state.userInfo.token.length,isPublicRequest:a,isAuthWithoutRedirect:n,isAuthWithRedirect:u}),e.data.token=o.store().$state.userInfo.token,e.data.uid=o.store().$state.userInfo.uid):e.url.includes("user/login")||(e._requiresAuth=!0,e._requiresRedirect=u)}}}),e.index.addInterceptor("navigateTo",{invoke:t=>e.index.$u.page()!=t.url});const _={request:s=>new Promise(((u,i)=>{var d,_;s.data||(s.data={});const h=((e,t)=>{const o=t||{},r={uid:o.uid||"guest",list_type:o.list_type,category:o.category,user_id:o.user_id,type_id:o.type_id,shi_id:o.shi_id,qu_id:o.qu_id,sort:o.sort,keyword:o.keyword,page:o.page,page_size:o.page_size,date:o.date,huodong_date:o.huodong_date,baoming_date:o.baoming_date,is_tuijian:o.is_tuijian,baoming_status:o.baoming_status,huodong_status:o.huodong_status,time_key:Math.floor(Date.now()/6e4)},s=Object.fromEntries(Object.entries(r).filter((([e,t])=>void 0!==t)));return`api_${e}_${Object.keys(s).length>0?JSON.stringify(s):"default"}`})(s.url,s.data);if(a.has(h)){const e=n.get(h)||Date.now(),t=Date.now()-e;if(t<200)return console.log(`防抖阻止重复请求 [${h}]，已等待 ${t}ms`),a.get(h);console.log(`清理旧请求 [${h}]，允许新请求`),a.delete(h),n.delete(h)}let m=!1,f=setTimeout((()=>{m=!0,a.delete(h),n.delete(h),console.error(`请求超时 [${h}]，已清理缓存`),i(new Error("请求超时"))}),15e3);if(s._requiresAuth&&(!(null==(d=o.store().$state.userInfo)?void 0:d.token)||!(null==(_=o.store().$state.userInfo)?void 0:_.uid))){clearTimeout(f);return void u({status:"relogin",msg:"请先登录后再操作"})}const p=["huodong/add_huodong","huodong/update_huodong"].some((e=>s.url.includes(e))),w=s.data&&Object.values(s.data).some((e=>Array.isArray(e))),$=p?"application/x-www-form-urlencoded":w?"application/json":"application/x-www-form-urlencoded";return s.url?(n.set(h,Date.now()),console.log(`开始新请求 [${h}]，当前时间: ${(new Date).toISOString()}`),a.set(h,new Promise(((d,_)=>{e.index.request({url:t.BaseUrl+s.url,method:s.method||"GET",data:s.data,header:{"content-type":$},success:async e=>{var _,p,w;if(m)return;clearTimeout(f),a.delete(h),n.delete(h);const $=Date.now()-(n.get(h)||Date.now());if(console.log(`请求成功 [${h}]，耗时: ${$}ms`),200!==e.statusCode)return console.error(`HTTP错误: ${e.statusCode}`),s.url&&(s.url.includes("comment_card")||s.url.includes("comment_feed")||s.url.includes("like_card")||s.url.includes("like_feed"))&&console.error("API调用失败:",{url:s.url,statusCode:e.statusCode,data:s.data}),i(new Error(`HTTP错误: ${e.statusCode}`));if(!e.data)return console.error("响应数据为空"),i(new Error("响应数据为空"));const{status:k,msg:y}=e.data;if("relogin"===k){const a=s.url.replace(t.BaseUrl,""),n=l(a),i=c(a),d=g(a),h=o.store().$state.userInfo;if(console.log("API返回relogin状态，详细信息:",{apiPath:a,isPublicRequest:n,isAuthWithoutRedirect:i,isAuthWithRedirect:d,hasUserInfo:!!(h&&h.uid&&h.token),uid:null==h?void 0:h.uid,tokenLength:null==(_=null==h?void 0:h.token)?void 0:_.length}),(s._isPublicRequest||n)&&(console.warn("公开API返回relogin状态，这可能是后端逻辑问题",{apiPath:a,isPublicRequest:n,hasDefaultToken:"00000000000000000000000000000000"===(null==(p=s.data)?void 0:p.token)}),"00000000000000000000000000000000"===(null==(w=s.data)?void 0:w.token))){console.log("公开API使用默认token，忽略relogin状态"),console.log("原始响应数据:",e.data);let t=e.data;if("string"==typeof t){console.log("检测到字符串响应，提取JSON...");try{const e=t.indexOf('{"status"');if(-1!==e){const o=t.substring(e);t=JSON.parse(o),console.log("JSON解析成功，返回解析后的数据:",t)}}catch(q){console.error("JSON解析失败:",q)}}return u(t)}return h&&h.uid&&h.token&&(console.warn("用户已登录但API返回relogin，可能是token过期或权限问题"),i)?(console.log("API不需要自动跳转，返回relogin状态供页面处理"),u(e.data)):(console.log("需要重新登录"),(s._requiresRedirect||d)&&(o.store().flag||(o.store().flag=!0,console.log("跳转到登录页"),r.navto("/pages/bundle/common/login"),setTimeout((()=>o.store().flag=!1),1500))),u(e.data))}return"empty"===k?(console.log("服务器返回空数据"),u("n")):"error"===k?(console.error("服务器返回错误:",y),y&&y.includes("Missing parameter [ uid ]")?(console.log("检测到缺少uid参数，可能是用户未登录"),u({status:"relogin",msg:"请先登录后再操作"})):u(e.data)):(d(e.data),void u(e.data))},fail:e=>{m||(clearTimeout(f),a.delete(h),n.delete(h),console.error(`请求失败 [${h}]:`,e),_(e),i(e))},complete:()=>{}})}))),a.get(h)):(clearTimeout(f),void i(new Error("请求URL为空")))})),getCoordinate:o=>new Promise(((r,s)=>{e.index.request({url:`https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${o.locations}&coordsys=gps&output=JSON&key=${t.gaodeKey}`,success:e=>r(e.data),fail:e=>s(e)})})),getAddr:o=>new Promise(((r,s)=>{e.index.request({url:`https://restapi.amap.com/v3/geocode/regeo?output=JSON&location=${o.location}&key=${t.gaodeKey}&extensions=${o.extensions?o.extensions:"base"}`,success:e=>r(e.data),fail:e=>s(e)})})),getArrayBuffer:o=>new Promise(((s,a)=>{const n=Object.values(o.data).some((e=>Array.isArray(e))),u=n?"application/json":"application/x-www-form-urlencoded";console.log(`ArrayBuffer请求内容类型: ${u}, 是否包含数组: ${n}`),e.index.request({url:t.BaseUrl+o.url,method:o.method,data:o.data,header:{"content-type":u},responseType:"arraybuffer",success:t=>{const{status:o,msg:a}=t.data;if("relogin"===o)e.index.$u.toast("请重新登录"),e.index.$u.debounce(r.navto("/pages/bundle/common/login"),1e3);else if("error"===o)e.index.$u.toast(a);else{if("empty"===o)return e.index.$u.toast("暂无数据"),s("n");a&&e.index.$u.toast(a)}s(t.data)},fail:e=>{a(e)}})})),upload:s=>new Promise(((a,n)=>{var u,i;let d=!1,l=setTimeout((()=>{d=!0,console.error("上传请求超时:",s.url),n(new Error("上传请求超时"))}),3e4);console.log(`发起上传请求: ${s.url}, 文件路径: ${s.data}`);const c=s.formData||{};(null==(u=o.store().$state.userInfo)?void 0:u.uid)&&(c.uid=o.store().$state.userInfo.uid),(null==(i=o.store().$state.userInfo)?void 0:i.token)&&(c.token=o.store().$state.userInfo.token),e.index.uploadFile({url:t.BaseUrl+s.url,filePath:s.data,name:s.name,formData:c,success:t=>{if(!d){if(clearTimeout(l),console.log(`上传成功: ${s.url}, 状态码: ${t.statusCode}, 响应:`,t.data),200!==t.statusCode)return console.error(`上传HTTP错误: ${t.statusCode}`),n(new Error(`上传HTTP错误: ${t.statusCode}`));try{const o=JSON.parse(t.data),{status:s,msg:n}=o;if("relogin"===s)return console.log("上传需要重新登录"),e.index.showToast({title:"请先登录",icon:"none"}),setTimeout((()=>r.navto("/pages/bundle/common/login")),1e3),a(o);if("error"===s)return console.error("上传服务器返回错误:",n),e.index.showToast({title:n||"上传失败",icon:"none"}),a(o);if("empty"===s)return console.log("上传服务器返回空数据"),e.index.showToast({title:"暂无数据",icon:"none"}),a("n");a(o)}catch(o){console.error("解析上传响应失败:",o),n(new Error("解析上传响应失败"))}}},fail:e=>{d||(clearTimeout(l),console.error(`上传失败: ${s.url}`,e),n(e))}})})),u(e,t,o,r){return t=t||{},r||(r={}),r.name=o,r.url=e,r.data=t,r.formData=r.formData||{},this.upload(r)},aB(e,t,o,r){return t=t||{},r||(r={}),r.url=e,r.data=t,r.method=o||"POST",this.getArrayBuffer(r)},g(e,t,o){return o||(o={}),o.url=e,o.data=t,o.method="GET",this.request(o)},p(e,t,o){return t=t||{},o||(o={}),o.url=e,o.data=t,o.method="POST",this.request(o)},put(e,t,o){return t=t||{},o||(o={}),o.url=e,o.data=t,o.method="PUT",this.request(o)},delete(e,t,o){return t=t||{},o||(o={}),o.url=e,o.data=t,o.method="DELETE",this.request(o)}};exports.requset=_;
//# sourceMappingURL=request.js.map
