{"version": 3, "file": "index.js", "sources": ["../../../../../../src/pages/bundle/report/index.vue", "../../../../../../uniPage:/cGFnZXNcYnVuZGxlXHJlcG9ydFxpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"page\">\n    <view class=\"container\">\n      <!-- 页面标题 -->\n      <view class=\"page-header\">\n        <u-navbar \n            title=\"投诉/举报\" \n            :border=\"false\"\n            :background=\"{ backgroundColor: '#ffffff' }\"\n            :titleStyle=\"{ color: '#333333', fontSize: '32rpx', fontWeight: 'bold' }\"\n            leftIcon=\"arrow-left\"\n            @leftClick=\"goBack\"\n        ></u-navbar>\n      </view>\n\n      <!-- 步骤指示器 -->\n      <view class=\"steps-container\">\n        <view class=\"step-item\" :class=\"{ active: currentStep >= 1, completed: currentStep > 1 }\">\n          <view class=\"step-number\">1</view>\n          <text class=\"step-text\">选择对象</text>\n        </view>\n        <view class=\"step-line\" :class=\"{ active: currentStep > 1 }\"></view>\n        <view class=\"step-item\" :class=\"{ active: currentStep >= 2, completed: currentStep > 2 }\">\n          <view class=\"step-number\">2</view>\n          <text class=\"step-text\">选择理由</text>\n        </view>\n        <view class=\"step-line\" :class=\"{ active: currentStep > 2 }\"></view>\n        <view class=\"step-item\" :class=\"{ active: currentStep >= 3 }\">\n          <view class=\"step-number\">3</view>\n          <text class=\"step-text\">提交详情</text>\n        </view>\n      </view>\n\n      <!-- 步骤1：选择举报对象 -->\n      <view v-if=\"currentStep === 1\" class=\"step-content\">\n        <view class=\"section-title\">\n          <u-text text=\"请选择要举报的用户\" size=\"32rpx\" bold color=\"#333\"></u-text>\n        </view>\n        \n        <view v-if=\"loading\" class=\"loading-container\">\n          <u-loading-icon mode=\"spinner\" size=\"60rpx\"></u-loading-icon>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        \n        <view v-else-if=\"participants.length === 0\" class=\"empty-container\">\n          <u-empty text=\"暂无其他参与者\" mode=\"data\"></u-empty>\n        </view>\n        \n        <view v-else class=\"participants-list\">\n          <view \n              v-for=\"participant in participants\" \n              :key=\"participant.uid\"\n              class=\"participant-item\"\n              @click=\"selectParticipant(participant)\"\n          >\n            <u-avatar \n                :src=\"participant.avatar\" \n                size=\"80rpx\"\n                :customStyle=\"{ marginRight: '24rpx' }\"\n            ></u-avatar>\n            <view class=\"participant-info\">\n              <text class=\"participant-name\">{{ participant.nickname }}</text>\n              <text class=\"participant-role\">{{ getRoleText(participant.role_type) }}</text>\n            </view>\n            <u-icon name=\"arrow-right\" size=\"20\" color=\"#999\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 步骤2：选择举报理由 -->\n      <view v-if=\"currentStep === 2\" class=\"step-content\">\n        <view class=\"section-title\">\n          <u-text text=\"请选择举报理由\" size=\"32rpx\" bold color=\"#333\"></u-text>\n        </view>\n        \n        <view class=\"selected-target\">\n          <text class=\"target-label\">举报对象：</text>\n          <text class=\"target-name\">{{ selectedParticipant?.nickname }}</text>\n        </view>\n        \n        <view v-if=\"loadingOptions\" class=\"loading-container\">\n          <u-loading-icon mode=\"spinner\" size=\"60rpx\"></u-loading-icon>\n          <text class=\"loading-text\">加载举报选项...</text>\n        </view>\n        \n        <view v-else class=\"report-options-list\">\n          <view \n              v-for=\"option in reportOptions\" \n              :key=\"option.id\"\n              class=\"report-option-item\"\n              @click=\"selectReportOption(option)\"\n          >\n            <view class=\"option-content\">\n              <text class=\"option-title\">{{ option.title }}</text>\n              <text class=\"option-description\">{{ option.description }}</text>\n            </view>\n            <u-icon name=\"arrow-right\" size=\"20\" color=\"#999\"></u-icon>\n          </view>\n        </view>\n        \n        <view class=\"step-buttons\">\n          <u-button \n              text=\"上一步\" \n              color=\"#f5f5f5\"\n              :customStyle=\"buttonStyles.secondary\"\n              @click=\"prevStep\"\n          ></u-button>\n        </view>\n      </view>\n\n      <!-- 步骤3：填写举报详情 -->\n      <view v-if=\"currentStep === 3\" class=\"step-content\">\n        <view class=\"section-title\">\n          <u-text text=\"举报详情\" size=\"32rpx\" bold color=\"#333\"></u-text>\n        </view>\n        \n        <view class=\"report-summary\">\n          <view class=\"summary-item\">\n            <text class=\"summary-label\">举报对象：</text>\n            <text class=\"summary-value\">{{ selectedParticipant?.nickname }}</text>\n          </view>\n          <view class=\"summary-item\">\n            <text class=\"summary-label\">举报理由：</text>\n            <text class=\"summary-value\">{{ selectedReportOption?.title }}</text>\n          </view>\n        </view>\n        \n        <view class=\"detail-form\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">详细说明 <text class=\"required\">*</text></text>\n            <u-textarea\n                v-model=\"reportDetail\"\n                placeholder=\"请详细描述举报内容，提供具体事实和情况说明...\"\n                :height=\"300\"\n                :maxlength=\"500\"\n                :show-confirm-bar=\"false\"\n                :customStyle=\"textareaStyles\"\n            ></u-textarea>\n            <text class=\"char-count\">{{ reportDetail.length }}/500</text>\n          </view>\n        </view>\n        \n        <view class=\"step-buttons\">\n          <u-button \n              text=\"上一步\" \n              color=\"#f5f5f5\"\n              :customStyle=\"buttonStyles.secondary\"\n              @click=\"prevStep\"\n          ></u-button>\n          <u-button \n              text=\"提交举报\" \n              color=\"linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%)\"\n              :customStyle=\"buttonStyles.primary\"\n              :loading=\"submitting\"\n              @click=\"submitReport\"\n          ></u-button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\nimport { getAuthParams } from '@/utils/auth';\nimport { store } from '@/store';\n\n// 页面参数\nconst activityId = ref('');\n\n// 步骤控制\nconst currentStep = ref(1);\n\n// 数据状态\nconst loading = ref(false);\nconst loadingOptions = ref(false);\nconst submitting = ref(false);\n\n// 数据\nconst participants = ref([]);\nconst reportOptions = ref([]);\nconst selectedParticipant = ref(null);\nconst selectedReportOption = ref(null);\nconst reportDetail = ref('');\n\n// 样式配置\nconst buttonStyles = {\n  primary: {\n    color: '#fff',\n    fontWeight: 'bold',\n    borderRadius: '30rpx',\n    fontSize: '28rpx',\n    width: '45%'\n  },\n  secondary: {\n    color: '#666',\n    fontWeight: 'bold',\n    borderRadius: '30rpx',\n    fontSize: '28rpx',\n    width: '45%'\n  }\n};\n\nconst textareaStyles = {\n  borderColor: '#E5E5E5',\n  borderRadius: '20rpx',\n  padding: '20rpx',\n  backgroundColor: '#fafafa'\n};\n\n// 页面加载\nonLoad((options) => {\n  if (options.activity_id) {\n    activityId.value = options.activity_id;\n    loadParticipants();\n  } else {\n    uni.$u.toast('活动ID缺失');\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }\n});\n\n// 获取角色文本\nconst getRoleText = (roleType) => {\n  const roleMap = {\n    '0': '管理员',\n    '1': '分会长',\n    '2': '普通用户',\n    '3': '场地方',\n    '4': '城市分会长',\n    '5': '邀请用户'\n  };\n  return roleMap[roleType] || '用户';\n};\n\n// 加载参与者列表\nconst loadParticipants = async () => {\n  loading.value = true;\n  try {\n    const authParams = getAuthParams();\n    if (!authParams) {\n      uni.$u.toast('请先登录');\n      uni.navigateBack();\n      return;\n    }\n    \n    // 检查举报权限\n    const permissionRes = await uni.request({\n      url: `${store().$state.config.apiBaseUrl}User/check_report_permission`,\n      method: 'POST',\n      data: authParams\n    });\n    \n    if (permissionRes.data.status !== 'ok') {\n      uni.$u.toast(permissionRes.data.msg || '无举报权限');\n      uni.navigateBack();\n      return;\n    }\n    \n    // 获取活动参与者列表\n    const participantsRes = await uni.request({\n      url: `${store().$state.config.apiBaseUrl}User/get_activity_participants`,\n      method: 'POST',\n      data: {\n        ...authParams,\n        activity_id: activityId.value\n      }\n    });\n    \n    if (participantsRes.data.status === 'ok') {\n      participants.value = participantsRes.data.data || [];\n    } else {\n      uni.$u.toast(participantsRes.data.msg || '获取参与者列表失败');\n    }\n  } catch (error) {\n    console.error('加载参与者列表失败:', error);\n    uni.$u.toast('操作失败，请稍后重试');\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 选择参与者\nconst selectParticipant = (participant) => {\n  selectedParticipant.value = participant;\n  currentStep.value = 2;\n  loadReportOptions();\n};\n\n// 加载举报选项\nconst loadReportOptions = async () => {\n  loadingOptions.value = true;\n  try {\n    const res = await uni.request({\n      url: `${store().$state.config.apiBaseUrl}User/get_report_options`,\n      method: 'GET'\n    });\n    \n    if (res.data.status === 'ok') {\n      reportOptions.value = res.data.data || [];\n    } else {\n      uni.$u.toast('获取举报选项失败');\n    }\n  } catch (error) {\n    console.error('加载举报选项失败:', error);\n    uni.$u.toast('操作失败，请稍后重试');\n  } finally {\n    loadingOptions.value = false;\n  }\n};\n\n// 选择举报理由\nconst selectReportOption = (option) => {\n  selectedReportOption.value = option;\n  currentStep.value = 3;\n};\n\n// 上一步\nconst prevStep = () => {\n  if (currentStep.value > 1) {\n    currentStep.value--;\n  }\n};\n\n// 提交举报\nconst submitReport = async () => {\n  if (!reportDetail.value.trim()) {\n    uni.$u.toast('请输入举报详情');\n    return;\n  }\n  \n  submitting.value = true;\n  try {\n    const authParams = getAuthParams();\n    const res = await uni.request({\n      url: `${store().$state.config.apiBaseUrl}User/submit_report`,\n      method: 'POST',\n      data: {\n        ...authParams,\n        reported_uid: selectedParticipant.value.uid,\n        activity_id: activityId.value,\n        report_option_id: selectedReportOption.value.id,\n        report_detail: reportDetail.value\n      }\n    });\n    \n    if (res.data.status === 'ok') {\n      uni.showModal({\n        title: '举报成功',\n        content: '您的举报已提交，我们会尽快处理。感谢您的反馈！',\n        showCancel: false,\n        success: () => {\n          uni.navigateBack();\n        }\n      });\n    } else {\n      uni.$u.toast(res.data.msg || '举报提交失败');\n    }\n  } catch (error) {\n    console.error('提交举报失败:', error);\n    uni.$u.toast('操作失败，请稍后重试');\n  } finally {\n    submitting.value = false;\n  }\n};\n\n// 返回\nconst goBack = () => {\n  uni.navigateBack();\n};\n</script>\n\n<style scoped>\n.page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%);\n}\n\n.container {\n  padding: 0 30rpx 30rpx;\n}\n\n.page-header {\n  background: #ffffff;\n  margin: 0 -30rpx 30rpx;\n}\n\n/* 步骤指示器 */\n.steps-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n  background: #ffffff;\n  border-radius: 20rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.step-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n}\n\n.step-number {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  background: #E5E5E5;\n  color: #999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n  transition: all 0.3s ease;\n}\n\n.step-item.active .step-number {\n  background: #6AC086;\n  color: #ffffff;\n}\n\n.step-item.completed .step-number {\n  background: #6AC086;\n  color: #ffffff;\n}\n\n.step-text {\n  font-size: 24rpx;\n  color: #999;\n  transition: all 0.3s ease;\n}\n\n.step-item.active .step-text {\n  color: #6AC086;\n  font-weight: bold;\n}\n\n.step-line {\n  width: 100rpx;\n  height: 4rpx;\n  background: #E5E5E5;\n  margin: 0 20rpx;\n  margin-bottom: 30rpx;\n  transition: all 0.3s ease;\n}\n\n.step-line.active {\n  background: #6AC086;\n}\n\n/* 步骤内容 */\n.step-content {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 40rpx 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-title {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n/* 加载和空状态 */\n.loading-container, .empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 0;\n}\n\n.loading-text {\n  margin-top: 20rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n\n/* 参与者列表 */\n.participants-list {\n  max-height: 600rpx;\n  overflow-y: auto;\n}\n\n.participant-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.participant-item:last-child {\n  border-bottom: none;\n}\n\n.participant-item:active {\n  background-color: #f8f9fa;\n}\n\n.participant-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.participant-name {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.participant-role {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 选中目标显示 */\n.selected-target {\n  background: #f8f9fa;\n  padding: 20rpx;\n  border-radius: 12rpx;\n  margin-bottom: 30rpx;\n  border-left: 6rpx solid #6AC086;\n}\n\n.target-label {\n  font-size: 26rpx;\n  color: #666;\n  margin-right: 10rpx;\n}\n\n.target-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n}\n\n/* 举报选项列表 */\n.report-options-list {\n  max-height: 500rpx;\n  overflow-y: auto;\n}\n\n.report-option-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.report-option-item:last-child {\n  border-bottom: none;\n}\n\n.report-option-item:active {\n  background-color: #f8f9fa;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.option-description {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 举报摘要 */\n.report-summary {\n  background: #f8f9fa;\n  padding: 30rpx;\n  border-radius: 12rpx;\n  margin-bottom: 30rpx;\n}\n\n.summary-item {\n  display: flex;\n  margin-bottom: 15rpx;\n}\n\n.summary-item:last-child {\n  margin-bottom: 0;\n}\n\n.summary-label {\n  font-size: 28rpx;\n  color: #666;\n  min-width: 140rpx;\n}\n\n.summary-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  flex: 1;\n}\n\n/* 表单 */\n.detail-form {\n  margin-bottom: 50rpx;\n}\n\n.form-item {\n  margin-bottom: 30rpx;\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  margin-bottom: 15rpx;\n  display: block;\n}\n\n.required {\n  color: #ff4757;\n}\n\n.char-count {\n  font-size: 24rpx;\n  color: #999;\n  text-align: right;\n  margin-top: 10rpx;\n  display: block;\n}\n\n/* 按钮 */\n.step-buttons {\n  display: flex;\n  justify-content: space-between;\n  gap: 30rpx;\n  margin-top: 50rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/report/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["activityId", "ref", "currentStep", "loading", "loadingOptions", "submitting", "participants", "reportOptions", "selectedParticipant", "selectedReportOption", "reportDetail", "buttonStyles", "primary", "color", "fontWeight", "borderRadius", "fontSize", "width", "secondary", "textareaStyles", "borderColor", "padding", "backgroundColor", "common_vendor", "onLoad", "options", "activity_id", "value", "uni", "$u", "toast", "setTimeout", "index", "navigateBack", "loadParticipants", "async", "authParams", "getAuthParams", "permissionRes", "request", "url", "store", "$state", "config", "apiBaseUrl", "method", "data", "status", "msg", "participantsRes", "error", "console", "loadReportOptions", "res", "prevStep", "submitReport", "trim", "reported_uid", "uid", "report_option_id", "id", "report_detail", "showModal", "title", "content", "showCancel", "success", "goBack", "roleType", "participant", "option", "wx", "createPage", "MiniProgramPage"], "mappings": "2wCAyKM,MAAAA,EAAaC,EAAAA,IAAI,IAGjBC,EAAcD,EAAAA,IAAI,GAGlBE,EAAUF,EAAAA,KAAI,GACdG,EAAiBH,EAAAA,KAAI,GACrBI,EAAaJ,EAAAA,KAAI,GAGjBK,EAAeL,EAAAA,IAAI,IACnBM,EAAgBN,EAAAA,IAAI,IACpBO,EAAsBP,EAAAA,IAAI,MAC1BQ,EAAuBR,EAAAA,IAAI,MAC3BS,EAAeT,EAAAA,IAAI,IAGnBU,EAAe,CACnBC,QAAS,CACPC,MAAO,OACPC,WAAY,OACZC,aAAc,QACdC,SAAU,QACVC,MAAO,OAETC,UAAW,CACTL,MAAO,OACPC,WAAY,OACZC,aAAc,QACdC,SAAU,QACVC,MAAO,QAILE,EAAiB,CACrBC,YAAa,UACbL,aAAc,QACdM,QAAS,QACTC,gBAAiB,WAIbC,EAAAC,QAAEC,IACFA,EAAQC,aACV1B,EAAW2B,MAAQF,EAAQC,kBAG3BE,EAAAA,MAAIC,GAAGC,MAAM,UACbC,YAAW,KACTH,EAAGI,MAACC,cAAY,GACf,MACL,IAII,MAaAC,EAAmBC,UACvBhC,EAAQwB,OAAQ,EACZ,IACI,MAAAS,EAAaC,EAAAA,gBACnB,IAAKD,EAGH,OAFAR,EAAAA,MAAIC,GAAGC,MAAM,aACbF,EAAGI,MAACC,eAKN,MAAMK,QAAsBV,EAAGI,MAACO,QAAQ,CACtCC,IAAK,GAAGC,UAAQC,OAAOC,OAAOC,yCAC9BC,OAAQ,OACRC,KAAMV,IAGJ,GAA8B,OAA9BE,EAAcQ,KAAKC,OAGrB,OAFAnB,EAAGI,MAACH,GAAGC,MAAMQ,EAAcQ,KAAKE,KAAO,cACvCpB,EAAGI,MAACC,eAKN,MAAMgB,QAAwBrB,EAAGI,MAACO,QAAQ,CACxCC,IAAK,GAAGC,UAAQC,OAAOC,OAAOC,2CAC9BC,OAAQ,OACRC,KAAM,IACDV,EACHV,YAAa1B,EAAW2B,SAIQ,OAAhCsB,EAAgBH,KAAKC,OACvBzC,EAAaqB,MAAQsB,EAAgBH,KAAKA,MAAQ,GAElDlB,EAAGI,MAACH,GAAGC,MAAMmB,EAAgBH,KAAKE,KAAO,YAE5C,OAAQE,GACCC,QAAAD,MAAM,aAAcA,GAC5BtB,EAAAA,MAAIC,GAAGC,MAAM,aACjB,CAAY,QACR3B,EAAQwB,OAAQ,CAClB,GAWIyB,EAAoBjB,UACxB/B,EAAeuB,OAAQ,EACnB,IACF,MAAM0B,QAAYzB,EAAGI,MAACO,QAAQ,CAC5BC,IAAK,GAAGC,UAAQC,OAAOC,OAAOC,oCAC9BC,OAAQ,QAGc,OAApBQ,EAAIP,KAAKC,OACXxC,EAAcoB,MAAQ0B,EAAIP,KAAKA,MAAQ,GAEvClB,EAAAA,MAAIC,GAAGC,MAAM,WAEhB,OAAQoB,GACCC,QAAAD,MAAM,YAAaA,GAC3BtB,EAAAA,MAAIC,GAAGC,MAAM,aACjB,CAAY,QACR1B,EAAeuB,OAAQ,CACzB,GAUI2B,EAAW,KACXpD,EAAYyB,MAAQ,GACVzB,EAAAyB,OACd,EAII4B,EAAepB,UACnB,GAAKzB,EAAaiB,MAAM6B,OAAxB,CAKAnD,EAAWsB,OAAQ,EACf,IACI,MAAAS,EAAaC,EAAAA,gBACbgB,QAAYzB,EAAGI,MAACO,QAAQ,CAC5BC,IAAK,GAAGC,UAAQC,OAAOC,OAAOC,+BAC9BC,OAAQ,OACRC,KAAM,IACDV,EACHqB,aAAcjD,EAAoBmB,MAAM+B,IACxChC,YAAa1B,EAAW2B,MACxBgC,iBAAkBlD,EAAqBkB,MAAMiC,GAC7CC,cAAenD,EAAaiB,SAIR,OAApB0B,EAAIP,KAAKC,OACXnB,EAAAA,MAAIkC,UAAU,CACZC,MAAO,OACPC,QAAS,0BACTC,YAAY,EACZC,QAAS,KACPtC,EAAGI,MAACC,cAAY,IAIpBL,EAAGI,MAACH,GAAGC,MAAMuB,EAAIP,KAAKE,KAAO,SAEhC,OAAQE,GACCC,QAAAD,MAAM,UAAWA,GACzBtB,EAAAA,MAAIC,GAAGC,MAAM,aACjB,CAAY,QACRzB,EAAWsB,OAAQ,CACrB,CAlCA,MAFEC,EAAAA,MAAIC,GAAGC,MAAM,UAoCf,EAIIqC,EAAS,KACbvC,EAAGI,MAACC,cAAY,uqBAjJGmC,cACH,CACd,EAAK,MACL,EAAK,MACL,EAAK,OACL,EAAK,MACL,EAAK,QACL,EAAK,QAEQA,IAAa,2CAmDJ,CAACC,IACzB7D,EAAoBmB,MAAQ0C,EAC5BnE,EAAYyB,MAAQ,oBA9DF,IAACyC,sXAyFM,CAACE,IAC1B7D,EAAqBkB,MAAQ2C,EAC7BpE,EAAYyB,MAAQ,CAAA,muBC3TtB4C,GAAGC,WAAWC"}