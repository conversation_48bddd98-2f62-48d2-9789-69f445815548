"use strict";const r=require("../../../../common/vendor.js"),o={name:"u-image",mixins:[r.mpMixin,r.mixin,r.props$17],data(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{},show:!1}},watch:{src:{immediate:!0,handler(r){r?(this.isError=!1,this.loading=!0):this.isError=!0}}},computed:{transStyle(){let o={};return o.width=r.addUnit(this.width),o.height=r.addUnit(this.height),o},wrapStyle(){let o={width:"100%",height:"100%"};return o.borderRadius="circle"==this.shape?"10000px":r.addUnit(this.radius),o.overflow=this.radius>0?"hidden":"visible",r.deepMerge(o,r.addStyle(this.customStyle))}},mounted(){this.show=!0},emits:["click","error","load"],methods:{addUnit:r.addUnit,onClick(r){this.$emit("click",r)},onErrorHandler(r){this.loading=!1,this.isError=!0,this.$emit("error",r)},onLoadHandler(r){this.loading=!1,this.isError=!1,this.$emit("load",r),this.removeBgColor()},removeBgColor(){this.backgroundStyle={backgroundColor:this.bgColor||"#ffffff"}}}};if(!Array){(r.resolveComponent("u-icon")+r.resolveComponent("u-transition"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../u-transition/u-transition.js"))();const i=r._export_sfc(o,[["render",function(o,i,t,e,n,d){return r.e({a:!n.isError},n.isError?{}:{b:o.src,c:o.mode,d:r.o(((...r)=>d.onErrorHandler&&d.onErrorHandler(...r))),e:r.o(((...r)=>d.onLoadHandler&&d.onLoadHandler(...r))),f:o.showMenuByLongpress,g:o.lazyLoad,h:"circle"==o.shape?"10000px":d.addUnit(o.radius)},{i:o.showLoading&&n.loading},o.showLoading&&n.loading?{j:r.p({name:o.loadingIcon}),k:"circle"==o.shape?"50%":d.addUnit(o.radius),l:this.bgColor,m:d.addUnit(o.width),n:d.addUnit(o.height)}:{},{o:o.showError&&n.isError&&!n.loading},o.showError&&n.isError&&!n.loading?{p:r.p({name:o.errorIcon}),q:"circle"==o.shape?"50%":d.addUnit(o.radius)}:{},{r:r.o(((...r)=>d.onClick&&d.onClick(...r))),s:r.s(d.wrapStyle),t:r.s(n.backgroundStyle),v:r.s(d.transStyle),w:r.p({mode:"fade",show:n.show,duration:o.fade?1e3:0})})}],["__scopeId","data-v-18b4c231"]]);wx.createComponent(i);
//# sourceMappingURL=u-image.js.map
