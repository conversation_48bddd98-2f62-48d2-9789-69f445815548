{"version": 3, "file": "index.js", "sources": ["../../../../../../../src/pages/bundle/world/diary/index.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvc3JjL3BhZ2VzL2J1bmRsZS93b3JsZC9kaWFyeS9pbmRleC52dWU"], "sourcesContent": ["<script setup>\nimport { ref, onMounted, onActivated, onUnmounted } from 'vue';\nimport { getDiaries } from '@/api/index.js';\nimport { store } from '@/store';\nimport { navto } from '@/utils';\n\nconsole.log('DiaryIndex: 脚本开始执行');\n\n// 状态管理\nconst diaries = ref([]);\nconst loading = ref(true); // 恢复正常的加载状态\nconst refreshing = ref(false);\nconst loadingMore = ref(false);\nconst hasMore = ref(true);\nconst currentPage = ref(1);\nconst pageSize = 10;\n\nconsole.log('DiaryIndex: 初始化完成');\n\n// 加载日记列表\nconst loadDiaries = async (page = 1, isRefresh = false) => {\n  try {\n    console.log('DiaryIndex: 开始加载日记数据', { page, isRefresh });\n\n    if (isRefresh) {\n      refreshing.value = true;\n      currentPage.value = 1;\n    } else if (page > 1) {\n      loadingMore.value = true;\n    } else {\n      loading.value = true;\n    }\n\n    const params = {\n      page: page,\n      page_size: pageSize,\n      uid: store().$state.userInfo?.uid || 0,\n      token: store().$state.userInfo?.token || '',\n      type: 'diary' // 只获取日记类型\n    };\n\n    console.log('DiaryIndex: API请求参数', params);\n    const res = await getDiaries(params);\n    console.log('DiaryIndex: API响应结果', res);\n\n    if (res.status === 'ok') {\n      const newDiaries = res.data?.list || [];\n      console.log('DiaryIndex: 获取到日记数据', newDiaries.length, '条');\n\n      if (isRefresh || page === 1) {\n        diaries.value = newDiaries;\n      } else {\n        diaries.value = [...diaries.value, ...newDiaries];\n      }\n\n      hasMore.value = newDiaries.length === pageSize;\n      currentPage.value = page;\n    } else if (res.status === 'empty') {\n      console.log('DiaryIndex: 服务器返回空数据');\n      if (isRefresh || page === 1) {\n        diaries.value = [];\n      }\n      hasMore.value = false;\n    } else {\n      console.warn('DiaryIndex: API返回错误状态', res.status, res.msg);\n      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });\n    }\n  } catch (error) {\n    console.error('DiaryIndex: 加载日记失败:', error);\n    uni.showToast({ title: '加载失败', icon: 'none' });\n  } finally {\n    loading.value = false;\n    refreshing.value = false;\n    loadingMore.value = false;\n  }\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  loadDiaries(1, true);\n};\n\n// 上拉加载更多\nconst onLoadMore = () => {\n  if (!loadingMore.value && hasMore.value) {\n    loadDiaries(currentPage.value + 1);\n  }\n};\n\n// {{ AURA-X: Modify - 修改日记详情页跳转路径，使用独立的diary/detail.vue页面. Confirmed via 寸止 }}\n// 查看日记详情\nconst viewDiary = (diary) => {\n  navto(`/pages/bundle/world/diary/detail?id=${diary.id}`);\n};\n\n// 格式化时间\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '';\n\n  // 修复iOS日期格式问题\n  const formattedTimeStr = timeStr.replace(/-/g, '/');\n  const time = new Date(formattedTimeStr);\n  const now = new Date();\n  const diff = now - time;\n\n  // 1小时内显示xx分钟前\n  if (diff < 3600000) { // 1小时 = 3600000毫秒\n    const minutes = Math.floor(diff / 60000);\n    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;\n  }\n\n  // 1天内显示xx小时前\n  if (diff < 86400000) { // 1天 = 86400000毫秒\n    const hours = Math.floor(diff / 3600000);\n    return `${hours}小时前`;\n  }\n\n  // 超过1天显示具体日期\n  const year = time.getFullYear();\n  const month = String(time.getMonth() + 1).padStart(2, '0');\n  const day = String(time.getDate()).padStart(2, '0');\n  const hours = String(time.getHours()).padStart(2, '0');\n  const minutes = String(time.getMinutes()).padStart(2, '0');\n\n  // 判断是否是今年\n  if (year === now.getFullYear()) {\n    return `${month}-${day} ${hours}:${minutes}`;\n  } else {\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\n  }\n};\n\n// {{ AURA-X: Modify - 修复位置信息JSON解析错误，添加HTML实体解码处理. Confirmed via 寸止. }}\n// 安全解析位置信息\nconst getLocationName = (locationStr) => {\n  if (!locationStr) return '';\n\n  try {\n    // 检查是否是JSON格式（以{开头）\n    if (typeof locationStr === 'string' && locationStr.trim().startsWith('{')) {\n      // 先进行HTML实体解码处理\n      let decodedStr = locationStr\n        .replace(/&quot;/g, '\"')\n        .replace(/&amp;/g, '&')\n        .replace(/&lt;/g, '<')\n        .replace(/&gt;/g, '>')\n        .replace(/&#39;/g, \"'\");\n\n      const locationObj = JSON.parse(decodedStr);\n      return locationObj.name || locationObj.address || locationStr;\n    }\n    // 如果不是JSON格式，直接返回原字符串（纯文本位置信息）\n    return locationStr;\n  } catch (error) {\n    // 如果解析失败，直接返回原字符串\n    console.warn('位置信息JSON解析失败:', error, '原始数据:', locationStr);\n    return locationStr;\n  }\n};\n\n// {{ AURA-X: Add - 监听发布成功事件，自动刷新列表. Confirmed via 寸止 }}\n// 监听发布成功事件\nconst handleRefreshList = () => {\n  console.log('DiaryIndex: 收到刷新事件，重新加载数据');\n  loadDiaries(1, true);\n};\n\n// {{ AURA-X: Modify - 修改为按需加载，不在挂载时自动加载. Confirmed via 寸止. }}\n// 组件挂载时不自动加载数据\nonMounted(() => {\n  console.log('DiaryIndex: 组件挂载，等待按需加载');\n\n  // 监听发布成功事件\n  uni.$on('refreshFeedList', handleRefreshList);\n});\n\n// {{ AURA-X: Add - 添加外部调用接口，供父组件按需加载数据. Confirmed via 寸止. }}\n/**\n * 外部调用接口：加载日记数据\n * 供父组件在切换到日记tab时调用\n */\nconst loadDiaryData = () => {\n  console.log('DiaryIndex loadDiaryData - 被父组件调用');\n  loadDiaries();\n};\n\n// 暴露给父组件的方法\ndefineExpose({\n  loadDiaryData\n});\n\n// 组件激活时也加载数据（用于tab切换场景）\nonActivated(() => {\n  console.log('DiaryIndex: 组件激活，重新加载数据');\n  if (diaries.value.length === 0) {\n    loadDiaries();\n  }\n});\n\n// 组件卸载时移除事件监听\nonUnmounted(() => {\n  uni.$off('refreshFeedList', handleRefreshList);\n});\n</script>\n\n<template>\n  <view class=\"diary-container\">\n\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <u-loading-icon mode=\"circle\" size=\"30\" color=\"#6AC086\"></u-loading-icon>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 日记列表 -->\n    <scroll-view \n      v-else\n      class=\"diary-scroll\"\n      scroll-y\n      refresher-enabled\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"onLoadMore\"\n    >\n      <!-- 空状态 -->\n      <view v-if=\"diaries.length === 0\" class=\"empty-container\">\n        <image src=\"/static/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">还没有日记哦</text>\n        <text class=\"empty-desc\">记录生活的点点滴滴</text>\n      </view>\n\n      <!-- 日记列表 -->\n      <view v-else class=\"diary-list\">\n        <view \n          v-for=\"diary in diaries\" \n          :key=\"diary.id\" \n          class=\"diary-item\"\n          @click=\"viewDiary(diary)\"\n        >\n          <!-- 用户信息 -->\n          <view class=\"diary-header\">\n            <image \n              :src=\"diary.user?.avatar_url || '/static/default-avatar.png'\" \n              class=\"user-avatar\"\n              mode=\"aspectFill\"\n            ></image>\n            <view class=\"user-info\">\n              <text class=\"user-nickname\">{{ diary.user?.nickname || '匿名用户' }}</text>\n              <text class=\"diary-time\">{{ formatTime(diary.created_at) }}</text>\n            </view>\n            <!-- 私密标识 -->\n            <view v-if=\"diary.privacy === 'private'\" class=\"privacy-badge\">\n              <u-icon name=\"lock\" size=\"12\" color=\"#999\"></u-icon>\n              <text class=\"privacy-text\">私密</text>\n            </view>\n          </view>\n\n          <!-- 日记内容 -->\n          <view class=\"diary-content\">\n            <text class=\"diary-text\">{{ diary.content }}</text>\n          </view>\n\n          <!-- 图片展示 -->\n          <view v-if=\"diary.images && diary.images.length > 0\" class=\"diary-images\">\n            <image \n              v-for=\"(img, index) in diary.images.slice(0, 3)\" \n              :key=\"index\"\n              :src=\"img\"\n              class=\"diary-image\"\n              mode=\"aspectFill\"\n            ></image>\n            <view v-if=\"diary.images.length > 3\" class=\"more-images\">\n              <text class=\"more-text\">+{{ diary.images.length - 3 }}</text>\n            </view>\n          </view>\n\n          <!-- 位置信息 -->\n          <view v-if=\"diary.location\" class=\"diary-location\">\n            <u-icon name=\"map\" size=\"12\" color=\"#999\"></u-icon>\n            <text class=\"location-text\">{{ getLocationName(diary.location) }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"loadingMore\" class=\"loading-more\">\n        <u-loading-icon mode=\"circle\" size=\"20\" color=\"#6AC086\"></u-loading-icon>\n        <text class=\"loading-more-text\">加载更多...</text>\n      </view>\n\n      <!-- 没有更多 -->\n      <view v-if=\"!hasMore && diaries.length > 0\" class=\"no-more\">\n        <text class=\"no-more-text\">没有更多了</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<style lang=\"scss\" scoped>\n.diary-container {\n  height: 100%;\n  background-color: #f8f9fa;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400rpx;\n  \n  .loading-text {\n    margin-top: 20rpx;\n    font-size: 28rpx;\n    color: #666;\n  }\n}\n\n.diary-scroll {\n  height: 100%;\n}\n\n.empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n  \n  .empty-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: 40rpx;\n  }\n  \n  .empty-text {\n    font-size: 32rpx;\n    color: #333;\n    margin-bottom: 16rpx;\n  }\n  \n  .empty-desc {\n    font-size: 28rpx;\n    color: #999;\n  }\n}\n\n.diary-list {\n  padding: 20rpx;\n}\n\n.diary-item {\n  background: #ffffff;\n  border-radius: 16rpx;\n  padding: 32rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.diary-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n  \n  .user-avatar {\n    width: 80rpx;\n    height: 80rpx;\n    border-radius: 50%;\n    margin-right: 24rpx;\n  }\n  \n  .user-info {\n    flex: 1;\n    \n    .user-nickname {\n      display: block;\n      font-size: 30rpx;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 8rpx;\n    }\n    \n    .diary-time {\n      font-size: 24rpx;\n      color: #999;\n    }\n  }\n  \n  .privacy-badge {\n    display: flex;\n    align-items: center;\n    padding: 8rpx 16rpx;\n    background: #f5f5f5;\n    border-radius: 20rpx;\n    \n    .privacy-text {\n      font-size: 20rpx;\n      color: #999;\n      margin-left: 8rpx;\n    }\n  }\n}\n\n.diary-content {\n  margin-bottom: 24rpx;\n  \n  .diary-text {\n    font-size: 30rpx;\n    line-height: 1.6;\n    color: #333;\n  }\n}\n\n.diary-images {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 24rpx;\n  \n  .diary-image {\n    width: 200rpx;\n    height: 200rpx;\n    border-radius: 12rpx;\n  }\n  \n  .more-images {\n    width: 200rpx;\n    height: 200rpx;\n    border-radius: 12rpx;\n    background: rgba(0, 0, 0, 0.5);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    .more-text {\n      color: #fff;\n      font-size: 28rpx;\n      font-weight: 500;\n    }\n  }\n}\n\n.diary-location {\n  display: flex;\n  align-items: center;\n  \n  .location-text {\n    font-size: 24rpx;\n    color: #999;\n    margin-left: 8rpx;\n  }\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n  \n  .loading-more-text {\n    margin-left: 16rpx;\n    font-size: 28rpx;\n    color: #666;\n  }\n}\n\n.no-more {\n  display: flex;\n  justify-content: center;\n  padding: 40rpx;\n  \n  .no-more-text {\n    font-size: 28rpx;\n    color: #999;\n  }\n}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/src/pages/bundle/world/diary/index.vue'\nwx.createComponent(Component)"], "names": ["console", "log", "diaries", "ref", "loading", "refreshing", "loadingMore", "hasMore", "currentPage", "loadDiaries", "async", "page", "isRefresh", "value", "params", "page_size", "uid", "store", "$state", "userInfo", "token", "type", "res", "getDiaries", "status", "newDiaries", "_c", "data", "list", "length", "warn", "msg", "showToast", "title", "icon", "error", "uni", "index", "onRefresh", "onLoadMore", "formatTime", "timeStr", "formattedTimeStr", "replace", "time", "Date", "now", "diff", "minutes", "Math", "floor", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "getMinutes", "getLocationName", "locationStr", "trim", "startsWith", "decodedStr", "locationObj", "JSON", "parse", "name", "address", "handleRefreshList", "onMounted", "$on", "expose", "loadDiaryData", "onActivated", "onUnmounted", "$off", "diary", "navto", "id", "wx", "createComponent", "Component"], "mappings": "+sBAMAA,QAAQC,IAAI,sBAGZ,MAAMC,EAAUC,EAAAA,IAAI,IACdC,EAAUD,EAAAA,KAAI,GACdE,EAAaF,EAAAA,KAAI,GACjBG,EAAcH,EAAAA,KAAI,GAClBI,EAAUJ,EAAAA,KAAI,GACdK,EAAcL,EAAAA,IAAI,GAGxBH,QAAQC,IAAI,qBAGZ,MAAMQ,EAAcC,MAAOC,EAAO,EAAGC,GAAY,eAC3C,IACFZ,QAAQC,IAAI,uBAAwB,CAAEU,OAAMC,cAExCA,GACFP,EAAWQ,OAAQ,EACnBL,EAAYK,MAAQ,GACXF,EAAO,EAChBL,EAAYO,OAAQ,EAEpBT,EAAQS,OAAQ,EAGlB,MAAMC,EAAS,CACbH,OACAI,UApBW,GAqBXC,KAAKC,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUH,MAAO,EACrCI,OAAOH,OAAAA,EAAAA,EAAAA,QAAQC,OAAOC,mBAAUC,QAAS,GACzCC,KAAM,SAGArB,QAAAC,IAAI,sBAAuBa,GACnC,MAAMQ,QAAYC,aAAWT,GAGzB,GAFId,QAAAC,IAAI,sBAAuBqB,GAEhB,OAAfA,EAAIE,OAAiB,CACvB,MAAMC,GAAa,OAAAC,EAAAJ,EAAIK,WAAJ,EAAAD,EAAUE,OAAQ,GACrC5B,QAAQC,IAAI,sBAAuBwB,EAAWI,OAAQ,KAGpD3B,EAAQW,MADND,GAAsB,IAATD,EACCc,EAEA,IAAIvB,EAAQW,SAAUY,GAGhClB,EAAAM,MAxCG,KAwCKY,EAAWI,OAC3BrB,EAAYK,MAAQF,CAC1B,KAA8B,UAAfW,EAAIE,QACbxB,QAAQC,IAAI,yBACRW,GAAsB,IAATD,KACfT,EAAQW,MAAQ,IAElBN,EAAQM,OAAQ,IAEhBb,QAAQ8B,KAAK,wBAAyBR,EAAIE,OAAQF,EAAIS,aAClDC,UAAU,CAAEC,MAAOX,EAAIS,KAAO,OAAQG,KAAM,SAEnD,OAAQC,GACCnC,QAAAmC,MAAM,sBAAuBA,GACrCC,EAAGC,MAACL,UAAU,CAAEC,MAAO,OAAQC,KAAM,QACzC,CAAY,QACR9B,EAAQS,OAAQ,EAChBR,EAAWQ,OAAQ,EACnBP,EAAYO,OAAQ,CACtB,GAIIyB,EAAY,KAChB7B,EAAY,GAAG,EAAI,EAIf8B,EAAa,MACZjC,EAAYO,OAASN,EAAQM,OACpBJ,EAAAD,EAAYK,MAAQ,EAClC,EAUI2B,EAAcC,IAClB,IAAKA,EAAgB,MAAA,GAGrB,MAAMC,EAAmBD,EAAQE,QAAQ,KAAM,KACzCC,EAAO,IAAIC,KAAKH,GAChBI,EAAM,IAAID,KACVE,EAAOD,EAAMF,EAGnB,GAAIG,EAAO,KAAS,CAClB,MAAMC,EAAUC,KAAKC,MAAMH,EAAO,KAC3BC,OAAAA,GAAW,EAAI,KAAO,GAAGA,MAClC,CAGA,GAAID,EAAO,MAAU,CAEnB,MAAO,GADOE,KAAKC,MAAMH,EAAO,UAElC,CAGM,MAAAI,EAAOP,EAAKQ,cACZC,EAAQC,OAAOV,EAAKW,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOV,EAAKc,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOV,EAAKgB,YAAYJ,SAAS,EAAG,KAC5CR,EAAUM,OAAOV,EAAKiB,cAAcL,SAAS,EAAG,KAGlD,OAAAL,IAASL,EAAIM,cACR,GAAGC,KAASI,KAAOE,KAASX,IAE5B,GAAGG,KAAQE,KAASI,KAAOE,KAASX,GAC7C,EAKIc,EAAmBC,IACvB,IAAKA,EAAoB,MAAA,GAErB,IAEE,GAAuB,iBAAhBA,GAA4BA,EAAYC,OAAOC,WAAW,KAAM,CAErE,IAAAC,EAAaH,EACdpB,QAAQ,UAAW,KACnBA,QAAQ,SAAU,KAClBA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KACjBA,QAAQ,SAAU,KAEf,MAAAwB,EAAcC,KAAKC,MAAMH,GACxB,OAAAC,EAAYG,MAAQH,EAAYI,SAAWR,CACpD,CAEO,OAAAA,CACR,OAAQ5B,GAGA,OADPnC,QAAQ8B,KAAK,gBAAiBK,EAAO,QAAS4B,GACvCA,CACT,GAKIS,EAAoB,KACxBxE,QAAQC,IAAI,6BACZQ,EAAY,GAAG,EAAI,EAKrBgE,EAAAA,WAAU,KACRzE,QAAQC,IAAI,2BAGZmC,EAAAA,MAAIsC,IAAI,kBAAmBF,EAAiB,WAcjCG,EAAA,CACXC,cAPoB,KACpB5E,QAAQC,IAAI,4CAUd4E,EAAAA,aAAY,KACV7E,QAAQC,IAAI,2BACiB,IAAzBC,EAAQW,MAAMgB,WAElB,IAIFiD,EAAAA,aAAY,KACV1C,EAAAA,MAAI2C,KAAK,kBAAmBP,EAAiB,ywBA9G7B,CAACQ,IACjBC,EAAAA,MAAM,uCAAuCD,EAAME,KAAI,iPC3FzDC,GAAGC,gBAAgBC"}