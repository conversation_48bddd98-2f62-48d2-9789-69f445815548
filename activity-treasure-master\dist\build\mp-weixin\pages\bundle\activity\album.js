"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/index.js"),t=require("../../../store/index.js");if(require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/index.js"),require("../../../utils/systemInfo.js"),require("../../../utils/auth.js"),require("../../../store/counter.js"),require("../../../utils/cacheManager.js"),!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../../node-modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../../node-modules/uview-plus/components/u-loading-icon/u-loading-icon.js"))();const a={__name:"album",setup(a){const i=e.ref(0),s=e.ref([]),n=e.ref({name:"",publisher_uid:0}),u=e.ref(!1),r=e.ref(!1),l=e.ref(0),c=e.computed((()=>t.store().$state.userInfo));e.onLoad((o=>{o.activity_id?(i.value=parseInt(o.activity_id),d()):(e.index.showToast({title:"参数错误",icon:"error"}),setTimeout((()=>{e.index.navigateBack()}),1500))}));const d=async()=>{try{u.value=!0;const t=await o.huodongget_activity_photos({activity_id:i.value,page:1,page_size:100});"ok"===t.status?(s.value=t.data||[],l.value=t.count||0,s.value.length>0&&(n.value.name="活动相册")):"empty"!==t.status&&"n"!==t&&t.data?e.index.showToast({title:t.msg||"加载失败",icon:"error"}):(s.value=[],l.value=0,console.log("活动相册暂无图片"))}catch(t){console.error("加载图片失败:",t),e.index.showToast({title:"加载失败",icon:"error"})}finally{u.value=!1}},v=()=>{e.index.navigateBack()},h=e.computed((()=>c.value&&c.value.uid)),m=e=>!(!c.value||!c.value.uid)&&(e.user_id===c.value.uid||n.value.publisher_uid===c.value.uid),p=()=>{r.value=!0},g=()=>{r.value=!1},f=()=>{g(),x("camera")},w=()=>{g(),x("album")},x=o=>{e.index.chooseImage({count:1,sourceType:[o],success:e=>{_(e.tempFilePaths[0])},fail:o=>{console.error("选择图片失败:",o),e.index.showToast({title:"选择图片失败",icon:"error"})}})},_=async t=>{try{e.index.showLoading({title:"上传中..."});const a=await o.upload_img(t);if("ok"!==a.status)throw new Error(a.msg||"图片上传失败");const s=await o.huodongupload_activity_photo({uid:c.value.uid,token:c.value.token,activity_id:i.value,photo_url:a.data});"ok"===s.status?(e.index.showToast({title:"上传成功",icon:"success"}),d()):e.index.showToast({title:s.msg||"上传失败",icon:"error"})}catch(a){console.error("上传图片失败:",a),e.index.showToast({title:a.message||"上传失败",icon:"error"})}finally{e.index.hideLoading()}},y=o=>{e.index.showModal({title:"确认删除",content:"确定要删除这张图片吗？",success:e=>{e.confirm&&j(o.id)}})},j=async t=>{try{e.index.showLoading({title:"删除中..."});const a=await o.huodongdelete_activity_photo({uid:c.value.uid,token:c.value.token,photo_id:t});"ok"===a.status?(e.index.showToast({title:"删除成功",icon:"success"}),d()):e.index.showToast({title:a.msg||"删除失败",icon:"error"})}catch(a){console.error("删除图片失败:",a),e.index.showToast({title:"删除失败",icon:"error"})}finally{e.index.hideLoading()}};return(o,t)=>e.e({a:e.p({name:"arrow-left",color:"#333",size:"20"}),b:e.o(v),c:n.value.name},n.value.name?{d:e.t(n.value.name),e:e.t(l.value)}:{},{f:s.value.length>0},s.value.length>0?{g:e.f(s.value,((o,t,a)=>{var i,n;return e.e({a:o.photo_url,b:(null==(i=o.user)?void 0:i.avatar)||"/static/default-avatar.png",c:e.t((null==(n=o.user)?void 0:n.nickname)||"匿名用户"),d:m(o)},m(o)?{e:"23733201-1-"+a,f:e.p({name:"close-circle-fill",color:"#ff4757",size:"16"}),g:e.o((e=>y(o)),o.id)}:{},{h:o.id,i:e.o((o=>(o=>{const t=s.value.map((e=>e.photo_url));e.index.previewImage({urls:t,current:o})})(t)),o.id),j:e.o((t=>(o=>{m(o)&&e.index.showActionSheet({itemList:["删除图片"],success:e=>{0===e.tapIndex&&y(o)}})})(o)),o.id)})}))}:u.value?{}:{i:e.p({name:"photo",color:"#ccc",size:"60"})},{h:!u.value,j:u.value},u.value?{k:e.p({mode:"flower"})}:{},{l:e.unref(h)},e.unref(h)?{m:e.p({name:"plus",color:"#fff",size:"24"}),n:e.o(p)}:{},{o:r.value},r.value?{p:e.p({name:"camera",color:"#6AC086",size:"20"}),q:e.o(f),r:e.p({name:"photo",color:"#6AC086",size:"20"}),s:e.o(w),t:e.o(g),v:e.o((()=>{})),w:e.o(g)}:{})}},i=e._export_sfc(a,[["__scopeId","data-v-23733201"]]);wx.createPage(i);
//# sourceMappingURL=album.js.map
