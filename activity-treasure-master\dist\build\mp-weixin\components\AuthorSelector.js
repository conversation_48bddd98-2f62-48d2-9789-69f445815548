"use strict";const e=require("../common/vendor.js"),a=require("../utils/index.js");if(require("../store/index.js"),require("../store/counter.js"),require("../api/index.js"),require("../utils/request.js"),require("../utils/BaseUrl.js"),require("../utils/auth.js"),require("../utils/cacheManager.js"),require("../utils/systemInfo.js"),!Array){e.resolveComponent("u-icon")()}Math;const t={__name:"AuthorSelector",props:{modelValue:{type:Object,default:null},placeholder:{type:String,default:"选择作者"}},emits:["update:modelValue"],setup(t,{emit:l}){const r=t,u=e.ref(r.modelValue);e.watch((()=>r.modelValue),(e=>{u.value=e}));const o=()=>{a.navto("/pages/bundle/world/author/search?type=select")},s=e=>{e.stopPropagation(),u.value=null,l("update:modelValue",null)};return e.index.$on("authorSelected",(e=>{u.value=e,l("update:modelValue",e)})),e.onUnmounted((()=>{e.index.$off("authorSelected")})),(a,l)=>e.e({a:e.p({name:"account-fill",size:"20",color:"#999"}),b:u.value},u.value?e.e({c:u.value.avatar},u.value.avatar?{d:u.value.avatar}:{e:e.t(u.value.name.charAt(0))},{f:e.t(u.value.name),g:u.value.category},u.value.category?{h:e.t(u.value.category)}:{},{i:e.o(s),j:e.p({name:"close-circle-fill",size:"18",color:"#ccc"})}):{k:e.t(t.placeholder),l:e.p({name:"arrow-right",size:"16",color:"#ccc"})},{m:u.value?1:"",n:e.o(o)})}},l=e._export_sfc(t,[["__scopeId","data-v-40bf9b46"]]);wx.createComponent(l);
//# sourceMappingURL=AuthorSelector.js.map
