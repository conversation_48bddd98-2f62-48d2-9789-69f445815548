"use strict";const e=require("../common/vendor.js");if(require("../utils/request.js"),require("../store/index.js"),require("../utils/BaseUrl.js"),require("../utils/index.js"),require("../api/index.js"),require("../utils/systemInfo.js"),require("../utils/auth.js"),require("../store/counter.js"),require("../utils/cacheManager.js"),!Array){e.resolveComponent("u-text")()}Math;const t={__name:"myStitle",props:{size:{type:String||Number,default:"14"},color:{type:String,default:"#000"},title:{type:String},blod:{type:Boolean,default:!1},rSize:{type:String||Number,default:"20"},rTitle:{type:String},rColor:{type:String},icon:{type:String},iconStyle:{type:Object,default:()=>({})}},emits:["click"],setup:(t,{emit:r})=>(i,o)=>e.e({a:t.title},t.title?{b:e.t(t.title),c:t.color,d:t.size+"rpx",e:t.blod?"bold":"normal"}:{},{f:e.p({"suffix-icon":t.icon,"icon-style":t.iconStyle,color:t.rColor?t.rColor:t.color,text:t.rTitle,size:t.rSize+"rpx"}),g:e.o((e=>r("click")))})};wx.createComponent(t);
//# sourceMappingURL=myStitle.js.map
