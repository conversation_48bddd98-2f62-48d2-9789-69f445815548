"use strict";const e=require("../../../common/vendor.js"),t={components:{myTitle:()=>"../../../components/myTitle.js"},data:()=>({}),methods:{agreeAndClose(){e.index.navigateBack()}}};if(!Array){(e.resolveComponent("myTitle")+e.resolveComponent("u-button"))()}Math||((()=>"../../../components/myTitle.js")+(()=>"../../../node-modules/uview-plus/components/u-button/u-button.js"))();const o=e._export_sfc(t,[["render",function(t,o,n,r,a,i){return{a:e.p({bgColor:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",height:"200rpx",title:"会员服务协议",backShow:!0,color:"#ffffff",blod:!0}),b:e.t((new Date).toLocaleDateString()),c:e.o(i.agreeAndClose),d:e.p({text:"我已阅读并同意",shape:"circle",color:"linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)",customStyle:{width:"100%",height:"88rpx",fontSize:"32rpx",fontWeight:"600",boxShadow:"0 4rpx 16rpx rgba(106, 192, 134, 0.3)"}})}}],["__scopeId","data-v-1cfd954e"]]);wx.createPage(o);
//# sourceMappingURL=membershipAgreement.js.map
