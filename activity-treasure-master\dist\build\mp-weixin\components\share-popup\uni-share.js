"use strict";const e=require("../../common/vendor.js"),s=require("./uni-image-menu.js");class n extends s.NvImageMenu{constructor(e){super(),this.isShow=super.isShow}async show(s,n){var o=[];plus.share.getServices((t=>{let i=(t=t.filter((e=>e.nativeClient))).map((e=>e.id));s.menus.forEach((e=>{(i.includes(e.share.provider)||"string"==typeof e.share)&&o.push(e)})),super.show({list:o,cancelText:s.cancelText},(t=>{n(t),"clickMenu"==t.event&&("string"==typeof o[t.index].share?this[o[t.index].share](s):e.index.share({...s.content,...o[t.index].share,success:e=>{console.log("success:"+JSON.stringify(e)),super.hide()},fail:function(s){console.log("fail:"+JSON.stringify(s)),e.index.showModal({content:JSON.stringify(s),showCancel:!1,confirmText:"知道了"})}}))}))}),(s=>{e.index.showModal({title:"获取服务供应商失败："+JSON.stringify(s),showCancel:!1,confirmText:"知道了"}),console.error("获取服务供应商失败："+JSON.stringify(s))}))}hide(){super.hide()}copyurl(s){console.log("copyurl",s),e.index.setClipboardData({data:s.content.href,success:()=>{console.log("success"),e.index.hideToast(),e.index.showToast({title:"复制成功",icon:"none"}),super.hide()},fail:s=>{e.index.showModal({content:JSON.stringify(s),showCancel:!1})}})}shareSystem(s){console.log("shareSystem",s),plus.share.sendWithSystem({type:"text",content:s.content.title+s.content.summary||"",href:s.content.href},(e=>{console.log("分享成功"),super.hide()}),(s=>{console.log("分享失败："+JSON.stringify(s)),e.index.showModal({title:"获取服务供应商失败："+JSON.stringify(s),showCancel:!1,confirmText:"知道了"})}))}}exports.UniShare=n;
//# sourceMappingURL=uni-share.js.map
