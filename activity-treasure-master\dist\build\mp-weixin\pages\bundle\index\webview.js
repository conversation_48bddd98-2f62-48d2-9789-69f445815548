"use strict";const e=require("../../../common/vendor.js");require("../../../store/index.js"),require("../../../store/counter.js"),require("../../../utils/index.js"),require("../../../api/index.js"),require("../../../utils/request.js"),require("../../../utils/BaseUrl.js"),require("../../../utils/auth.js"),require("../../../utils/cacheManager.js"),require("../../../utils/systemInfo.js");const r={__name:"webview",setup(r){const s=e.ref("");return e.onLoad((e=>{s.value=e.url})),(e,r)=>({a:s.value})},__runtimeHooks:3};wx.createPage(r);
//# sourceMappingURL=webview.js.map
