{"version": 3, "file": "u-tabs.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-tabs/u-tabs.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXRhYnMvdS10YWJzLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"u-tabs\" :class=\"[customClass]\">\n\t\t<view class=\"u-tabs__wrapper\">\n\t\t\t<slot name=\"left\" />\n\t\t\t<view class=\"u-tabs__wrapper__scroll-view-wrapper\">\n\t\t\t\t<scroll-view\n\t\t\t\t\t:scroll-x=\"scrollable\"\n\t\t\t\t\t:scroll-left=\"scrollLeft\"\n\t\t\t\t\tscroll-with-animation\n\t\t\t\t\tclass=\"u-tabs__wrapper__scroll-view\"\n\t\t\t\t\t:show-scrollbar=\"false\"\n\t\t\t\t\tref=\"u-tabs__wrapper__scroll-view\"\n\t\t\t\t>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav\"\n\t\t\t\t\t\tref=\"u-tabs__wrapper__nav\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__item\"\n\t\t\t\t\t\t\tv-for=\"(item, index) in list\"\n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t@tap=\"clickHandler(item, index)\"\n\t\t\t\t\t\t\t@longpress=\"longPressHandler(item,index)\"\n\t\t\t\t\t\t\t:ref=\"`u-tabs__wrapper__nav__item-${index}`\"\n\t\t\t\t\t\t\t:style=\"[addStyle(itemStyle), {flex: scrollable ? '' : 1}]\"\n\t\t\t\t\t\t\t:class=\"[`u-tabs__wrapper__nav__item-${index}`, item.disabled && 'u-tabs__wrapper__nav__item--disabled']\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<slot v-if=\"$slots.content\" name=\"content\" :item=\"item\" :keyName=\"keyName\" :index=\"index\" />\n\t\t\t\t\t\t\t<slot v-else-if=\"!$slots.content && ($slots.default || $slots.$default)\"\n\t\t\t\t\t\t\t\t:item=\"item\" :keyName=\"keyName\" :index=\"index\" />\n\t\t\t\t\t\t\t<text v-else\n\t\t\t\t\t\t\t\t:class=\"[item.disabled && 'u-tabs__wrapper__nav__item__text--disabled']\"\n\t\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__item__text\"\n\t\t\t\t\t\t\t\t:style=\"[textStyle(index)]\"\n\t\t\t\t\t\t\t>{{ item[keyName] }}</text>\n\t\t\t\t\t\t\t<u-badge\n\t\t\t\t\t\t\t\t:show=\"!!(item.badge && (item.badge.show || item.badge.isDot || item.badge.value))\"\n\t\t\t\t\t\t\t\t:isDot=\"item.badge && item.badge.isDot || propsBadge.isDot\"\n\t\t\t\t\t\t\t\t:value=\"item.badge && item.badge.value || propsBadge.value\"\n\t\t\t\t\t\t\t\t:max=\"item.badge && item.badge.max || propsBadge.max\"\n\t\t\t\t\t\t\t\t:type=\"item.badge && item.badge.type || propsBadge.type\"\n\t\t\t\t\t\t\t\t:showZero=\"item.badge && item.badge.showZero || propsBadge.showZero\"\n\t\t\t\t\t\t\t\t:bgColor=\"item.badge && item.badge.bgColor || propsBadge.bgColor\"\n\t\t\t\t\t\t\t\t:color=\"item.badge && item.badge.color || propsBadge.color\"\n\t\t\t\t\t\t\t\t:shape=\"item.badge && item.badge.shape || propsBadge.shape\"\n\t\t\t\t\t\t\t\t:numberType=\"item.badge && item.badge.numberType || propsBadge.numberType\"\n\t\t\t\t\t\t\t\t:inverted=\"item.badge && item.badge.inverted || propsBadge.inverted\"\n\t\t\t\t\t\t\t\tcustomStyle=\"margin-left: 4px;\"\n\t\t\t\t\t\t\t></u-badge>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\tref=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\t\t\twidth: addUnit(lineWidth),\n\t\t\t\t\t\t\t\theight: addUnit(lineHeight),\n\t\t\t\t\t\t\t\tbackground: lineColor,\n\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\n\t\t\t\t\t\t\t}]\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\tref=\"u-tabs__wrapper__nav__line\"\n\t\t\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\t\t\twidth: addUnit(lineWidth),\n\t\t\t\t\t\t\t\ttransform: `translate(${lineOffsetLeft}px)`,\n\t\t\t\t\t\t\t\ttransitionDuration: `${firstTime ? 0 : duration}ms`,\n\t\t\t\t\t\t\t\theight: addUnit(lineHeight),\n\t\t\t\t\t\t\t\tbackground: lineColor,\n\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\n\t\t\t\t\t\t\t}]\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t<slot name=\"right\" />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\tconst animation = uni.requireNativePlugin('animation')\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport defProps from '../../libs/config/props.js'\n\timport { addUnit, addStyle, deepMerge, getPx, sleep, sys } from '../../libs/function/index';\n\t/**\n\t * Tabs 标签\n\t * @description tabs标签组件，在标签多的时候，可以配置为左右滑动，标签少的时候，可以禁止滑动。 该组件的一个特点是配置为滚动模式时，激活的tab会自动移动到组件的中间位置。\n\t * @tutorial https://ijry.github.io/uview-plus/components/tabs.html\n\t * @property {String | Number}\tduration\t\t\t滑块移动一次所需的时间，单位秒（默认 200 ）\n\t * @property {String | Number}\tswierWidth\t\t\tswiper的宽度（默认 '750rpx' ）\n\t * @property {String}\tkeyName\t 从`list`元素对象中读取的键名（默认 'name' ）\n\t * @event {Function(index)} change 标签改变时触发 index: 点击了第几个tab，索引从0开始\n\t * @event {Function(index)} click 点击标签时触发 index: 点击了第几个tab，索引从0开始\n\t * @event {Function(index)} longPress 长按标签时触发 index: 点击了第几个tab，索引从0开始\n\t * @example <u-tabs :list=\"list\" :is-scroll=\"false\" :current=\"current\" @change=\"change\" @longPress=\"longPress\"></u-tabs>\n\t */\n\texport default {\n\t\tname: 'u-tabs',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfirstTime: true,\n\t\t\t\tscrollLeft: 0,\n\t\t\t\tscrollViewWidth: 0,\n\t\t\t\tlineOffsetLeft: 0,\n\t\t\t\ttabsRect: {\n\t\t\t\t\tleft: 0\n\t\t\t\t},\n\t\t\t\tinnerCurrent: 0,\n\t\t\t\tmoving: false,\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tcurrent: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler (newValue, oldValue) {\n\t\t\t\t\t// 内外部值不相等时，才尝试移动滑块\n\t\t\t\t\tif (newValue !== this.innerCurrent) {\n\t\t\t\t\t\tif (typeof newValue == 'string') {\n\t\t\t\t\t\t\tthis.innerCurrent = parseInt(newValue)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.innerCurrent = newValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.resize()\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// list变化时，重新渲染list各项信息\n\t\t\tlist() {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.resize()\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\ttextStyle() {\n\t\t\t\treturn index => {\n\t\t\t\t\tconst style = {}\n\t\t\t\t\t// 取当期是否激活的样式\n\t\t\t\t\tconst customeStyle = (index == this.innerCurrent)\n\t\t\t\t\t\t? addStyle(this.activeStyle) \n\t\t\t\t\t\t: addStyle(this.inactiveStyle)\n\t\t\t\t\t// 如果当前菜单被禁用，则加上对应颜色，需要在此做处理，是因为nvue下，无法在style样式中通过!import覆盖标签的内联样式\n\t\t\t\t\tif (this.list[index].disabled) {\n\t\t\t\t\t\tstyle.color = '#c8c9cc'\n\t\t\t\t\t}\n\t\t\t\t\treturn deepMerge(customeStyle, style)\n\t\t\t\t}\n\t\t\t},\n\t\t\tpropsBadge() {\n\t\t\t\treturn defProps.badge\n\t\t\t}\n\t\t},\n\t\tasync mounted() {\n\t\t\tthis.init()\n\t\t},\n\t\temits: ['click', 'longPress', 'change', 'update:current'],\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tsetLineLeft() {\n\t\t\t\tconst tabItem = this.list[this.innerCurrent];\n\t\t\t\tif (!tabItem) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 获取滑块该移动的位置\n\t\t\t\tlet lineOffsetLeft = this.list\n\t\t\t\t\t.slice(0, this.innerCurrent)\n\t\t\t\t\t.reduce((total, curr) => total + curr.rect.width, 0);\n                // 获取下划线的数值px表示法\n\t\t\t\tconst lineWidth = getPx(this.lineWidth);\n\t\t\t\tthis.lineOffsetLeft = lineOffsetLeft + (tabItem.rect.width - lineWidth) / 2\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 第一次移动滑块，无需过渡时间\n\t\t\t\tthis.animation(this.lineOffsetLeft, this.firstTime ? 0 : parseInt(this.duration))\n\t\t\t\t// #endif\n\n\t\t\t\t// 如果是第一次执行此方法，让滑块在初始化时，瞬间滑动到第一个tab item的中间\n\t\t\t\t// 这里需要一个定时器，因为在非nvue下，是直接通过style绑定过渡时间，需要等其过渡完成后，再设置为false(非第一次移动滑块)\n\t\t\t\tif (this.firstTime) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.firstTime = false\n\t\t\t\t\t}, 10);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// nvue下设置滑块的位置\n\t\t\tanimation(x, duration = 0) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tconst ref = this.$refs['u-tabs__wrapper__nav__line']\n\t\t\t\tanimation.transition(ref, {\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\ttransform: `translateX(${x}px)`\n\t\t\t\t\t},\n\t\t\t\t\tduration\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 点击某一个标签\n\t\t\tclickHandler(item, index) {\n\t\t\t\t// 因为标签可能为disabled状态，所以click是一定会发出的，但是change事件是需要可用的状态才发出\n\t\t\t\tthis.$emit('click', {\n\t\t\t\t\t...item,\n\t\t\t\t\tindex\n\t\t\t\t}, index)\n\t\t\t\t// 如果disabled状态，返回\n\t\t\t\tif (item.disabled) return\n\t\t\t\tthis.innerCurrent = index\n\t\t\t\tthis.resize()\n\t\t\t\tthis.$emit('update:current', index)\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\t...item,\n\t\t\t\t\tindex\n\t\t\t\t}, index)\n\t\t\t},\n\t\t\t// 长按事件\n\t\t\tlongPressHandler(item, index) {\n\t\t\t\tthis.$emit('longPress', {\n\t\t\t\t\t...item,\n\t\t\t\t\tindex\n\t\t\t\t})\n\t\t\t},\n\t\t\tinit() {\n\t\t\t\tsleep().then(() => {\n\t\t\t\t\tthis.resize()\n\t\t\t\t})\n\t\t\t},\n\t\t\tsetScrollLeft() {\n\t\t\t\t// 当前活动tab的布局信息，有tab菜单的width和left(为元素左边界到父元素左边界的距离)等信息\n\t\t\t\tif (this.innerCurrent < 0) {\n                    this.innerCurrent = 0;\n                }\n\t\t\t\tconst tabRect = this.list[this.innerCurrent]\n\t\t\t\t// 累加得到当前item到左边的距离\n\t\t\t\tconst offsetLeft = this.list\n\t\t\t\t\t.slice(0, this.innerCurrent)\n\t\t\t\t\t.reduce((total, curr) => {\n\t\t\t\t\t\treturn total + curr.rect.width\n\t\t\t\t\t}, 0)\n\t\t\t\t// 此处为屏幕宽度\n\t\t\t\tconst windowWidth = sys().windowWidth\n\t\t\t\t// 将活动的tabs-item移动到屏幕正中间，实际上是对scroll-view的移动\n\t\t\t\tlet scrollLeft = offsetLeft - (this.tabsRect.width - tabRect.rect.width) / 2 - (windowWidth - this.tabsRect\n\t\t\t\t\t.right) / 2 + this.tabsRect.left / 2\n\t\t\t\t// 这里做一个限制，限制scrollLeft的最大值为整个scroll-view宽度减去tabs组件的宽度\n\t\t\t\tscrollLeft = Math.min(scrollLeft, this.scrollViewWidth - this.tabsRect.width)\n\t\t\t\tthis.scrollLeft = Math.max(0, scrollLeft)\n\t\t\t},\n\t\t\t// 获取所有标签的尺寸\n\t\t\tresize() {\n\t\t\t\t// 如果不存在list，则不处理\n\t\t\t\tif(this.list.length === 0) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tPromise.all([this.getTabsRect(), this.getAllItemRect()]).then(([tabsRect, itemRect = []]) => {\n\t\t\t\t\t// 兼容在swiper组件中使用\n\t\t\t\t\tif (tabsRect.left > tabsRect.width) {\n\t\t\t\t\t\ttabsRect.right = tabsRect.right - Math.floor(tabsRect.left / tabsRect.width) * tabsRect.width\n\t\t\t\t\t\ttabsRect.left = tabsRect.left % tabsRect.width\n\t\t\t\t\t}\n\t\t\t\t\t// console.log(tabsRect)\n\t\t\t\t\tthis.tabsRect = tabsRect\n\t\t\t\t\tthis.scrollViewWidth = 0\n\t\t\t\t\titemRect.map((item, index) => {\n\t\t\t\t\t\t// 计算scroll-view的宽度，这里\n\t\t\t\t\t\tthis.scrollViewWidth += item.width\n\t\t\t\t\t\t// 另外计算每一个item的中心点X轴坐标\n\t\t\t\t\t\tthis.list[index].rect = item\n\t\t\t\t\t})\n\t\t\t\t\t// 获取了tabs的尺寸之后，设置滑块的位置\n\t\t\t\t\tthis.setLineLeft()\n\t\t\t\t\tthis.setScrollLeft()\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取导航菜单的尺寸\n\t\t\tgetTabsRect() {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tthis.queryRect('u-tabs__wrapper__scroll-view').then(size => resolve(size))\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取所有标签的尺寸\n\t\t\tgetAllItemRect() {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tconst promiseAllArr = this.list.map((item, index) => this.queryRect(\n\t\t\t\t\t\t`u-tabs__wrapper__nav__item-${index}`, true))\n\t\t\t\t\tPromise.all(promiseAllArr).then(sizes => resolve(sizes))\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取各个标签的尺寸\n\t\t\tqueryRect(el, item) {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t// $uGetRect为uView自带的节点查询简化方法，详见文档介绍：https://ijry.github.io/uview-plus/js/getRect.html\n\t\t\t\t// 组件内部一般用this.$uGetRect，对外的为uni.$u.getRect，二者功能一致，名称不同\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tthis.$uGetRect(`.${el}`).then(size => {\n\t\t\t\t\t\tresolve(size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// nvue下，使用dom模块查询元素高度\n\t\t\t\t// 返回一个promise，让调用此方法的主体能使用then回调\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(item ? this.$refs[el][0] : this.$refs[el], res => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-tabs {\n\n\t\t&__wrapper {\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\n\t\t\t&__scroll-view-wrapper {\n\t\t\t\tflex: 1;\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\toverflow: auto hidden;\n\t\t\t\t/* #endif */\n\t\t\t}\n\n\t\t\t&__scroll-view {\n\t\t\t\t@include flex;\n\t\t\t\tflex: 1;\n\t\t\t}\n\n\t\t\t&__nav {\n\t\t\t\t@include flex;\n\t\t\t\tposition: relative;\n\n\t\t\t\t&__item {\n\t\t\t\t\tpadding: 0 11px;\n\t\t\t\t\t@include flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\t/* #endif */\n\n\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t/* #ifdef H5 */\n\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t/* #endif */\n\t\t\t\t\t}\n\n\t\t\t\t\t&__text {\n\t\t\t\t\t\tfont-size: 15px;\n\t\t\t\t\t\tcolor: $u-content-color;\n                        white-space: nowrap !important;\n\n\t\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t\tcolor: $u-disabled-color !important;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&__line {\n\t\t\t\t\theight: 3px;\n\t\t\t\t\tbackground: $u-primary;\n\t\t\t\t\twidth: 30px;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tbottom: 2px;\n\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\ttransition-property: transform;\n\t\t\t\t\ttransition-duration: 300ms;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-tabs/u-tabs.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "firstTime", "scrollLeft", "scrollViewWidth", "lineOffsetLeft", "tabsRect", "left", "innerCurrent", "moving", "watch", "current", "immediate", "handler", "newValue", "oldValue", "this", "parseInt", "$nextTick", "resize", "list", "computed", "textStyle", "index", "style", "customeStyle", "addStyle", "activeStyle", "inactiveStyle", "deepMerge", "disabled", "color", "propsBadge", "defProps", "badge", "mounted", "init", "emits", "methods", "addUnit", "setLineLeft", "tabItem", "slice", "reduce", "total", "curr", "rect", "width", "lineWidth", "getPx", "setTimeout", "animation", "x", "duration", "clickHandler", "item", "$emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "common_vendor", "sleep", "then", "setScrollLeft", "tabRect", "offsetLeft", "windowWidth", "sys", "right", "Math", "min", "max", "length", "Promise", "all", "getTabsRect", "getAllItemRect", "itemRect", "floor", "map", "resolve", "queryRect", "size", "promiseAllArr", "sizes", "el", "$uGetRect", "wx", "createComponent", "Component"], "mappings": "6DA4GMA,EAAU,CACdC,KAAM,SACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzBC,KAAO,KACC,CACNC,WAAW,EACXC,WAAY,EACZC,gBAAiB,EACjBC,eAAgB,EAChBC,SAAU,CACTC,KAAM,GAEPC,aAAc,EACdC,QAAQ,IAGVC,MAAO,CACNC,QAAS,CACRC,WAAW,EACX,OAAAC,CAASC,EAAUC,GAEdD,IAAaE,KAAKR,eAEfQ,KAAAR,aADiB,iBAAZM,EACUG,SAASH,GAETA,EAErBE,KAAKE,WAAU,KACdF,KAAKG,QAAO,IAGf,GAGD,IAAAC,GACCJ,KAAKE,WAAU,KACdF,KAAKG,QAAO,GAEd,GAEDE,SAAU,CACT,SAAAC,GACC,OAAgBC,IACf,MAAMC,EAAQ,CAAC,EAETC,EAAgBF,GAASP,KAAKR,aACjCkB,EAAQA,SAACV,KAAKW,aACdD,EAAQA,SAACV,KAAKY,eAKVC,OAHHb,KAAKI,KAAKG,GAAOO,WACpBN,EAAMO,MAAQ,WAERF,EAASA,UAACJ,EAAcD,EAAK,CAErC,EACDQ,WAAa,IACLC,EAAQA,SAACC,OAGlB,aAAMC,GACLnB,KAAKoB,MACL,EACDC,MAAO,CAAC,QAAS,YAAa,SAAU,kBACxCC,QAAS,CACRZ,SAAAA,EAAQA,SACRa,QAAAA,EAAOA,QACP,WAAAC,GACC,MAAMC,EAAUzB,KAAKI,KAAKJ,KAAKR,cAC/B,IAAKiC,EACJ,OAGD,IAAIpC,EAAiBW,KAAKI,KACxBsB,MAAM,EAAG1B,KAAKR,cACdmC,QAAO,CAACC,EAAOC,IAASD,EAAQC,EAAKC,KAAKC,OAAO,GAEnD,MAAMC,EAAYC,EAAAA,MAAMjC,KAAKgC,WAC7BhC,KAAKX,eAAiBA,GAAkBoC,EAAQK,KAAKC,MAAQC,GAAa,EAQtEhC,KAAKd,WACRgD,YAAW,KACVlC,KAAKd,WAAY,CAAA,GACf,GAEJ,EAED,SAAAiD,CAAUC,EAAGC,EAAW,GAUvB,EAED,YAAAC,CAAaC,EAAMhC,GAElBP,KAAKwC,MAAM,QAAS,IAChBD,EACHhC,SACEA,GAECgC,EAAKzB,WACTd,KAAKR,aAAee,EACpBP,KAAKG,SACAH,KAAAwC,MAAM,iBAAkBjC,GAC7BP,KAAKwC,MAAM,SAAU,IACjBD,EACHhC,SACEA,GACH,EAED,gBAAAkC,CAAiBF,EAAMhC,GACtBP,KAAKwC,MAAM,YAAa,IACpBD,EACHhC,SAED,EACD,IAAAa,GACMsB,EAAAC,QAAGC,MAAK,KACZ5C,KAAKG,QAAO,GAEb,EACD,aAAA0C,GAEK7C,KAAKR,aAAe,IACRQ,KAAKR,aAAe,GAEpC,MAAMsD,EAAU9C,KAAKI,KAAKJ,KAAKR,cAEzBuD,EAAa/C,KAAKI,KACtBsB,MAAM,EAAG1B,KAAKR,cACdmC,QAAO,CAACC,EAAOC,IACRD,EAAQC,EAAKC,KAAKC,OACvB,GAEEiB,EAAcC,EAAGA,MAAGD,YAE1B,IAAI7D,EAAa4D,GAAc/C,KAAKV,SAASyC,MAAQe,EAAQhB,KAAKC,OAAS,GAAKiB,EAAchD,KAAKV,SACjG4D,OAAS,EAAIlD,KAAKV,SAASC,KAAO,EAEpCJ,EAAagE,KAAKC,IAAIjE,EAAYa,KAAKZ,gBAAkBY,KAAKV,SAASyC,OACvE/B,KAAKb,WAAagE,KAAKE,IAAI,EAAGlE,EAC9B,EAED,MAAAgB,GAEyB,IAArBH,KAAKI,KAAKkD,QAGbC,QAAQC,IAAI,CAACxD,KAAKyD,cAAezD,KAAK0D,mBAAmBd,MAAK,EAAEtD,EAAUqE,EAAW,OAEhFrE,EAASC,KAAOD,EAASyC,QACnBzC,EAAA4D,MAAQ5D,EAAS4D,MAAQC,KAAKS,MAAMtE,EAASC,KAAOD,EAASyC,OAASzC,EAASyC,MAC/EzC,EAAAC,KAAOD,EAASC,KAAOD,EAASyC,OAG1C/B,KAAKV,SAAWA,EAChBU,KAAKZ,gBAAkB,EACduE,EAAAE,KAAI,CAACtB,EAAMhC,KAEnBP,KAAKZ,iBAAmBmD,EAAKR,MAExB/B,KAAAI,KAAKG,GAAOuB,KAAOS,CAAA,IAGzBvC,KAAKwB,cACLxB,KAAK6C,eAAc,GAEpB,EAED,WAAAY,GACQ,OAAA,IAAIF,SAAmBO,IAC7B9D,KAAK+D,UAAU,gCAAgCnB,MAAaoB,GAAAF,EAAQE,IAAK,GAE1E,EAED,cAAAN,GACQ,OAAA,IAAIH,SAAmBO,IAC7B,MAAMG,EAAgBjE,KAAKI,KAAKyD,KAAI,CAACtB,EAAMhC,IAAUP,KAAK+D,UACzD,8BAA8BxD,KAAS,KACxCgD,QAAQC,IAAIS,GAAerB,MAAcsB,GAAAJ,EAAQI,IAAM,GAExD,EAED,SAAAH,CAAUI,EAAI5B,GAIN,OAAA,IAAIgB,SAAmBO,IAC7B9D,KAAKoE,UAAU,IAAID,KAAMvB,MAAaoB,IACrCF,EAAQE,EAAI,GACZ,GAaF,8tDCjUJK,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}