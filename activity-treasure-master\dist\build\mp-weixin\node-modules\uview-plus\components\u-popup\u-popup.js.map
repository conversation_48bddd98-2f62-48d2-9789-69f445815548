{"version": 3, "file": "u-popup.js", "sources": ["../../../../../../../node_modules/uview-plus/components/u-popup/u-popup.vue", "../../../../../../../uniComponent:/RDovd29ya3NwYWNlL21lZXR1cC9hY3Rpdml0eS10cmVhc3VyZS1tYXN0ZXIvbm9kZV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBvcHVwL3UtcG9wdXAudnVl"], "sourcesContent": ["<template>\n\t<view class=\"u-popup\"  :class=\"[customClass]\">\n\t\t<u-overlay\n\t\t\t:show=\"show\"\n\t\t\t@click=\"overlayClick\"\n\t\t\tv-if=\"overlay\"\n\t\t\t:zIndex=\"zIndex\"\n\t\t\t:duration=\"overlayDuration\"\n\t\t\t:customStyle=\"overlayStyle\"\n\t\t\t:opacity=\"overlayOpacity\"\n\t\t></u-overlay>\n\t\t<u-transition\n\t\t\t:show=\"show\"\n\t\t\t:customStyle=\"transitionStyle\"\n\t\t\t:mode=\"position\"\n\t\t\t:duration=\"duration\"\n\t\t\t@afterEnter=\"afterEnter\"\n\t\t\t@click=\"clickHandler\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-popup__content\"\n\t\t\t\t:style=\"[contentStyle]\"\n\t\t\t\**********=\"noop\"\n\t\t\t>\n\t\t\t\t<u-status-bar v-if=\"safeAreaInsetTop\"></u-status-bar>\n\t\t\t\t<slot></slot>\n\t\t\t\t<view\n\t\t\t\t\tv-if=\"closeable\"\n\t\t\t\t\**********=\"close\"\n\t\t\t\t\tclass=\"u-popup__content__close\"\n\t\t\t\t\t:class=\"['u-popup__content__close--' + closeIconPos]\"\n\t\t\t\t\thover-class=\"u-popup__content__close--hover\"\n\t\t\t\t\thover-stay-time=\"150\"\n\t\t\t\t>\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\tname=\"close\"\n\t\t\t\t\t\tcolor=\"#909399\"\n\t\t\t\t\t\tsize=\"18\"\n\t\t\t\t\t\tbold\n\t\t\t\t\t></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<u-safe-bottom v-if=\"safeAreaInsetBottom\"></u-safe-bottom>\n\t\t\t</view>\n\t\t</u-transition>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge, sleep, sys } from '../../libs/function/index';\n\t/**\n\t * popup 弹窗\n\t * @description 弹出层容器，用于展示弹窗、信息提示等内容，支持上、下、左、右和中部弹出。组件只提供容器，内部内容由用户自定义\n\t * @tutorial https://ijry.github.io/uview-plus/components/popup.html\n\t * @property {Boolean}\t\t\tshow\t\t\t\t是否展示弹窗 (默认 false )\n\t * @property {Boolean}\t\t\toverlay\t\t\t\t是否显示遮罩 （默认 true ）\n\t * @property {String}\t\t\tmode\t\t\t\t弹出方向（默认 'bottom' ）\n\t * @property {String | Number}\tduration\t\t\t动画时长，单位ms （默认 300 ）\n\t * @property {String | Number}\toverlayDuration\t\t遮罩层动画时长，单位ms （默认 350 ）\n\t * @property {Boolean}\t\t\tcloseable\t\t\t是否显示关闭图标（默认 false ）\n\t * @property {Object | String}\toverlayStyle\t\t自定义遮罩的样式\n\t * @property {String | Number}\toverlayOpacity\t\t遮罩透明度，0-1之间（默认 0.5）\n\t * @property {Boolean}\t\t\tcloseOnClickOverlay\t点击遮罩是否关闭弹窗 （默认  true ）\n\t * @property {String | Number}\tzIndex\t\t\t\t层级 （默认 10075 ）\n\t * @property {Boolean}\t\t\tsafeAreaInsetBottom\t是否为iPhoneX留出底部安全距离 （默认 true ）\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否留出顶部安全距离（状态栏高度） （默认 false ）\n\t * @property {String}\t\t\tcloseIconPos\t\t自定义关闭图标位置（默认 'top-right' ）\n\t * @property {String | Number}\tround\t\t\t\t圆角值（默认 0）\n\t * @property {Boolean}\t\t\tzoom\t\t\t\t当mode=center时 是否开启缩放（默认 true ）\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\n\t * @event {Function} open 弹出层打开\n\t * @event {Function} close 弹出层收起\n\t * @example <u-popup v-model:show=\"show\"><text>出淤泥而不染，濯清涟而不妖</text></u-popup>\n\t */\n\texport default {\n\t\tname: 'u-popup',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\toverlayDuration: this.duration + 50\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tshow(newValue, oldValue) {\n\t\t\t\tif (newValue === true) {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tconst children = this.$children\n\t\t\t\t\tthis.retryComputedComponentRect(children)\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\ttransitionStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tzIndex: this.zIndex,\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t}\n\t\t\t\tstyle[this.mode] = 0\n\t\t\t\tif (this.mode === 'left') {\n\t\t\t\t\treturn deepMerge(style, {\n\t\t\t\t\t\tbottom: 0,\n\t\t\t\t\t\ttop: 0,\n\t\t\t\t\t})\n\t\t\t\t} else if (this.mode === 'right') {\n\t\t\t\t\treturn deepMerge(style, {\n\t\t\t\t\t\tbottom: 0,\n\t\t\t\t\t\ttop: 0,\n\t\t\t\t\t})\n\t\t\t\t} else if (this.mode === 'top') {\n\t\t\t\t\treturn deepMerge(style, {\n\t\t\t\t\t\tleft: 0,\n\t\t\t\t\t\tright: 0\n\t\t\t\t\t})\n\t\t\t\t} else if (this.mode === 'bottom') {\n\t\t\t\t\treturn deepMerge(style, {\n\t\t\t\t\t\tleft: 0,\n\t\t\t\t\t\tright: 0,\n\t\t\t\t\t})\n\t\t\t\t} else if (this.mode === 'center') {\n\t\t\t\t\treturn deepMerge(style, {\n\t\t\t\t\t\talignItems: 'center',\n\t\t\t\t\t\t'justify-content': 'center',\n\t\t\t\t\t\ttop: 0,\n\t\t\t\t\t\tleft: 0,\n\t\t\t\t\t\tright: 0,\n\t\t\t\t\t\tbottom: 0\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tcontentStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\t// 通过设备信息的safeAreaInsets值来判断是否需要预留顶部状态栏和底部安全局的位置\n\t\t\t\t// 不使用css方案，是因为nvue不支持css的iPhoneX安全区查询属性\n\t\t\t\tconst {\n\t\t\t\t\tsafeAreaInsets\n\t\t\t\t} = sys()\n\t\t\t\tif (this.mode !== 'center') {\n\t\t\t\t\tstyle.flex = 1\n\t\t\t\t}\n\t\t\t\t// 背景色，一般用于设置为transparent，去除默认的白色背景\n\t\t\t\tif (this.bgColor) {\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\t}\n\t\t\t\tif(this.round) {\n\t\t\t\t\tconst value = addUnit(this.round)\n\t\t\t\t\tif(this.mode === 'top') {\n\t\t\t\t\t\tstyle.borderBottomLeftRadius = value\n\t\t\t\t\t\tstyle.borderBottomRightRadius = value\n\t\t\t\t\t} else if(this.mode === 'bottom') {\n\t\t\t\t\t\tstyle.borderTopLeftRadius = value\n\t\t\t\t\t\tstyle.borderTopRightRadius = value\n\t\t\t\t\t} else if(this.mode === 'center') {\n\t\t\t\t\t\tstyle.borderRadius = value\n\t\t\t\t\t} \n\t\t\t\t}\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t},\n\t\t\tposition() {\n\t\t\t\tif (this.mode === 'center') {\n\t\t\t\t\treturn this.zoom ? 'fade-zoom' : 'fade'\n\t\t\t\t}\n\t\t\t\tif (this.mode === 'left') {\n\t\t\t\t\treturn 'slide-left'\n\t\t\t\t}\n\t\t\t\tif (this.mode === 'right') {\n\t\t\t\t\treturn 'slide-right'\n\t\t\t\t}\n\t\t\t\tif (this.mode === 'bottom') {\n\t\t\t\t\treturn 'slide-up'\n\t\t\t\t}\n\t\t\t\tif (this.mode === 'top') {\n\t\t\t\t\treturn 'slide-down'\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\temits: [\"open\", \"close\", \"click\", \"update:show\"],\n\t\tmethods: {\n\t\t\t// 点击遮罩\n\t\t\toverlayClick() {\n\t\t\t\tif (this.closeOnClickOverlay) {\n\t\t\t\t\tthis.$emit('update:show', false)\n\t\t\t\t\tthis.$emit('close')\n\t\t\t\t}\n\t\t\t},\n\t\t\tclose(e) {\n\t\t\t\tthis.$emit('update:show', false)\n\t\t\t\tthis.$emit('close')\n\t\t\t},\n\t\t\tafterEnter() {\n\t\t\t\tthis.$emit('open')\n\t\t\t},\n\t\t\tclickHandler() {\n\t\t\t\t// 由于中部弹出时，其u-transition占据了整个页面相当于遮罩，此时需要发出遮罩点击事件，是否无法通过点击遮罩关闭弹窗\n\t\t\t\tif(this.mode === 'center') {\n\t\t\t\t\tthis.overlayClick()\n\t\t\t\t}\n\t\t\t\tthis.$emit('click')\n\t\t\t},\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tretryComputedComponentRect(children) {\n\t\t\t\t// 组件内部需要计算节点的组件\n\t\t\t\tconst names = ['u-calendar-month', 'u-album', 'u-collapse-item', 'u-dropdown', 'u-index-item', 'u-index-list',\n\t\t\t\t\t'u-line-progress', 'u-list-item', 'u-rate', 'u-read-more', 'u-row', 'u-row-notice', 'u-scroll-list',\n\t\t\t\t\t'u-skeleton', 'u-slider', 'u-steps-item', 'u-sticky', 'u-subsection', 'u-swipe-action-item', 'u-tabbar',\n\t\t\t\t\t'u-tabs', 'u-tooltip'\n\t\t\t\t]\n\t\t\t\t// 历遍所有的子组件节点\n\t\t\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\t\t\tconst child = children[i]\n\t\t\t\t\t// 拿到子组件的子组件\n\t\t\t\t\tconst grandChild = child.$children\n\t\t\t\t\t// 判断如果在需要重新初始化的组件数组中名中，并且存在init方法的话，则执行\n\t\t\t\t\tif (names.includes(child.$options.name) && typeof child?.init === 'function') {\n\t\t\t\t\t\t// 需要进行一定的延时，因为初始化页面需要时间\n\t\t\t\t\t\tsleep(50).then(() => {\n\t\t\t\t\t\t\tchild.init()\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\t// 如果子组件还有孙组件，进行递归历遍\n\t\t\t\t\tif (grandChild.length) {\n\t\t\t\t\t\tthis.retryComputedComponentRect(grandChild)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\t$u-popup-flex:1 !default;\n\t$u-popup-content-background-color: #fff !default;\n\n\t.u-popup {\n\t\tflex: $u-popup-flex;\n\n\t\t&__content {\n\t\t\tbackground-color: $u-popup-content-background-color;\n\t\t\tposition: relative;\n\n\t\t\t&--round-top {\n\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\tborder-bottom-left-radius: 10px;\n\t\t\t\tborder-bottom-right-radius: 10px;\n\t\t\t}\n\n\t\t\t&--round-left {\n\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\tborder-top-right-radius: 10px;\n\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\tborder-bottom-right-radius: 10px;\n\t\t\t}\n\n\t\t\t&--round-right {\n\t\t\t\tborder-top-left-radius: 10px;\n\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\tborder-bottom-left-radius: 10px;\n\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t}\n\n\t\t\t&--round-bottom {\n\t\t\t\tborder-top-left-radius: 10px;\n\t\t\t\tborder-top-right-radius: 10px;\n\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t}\n\n\t\t\t&--round-center {\n\t\t\t\tborder-top-left-radius: 10px;\n\t\t\t\tborder-top-right-radius: 10px;\n\t\t\t\tborder-bottom-left-radius: 10px;\n\t\t\t\tborder-bottom-right-radius: 10px;\n\t\t\t}\n\n\t\t\t&__close {\n\t\t\t\tposition: absolute;\n\n\t\t\t\t&--hover {\n\t\t\t\t\topacity: 0.4;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__close--top-left {\n\t\t\t\ttop: 15px;\n\t\t\t\tleft: 15px;\n\t\t\t}\n\n\t\t\t&__close--top-right {\n\t\t\t\ttop: 15px;\n\t\t\t\tright: 15px;\n\t\t\t}\n\n\t\t\t&__close--bottom-left {\n\t\t\t\tbottom: 15px;\n\t\t\t\tleft: 15px;\n\t\t\t}\n\n\t\t\t&__close--bottom-right {\n\t\t\t\tright: 15px;\n\t\t\t\tbottom: 15px;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/workspace/meetup/activity-treasure-master/node_modules/uview-plus/components/u-popup/u-popup.vue'\nwx.createComponent(Component)"], "names": ["_sfc_main", "name", "mixins", "mpMixin", "mixin", "props", "data", "overlayDuration", "this", "duration", "watch", "show", "newValue", "oldValue", "children", "$children", "retryComputedComponentRect", "computed", "transitionStyle", "style", "zIndex", "position", "display", "mode", "deepMerge", "bottom", "top", "left", "right", "alignItems", "contentStyle", "sys", "flex", "bgColor", "backgroundColor", "round", "value", "addUnit", "borderBottomLeftRadius", "borderBottomRightRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderRadius", "addStyle", "customStyle", "zoom", "emits", "methods", "overlayClick", "closeOnClickOverlay", "$emit", "close", "e", "afterEnter", "clickHandler", "names", "i", "length", "child", "<PERSON><PERSON><PERSON><PERSON>", "includes", "$options", "init", "sleep", "then", "wx", "createComponent", "Component"], "mappings": "6DA4EMA,EAAU,CACdC,KAAM,UACNC,OAAQ,CAACC,EAAAA,QAASC,EAAKA,MAAEC,YACzB,IAAAC,GACQ,MAAA,CACNC,gBAAiBC,KAAKC,SAAW,GAElC,EACDC,MAAO,CACN,IAAAC,CAAKC,EAAUC,GACd,IAAiB,IAAbD,EAAmB,CAEtB,MAAME,EAAWN,KAAKO,UACtBP,KAAKQ,2BAA2BF,EAEjC,CACD,GAEDG,SAAU,CACT,eAAAC,GACC,MAAMC,EAAQ,CACbC,OAAQZ,KAAKY,OACbC,SAAU,QACVC,QAAS,QAGN,OADEH,EAAAX,KAAKe,MAAQ,EACD,SAAdf,KAAKe,MAKgB,UAAdf,KAAKe,KAJRC,EAAAA,UAAUL,EAAO,CACvBM,OAAQ,EACRC,IAAK,IAOkB,QAAdlB,KAAKe,MAKS,WAAdf,KAAKe,KAJRC,EAAAA,UAAUL,EAAO,CACvBQ,KAAM,EACNC,MAAO,IAOgB,WAAdpB,KAAKe,KACRC,EAAAA,UAAUL,EAAO,CACvBU,WAAY,SACZ,kBAAmB,SACnBH,IAAK,EACLC,KAAM,EACNC,MAAO,EACPH,OAAQ,UAGV,EACD,YAAAK,GACC,MAAMX,EAAQ,CAAC,EAaf,GARIY,QACc,WAAdvB,KAAKe,OACRJ,EAAMa,KAAO,GAGVxB,KAAKyB,UACRd,EAAMe,gBAAkB1B,KAAKyB,SAE3BzB,KAAK2B,MAAO,CACd,MAAMC,EAAQC,EAAAA,QAAQ7B,KAAK2B,OACV,QAAd3B,KAAKe,MACPJ,EAAMmB,uBAAyBF,EAC/BjB,EAAMoB,wBAA0BH,GACT,WAAd5B,KAAKe,MACdJ,EAAMqB,oBAAsBJ,EAC5BjB,EAAMsB,qBAAuBL,GACN,WAAd5B,KAAKe,OACdJ,EAAMuB,aAAeN,EAEvB,CACA,OAAOZ,EAASA,UAACL,EAAOwB,EAAQA,SAACnC,KAAKoC,aACtC,EACD,QAAAvB,GACK,MAAc,WAAdb,KAAKe,KACDf,KAAKqC,KAAO,YAAc,OAEhB,SAAdrC,KAAKe,KACD,aAEU,UAAdf,KAAKe,KACD,cAEU,WAAdf,KAAKe,KACD,WAEU,QAAdf,KAAKe,KACD,kBADJ,CAGJ,GAEFuB,MAAO,CAAC,OAAQ,QAAS,QAAS,eAClCC,QAAS,CAER,YAAAC,GACKxC,KAAKyC,sBACHzC,KAAA0C,MAAM,eAAe,GAC1B1C,KAAK0C,MAAM,SAEZ,EACD,KAAAC,CAAMC,GACA5C,KAAA0C,MAAM,eAAe,GAC1B1C,KAAK0C,MAAM,QACX,EACD,UAAAG,GACC7C,KAAK0C,MAAM,OACX,EACD,YAAAI,GAEkB,WAAd9C,KAAKe,MACPf,KAAKwC,eAENxC,KAAK0C,MAAM,QACX,EAED,0BAAAlC,CAA2BF,GAE1B,MAAMyC,EAAQ,CAAC,mBAAoB,UAAW,kBAAmB,aAAc,eAAgB,eAC9F,kBAAmB,cAAe,SAAU,cAAe,QAAS,eAAgB,gBACpF,aAAc,WAAY,eAAgB,WAAY,eAAgB,sBAAuB,WAC7F,SAAU,aAGX,IAAA,IAASC,EAAI,EAAGA,EAAI1C,EAAS2C,OAAQD,IAAK,CACnC,MAAAE,EAAQ5C,EAAS0C,GAEjBG,EAAaD,EAAM3C,UAErBwC,EAAMK,SAASF,EAAMG,SAAS5D,OAAgC,mBAAT,MAAPyD,OAAO,EAAAA,EAAAI,OAExDC,QAAM,IAAIC,MAAK,KACdN,EAAMI,MAAK,IAITH,EAAWF,QACdjD,KAAKQ,2BAA2B2C,EAElC,CACD,unCClOHM,GAAGC,gBAAgBC", "x_google_ignoreList": [0]}